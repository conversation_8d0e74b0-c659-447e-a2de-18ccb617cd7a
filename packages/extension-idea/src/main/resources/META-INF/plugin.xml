<idea-plugin require-restart="true">
  <id>Code Wiz</id>

  <depends optional="true">com.intellij.platform</depends>
  <depends optional="true">com.intellij.modules.lang</depends>
  <depends optional="true">Git4Idea</depends>
  <depends optional="true">org.jetbrains.completion.full.line</depends>
  <depends optional="true">org.jetbrains.plugins.terminal</depends>

  <resource-bundle>codewiz</resource-bundle>

  <extensionPoints>
    <extensionPoint qualifiedName="com.xhs.codewiz.editorSupport"
      interface="com.xhs.codewiz.editor.util.CodewizEditorSupport"
      dynamic="true"/>

    <extensionPoint qualifiedName="com.xhs.codewiz.languageIdSupport"
      interface="com.xhs.codewiz.editor.language.LanguageInfoSupport"
      dynamic="true"/>
  </extensionPoints>

  <applicationListeners>
    <listener topic="com.xhs.codewiz.listener.topic.CodewizAgentProcessStatusListener"
      class="com.xhs.codewiz.client.ConnectManagerAgentRestartListener"/>

    <listener topic="com.xhs.codewiz.listener.topic.CodewizInlayListener"
      class="com.xhs.codewiz.completion.CodeWizInlayGotItListener"/>

    <listener class="com.xhs.codewiz.listener.CodeWizAppLifecycleListener"
      topic="com.intellij.ide.AppLifecycleListener"/>

    <listener class="com.xhs.codewiz.listener.MultiProjectManagerListener"
      topic="com.intellij.openapi.project.ProjectManagerListener"/>

    <listener class="com.xhs.codewiz.listener.ThemeChangeListener"
      topic="com.intellij.ide.ui.LafManagerListener"/>
  </applicationListeners>

  <projectListeners>
    <!--<listener topic="com.intellij.codeInsight.lookup.LookupManagerListener"
      class="com.xhs.copilot.editor.CopilotLookupListener"/>-->
    <listener topic="com.intellij.openapi.command.CommandListener"
      class="com.xhs.codewiz.listener.CodewizCommandListener"/>
    <listener
      class="com.xhs.codewiz.listener.CodeWizVirtualFileListener"
      topic="com.intellij.openapi.vfs.VirtualFileListener"/>

    <listener topic="com.intellij.openapi.fileEditor.FileEditorManagerListener"
      class="com.xhs.codewiz.listener.EditorFocusListener"/>

    <listener topic="com.intellij.openapi.fileEditor.FileEditorManagerListener"
      class="com.xhs.codewiz.listener.LSPEditorListener"/>

    <listener topic="com.intellij.codeInsight.lookup.LookupManagerListener"
      class="com.xhs.codewiz.listener.CodeWizLookupListener"/>

    <listener topic="com.intellij.openapi.fileEditor.FileEditorManagerListener$Before"
      class="com.xhs.codewiz.listener.BeforeEditorManagerListener"/>

    <listener topic="com.intellij.openapi.project.ProjectManagerListener"
      class="com.xhs.codewiz.listener.WorkspaceFolderListener"/>
    <listener topic="com.intellij.openapi.project.ModuleListener"
      class="com.xhs.codewiz.listener.WorkspaceFolderListener"/>
  </projectListeners>

  <extensions defaultExtensionNs="com.xhs.codewiz">
    <languageIdSupport implementation="com.xhs.codewiz.editor.language.ExtensionOverrideLanguageInfoSupport"/>
    <languageIdSupport order="last"
      implementation="com.xhs.codewiz.editor.language.DefaultLanguageInfoSupport"/>
  </extensions>


  <extensions defaultExtensionNs="com.intellij">

    <postStartupActivity implementation="com.xhs.codewiz.node.RunnerStartupActivity"/>

    <intentionAction>
      <className>com.xhs.codewiz.actions.problem.CodeProblemsIntentionAction</className>
    </intentionAction>

    <statusBarWidgetFactory id = "com.xhs.codewizWidget" implementation="com.xhs.codewiz.actions.statusBar.CodeWizWidgetFactory"/>

    <editorFactoryListener implementation="com.xhs.codewiz.listener.CodeWizEditorFactoryListener"/>

    <applicationService serviceImplementation="com.xhs.codewiz.actions.status.CodeWizStatusService"/>

    <applicationService serviceImplementation="com.xhs.codewiz.setting.CodeWizApplicationSettings"/>

    <applicationConfigurable
      instance="com.xhs.codewiz.setting.AppSettingsConfigurable"
      id="com.xhs.copilot.settings.AppSettingsConfigurable"
      displayName="Rednote CodeWiz"/>

    <notificationGroup id="rcs.notifications" displayType="BALLOON"
      key="notifications.rcs.group.name"/>

    <applicationService
      serviceInterface="com.xhs.codewiz.editor.CodeWizEditorManager"
      serviceImplementation="com.xhs.codewiz.editor.CodeWizEditorManagerImpl"/>

    <applicationService
      serviceInterface="com.xhs.codewiz.lang.agent.CodeWizAgentProcessService"
      serviceImplementation="com.xhs.codewiz.lang.agent.RestartableCodeWizAgentProcessService"
      testServiceImplementation="com.xhs.copilot.lang.agent.CopilotAgentProcessTestService"/>

    <applicationService
      serviceInterface="com.xhs.codewiz.completion.CodewizCompletionService"
      serviceImplementation="com.xhs.codewiz.completion.CodewizAgentCompletionService"/>

    <applicationService serviceImplementation="com.xhs.codewiz.listener.LSPManager"/>

    <editorFactoryDocumentListener implementation="com.xhs.codewiz.listener.CodeWizDocumentListener"/>

    <vfs.asyncListener implementation="com.xhs.codewiz.listener.AsyncFileChangesListener"/>"/>

<!--    <postStartupActivity implementation="com.xhs.codewiz.update.AutoUpdateActivityFromRemote"/>-->
    <postStartupActivity implementation="com.xhs.codewiz.listener.FileCloseReasonStartupActivity"/>


      <!--<toolWindow id="CodeWiz"
                  doNotActivateOnStart="true"
                  anchor="right"
                  secondary="false"
                  canCloseContents="false"
                  icon="icons/copilot_toolwindow.svg"
                  factoryClass="com.xhs.codewiz.factory.webview.BrowserWindowFactory"/>-->
    <!-- Chat editor file editor provider -->
    <fileEditorProvider implementation="com.xhs.codewiz.factory.editor.TopicEditorProvider"/>

    <!-- Custom icon provider for TopicEditor -->
    <iconProvider implementation="com.xhs.codewiz.factory.editor.TopicIconProvider"/>

  </extensions>


  <actions>

    <!-- statusBar Action start -->
    <action id="codewiz.rcs.openStatus" class="com.xhs.codewiz.actions.ShowStatusPanelAction"/>

    <action id="codewiz.rcs.enableCodewiz"
      class="com.xhs.codewiz.actions.EnableCopilotCompletionsAction">
      <keyboard-shortcut first-keystroke="ctrl shift alt O" keymap="$default"/>
    </action>

    <action id="codewiz.rcs.disableCodewiz" use-shortcut-of="codewiz.rcs.enableCodewiz"
      class="com.xhs.codewiz.actions.DisableGlobalCopilotCompletionsAction">
    </action>

    <group id="codewiz.rcs.statusBarPopup">
      <reference ref="codewiz.rcs.enableCodewiz"/>
      <reference ref="codewiz.rcs.disableCodewiz"/>
    </group>
    <!-- statusBar Action end -->

    <!-- apply completion result Action start -->
    <action id="codewiz.rcs.applyInlays"
      class="com.xhs.codewiz.actions.CodeWizApplyInlaysAction">
      <keyboard-shortcut first-keystroke="TAB" keymap="$default"/>
      <override-text place="MainMenu" text="Apply Completions to Editor"/>
      <override-text place="EditorPopup" text="Accept"/>
    </action>

    <action id="codewiz.rcs.applyInlaysNextWord"
      class="com.xhs.codewiz.actions.CodeWizApplyNextWordInlayAction">
      <keyboard-shortcut first-keystroke="control RIGHT" keymap="$default"/>
      <override-text place="MainMenu" text="Apply Next Word of Completion to Editor"/>
      <override-text place="EditorPopup" text="Accept Next Word"/>
    </action>

    <action id="codewiz.rcs.applyInlaysNextLine"
      class="com.xhs.codewiz.actions.CodeWizApplyNextLineInlayAction">
      <keyboard-shortcut first-keystroke="control alt RIGHT" keymap="$default"/>
      <keyboard-shortcut first-keystroke="control meta RIGHT" keymap="Mac OS X" replace-all="true"/>
      <keyboard-shortcut first-keystroke="control meta RIGHT" keymap="Mac OS X 10.5+" replace-all="true"/>
      <override-text place="MainMenu" text="Apply Next Line of Completion to Editor"/>
      <override-text place="EditorPopup" text="Accept Next Line"/>
    </action>

    <action id="codewiz.rcs.disposeInlays"
      class="com.xhs.codewiz.actions.CodeWizDisposeInlaysAction">
      <keyboard-shortcut first-keystroke="ESCAPE" keymap="$default"/>
      <override-text place="MainMenu" text="Hide Completions in Editor"/>
    </action>

    <group id="codewiz.rcs.editor.menu" popup="true">
      <separator/>
      <add-to-group group-id="EditorPopupMenu" anchor="first"/>
    </group>

    <group id="codewiz.rcs.CodeWizStatusBarItem">
      <separator/>
      <add-to-group group-id="codewiz.rcs.statusBarPopup"  anchor="last"/>
    </group>


      <action id="codewiz.rcs.triggerChatEditor"
              class="com.xhs.codewiz.factory.webview.action.TopicToolAction"
              text="topic dev tool"
              description="在编辑器中显示聊天页面">
          <keyboard-shortcut first-keystroke="ctrl shift T" keymap="$default"/>
          <add-to-group group-id="ToolsMenu" anchor="last"/>
          <add-to-group group-id="EditorPopupMenu" anchor="last"/>
      </action>
    <!-- <action id="codewiz.rcs.pic.preview" -->
    <!--         class="com.xhs.codewiz.factory.webview.action.ImagePreviewAction" -->
    <!--         text="图片预览" -->
    <!--         description="图片预览"> -->
    <!--   <add-to-group group-id="ToolsMenu" anchor="last"/> -->
    <!--   <add-to-group group-id="EditorPopupMenu" anchor="last"/> -->
    <!-- </action> -->
  </actions>

</idea-plugin>
codewiz.plugin.id=Code Wiz

action.codewiz.disableCodewiz.statusEnabled=\u7981\u7528\u8865\u5168

action.codewiz.enableCodewiz.statusEnabled=\u5f00\u542f\u8865\u5168

codewizStatus.ready=Ready

codewizStatus.completionInProgress=Retrieving completions

codewizStatus.agentWarning=Rednote CodeWiz is encountering temporary issues

codewizStatus.notSignedIn=Not signed in to Rednote CodeWiz

codewizStatus.agentError=Error

codewizStatus.agentBroken=<html>Failed to launch Rednote CodeWiz helper process.<br>Please restart your IDE to reinitialize it.</html>

codewizStatus.incompatibleClient=<html>Incompatible plugin version.<br>Please update the Rednote CodeWiz plugin.</html>

codewizStatus.unsupported=Rednote CodeWiz is not supported on this machine

codewizStatus.unknownError=Unknown error

statusBar.tooltipForError={0}

statusBar.tooltipForDisabled=\u70b9\u51fb\u5f00\u542f\u8865\u5168

statusBar.tooltipForEnabled=\u70b9\u51fb\u7981\u7528\u8865\u5168

statusBar.tooltipForErrorCustomMessage={0}: {1}

statusBar.popup.title=Rednote CodeWiz

notifications.rcs.group.name=Rednote CodeWiz

statusBar.displayName=Rednote CodeWiz

editor.inlayGotIt.header=\u4f60\u7684\u7b2c\u4e00\u4e2a\u0020\u0052\u0065\u0064\u006e\u006f\u0074\u0065\u0020\u0043\u006f\u0064\u0065\u0057\u0069\u007a\u0020\u5efa\u8bae\u5df2\u751f\u6210\u3002

editor.inlayGotIt.message=\u5982\u4f55\u91c7\u7eb3\u5efa\u8bae\uff1a<br>\u6309 <b>{0}</b> \u53ef\u5c06\u5176\u63d2\u5165\u7f16\u8f91\u5668\uff1b<br>\u6309 <b>{1}</b> \u53ef\u5c06\u5355\u8bcd\u63d2\u5165\u7f16\u8f91\u5668\uff1b<br>\u6309 <b>{2}</b> \u53ef\u5c06\u6574\u884c\u63d2\u5165\u7f16\u8f91\u5668\u3002

action.codewiz.rcs.enableCodewiz.text=\u5f00\u542f\u8865\u5168

action.codewiz.rcs.disableCodewiz.text=\u7981\u7528\u8865\u5168

group.codewiz.rcs.editor.menu.text=Rednote CodeWiz

requestsDisabledNotification.title=Rednote CodeWiz

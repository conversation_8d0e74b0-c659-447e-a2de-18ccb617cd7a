package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Content;
import com.xhs.codewiz.type.file.FileSystemError;
public class ReadFileResponse {
    private Content result; // optional
    private String error; // optional

    public Content getResult() {
        return result;
    }
    public void setResult(Content result) {
        this.result = result;
    }
    public String getError() {
        return error;
    }
    public void setError(String error) {
        this.error = error;
    }
}

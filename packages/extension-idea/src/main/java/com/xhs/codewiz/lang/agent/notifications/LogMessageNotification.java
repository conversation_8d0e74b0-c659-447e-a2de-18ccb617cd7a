package com.xhs.codewiz.lang.agent.notifications;

import com.google.gson.annotations.SerializedName;

public class LogMessageNotification implements LogMessage {
    @SerializedName("level")
    int level;
    @SerializedName("message")
    String message;
    @SerializedName("metadataStr")
    String metadata;

    public boolean isDebug() {
        return this.level == 0;
    }

    public boolean isInfo() {
        return this.level == 1;
    }

    public boolean isWarn() {
        return this.level == 2;
    }

    public boolean isError() {
        return this.level == 3;
    }

    public LogMessageNotification() {
    }

    public int getLevel() {
        return this.level;
    }

    public String getMessage() {
        return this.message;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

}


package com.xhs.codewiz.platform.model;

import com.xhs.codewiz.platform.enums.CommentStatus;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/2 14:20
 */
public class ShowDiffRequest {
    private List<DiffItem> diffItems;
    private int index;
    private String noteId;

    public ShowDiffRequest() {

    }

    public ShowDiffRequest(List<DiffItem> diffItems, int index) {
        this.diffItems = diffItems;
        this.index = index;
    }

    public List<DiffItem> getDiffItems() {
        return diffItems;
    }

    public void setDiffItems(List<DiffItem> diffItems) {
        this.diffItems = diffItems;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getNoteId() {
        return noteId;
    }

    public void setNoteId(String noteId) {
        this.noteId = noteId;
    }

    public static class DiffItem {
        private int startLine;
        private int endLine;
        private List<DiffComment> comments;

        public int getStartLine() {
            return startLine;
        }

        public void setStartLine(int startLine) {
            this.startLine = startLine;
        }

        public int getEndLine() {
            return endLine;
        }

        public void setEndLine(int endLine) {
            this.endLine = endLine;
        }

        public List<DiffComment> getComments() {
            return comments;
        }

        public void setComments(List<DiffComment> comments) {
            this.comments = comments;
        }
    }

    /**
     * Diff页面中的评论数据
     */
    public static class DiffComment {
        private int startLine;
        private int endLine;
        private String noteId;
        private String noteContent;
        private int index;
        private int size;

        private String collectId;
        private String channel;
        
        // 评论状态
        private CommentStatus status = CommentStatus.UNKNOWN;

        public DiffComment() {
        }

        public DiffComment(int startLine, int endLine, String noteId, String noteContent) {
            this.startLine = startLine;
            this.endLine = endLine;
            this.noteId = noteId;
            this.noteContent = noteContent;
        }

        public int getStartLine() {
            return startLine;
        }

        public void setStartLine(int startLine) {
            this.startLine = startLine;
        }

        public int getEndLine() {
            return endLine;
        }

        public void setEndLine(int endLine) {
            this.endLine = endLine;
        }

        public String getNoteId() {
            return noteId;
        }

        public void setNoteId(String noteId) {
            this.noteId = noteId;
        }

        public String getNoteContent() {
            return noteContent;
        }

        public void setNoteContent(String noteContent) {
            this.noteContent = noteContent;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }

        public CommentStatus getStatus() {
            return status;
        }

        public void setStatus(CommentStatus status) {
            this.status = status != null ? status : CommentStatus.UNKNOWN;
        }

        /**
         * 检查是否已经操作过
         */
        public boolean hasUserAction() {
            return status != CommentStatus.UNKNOWN;
        }

        public String getCollectId() {
            return collectId;
        }

        public void setCollectId(String collectId) {
            this.collectId = collectId;
        }

        public String getChannel() {
            return channel;
        }

        public void setChannel(String channel) {
            this.channel = channel;
        }
    }
}

package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 获取内容提供者提供的 File Content
 */
public class ReadContent {
    private String schemaProtocol = "workspace.read.content";
    private ReadContentParams params;

    public ReadContentParams getParams() {
        return params;
    }
    public void setParams(ReadContentParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadContentParams {
        /** 内容提供者的 ID */
        private String id;
        private String uri;
    
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

package com.xhs.codewiz.scheme.global;

import java.util.*;
/**
 * 心跳检测
 */
public class ExecuteHeartbeat {
    private String schemaProtocol = "global.execute.heartbeat";
    private ExecuteHeartbeatParams params;

    public ExecuteHeartbeatParams getParams() {
        return params;
    }
    public void setParams(ExecuteHeartbeatParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteHeartbeatParams {
        private Boolean ping; // optional
        private Map<String, Object> subModules; // optional
    
        public Boolean getPing() {
            return ping;
        }
        public void setPing(Boolean ping) {
            this.ping = ping;
        }
        public Map<String, Object> getSubModules() {
            return subModules;
        }
        public void setSubModules(Map<String, Object> subModules) {
            this.subModules = subModules;
        }
    }
}

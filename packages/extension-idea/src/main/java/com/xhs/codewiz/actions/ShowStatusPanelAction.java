package com.xhs.codewiz.actions;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.WindowManager;
import com.xhs.codewiz.actions.statusBar.CodeWizStatusBarWidget;
import org.jetbrains.annotations.NotNull;

public class ShowStatusPanelAction extends AnAction implements DumbAware {
    public ShowStatusPanelAction() {
    }

    public void update(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        boolean enabled = project != null && WindowManager.getInstance().getFrame(project) != null;
        e.getPresentation().setEnabled(enabled);
    }

    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project != null) {
            //未登录时需要先禁用再展示
            /*if (!PluginToUIMsgService.isLogin()) {
                CodeWizCompetionActionUtil.disabledCodewiz(e);
            }*/
            CodeWizStatusBarWidget.showStatusPopupAtCenter(project);
        }

    }
}


package com.xhs.codewiz.utils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.TypeAdapter;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.xhs.codewiz.type.content.ActionKind;
import com.xhs.codewiz.type.global.Severity;
import com.xhs.codewiz.type.platform.BuildInPanel;
import com.xhs.codewiz.type.platform.BuildInPanel.BuildInPanelDiff;
import com.xhs.codewiz.type.platform.BuildInPanel.BuildInPanelProblems;
import com.xhs.codewiz.type.platform.BuildInPanel.BuildInPanelViews;
import com.xhs.codewiz.type.platform.PlatformName;
import com.xhs.codewiz.type.platform.SeriesName;
import com.xhs.codewiz.type.workspace.FileChanges;
import com.xhs.codewiz.type.workspace.FileChanges.FileChangesContent;
import com.xhs.codewiz.type.workspace.FileChanges.FileChangesFile;

public class GsonUtil {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    private static final TypeAdapter<LocalDateTime> LOCAL_DATE_TIME_ADAPTER = new TypeAdapter<LocalDateTime>() {
        @Override
        public void write(JsonWriter out, LocalDateTime value) throws IOException {
            if (value == null) {
                out.nullValue();
            } else {
                out.value(value.format(DATE_TIME_FORMATTER));
            }
        }

        @Override
        public LocalDateTime read(JsonReader in) throws IOException {
            if (in.peek() == com.google.gson.stream.JsonToken.NULL) {
                in.nextNull();
                return null;
            }
            String dateTimeString = in.nextString();
            return LocalDateTime.parse(dateTimeString, DATE_TIME_FORMATTER);
        }
    };

    public static final TypeAdapter<Severity> SEVERITY_ADAPTER = new TypeAdapter<Severity>() {
        @Override
        public void write(JsonWriter out, Severity value) throws IOException {
            if (value == null) {
                out.nullValue();
            } else {
                out.value(value.getValue());
            }
        }

        @Override
        public Severity read(JsonReader in) throws IOException {
            if (in.peek() == com.google.gson.stream.JsonToken.NULL) {
                in.nextNull();
                return null;
            }
            int value = in.nextInt();
            for (Severity severity : Severity.values()) {
                if (severity.getValue() == value) {
                    return severity;
                }
            }
            throw new IllegalArgumentException("Unknown Severity value: " + value);
        }
    };

    public static final TypeAdapter<PlatformName> PLATFORM_NAME_ADAPTER = new TypeAdapter<PlatformName>() {
        @Override
        public void write(JsonWriter out, PlatformName value) throws IOException {
            if (value == null) {
                out.nullValue();
            } else {
                out.value(value.getValue());
            }
        }

        @Override
        public PlatformName read(JsonReader in) throws IOException {
            if (in.peek() == com.google.gson.stream.JsonToken.NULL) {
                in.nextNull();
                return null;
            }
            String value = in.nextString();
            for (PlatformName platformName : PlatformName.values()) {
                if (platformName.getValue().equals(value)) {
                    return platformName;
                }
            }
            throw new IllegalArgumentException("Unknown Severity value: " + value);
        }
    };

    public static final TypeAdapter<ActionKind> ACTION_KIND_ADAPTER = new TypeAdapter<ActionKind>() {
        @Override
        public void write(JsonWriter out, ActionKind value) throws IOException {
            if (value == null) {
                out.nullValue();
            } else {
                out.value(value.getValue());
            }
        }

        @Override
        public ActionKind read(JsonReader in) throws IOException {
            if (in.peek() == com.google.gson.stream.JsonToken.NULL) {
                in.nextNull();
                return null;
            }
            String value = in.nextString();
            for (ActionKind actionKind : ActionKind.values()) {
                if (actionKind.getValue().equals(value)) {
                    return actionKind;
                }
            }
            throw new IllegalArgumentException("Unknown Severity value: " + value);
        }
    };


    public static final TypeAdapter<SeriesName> SERIES_NAME_ADAPTER = new TypeAdapter<SeriesName>() {
        @Override
        public void write(JsonWriter out, SeriesName value) throws IOException {
            if (value == null) {
                out.nullValue();
            } else {
                out.value(value.getValue());
            }
        }

        @Override
        public SeriesName read(JsonReader in) throws IOException {
            if (in.peek() == com.google.gson.stream.JsonToken.NULL) {
                in.nextNull();
                return null;
            }
            String value = in.nextString();
            for (SeriesName seriesName : SeriesName.values()) {
                if (seriesName.getValue().equals(value)) {
                    return seriesName;
                }
            }
            throw new IllegalArgumentException("Unknown Severity value: " + value);
        }
    };

    private static final Gson gsonOnDisableHtmlEscaping = new GsonBuilder()
            .disableHtmlEscaping()
            .registerTypeAdapter(LocalDateTime.class, LOCAL_DATE_TIME_ADAPTER)
            .registerTypeAdapter(Severity.class, SEVERITY_ADAPTER)
            .create();

    private static final Gson gson = new GsonBuilder()
            .registerTypeAdapter(LocalDateTime.class, LOCAL_DATE_TIME_ADAPTER)
            .registerTypeAdapter(Severity.class, SEVERITY_ADAPTER)
            .registerTypeAdapter(PlatformName.class, PLATFORM_NAME_ADAPTER)
            .registerTypeAdapter(SeriesName.class, SERIES_NAME_ADAPTER)
            .registerTypeAdapter(ActionKind.class, ACTION_KIND_ADAPTER)
            .registerTypeAdapterFactory(
                    // 文件变更父子类序列化设置
                    RuntimeTypeAdapterFactory
                            .of(FileChanges.class, "type")
                            .registerSubtype(FileChangesFile.class, "file")
                            .registerSubtype(FileChangesContent.class, "content")
            ).registerTypeAdapterFactory(
                    // 面板变更父子类序列化设置
                    RuntimeTypeAdapterFactory
                            .of(BuildInPanel.class, "type")
                            .registerSubtype(BuildInPanelDiff.class, "diff")
                            .registerSubtype(BuildInPanelProblems.class, "problems")
                            .registerSubtype(BuildInPanelViews.class, "views")
                            .registerSubtype(BuildInPanel.BuildInPanelWebview.class, "webview")
            )
            .create();


    public static String toJson(Object object) {
        return toJson(object, false);
    }

    public static String toJson(Object object, boolean disableHtmlEscaping) {
        if (disableHtmlEscaping) {
            return gsonOnDisableHtmlEscaping.toJson(object);
        }
        return gson.toJson(object);
    }

    public static <T> T fromJson(String json, Class<T> classOfT) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return gson.fromJson(json, classOfT);
    }

    public static <T> T fromJson(String json, TypeToken<T> typeToken) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return gson.fromJson(json, typeToken.getType());
    }

    public static JsonObject fromJson(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonParser.parseString(json).getAsJsonObject();
    }

    @SafeVarargs
    public static <K, V> Map<K, V> mergeMap(@NotNull Map<K, ? extends V>... maps) {
        if (maps.length == 0) {
            return Collections.emptyMap();
        } else if (maps.length == 1) {
            return Map.copyOf(maps[0]);
        } else {
            Map<K, V> all = null;
            Map[] var2 = maps;
            int var3 = maps.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                Map<K, ? extends V> map = var2[var4];
                if (!map.isEmpty()) {
                    if (all == null) {
                        all = new HashMap();
                    }

                    all.putAll(map);
                }
            }

            return all == null ? Collections.emptyMap() : Collections.unmodifiableMap(all);
        }
    }
}

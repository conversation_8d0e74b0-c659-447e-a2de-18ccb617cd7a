package com.xhs.codewiz.type.content;

import java.util.*;

/**
 * 装饰器渲染设置的基础配置
 */
public class DecorationRenderOptionsBase {
    /** CSS border 样式 */
    private String border;

    /** CSS cursor 样式 */
    private String cursor;

    /** 背景颜色 */
    private String backgroundColor;

    /** border 颜色 */
    private String borderColor;

    /** 文本颜色 */
    private String color;

    /** 在文本前添加的装饰器 */
    private DecorationAttachmentRenderOptions before;

    /** CSS letter-spacing 样式 */
    private String letterSpacing;

    /** CSS text-decoration 样式 */
    private String textDecoration;

    /** CSS font-style 样式 */
    private String fontStyle;

    /** CSS border-spacing 样式 */
    private String borderSpacing;

    /** CSS outline 样式 */
    private String outline;

    /** CSS outline 样式 */
    private String outlineStyle;

    /** CSS outline 宽度 */
    private String outlineWidth;

    /** CSS border-radius 样式 */
    private String borderRadius;

    /** 装饰器在行号区域的图标绝对路径 */
    private String gutterIconPath;

    /** CSS border 宽度 */
    private String borderWidth;

    /** outline 颜色 */
    private String outlineColor;

    /** CSS background-size 属性，描述装饰器在行号区域的图标大小 */
    private String gutterIconSize;

    /** 在文本后添加的装饰器 */
    private DecorationAttachmentRenderOptions after;

    /** CSS border 样式 */
    private String borderStyle;

    /** CSS 透明度 */
    private String opacity;

    /** 概览标尺颜色 */
    private String overviewRulerColor;

    /** CSS font-weight 样式 */
    private String fontWeight;

    public String getBorder() {
        return border;
    }

    public void setBorder(String border) {
        this.border = border;
    }

    public String getCursor() {
        return cursor;
    }

    public void setCursor(String cursor) {
        this.cursor = cursor;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public DecorationAttachmentRenderOptions getBefore() {
        return before;
    }

    public void setBefore(DecorationAttachmentRenderOptions before) {
        this.before = before;
    }

    public String getLetterSpacing() {
        return letterSpacing;
    }

    public void setLetterSpacing(String letterSpacing) {
        this.letterSpacing = letterSpacing;
    }

    public String getTextDecoration() {
        return textDecoration;
    }

    public void setTextDecoration(String textDecoration) {
        this.textDecoration = textDecoration;
    }

    public String getFontStyle() {
        return fontStyle;
    }

    public void setFontStyle(String fontStyle) {
        this.fontStyle = fontStyle;
    }

    public String getBorderSpacing() {
        return borderSpacing;
    }

    public void setBorderSpacing(String borderSpacing) {
        this.borderSpacing = borderSpacing;
    }

    public String getOutline() {
        return outline;
    }

    public void setOutline(String outline) {
        this.outline = outline;
    }

    public String getOutlineStyle() {
        return outlineStyle;
    }

    public void setOutlineStyle(String outlineStyle) {
        this.outlineStyle = outlineStyle;
    }

    public String getOutlineWidth() {
        return outlineWidth;
    }

    public void setOutlineWidth(String outlineWidth) {
        this.outlineWidth = outlineWidth;
    }

    public String getBorderRadius() {
        return borderRadius;
    }

    public void setBorderRadius(String borderRadius) {
        this.borderRadius = borderRadius;
    }

    public String getGutterIconPath() {
        return gutterIconPath;
    }

    public void setGutterIconPath(String gutterIconPath) {
        this.gutterIconPath = gutterIconPath;
    }

    public String getBorderWidth() {
        return borderWidth;
    }

    public void setBorderWidth(String borderWidth) {
        this.borderWidth = borderWidth;
    }

    public String getOutlineColor() {
        return outlineColor;
    }

    public void setOutlineColor(String outlineColor) {
        this.outlineColor = outlineColor;
    }

    public String getGutterIconSize() {
        return gutterIconSize;
    }

    public void setGutterIconSize(String gutterIconSize) {
        this.gutterIconSize = gutterIconSize;
    }

    public DecorationAttachmentRenderOptions getAfter() {
        return after;
    }

    public void setAfter(DecorationAttachmentRenderOptions after) {
        this.after = after;
    }

    public String getBorderStyle() {
        return borderStyle;
    }

    public void setBorderStyle(String borderStyle) {
        this.borderStyle = borderStyle;
    }

    public String getOpacity() {
        return opacity;
    }

    public void setOpacity(String opacity) {
        this.opacity = opacity;
    }

    public String getOverviewRulerColor() {
        return overviewRulerColor;
    }

    public void setOverviewRulerColor(String overviewRulerColor) {
        this.overviewRulerColor = overviewRulerColor;
    }

    public String getFontWeight() {
        return fontWeight;
    }

    public void setFontWeight(String fontWeight) {
        this.fontWeight = fontWeight;
    }

}

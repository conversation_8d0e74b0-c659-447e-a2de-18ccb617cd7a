package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.TabGroup;
import com.xhs.codewiz.type.global.ZeroBasedIndex;
/**
 * 更新 TabGroup 信息
 */
public class UpdateTabGroup {
    private String schemaProtocol = "workspace.update.tabgroup";
    private UpdateTabGroupParams params;

    public UpdateTabGroupParams getParams() {
        return params;
    }
    public void setParams(UpdateTabGroupParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateTabGroupParams {
        /** 更新后的 TabGroup 信息 */
        private TabGroup tabGroup;
        /** TabGroup 索引 */
        private Integer tabGroupIndex;
    
        public TabGroup getTabGroup() {
            return tabGroup;
        }
        public void setTabGroup(TabGroup tabGroup) {
            this.tabGroup = tabGroup;
        }
        public Integer getTabGroupIndex() {
            return tabGroupIndex;
        }
        public void setTabGroupIndex(Integer tabGroupIndex) {
            this.tabGroupIndex = tabGroupIndex;
        }
    }
}

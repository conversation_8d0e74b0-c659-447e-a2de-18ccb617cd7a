package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.FileOpenOptions;
import com.xhs.codewiz.type.file.Uri;
import com.xhs.codewiz.type.file.Content;
/**
 * 打开文件
 */
public class ExecuteOpen {
    private String schemaProtocol = "file.execute.open";
    private ExecuteOpenParams params;

    public ExecuteOpenParams getParams() {
        return params;
    }
    public void setParams(ExecuteOpenParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteOpenParams {
        private FileOpenOptions options;
        /** 要打开的文件 */
        private String uri;
        /** 文件内容 */
        private Content content; // optional
    
        public FileOpenOptions getOptions() {
            return options;
        }
        public void setOptions(FileOpenOptions options) {
            this.options = options;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
        public Content getContent() {
            return content;
        }
        public void setContent(Content content) {
            this.content = content;
        }
    }
}

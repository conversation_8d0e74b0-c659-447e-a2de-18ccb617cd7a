package com.xhs.codewiz.lang.agent.rpc;


import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSyntaxException;
import com.xhs.codewiz.editor.request.VirtualFileUri;
import com.xhs.codewiz.lang.agent.lsp.TextDocumentSyncKind;
import java.util.Iterator;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class JsonRPC {
    public static final Gson GSON = (new GsonBuilder()).registerTypeAdapter(TextDocumentSyncKind.class, new TextDocumentSyncKind.TypeAdapter()).registerTypeAdapter(
        VirtualFileUri.class, new VirtualFileUri.TypeAdapter()).create();

    private JsonRPC() {
    }

    public static String serializeCommand(int requestId, @NotNull String name, @NotNull Object command, @Nullable JsonObject additionalProperties) {
        JsonObject json = new JsonObject();
        json.addProperty("jsonrpc", "2.0");
        json.addProperty("id", requestId);
        json.addProperty("method", name);
        json.add("params", mergeProperties(GSON.toJsonTree(command), additionalProperties));
        return GSON.toJson(json);
    }

    public static String serializeNotification(@NotNull String name, @NotNull Object command, @Nullable JsonObject additionalProperties) {
        JsonObject json = new JsonObject();
        json.addProperty("jsonrpc", "2.0");
        json.addProperty("method", name);
        json.add("params", mergeProperties(GSON.toJsonTree(command), additionalProperties));
        return GSON.toJson(json);
    }

    public static String serializeResponse(@NotNull JsonRpcClientResponse response) {
        JsonObject json = new JsonObject();
        json.addProperty("jsonrpc", "2.0");
        json.addProperty("id", response.getRequestId());
        json.add("result", GSON.toJsonTree(response.getResult()));
        return GSON.toJson(json);
    }

    public static @NotNull JsonRpcResponse parseResponse(@NotNull String responseContent) throws JsonRpcErrorException, JsonParseException {
        JsonObject response = (JsonObject)GSON.fromJson(responseContent, JsonObject.class);
        if (response.has("error")) {
            int id = response.getAsJsonPrimitive("id").getAsInt();
            throw new JsonRpcErrorException(id, (JsonRpcError)GSON.fromJson(response.get("error"), JsonRpcError.class));
        } else {
            String methodName;
            if (isServersideNotification(response)) {
                methodName = response.getAsJsonPrimitive("method").getAsString();
                return new JsonRpcResponse((Integer)null, methodName, response.get("params"));
            } else if (isServersideRequest(response)) {
                methodName = response.getAsJsonPrimitive("method").getAsString();
                return new JsonRpcResponse(response.getAsJsonPrimitive("id").getAsInt(), methodName, response.get("params"));
            } else {
                return new JsonRpcResponse(response.getAsJsonPrimitive("id").getAsInt(), (String)null, response.get("result"));
            }
        }
    }

    private static boolean isServersideNotification(JsonObject response) {
        if (!response.has("id")) {
            return true;
        } else if (response.has("method")) {
            String method = response.get("method").getAsString();
            return "client/registerCapability".equals(method);
        } else {
            return false;
        }
    }

    private static boolean isServersideRequest(JsonObject response) {
        return response.has("method") && response.has("id");
    }

    public static <T> @NotNull T parseResponse(@NotNull JsonElement json, Class<T> responseType) throws JsonSyntaxException {
        return GSON.fromJson(json, responseType);
    }

    private static JsonElement mergeProperties(@NotNull JsonElement main, @Nullable JsonObject additionalProperties) {
        if (additionalProperties != null) {
            assert main.isJsonObject();

            JsonObject mainObject = main.getAsJsonObject();
            Iterator var3 = additionalProperties.entrySet().iterator();

            while(var3.hasNext()) {
                Map.Entry<String, JsonElement> property = (Map.Entry)var3.next();
                String name = (String)property.getKey();
                JsonElement value = (JsonElement)property.getValue();
                if (value.isJsonPrimitive()) {
                    assert !mainObject.has(name) : "main already contains property: " + name + " with value: " + mainObject.get(name);

                    mainObject.add(name, value);
                } else {
                    if (!value.isJsonObject()) {
                        throw new UnsupportedOperationException("unsupported property. name: " + name + ", value: " + value);
                    }

                    if (mainObject.has(name)) {
                        mergeProperties(mainObject.get(name), value.getAsJsonObject());
                    } else {
                        mainObject.add(name, value);
                    }
                }
            }
        }

        return main;
    }
}

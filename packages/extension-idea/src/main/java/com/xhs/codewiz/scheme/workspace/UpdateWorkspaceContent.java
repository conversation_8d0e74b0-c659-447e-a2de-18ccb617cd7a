package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.FileChanges;
/**
 * 更新工作区内容
 */
public class UpdateWorkspaceContent {
    private String schemaProtocol = "workspace.update.workspacecontent";
    private UpdateWorkspaceContentParams params;

    public UpdateWorkspaceContentParams getParams() {
        return params;
    }
    public void setParams(UpdateWorkspaceContentParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateWorkspaceContentParams {
        /** 工作区内容编辑列表 */
        private List<FileChanges> changes;
    
        public List<FileChanges> getChanges() {
            return changes;
        }
        public void setChanges(List<FileChanges> changes) {
            this.changes = changes;
        }
    }
}

package com.xhs.codewiz.client.model;

import java.util.List;
import java.util.Map;

/**
 * Author: liukunpeng Date: 2025-07-15 Description:
 */
public class Dependence {
  // 远端需要使用的插件名称
  private String name;
  // 远端需要使用的插件版本号, 格式为 [x, y, z]
  private List<Integer> version;
  // 远端需要使用的插件的依赖选项
  private Map<String, Object> dependenceOptions;
  // 远端需要使用的插件的激活选项
  private Map<String, Object> activeOptions;

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public List<Integer> getVersion() {
    return version;
  }

  public void setVersion(List<Integer> version) {
    this.version = version;
  }

  public Map<String, Object> getDependenceOptions() {
    return dependenceOptions;
  }

  public void setDependenceOptions(Map<String, Object> dependenceOptions) {
    this.dependenceOptions = dependenceOptions;
  }

  public Map<String, Object> getActiveOptions() {
    return activeOptions;
  }

  public void setActiveOptions(Map<String, Object> activeOptions) {
    this.activeOptions = activeOptions;
  }
}

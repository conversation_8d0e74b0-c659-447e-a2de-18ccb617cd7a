// package com.xhs.codewiz.service;
//
// import org.jetbrains.annotations.NotNull;
//
// import com.intellij.openapi.application.ApplicationManager;
// import com.intellij.openapi.components.Service;
// import com.intellij.openapi.fileEditor.FileEditorManager;
// import com.intellij.openapi.fileEditor.OpenFileDescriptor;
// import com.intellij.openapi.project.Project;
// import com.intellij.openapi.vfs.VirtualFile;
// import com.xhs.codewiz.listener.SmartFileOpenManager;
//
// /**
//  * 智能文件编辑器服务
//  * 提供智能的文件打开功能，自动处理topic editor场景下的窗口选择
//  *
//  * <AUTHOR>
//  * @date 2025/1/27 18:40
//  */
// @Service(Service.Level.PROJECT)
// public final class SmartFileEditorService {
//
//     private final Project project;
//
//     public SmartFileEditorService(@NotNull Project project) {
//         this.project = project;
//     }
//
//     /**
//      * 获取项目的 SmartFileEditorService 实例
//      */
//     public static SmartFileEditorService getInstance(@NotNull Project project) {
//         return project.getService(SmartFileEditorService.class);
//     }
//
//     /**
//      * 智能打开文件
//      * 这是对外提供的主要API，其他代码应该调用这个方法而不是直接调用FileEditorManager.openFile
//      *
//      * @param file        要打开的文件
//      * @param focusEditor 是否聚焦到打开的编辑器
//      */
//     public void openFile(@NotNull VirtualFile file, boolean focusEditor) {
//         ApplicationManager.getApplication().invokeLater(() -> {
//             SmartFileOpenManager smartManager = SmartFileOpenManager.getInstance(project);
//
//             // 尝试智能打开
//             boolean handled = smartManager.smartOpenFile(file, focusEditor);
//
//             if (!handled) {
//                 // 如果没有被智能处理，使用默认的文件打开方式
//                 FileEditorManager.getInstance(project).openFile(file, focusEditor);
//             }
//         });
//     }
//
//     /**
//      * 智能打开文件（带行号和列号）
//      *
//      * @param file        要打开的文件
//      * @param line        行号（从0开始）
//      * @param column      列号（从0开始）
//      * @param focusEditor 是否聚焦到打开的编辑器
//      */
//     public void openFile(@NotNull VirtualFile file, int line, int column, boolean focusEditor) {
//         ApplicationManager.getApplication().invokeLater(() -> {
//             SmartFileOpenManager smartManager = SmartFileOpenManager.getInstance(project);
//
//             // 尝试智能打开
//             boolean handled = smartManager.smartOpenFile(file, focusEditor);
//
//             if (!handled) {
//                 // 如果没有被智能处理，使用默认的文件打开方式
//                 OpenFileDescriptor descriptor = new OpenFileDescriptor(project, file, line, column);
//                 descriptor.navigate(focusEditor);
//             } else {
//                 // 如果被智能处理了，还需要导航到指定位置
//                 OpenFileDescriptor descriptor = new OpenFileDescriptor(project, file, line, column);
//                 descriptor.navigate(focusEditor);
//             }
//         });
//     }
//
//     /**
//      * 检查当前是否在topic editor上下文中
//      *
//      * @return true表示当前焦点在topic editor中
//      */
//     public boolean isInTopicEditorContext() {
//         // 这里可以添加检查逻辑，暂时返回false
//         return false;
//     }
// }

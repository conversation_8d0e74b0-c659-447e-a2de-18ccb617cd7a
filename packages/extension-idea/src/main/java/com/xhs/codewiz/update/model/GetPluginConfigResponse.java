package com.xhs.codewiz.update.model;

/**
 * Author: liukunpeng Date: 2025-08-25 Description:
 */
public class GetPluginConfigResponse {
    private Integer code;
    private String message;
    private PluginConfigInfo data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public PluginConfigInfo getData() {
        return data;
    }

    public void setData(PluginConfigInfo data) {
        this.data = data;
    }

    public static class PluginConfigInfo extends GetPluginConfigRequest {
        private String env;
        private String artifactName;
        private String artifactVersion;
        private String artifactUrl;
        private boolean isUpdate;

        public String getEnv() {
            return env;
        }
        public void setEnv(String env) {
            this.env = env;
        }
        public String getArtifactName() {
            return artifactName;
        }
        public void setArtifactName(String artifactName) {
            this.artifactName = artifactName;
        }
        public String getArtifactVersion() {
            return artifactVersion;
        }
        public void setArtifactVersion(String artifactVersion) {
            this.artifactVersion = artifactVersion;
        }
        public String getArtifactUrl() {
            return artifactUrl;
        }
        public void setArtifactUrl(String artifactUrl) {
            this.artifactUrl = artifactUrl;
        }
        public boolean isUpdate() {
            return isUpdate;
        }
        public void setUpdate(boolean isUpdate) {
            this.isUpdate = isUpdate;
        }
    }
}

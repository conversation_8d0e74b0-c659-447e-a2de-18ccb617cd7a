package com.xhs.codewiz.client.message;

import com.google.gson.GsonBuilder;
import com.google.gson.JsonParseException;
import com.xhs.codewiz.client.websocket.RcsWebSocketUtils;
import java.io.StringReader;
import java.util.Map;
import java.util.function.Consumer;
import org.eclipse.lsp4j.jsonrpc.json.JsonRpcMethod;
import org.eclipse.lsp4j.jsonrpc.json.MessageJsonHandler;
import org.eclipse.lsp4j.jsonrpc.messages.Message;

public class RcsMessageJsonHandler
  extends MessageJsonHandler
{
  public RcsMessageJsonHandler(Map<String, JsonRpcMethod> supportedMethods) {
    super(supportedMethods);
  }
  
  public RcsMessageJsonHandler(Map<String, JsonRpcMethod> supportedMethods, Consumer<GsonBuilder> configureGson) {
    super(supportedMethods, configureGson);
  }

  public Message parseMessage(CharSequence input) throws JsonParseException {
    StringReader reader = new StringReader(RcsWebSocketUtils.removeContentLengthHeader(input));
    return parseMessage(reader);
  }
}
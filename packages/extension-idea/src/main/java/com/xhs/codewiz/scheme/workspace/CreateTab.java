package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.Tab;
import com.xhs.codewiz.type.global.ZeroBasedIndex;
/**
 * 创建显示的 Tab 标签
 */
public class CreateTab {
    private String schemaProtocol = "workspace.create.tab";
    private CreateTabParams params;

    public CreateTabParams getParams() {
        return params;
    }
    public void setParams(CreateTabParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateTabParams {
        private Tab tab;
        /** Tab 在 TabGroup 中的索引 */
        private Integer tabIndex;
        /** Tab 对应 TabGroup 的索引 */
        private Integer tabGroupIndex;
    
        public Tab getTab() {
            return tab;
        }
        public void setTab(Tab tab) {
            this.tab = tab;
        }
        public Integer getTabIndex() {
            return tabIndex;
        }
        public void setTabIndex(Integer tabIndex) {
            this.tabIndex = tabIndex;
        }
        public Integer getTabGroupIndex() {
            return tabGroupIndex;
        }
        public void setTabGroupIndex(Integer tabGroupIndex) {
            this.tabGroupIndex = tabGroupIndex;
        }
    }
}

package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
import com.xhs.codewiz.type.content.ActionContext;
import com.xhs.codewiz.type.content.Range;
/**
 * 获取可使用 Action。\n注意，返回的 action 可能并非完整 action，建议在实际使用时先使用 Content.Read.ActionDetail 获取完整信息
 */
public class ReadAction {
    private String schemaProtocol = "content.read.action";
    private ReadActionParams params;

    public ReadActionParams getParams() {
        return params;
    }
    public void setParams(ReadActionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadActionParams {
        private String file;
        /** Action Provider ID */
        private String provider;
        private ActionContext context;
        private Range range;
    
        public String getFile() {
            return file;
        }
        public void setFile(String file) {
            this.file = file;
        }
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public ActionContext getContext() {
            return context;
        }
        public void setContext(ActionContext context) {
            this.context = context;
        }
        public Range getRange() {
            return range;
        }
        public void setRange(Range range) {
            this.range = range;
        }
    }
}

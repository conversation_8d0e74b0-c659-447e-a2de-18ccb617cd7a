package com.xhs.codewiz.utils;

import com.intellij.ide.util.PropertiesComponent;
import java.util.List;

/**
 * Author: liukunpeng Date: 2025-07-20 Description:
 */
public class LocalStorageUtil {
  public static final PropertiesComponent STORAGE_COMPONENT = PropertiesComponent.getInstance();

  public synchronized static void setProperty(String key, String value) {
    STORAGE_COMPONENT.setValue(key, value);
  }

  public static void setProperty(String key, boolean value) {
    STORAGE_COMPONENT.setValue(key, value);
  }

  public static String getProperty(String key) {
    return STORAGE_COMPONENT.getValue(key);
  }

  public static void removeProperty(String key) {
    STORAGE_COMPONENT.unsetValue(key);
  }

  public static void setList(String key, List<String> list) {
    STORAGE_COMPONENT.setList(key, list);
  }

  public static List<String> getList(String key) {
    return STORAGE_COMPONENT.getList(key);
  }

  public static boolean isTrueValue(String key) {
    return STORAGE_COMPONENT.isTrueValue(key);
  }

}

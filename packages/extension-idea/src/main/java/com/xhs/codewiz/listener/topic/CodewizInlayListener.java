package com.xhs.codewiz.listener.topic;

import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.Inlay;
import com.intellij.util.messages.Topic;
import com.xhs.codewiz.editor.CodeWizInlayRenderer;
import com.xhs.codewiz.editor.InlayDisposeContext;
import com.xhs.codewiz.editor.request.EditorRequest;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public interface CodewizInlayListener {
    Topic<CodewizInlayListener> TOPIC = Topic.create("codewiz.inlaysUpdate", CodewizInlayListener.class);

    void inlaysUpdated(@NotNull EditorRequest var1, @NotNull InlayDisposeContext var2, @NotNull Editor var3, @NotNull List<Inlay<CodeWizInlayRenderer>> var4);
}


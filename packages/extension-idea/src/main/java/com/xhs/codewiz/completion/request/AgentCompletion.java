package com.xhs.codewiz.completion.request;

import com.xhs.codewiz.completion.CodewizCompletion;
import com.xhs.codewiz.completion.util.CompletionUtil;
import com.xhs.codewiz.type.content.CompletionInlineItem;
import com.xhs.codewiz.utils.CodewizStringUtil;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class AgentCompletion implements CodewizCompletion {
    private final CompletionItem agentData;
    private final List<String> completion;
    private volatile boolean isCached = false;

    public AgentCompletion(CompletionItem agentData) {
        this.agentData = agentData;
        this.completion = List.of(CodewizStringUtil.splitLines(agentData.getFilterText()));
    }

    public @NotNull CodewizCompletion asCached() {
        return this.withCached(true);
    }

    public @Nullable CodewizCompletion withoutPrefix(@NotNull String prefix) {
        return CompletionUtil.apiChoiceWithoutPrefix(this, prefix);
    }

    public AgentCompletion(CompletionItem agentData, List<String> completion, boolean isCached) {
        this.agentData = agentData;
        this.completion = completion;
        this.isCached = isCached;
    }

    public CompletionItem getAgentData() {
        return this.agentData;
    }

    public List<String> getCompletion() {
        return this.completion;
    }

    public AgentCompletion withCompletion(List<String> completion) {
        return this.completion == completion ? this : new AgentCompletion(this.agentData, completion, this.isCached);
    }

    public boolean isCached() {
        return this.isCached;
    }

    private AgentCompletion withCached(boolean isCached) {
        return this.isCached == isCached ? this : new AgentCompletion(this.agentData, this.completion, isCached);
    }
}


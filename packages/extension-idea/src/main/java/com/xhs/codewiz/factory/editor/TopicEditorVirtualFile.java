package com.xhs.codewiz.factory.editor;

import com.intellij.openapi.fileTypes.FileType;
import com.intellij.testFramework.LightVirtualFile;

/**
 * <AUTHOR>
 * @date 2025/8/4 11:30
 */
public class TopicEditorVirtualFile extends LightVirtualFile {
    private String displayName;
    private String remoteChannel;
    private String webviewId;

    public TopicEditorVirtualFile(String name,
                                  String remoteChannel,
                                  String webviewId) {
        super(name);
        this.remoteChannel = remoteChannel;
        this.webviewId = webviewId;
        this.displayName = name;
    }

    public String getRemoteChannel() {
        return remoteChannel;
    }

    public String getWebviewId() {
        return webviewId;
    }

    public void setRemoteChannel(String remoteChannel) {
        this.remoteChannel = remoteChannel;
    }

    public void setWebviewId(String webviewId) {
        this.webviewId = webviewId;
    }

    @Override
    public String getName() {
        return displayName != null ? displayName : super.getName();
    }

    /**
     * 动态更新文件名称
     */
    public void updateName(String newName) {
        this.displayName = newName;
    }

    @Override
    public FileType getFileType() {
        return TopicFileType.INSTANCE;
    }
}

package com.xhs.codewiz.scheme.global;

import java.util.*;
import com.xhs.codewiz.type.global.Severity;
/**
 * 创建全局跟踪。
 */
public class CreateTrace {
    private String schemaProtocol = "global.create.trace";
    private CreateTraceParams params;

    public CreateTraceParams getParams() {
        return params;
    }
    public void setParams(CreateTraceParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateTraceParams {
        /** 跟踪参数 */
        private List<String> args; // optional
        /** 跟踪模块，一般按照 [模块].[子模块]...[子模块] 的格式进行命名。 */
        private String module;
        private Severity type;
        /** 跟踪版本, 格式为 Trace [number].[number].[number] */
        private String version;
    
        public List<String> getArgs() {
            return args;
        }
        public void setArgs(List<String> args) {
            this.args = args;
        }
        public String getModule() {
            return module;
        }
        public void setModule(String module) {
            this.module = module;
        }
        public Severity getType() {
            return type;
        }
        public void setType(Severity type) {
            this.type = type;
        }
        public String getVersion() {
            return version;
        }
        public void setVersion(String version) {
            this.version = version;
        }
    }
}

package com.xhs.codewiz.lang.agent.vscodeRpc;

import com.google.gson.JsonObject;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.lang.agent.rpc.JsonRPC;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcClientResponse;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import com.xhs.codewiz.utils.LoggerUtil;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class VSCodeJsonRpc {
    private static final Logger LOG = Logger.getInstance(VSCodeJsonRpc.class);

    private VSCodeJsonRpc() {
    }

    public static void sendCommand(@NotNull OutputStream target, int id, @NotNull JsonRpcCommand<?> command, @Nullable JsonObject additionalProperties) throws IOException {
        String name = command.getCommandName();

        String commandJSON = JsonRPC.serializeCommand(id, name, command, additionalProperties);
        sendBytes(target, commandJSON);
    }

    public static void sendCommonLsp(@NotNull OutputStream target, int id, @NotNull JsonObject request, String commandName) throws IOException {
        String commandJSON = JsonRPC.serializeCommand(id, commandName, request, null);
        sendBytes(target, commandJSON);
    }

    public static void sendNotification(@NotNull OutputStream target, @NotNull JsonRpcNotification notification, @Nullable JsonObject additionalProperties) throws IOException {
        String name = notification.getCommandName();
        String notificationJSON = JsonRPC.serializeNotification(name, notification, additionalProperties);
        sendBytes(target, notificationJSON);
    }
    public static void sendCommonNotification4UI(@NotNull OutputStream target, @NotNull JsonObject request, String commandName) throws IOException {
        String notificationJSON = JsonRPC.serializeNotification(commandName, request, null);
        sendBytes(target, notificationJSON);
    }

    public static void sendResponse(@NotNull OutputStream target, @NotNull JsonRpcClientResponse response) throws IOException {
        String responseJson = JsonRPC.serializeResponse(response);
        sendBytes(target, responseJson);
    }

    private static void sendBytes(@NotNull OutputStream target, @NotNull String commandJSON) throws IOException {
            byte[] commandBytes = commandJSON.getBytes(StandardCharsets.UTF_8);

        String headerPre = "Content-Length: " + commandBytes.length;
        String header = headerPre + "\r\n\r\n";
        if (LOG.isTraceEnabled()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "VSCodeJsonRpc.sendBytes Sending JSON-RPC message: --- " + headerPre + commandJSON);
        }

        target.write(header.getBytes(StandardCharsets.UTF_8));
        target.write(commandBytes);
        target.flush();
    }
}


package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.TerminalExitReason;
/**
 * 关闭结果
 */
public class ExecuteCloseTerminalResponse {
    /** 关闭原因 */
    private TerminalExitReason reason;
    /** 关闭结果代码 */
    private Integer code; // optional

    public TerminalExitReason getReason() {
        return reason;
    }
    public void setReason(TerminalExitReason reason) {
        this.reason = reason;
    }
    public Integer getCode() {
        return code;
    }
    public void setCode(Integer code) {
        this.code = code;
    }
}

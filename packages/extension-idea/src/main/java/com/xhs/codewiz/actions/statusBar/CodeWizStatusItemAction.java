package com.xhs.codewiz.actions.statusBar;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.Presentation;
import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.util.Pair;
import com.xhs.codewiz.actions.status.CodeWizStatus;
import com.xhs.codewiz.actions.status.CodeWizStatusService;
import org.jetbrains.annotations.NotNull;

class CodeWizStatusItemAction extends AnAction implements DumbAware {
    CodeWizStatusItemAction() {
    }

    public void update(@NotNull AnActionEvent e) {
        Presentation presentation = e.getPresentation();
        presentation.setEnabled(false);
        Pair<CodeWizStatus, String> status = CodeWizStatusService.getCurrentStatus();
        presentation.setDisabledIcon((status.first).getIcon());
        presentation.setText((status.first).getPresentableText());
    }

    public void actionPerformed(@NotNull AnActionEvent e) {
    }
}


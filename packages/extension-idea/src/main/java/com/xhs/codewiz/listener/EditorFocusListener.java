package com.xhs.codewiz.listener;


import com.intellij.diff.editor.DiffFileType;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.LogicalPosition;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManagerEvent;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.fileEditor.TextEditor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.editor.CodeWizEditorManager;
import com.xhs.codewiz.editor.InlayDisposeContext;
import com.xhs.codewiz.editor.request.ActiveFileInfo;
import com.xhs.codewiz.scheme.content.UpdateRanges;
import com.xhs.codewiz.scheme.content.UpdateRanges.UpdateRangesParams;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.utils.LoggerUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

public class EditorFocusListener implements FileEditorManagerListener {
    private static final Logger LOG = Logger.getInstance(EditorFocusListener.class);
    private final Project project;

    public EditorFocusListener(@NotNull Project project) {
        this.project = project;
    }

    public void selectionChanged(@NotNull FileEditorManagerEvent event) {
        VirtualFile oldFile = event.getOldFile();
        if (oldFile != null && oldFile.isValid()) {
            PsiFile psiFile = PsiManager.getInstance(this.project).findFile(oldFile);
            if (psiFile != null && psiFile.isValid()) {
                FileEditor oldEditor = event.getOldEditor();
                if (oldEditor instanceof TextEditor) {
                    Editor editor = ((TextEditor)oldEditor).getEditor();
                    CodeWizEditorManager.getInstance().disposeInlays(editor, InlayDisposeContext.UserAction, "EditorChange");
                }

            }
        }
        //开始尝试获取特殊选中代码
        if (event.getNewFile() != null) {
            /*Boolean isTempFile = event.getNewFile().getUserData(TEMP_FILE_FLAG);
            if (null != isTempFile && isTempFile) {
                return;
            }*/
            this.triggerContextCurrentFileTagUpdate(event.getManager().getSelectedTextEditor(), event.getNewFile());
        } else {
            this.triggerContextCurrentFileTagUpdate(null, null);
        }
    }

    private void triggerContextCurrentFileTagUpdate(Editor editor, VirtualFile file) {
        UpdateRanges ranges = new UpdateRanges();
        UpdateRangesParams params = new UpdateRangesParams();
        List<Range> rangeList = new ArrayList<>();
        if (editor != null && file != null && !file.getFileType().isBinary()) { //FileUtil.isValidProjectFile(editor.getProject(), file)
            ActiveFileInfo activeFileInfo = ActiveFileInfo.getFromEditor(editor);
            params.setUri(file.getUrl());
            Range range = new Range();
            LogicalPosition caretPosition = editor.getCaretModel().getLogicalPosition();
            Position backupPosition = new Position();
            backupPosition.setLine(caretPosition.line);
            backupPosition.setCharacter(caretPosition.column);
            if (null != activeFileInfo.getStart()) {
                range.setStart(activeFileInfo.getStart());
            } else {
                range.setStart(backupPosition);
            }
            if (null != activeFileInfo.getEnd()) {
                range.setEnd(activeFileInfo.getEnd());
            } else {
                range.setEnd(backupPosition);
            }
            rangeList.add(range);
            params.setRanges(rangeList);
            ranges.setParams(params);
            RcsWebSocketManager.INSTANCE.sendNotification(ranges, editor.getProject());
        } else if (file != null && file.getFileType() instanceof DiffFileType) {
            LoggerUtil.INSTANCE.logDebug(LOG, "codewiz triggerContextCurrentFileTagUpdate ignore diff file");
        }
    }
}


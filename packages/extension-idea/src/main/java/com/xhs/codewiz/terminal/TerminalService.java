package com.xhs.codewiz.terminal;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Computable;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentManager;
import com.jediterm.terminal.ProcessTtyConnector;
import com.jediterm.terminal.TtyConnector;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.scheme.global.CreateStreamString;
import com.xhs.codewiz.scheme.global.CreateStreamString.CreateStreamStringParams;
import com.xhs.codewiz.scheme.platform.CreateTerminal.CreateTerminalParams;
import com.xhs.codewiz.scheme.platform.CreateTerminalResponse;
import com.xhs.codewiz.scheme.platform.DeleteTerminal;
import com.xhs.codewiz.scheme.platform.DeleteTerminal.DeleteTerminalParams;
import com.xhs.codewiz.scheme.platform.ExecuteHideTerminal;
import com.xhs.codewiz.scheme.platform.ExecuteShowTerminal;
import com.xhs.codewiz.scheme.platform.ExecuteShowTerminal.ExecuteShowTerminalParamsOptions;
import com.xhs.codewiz.scheme.platform.ExecuteTerminal;
import com.xhs.codewiz.scheme.platform.ExecuteTerminal.ExecuteTerminalParams;
import com.xhs.codewiz.scheme.platform.ExecuteTerminal.ExecuteTerminalParamsOptions;
import com.xhs.codewiz.scheme.platform.ExecuteTerminalResponse;
import com.xhs.codewiz.scheme.platform.ReadTerminal;
import com.xhs.codewiz.scheme.platform.ReadTerminal.ReadTerminalParams;
import com.xhs.codewiz.scheme.platform.ReadTerminalResponse;
import com.xhs.codewiz.scheme.platform.UpdateTerminalState;
import com.xhs.codewiz.scheme.platform.UpdateTerminalState.UpdateTerminalStateParams;
import com.xhs.codewiz.scheme.platform.UpdateTerminalState.UpdateTerminalStateParamsState;
import com.xhs.codewiz.type.platform.TerminalExitReason;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.awt.Component;
import java.awt.Container;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.plugins.terminal.ShellTerminalWidget;
import com.xhs.codewiz.scheme.platform.CreateTerminal;
import org.jetbrains.plugins.terminal.TerminalProjectOptionsProvider;
import org.jetbrains.plugins.terminal.TerminalView;

/**
 * Author: liukunpeng Date: 2025-07-23 Description:
 */
@SuppressWarnings({"removal", "deprecation"})
public class TerminalService {
  private static final Logger logger = Logger.getInstance(TerminalService.class);
  //重启不销毁的 key->terminalId value->id,[channel, widget]
  private static Map<Project, Map<String,  Pair<String, ShellTerminalWidget>>> terminalMap = new ConcurrentHashMap<>();
  //重启需要销毁的，这个需要关联项目，关闭时同时删除 key->projectUri value->id,[channel, widget]
  private static Map<Project, Map<String,  Pair<String, ShellTerminalWidget>>> terminalWeakMap = new ConcurrentHashMap<>();
  //key->执行ID,value->left[坐标(忽略空行)],right[执行的指令]
  private static Map<String, Integer> executeTerminalIdMap = new ConcurrentHashMap<>();
  //key->project, value->key->terminalId,value->shellPrefix
  private static Map<Project, Map<String, String>> terminalShellPrefixMap = new ConcurrentHashMap<>();
  /**
   * 根据远端请求参数创建本地终端（仅支持工作目录和tab名）
   */
  public static CreateTerminalResponse createTerminal(String str, String channel) {
    CreateTerminal createTerminal = GsonUtil.fromJson(str, CreateTerminal.class);
    CreateTerminalResponse response = new CreateTerminalResponse();
    if (null == createTerminal
        || null == createTerminal.getParams()
        || null == createTerminal.getParams().getTerminal()) {
      return response;
    }
    CreateTerminalParams params = createTerminal.getParams();
    String terminalId = params.getId();
    String tabName = params.getTerminal().getName() != null ? params.getTerminal().getName() : "Terminal";
    String cwd = params.getTerminal().getCwd();

    ShellTerminalWidget shellTerminalWidget = null;
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    Set<String> allTabName = new HashSet<>();
    if (terminalMap.containsKey(terminalId)) {
      Map<String, Pair<String, ShellTerminalWidget>> projectMap = terminalMap.computeIfAbsent(project, k -> new HashMap<>());
      if (projectMap.containsKey(terminalId)) {
        shellTerminalWidget = projectMap.get(terminalId).getRight();
      } else {
        for (Pair<String, ShellTerminalWidget> pair : projectMap.values()) {
          allTabName.add(pair.getRight().getName());
        }
      }
    } else if (terminalWeakMap.containsKey(project)) {
      Map<String, Pair<String, ShellTerminalWidget>> projectWeakMap = terminalWeakMap.computeIfAbsent(project, k -> new HashMap<>());
      if (projectWeakMap.containsKey(terminalId)) {
        shellTerminalWidget = projectWeakMap.get(terminalId).getRight();
      } else {
        for (Pair<String, ShellTerminalWidget> pair : projectWeakMap.values()) {
          allTabName.add(pair.getRight().getName());
        }
      }
    }
    if (null != shellTerminalWidget) {
      ProcessTtyConnector connector = shellTerminalWidget.getProcessTtyConnector();
      if (null != connector) {
        response.setProcessId((int)connector.getProcess().pid());
      }
      return response;
    }

    ToolWindow toolWindow = ToolWindowManager.getInstance(project).getToolWindow("Terminal");
    if (null == toolWindow) {
      return response;
    }
    ApplicationManager.getApplication().invokeAndWait(() -> {
      boolean pid = ApplicationManager.getApplication().runWriteAction((Computable<Boolean>)() -> {
        TerminalView terminalView = TerminalView.getInstance(project);
        ShellTerminalWidget create = terminalView.createLocalShellWidget(cwd, tabName);
        addDisposer(project, create, terminalId);
        TtyConnector ttyConnector = create.getTtyConnector();
        if (ttyConnector != null) {
          LoggerUtil.INSTANCE.logDebug(logger, "ToolInvokeProcessor runTerminal ttyConnector not null,before");
        }
        // 显示初始 message（如果有）
        if (params.getTerminal().getMessage() != null && !params.getTerminal().getMessage().isEmpty()) {
          try {
            create.writePlainMessage(params.getTerminal().getMessage().replace("\"", "\\\"") + "\"");
          } catch (Exception e) {
            throw new RuntimeException(e);
          }
        }
        if (null != params.getTerminal().getIsTransient() && params.getTerminal().getIsTransient()) {
          Map<String, Pair<String, ShellTerminalWidget>> projectWeakMap = terminalWeakMap.computeIfAbsent(project, k -> new HashMap<>());
          projectWeakMap.put(terminalId, Pair.of(channel, create));
        } else {
          Map<String, Pair<String, ShellTerminalWidget>> projectMap = terminalMap.computeIfAbsent(project, k -> new HashMap<>());
          projectMap.put(terminalId, Pair.of(channel, create));
        }
        return true;
      });
    });
    ShellTerminalWidget widget = null;
    if (terminalMap.containsKey(project)) {
      Map<String, Pair<String, ShellTerminalWidget>> projectMap = terminalMap.computeIfAbsent(project, k -> new HashMap<>());
      widget = projectMap.get(params.getId()).getRight();
    } else if (terminalWeakMap.containsKey(project)) {
      Map<String, Pair<String, ShellTerminalWidget>> projectWeakMap = terminalWeakMap.computeIfAbsent(project, k -> new HashMap<>());
      widget = projectWeakMap.get(params.getId()).getRight();
    }
    if (widget == null) {
      return response;
    }
    int retry = 10;
    ProcessTtyConnector connector = widget.getProcessTtyConnector();
    while (connector == null && retry-- > 0) {
      ThreadUtil.sleep(500);
      connector = widget.getProcessTtyConnector();
    }
    if (null != connector) {
      response.setProcessId((int)connector.getProcess().pid());
    }
    //这里必须休眠！！不然run指令进来太快，会导致run指令输出异常
    ThreadUtil.execute(() -> {
      ThreadUtil.sleep(100);
      UpdateTerminalState update = new UpdateTerminalState();
      UpdateTerminalStateParams updateParams = new UpdateTerminalStateParams();
      UpdateTerminalStateParamsState state = new UpdateTerminalStateParamsState();
      state.setShell(TerminalProjectOptionsProvider.getInstance(project).getShellPath());
      state.setShellIntegrationAvailable(null != response.getProcessId());
      state.setIsInteractedWith(false);
      updateParams.setState(state);
      updateParams.setTerminal(terminalId);
      update.setParams(updateParams);
      RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(channel, terminalId, update, project);
    });
    return response;
  }

  public static void deleteTerminal(String str, String channel) {
    DeleteTerminal deleteTerminal = GsonUtil.fromJson(str, DeleteTerminal.class);
    if (null == deleteTerminal
        || null == deleteTerminal.getParams()
        || StringUtils.isEmpty(deleteTerminal.getParams().getTerminalId())) {
      return;
    }
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    Map<String, Pair<String, ShellTerminalWidget>> projectMap = terminalMap.computeIfAbsent(project, k -> new HashMap<>());
    if (projectMap.containsKey(deleteTerminal.getParams().getTerminalId())) {
      closeTerminal(project, projectMap.get(deleteTerminal.getParams().getTerminalId()).getRight());
      projectMap.remove(deleteTerminal.getParams().getTerminalId());
      return;
    }
    Map<String, Pair<String, ShellTerminalWidget>> projectWeakMap = terminalWeakMap.computeIfAbsent(project, k -> new HashMap<>());
    if (projectWeakMap.containsKey(deleteTerminal.getParams().getTerminalId())) {
      closeTerminal(project, projectWeakMap.get(deleteTerminal.getParams().getTerminalId()).getRight());
      projectWeakMap.remove(deleteTerminal.getParams().getTerminalId());
    }
  }

  public static void showTerminal(String str, String channel) {
    ExecuteShowTerminal showTerminal = GsonUtil.fromJson(str, ExecuteShowTerminal.class);
    if (null == showTerminal
        || null == showTerminal.getParams()
        || null == showTerminal.getParams().getTerminal()) {
      return;
    }
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    ExecuteShowTerminalParamsOptions options = showTerminal.getParams().getOptions();
    boolean isFocus = null != options && options.getDidFocus();

    Map<String, Pair<String, ShellTerminalWidget>> projectMap = terminalMap.computeIfAbsent(project, k -> new HashMap<>());
    if (projectMap.containsKey(showTerminal.getParams().getTerminal())) {
      show(project, projectMap.get(showTerminal.getParams().getTerminal()).getRight(), isFocus);
      return;
    }
    Map<String, Pair<String, ShellTerminalWidget>> projectWeakMap = terminalWeakMap.computeIfAbsent(project, k -> new HashMap<>());
    if (projectWeakMap.containsKey(showTerminal.getParams().getTerminal())) {
      show(project, projectWeakMap.get(showTerminal.getParams().getTerminal()).getRight(), isFocus);
    }
  }

  public static void hideTerminal(String str, String channel) {
    ExecuteHideTerminal hideTerminal = GsonUtil.fromJson(str, ExecuteHideTerminal.class);
    if (null == hideTerminal
        || null == hideTerminal.getParams()) {
      return;
    }
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    ToolWindow terminalWindow = ToolWindowManager.getInstance(project).getToolWindow("Terminal");
    terminalWindow.hide();
  }

  public static ExecuteTerminalResponse runTerminal(String str, String channel) {
    ExecuteTerminal executeTerminal = GsonUtil.fromJson(str, ExecuteTerminal.class);
    ExecuteTerminalResponse response = new ExecuteTerminalResponse();
    response.setCode(1);
    if (null == executeTerminal || null == executeTerminal.getParams()) {
      LoggerUtil.INSTANCE.logWarn(logger, "runTerminal params is null");
      return response;
    }
    ExecuteTerminalParams params = executeTerminal.getParams();
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    ShellTerminalWidget shellTerminalWidget = null;
    if (terminalMap.containsKey(project)) {
      Map<String, Pair<String, ShellTerminalWidget>> projectMap = terminalMap.computeIfAbsent(project, k -> new HashMap<>());
      if (projectMap.containsKey(params.getTerminal())) {
        shellTerminalWidget = projectMap.get(params.getTerminal()).getRight();
      }
    } else if (terminalWeakMap.containsKey(project)) {
      Map<String, Pair<String, ShellTerminalWidget>> projectWeakMap = terminalWeakMap.computeIfAbsent(project, k -> new HashMap<>());
      if (projectWeakMap.containsKey(params.getTerminal())) {
        shellTerminalWidget = projectWeakMap.get(params.getTerminal()).getRight();
      }
    }
    if (null == shellTerminalWidget) {
      LoggerUtil.INSTANCE.logWarn(logger, "runTerminal shellTerminalWidget is null");
      return response;
    }
    ExecuteTerminalParamsOptions options = params.getOptions();
    String message = params.getMessage();
    try {
      //在执行前，需要记录当前执行命令的行数
      List<String> terminalText = new ArrayList<>();
      int waitTry = 20;
      //确保已经show完毕
      while ((terminalText = TerminalUtil.getCurrentTerminalText(shellTerminalWidget)).isEmpty() && waitTry-- > 0) {
        ThreadUtil.sleep(300);
      }
      if (!terminalText.isEmpty()) {
        //首次执行，记录前缀
        Map<String, String> shellPrefixMap = terminalShellPrefixMap.computeIfAbsent(project, k -> new HashMap<>());
        if (!shellPrefixMap.containsKey(params.getTerminal())
            || StringUtils.isEmpty(shellPrefixMap.get(params.getTerminal()))) {
          shellPrefixMap.put(params.getTerminal(), terminalText.get(terminalText.size() - 1).trim());
        }
      }
      TtyConnector ttyConnector = shellTerminalWidget.getTtyConnector();
      for (char c : message.toCharArray()) {
        ttyConnector.write(String.valueOf(c));
        ThreadUtil.sleep(10);
      }
      if (null == options || null == options.getDoNotExecute() || !options.getDoNotExecute()) {
        ttyConnector.write("\n");
      }
      if (null == options || null == options.getNeverRead() || !options.getNeverRead()) {
        //本次执行坐标在写入之后的最后一行
        executeTerminalIdMap.put(params.getExecuteId(), terminalText.size() - 1);
      }
      ThreadUtil.execute(() -> {
        UpdateTerminalState update = new UpdateTerminalState();
        UpdateTerminalStateParams updateParams = new UpdateTerminalStateParams();
        UpdateTerminalStateParamsState state = new UpdateTerminalStateParamsState();
        state.setShell(TerminalProjectOptionsProvider.getInstance(project).getShellPath());
        state.setShellIntegrationAvailable(true);
        state.setIsInteractedWith(true);
        updateParams.setState(state);
        updateParams.setTerminal(params.getTerminal());
        update.setParams(updateParams);
        RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(channel, params.getTerminal(), update, project);
      });
      response.setCode(0);
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(logger, "Failed to execute command", e);
    }
    return response;
  }
  public static ReadTerminalResponse readTerminal(String str, String channel) {
    ReadTerminal read = GsonUtil.fromJson(str, ReadTerminal.class);
    ReadTerminalResponse response = new ReadTerminalResponse();
    response.setChunksCounts(0);
    if (null == read || null == read.getParams()) {
      LoggerUtil.INSTANCE.logWarn(logger, "readTerminal params is null");
      return response;
    }
    ReadTerminalParams params = read.getParams();
    String terminalId = params.getTerminalId();
    String executeId = params.getExecuteId();
    String streamId = params.getStreamId();
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    ShellTerminalWidget shellTerminalWidget = null;
    if (terminalMap.containsKey(project)) {
      Map<String, Pair<String, ShellTerminalWidget>> projectMap = terminalMap.computeIfAbsent(project, k -> new HashMap<>());
      if (projectMap.containsKey(terminalId)) {
        shellTerminalWidget = projectMap.get(terminalId).getRight();
      }
    } else if (terminalWeakMap.containsKey(project)) {
      Map<String, Pair<String, ShellTerminalWidget>> projectWeakMap = terminalWeakMap.computeIfAbsent(project, k -> new HashMap<>());
      if (projectWeakMap.containsKey(terminalId)) {
        shellTerminalWidget = projectWeakMap.get(terminalId).getRight();
      }
    }
    if (null == shellTerminalWidget) {
      LoggerUtil.INSTANCE.logWarn(logger, "readTerminal shellTerminalWidget is null");
      response.setChunksCounts(1);
      sendEmptyChunks(terminalId, executeId, streamId, channel, project);
      return response;
    }
    int waitTry = 10; //加大循环等待时间，有些操作确实耗时间
    //确保已经执行完毕
    while (shellTerminalWidget.hasRunningCommands() && waitTry-- > 0) {
      ThreadUtil.sleep(500);
    }
    Integer index = executeTerminalIdMap.getOrDefault(executeId, null);
    //确保映射已经完成
    int retryTime = 60;
    while (null == index && retryTime-- > 0) {
      ThreadUtil.sleep(500);
      index = executeTerminalIdMap.getOrDefault(executeId, null);
      retryTime--;
    }
    if (null == index) {
      LoggerUtil.INSTANCE.logWarn(logger, "readTerminal execute history is null");
      response.setChunksCounts(1);
      sendEmptyChunks(terminalId, executeId, streamId, channel, project);
      return response;
    }
    List<String> terminalText = TerminalUtil.getTerminalText(shellTerminalWidget);
    if (index >= terminalText.size()) {
      LoggerUtil.INSTANCE.logWarn(logger, "pair index more than terminalText line number");
      response.setChunksCounts(1);
      sendEmptyChunks(terminalId, executeId, streamId, channel, project);
      return response;
    }
    //只保留前缀，如：user@F7T233465C extension-idea %
    String prefix = "";
    try {
      Map<String, String> shellPrefixMap = terminalShellPrefixMap.computeIfAbsent(project, k -> new HashMap<>());
      prefix = shellPrefixMap.getOrDefault(terminalId, "");
      Pair<String, Integer> orderPair = TerminalUtil.getTerminalAskPrefix(terminalText, index, prefix, project.getName());
      prefix = orderPair.getLeft();
      index = orderPair.getRight();
    } catch (IndexOutOfBoundsException e) {
      LoggerUtil.INSTANCE.logWarn(logger, "getTerminalAskPrefix IndexOutOfBoundsException, cmdHistory = " + GsonUtil.toJson(terminalText)
          + ", index = " + index);
    }

    int streamIndex = 0;
    int size = terminalText.size();
    for (int i = index; i < size; i++) {
      if (StringUtils.isNotEmpty(prefix) && (terminalText.get(i).startsWith(prefix) || prefix.startsWith(terminalText.get(i).trim()))) {
        //遇到下一次input了，那么直接跳出
        break;
      }
      //发送本行数据
      CreateStreamString streamString = new CreateStreamString();
      CreateStreamStringParams streamStringParams = new CreateStreamStringParams();
      streamStringParams.setStreamId(streamId);
      streamStringParams.setIndex(streamIndex++);
      streamStringParams.setValue("\u001b]633;C\u0007" + terminalText.get(i) + "\r\n");
      streamString.setParams(streamStringParams);
      //这个特殊，使用terminalId + executeId
      String providerId = terminalId + "/" + executeId;
      RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(channel, providerId, streamString, project);

      //读取完毕，但是没有识别到终止行(即待机空行)，那么需要尝试重新读取terminal内容
      if (i == size - 1) {
        List<String> newTerminalText;
        //20次，一次3-4s，如果这个时间段一点输出没有，那么不如直接返回
        int getNewReTry = 30;
        while (isTerminalTextSame(terminalText, (newTerminalText = TerminalUtil.getTerminalText(shellTerminalWidget)), index) &&
            getNewReTry-- > 0) {
          ThreadUtil.sleep(1000);
        }
        //查询为空，异常跳出
       if (CollectionUtils.isEmpty(newTerminalText)) {
          break;
        }
        //需要判定是否允许继续读取
        int unSameIndex = getUnSameStart(terminalText, newTerminalText, index);
        if (unSameIndex >= 0) {
          terminalText = newTerminalText;
          i = unSameIndex - 1;
          size = terminalText.size();
        }
      }
    }
    if (streamIndex == 0) {
      //说明没有任何输出
      response.setChunksCounts(1);
      sendEmptyChunks(terminalId, executeId, streamId, channel, project);
      return response;
    }
    //处理完毕，或者超时一直没有读取到新内容，那么直接返回
    response.setChunksCounts(streamIndex);
    return response;
  }
  private static void sendEmptyChunks(String terminalId, String executeId, String streamId, String channel, Project project) {
    CreateStreamString streamString = new CreateStreamString();
    CreateStreamStringParams streamStringParams = new CreateStreamStringParams();
    streamStringParams.setStreamId(streamId);
    streamStringParams.setIndex(0);
    streamStringParams.setValue("\u001b]633;C\u0007\r\n");
    streamString.setParams(streamStringParams);
    //这个特殊，使用terminalId + executeId
    String providerId = terminalId + "/" + executeId;
    RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(channel, providerId, streamString, project);
  }
  private static boolean isTerminalTextSame(List<String> oldText, List<String> newText, int index) {
    if (newText.size() != oldText.size()) {
      return false;
    }
    // 逐行对比
    for (int i = index; i < newText.size(); i++) {
      if (!StringUtils.equals(oldText.get(i), newText.get(i))) {
        return false;
      }
    }
    return true;
  }
  public static int getUnSameStart(List<String> oldText, List<String> newText, int index) {
    if (newText.size() <= index) {
      //理论上来说，new的肯定比之前的index多，所以这个属于异常场景，认为结束就行
      return -1;
    }
    int end = Math.min(oldText.size(), newText.size());
    for (int i = index; i < end; i++) {
      if (!StringUtils.equals(oldText.get(i), newText.get(i))) {
        return i;
      }
    }
    if (oldText.size() >= newText.size()) {
      return -1;
    } else {
      return oldText.size();
    }
  }

  private static void show(Project project, ShellTerminalWidget widget, boolean isFocus) {
    // 获取 Terminal 工具窗口
    ToolWindow terminalWindow = ToolWindowManager.getInstance(project).getToolWindow("Terminal");
    if (terminalWindow == null) {
      return;
    }
    // 获取内容管理器
    ContentManager contentManager = terminalWindow.getContentManager();
    // 遍历查找关联的 Content
    for (Content content : contentManager.getContents()) {
      if (containsComponent(content.getComponent(), widget.getComponent())) {
        // 选中tab并激活Terminal窗口
        contentManager.setSelectedContent(content);
        // 显示Terminal工具窗口（可选）
        ApplicationManager.getApplication().invokeLater(() -> {
          terminalWindow.activate(null, isFocus);
        });
        break;
      }
    }
  }

  private static void closeTerminal(Project project, ShellTerminalWidget widget) {
    ToolWindow toolWindow = ToolWindowManager.getInstance(project).getToolWindow("Terminal");
    if (toolWindow == null) {
      return;
    }
    ContentManager contentManager = toolWindow.getContentManager();
    for (Content content : contentManager.getContents()) {
      if (content.getComponent().equals(widget.getComponent())
          || StringUtils.endsWith(content.getDisplayName(), widget.getName())) {
        ApplicationManager.getApplication().invokeLater(() -> {
          contentManager.removeContent(content, false); // true 表示释放资源
        });
        break;
      }
    }
  }
  private static void addDisposer(Project project, ShellTerminalWidget widget, String terminalId) {
    Disposer.register(widget, () -> {
      String channel = null;
      if (terminalMap.containsKey(project)) {
        Map<String, Pair<String, ShellTerminalWidget>> projectMap = terminalMap.computeIfAbsent(project, k -> new HashMap<>());
        if (projectMap.containsKey(terminalId)) {
          channel = projectMap.remove(terminalId).getLeft();
        }
      } else if (terminalWeakMap.containsKey(project)) {
        Map<String, Pair<String, ShellTerminalWidget>> projectWeakMap = terminalWeakMap.computeIfAbsent(project, k -> new HashMap<>());
        if (projectWeakMap.containsKey(terminalId)) {
          channel = projectWeakMap.remove(terminalId).getLeft();
        }
      }
      Map<String, String> shellPrefixMap = terminalShellPrefixMap.getOrDefault(project, new HashMap<>());
      shellPrefixMap.remove(terminalId);

      DeleteTerminal deleteTerminal = new DeleteTerminal();
      DeleteTerminalParams deleteTerminalParams = new DeleteTerminalParams();
      deleteTerminalParams.setTerminalId(terminalId);
      deleteTerminalParams.setCode(1);
      deleteTerminalParams.setReason(TerminalExitReason.User);
      deleteTerminal.setParams(deleteTerminalParams);
      RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(channel, terminalId, deleteTerminal, project);
    });
  }
  public static void clearWeakMap(Project project) {
    Map<String, Pair<String, ShellTerminalWidget>> projectWeakMap = terminalWeakMap.remove(project);
    if (null != projectWeakMap) {
      Map<String, String> shellPrefixMap = terminalShellPrefixMap.getOrDefault(project, new HashMap<>());
      for (Pair<String, ShellTerminalWidget> widget : projectWeakMap.values()) {
        closeTerminal(project, widget.getRight());
      }
      for (String terminalId : projectWeakMap.keySet()) {
        shellPrefixMap.remove(terminalId);
      }
    }
  }
  public static boolean containsComponent(Component parent, Component target) {
    if (parent == target) return true;
    if (parent instanceof Container) {
      for (Component comp : ((Container) parent).getComponents()) {
        if (containsComponent(comp, target)) return true;
      }
    }
    return false;
  }
}

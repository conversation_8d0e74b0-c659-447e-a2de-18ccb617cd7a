package com.xhs.codewiz.type.file;

import java.util.*;
import com.xhs.codewiz.type.workspace.ViewColumn;

/**
 * 打开文件的选项
 */
public class FileOpenOptions {
    /** 视图列，通常用于指定文件在编辑器中的显示位置。\n目前只需要支持 isBeside */
    private ViewColumn viewColumn;

    /** 是否为未命名文件 */
    private Boolean isUntitled;

    /** 文件是否已修改 */
    private Boolean isDirty;

    /** 是否需要显示文件 */
    private Boolean didShow;

    /** 是否需要在文件列表中显示文件，值存在时表示需要 */
    private FileOpenOptionsDidRevealInExplorer didRevealInExplorer;

    /** 是否需要聚焦文件 */
    private Boolean didFocus;

    public ViewColumn getViewColumn() {
        return viewColumn;
    }

    public void setViewColumn(ViewColumn viewColumn) {
        this.viewColumn = viewColumn;
    }

    public Boolean getIsUntitled() {
        return isUntitled;
    }

    public void setIsUntitled(Boolean isUntitled) {
        this.isUntitled = isUntitled;
    }

    public Boolean getIsDirty() {
        return isDirty;
    }

    public void setIsDirty(Boolean isDirty) {
        this.isDirty = isDirty;
    }

    public Boolean getDidShow() {
        return didShow;
    }

    public void setDidShow(Boolean didShow) {
        this.didShow = didShow;
    }

    public FileOpenOptionsDidRevealInExplorer getDidRevealInExplorer() {
        return didRevealInExplorer;
    }

    public void setDidRevealInExplorer(FileOpenOptionsDidRevealInExplorer didRevealInExplorer) {
        this.didRevealInExplorer = didRevealInExplorer;
    }

    public Boolean getDidFocus() {
        return didFocus;
    }

    public void setDidFocus(Boolean didFocus) {
        this.didFocus = didFocus;
    }

/**
 * 是否需要在文件列表中显示文件，值存在时表示需要
 */
public static class FileOpenOptionsDidRevealInExplorer {
    /** 是否展开文件所在的目录，默认为 `false` */
    private Boolean expandDirectory;

    public Boolean getExpandDirectory() {
        return expandDirectory;
    }

    public void setExpandDirectory(Boolean expandDirectory) {
        this.expandDirectory = expandDirectory;
    }

}
}

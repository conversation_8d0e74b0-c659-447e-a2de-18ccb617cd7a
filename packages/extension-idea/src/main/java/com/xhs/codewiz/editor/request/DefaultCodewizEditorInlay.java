package com.xhs.codewiz.editor.request;


import com.xhs.codewiz.completion.enums.CodewizCompletionType;
import java.util.List;
import javax.annotation.concurrent.Immutable;
import org.jetbrains.annotations.NotNull;

@Immutable
public final class DefaultCodewizEditorInlay implements CodewizEditorInlay {
    private final @NotNull CodewizCompletionType type;
    private final int editorOffset;
    private final @NotNull List<String> completionLines;

    public DefaultCodewizEditorInlay(@NotNull CodewizCompletionType type, int editorOffset, @NotNull List<String> completionLines) {
        this.type = type;
        this.editorOffset = editorOffset;
        this.completionLines = completionLines;
    }

    public @NotNull CodewizCompletionType getType() {
        return this.type;
    }

    public @NotNull List<String> getLines() {
        return this.completionLines;
    }

    public int getEditorOffset() {
        return this.editorOffset;
    }

    public @NotNull List<String> getCompletionLines() {
        return this.completionLines;
    }
}


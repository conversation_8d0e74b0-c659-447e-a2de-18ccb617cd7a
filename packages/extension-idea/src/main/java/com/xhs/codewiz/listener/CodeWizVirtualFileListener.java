package com.xhs.codewiz.listener;

import com.google.common.collect.Lists;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectLocator;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileEvent;
import com.intellij.openapi.vfs.VirtualFileListener;
import com.intellij.openapi.vfs.VirtualFilePropertyEvent;
import com.xhs.codewiz.lang.LspServiceForTylm;
import com.xhs.codewiz.lang.entity.FileSaveParams;
import com.xhs.codewiz.lang.entity.RenameFilesParams;
import com.xhs.codewiz.utils.FileUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.io.File;
import org.eclipse.lsp4j.FileRename;
import org.eclipse.lsp4j.TextDocumentIdentifier;
import org.jetbrains.annotations.NotNull;

public class CodeWizVirtualFileListener implements VirtualFileListener {
  private static final Logger log = Logger.getInstance(CodeWizVirtualFileListener.class);

  @Override
  public void fileCreated(@NotNull VirtualFileEvent event) {
    LSPManager.getInstance().notifyDidCreate(event.getFile());
  }

  @Override
  public void fileDeleted(@NotNull VirtualFileEvent event) {
    LSPManager.getInstance().notifyDidDelete(event.getFile());
  }

  @Override
  public void contentsChanged(@NotNull VirtualFileEvent event) {
    VirtualFile file = event.getFile();
    log.debug("contentsChanged: " + file.getPath());
    if (!file.getFileType().isBinary() && file.isWritable() && file.isValid()) {
      ThreadUtil.executeIndex(() -> {
        log.debug("File saved: " + file.getPath());
        this.triggerSaveEvent(file);
      });
    }
  }

  public void propertyChanged(@NotNull VirtualFilePropertyEvent event) {
    if ("name".equals(event.getPropertyName()) && event.getOldValue() instanceof String) {
      ThreadUtil.executeIndex(() -> {
        try {
          Project project = ProjectLocator.getInstance().guessProjectForFile(event.getFile());
          if (project == null || !LspServiceForTylm.checkProjectConnectStatus(project, true)) {
            return;
          }
          if (FileUtil.isIgnoreIndexFile(project, event.getFile())) {
            log.debug("ignore rename index file:" + event.getFile().getPath());
            return;
          };
          RenameFilesParams params = new RenameFilesParams();
          FileRename fileRename = new FileRename();
          File fileDir = new File(event.getFile().getPresentableUrl()).getParentFile();
          fileRename.setOldUri(new File(fileDir, (String)event.getOldValue()).getAbsolutePath());
          fileRename.setNewUri(event.getFile().getPresentableUrl());
          if (fileRename.getNewUri() == null || fileRename.getNewUri().equals(fileRename.getOldUri())) {
            return;
          }
          params.setFiles(Lists.newArrayList(fileRename));
          log.debug("File renamed from: " + event.getOldValue() + " to: " + event.getFile().getPresentableUrl());
          LspServiceForTylm.didRenameFiles(params);
        }
        catch (Throwable e) {
          log.warn("Encountered error when move file:" + event, e);
        }
      });
    }
  }

  private void triggerSaveEvent(VirtualFile file) {
    try {
      Project project = ProjectLocator.getInstance().guessProjectForFile(file);
      if (project == null) {
        log.warn("ignore save event:" + file.getPath() + ", project not ready.");
        return;
      }
      if (FileUtil.isIgnoreIndexFile(project, file)) {
        log.debug("ignore save index file:" + file.getPath());
        return;
      }
      LSPManager.getInstance().notifyDidSave(file);
      if (LspServiceForTylm.checkProjectConnectStatus(project, false)) {
        FileSaveParams params = new FileSaveParams();
        TextDocumentIdentifier textDocument = new TextDocumentIdentifier(file.getPresentableUrl());
        params.setTextDocument(textDocument);
        LspServiceForTylm.didSave(params);
      }
    }
    catch (Throwable e) {
      log.warn("Encountered error when save file:" + file, e);
    }
  }

}
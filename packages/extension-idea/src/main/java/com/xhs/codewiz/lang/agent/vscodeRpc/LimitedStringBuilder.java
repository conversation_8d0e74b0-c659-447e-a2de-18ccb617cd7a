package com.xhs.codewiz.lang.agent.vscodeRpc;


import net.jcip.annotations.ThreadSafe;
import org.jetbrains.annotations.NotNull;

@ThreadSafe
class LimitedStringBuilder {
    private final int capacity;
    private final StringBuilder builder;

    public LimitedStringBuilder(int capacity) {
        this.capacity = capacity;
        this.builder = new StringBuilder();
    }

    public synchronized void append(@NotNull CharSequence chars) {
        int length = chars.length();
        if (length != 0) {
            int builderLength = this.builder.length();
            int excess = this.capacity - builderLength - length;
            if (excess < 0 && builderLength > 0) {
                this.builder.delete(0, Math.min(builderLength, -excess));
            }

            if (length > this.capacity) {
                this.builder.append(chars.subSequence(length - this.capacity, length));
            } else {
                this.builder.append(chars);
            }

        }
    }

    public synchronized void clear() {
        this.builder.setLength(0);
    }

    public synchronized String toString() {
        return this.builder.toString();
    }
}

package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.editor.request.VirtualFileUri;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import org.jetbrains.annotations.NotNull;

public final class DidFocusCommand implements JsonRpcNotification {
    @SerializedName("uri")
    private final @NotNull VirtualFileUri uri;

    @SerializedName("oldUri")
    private final @NotNull VirtualFileUri oldUri;

    public @NotNull String getCommandName() {
        return "textDocument/didFocus";
    }

    public DidFocusCommand(@NotNull VirtualFileUri uri,@NotNull VirtualFileUri oldUri) {
        this.uri = uri;
        this.oldUri = oldUri;
    }

    public @NotNull VirtualFileUri getUri() {
        return this.uri;
    }

    public VirtualFileUri getOldUri() {
        return oldUri;
    }
}


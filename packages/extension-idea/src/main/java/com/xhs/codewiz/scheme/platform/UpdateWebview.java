package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 更新 Webview
 */
public class UpdateWebview {
    private String schemaProtocol = "platform.update.webview";
    private UpdateWebviewParams params;

    public UpdateWebviewParams getParams() {
        return params;
    }
    public void setParams(UpdateWebviewParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateWebviewParams {
        /** 要更新的 Webview Provider ID */
        private String provider;
        private List<UpdateWebviewParamsChanges> changes;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public List<UpdateWebviewParamsChanges> getChanges() {
            return changes;
        }
        public void setChanges(List<UpdateWebviewParamsChanges> changes) {
            this.changes = changes;
        }
    }

        public static class UpdateWebviewParamsChanges {
        /** Webview 属性值 */
        private Object value;
        /** Webview 属性 path， 如 `options.enableScripts`. */
        private String key;
    
        public Object getValue() {
            return value;
        }
        public void setValue(Object value) {
            this.value = value;
        }
        public String getKey() {
            return key;
        }
        public void setKey(String key) {
            this.key = key;
        }
    }
}

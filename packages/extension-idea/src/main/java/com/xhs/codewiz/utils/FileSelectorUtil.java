package com.xhs.codewiz.utils;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ModalityState;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.fileChooser.FileChooserDescriptor;
import com.intellij.openapi.fileChooser.FileChooserDialog;
import com.intellij.openapi.fileChooser.FileChooserFactory;
import com.intellij.openapi.fileChooser.FileSaverDescriptor;
import com.intellij.openapi.fileChooser.FileSaverDialog;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.scheme.platform.ExecuteShowFileSelector;
import com.xhs.codewiz.scheme.platform.ExecuteShowFileSelector.ExecuteShowFileSelectorParams;
import com.xhs.codewiz.scheme.platform.ExecuteShowSaveDialog;
import com.xhs.codewiz.scheme.platform.ExecuteShowSaveDialog.ExecuteShowSaveDialogParams;
import com.xhs.codewiz.service.WebviewService;
import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-07-30 Description:
 */
public class FileSelectorUtil {
  private static final Logger log = Logger.getInstance(WebviewService.class);
  public static List<String> showFileSelector(String str, String channel) {
    ExecuteShowFileSelector showFileSelector = GsonUtil.fromJson(str, ExecuteShowFileSelector.class);
    if (null == showFileSelector || null == showFileSelector.getParams()) {
      return Lists.newArrayList();
    }
    LoggerUtil.INSTANCE.logInfo(log, "showFileSelector params:" + showFileSelector.getParams());
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    List<VirtualFile> files = showSelector(project, showFileSelector.getParams());
    if (CollectionUtils.isNotEmpty(files)) {
      return files.stream().map(file -> file.getUrl()).collect(Collectors.toList());
    }
    return Lists.newArrayList();
  }

  /**
   * 显示保存对话框
   */
  public static String showSaveDialog(String str, String channel) {
    ExecuteShowSaveDialog showSaveDialog = GsonUtil.fromJson(str, ExecuteShowSaveDialog.class);
    if (null == showSaveDialog || null == showSaveDialog.getParams()) {
      return null;
    }
    LoggerUtil.INSTANCE.logInfo(log, "showSaveDialog params:" + showSaveDialog.getParams());
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    File savedFile = showSaveSelector(project, showSaveDialog.getParams());
    if (savedFile != null) {
      String filePath = savedFile.getPath();
      filePath = StringUtils.startsWith(filePath, "file://") ? filePath : "file://" + filePath;
      return filePath;
    }
    return null;
  }

    /**
   * 弹出保存对话框
   */
  private static File showSaveSelector(Project project, ExecuteShowSaveDialogParams params) {
    // 获取所有扩展名
    List<String> allExtensions = Lists.newArrayList();
    Map<String, List<String>> filters = params.getFilters();
    if (filters != null && !filters.isEmpty()) {
      for (List<String> extensions : filters.values()) {
        for (String ext : extensions) {
          String cleanExt = ext.startsWith(".") ? ext.substring(1) : ext;
          allExtensions.add(cleanExt);
        }
      }
    }
    
    // 构造文件保存器描述符
    FileSaverDescriptor descriptor = new FileSaverDescriptor(
        params.getTitle() != null ? params.getTitle() : "保存文件",
        params.getSaveLabel() != null ? params.getSaveLabel() : "保存",
        allExtensions.toArray(new String[0])
    );
    
    // 默认目录和文件名
    VirtualFile directoryPath = null;
    String fileName = null;
    String defaultUri = params.getDefaultUri();
    if (StringUtils.isNotEmpty(defaultUri)) {
      String directory = defaultUri.substring(0, defaultUri.lastIndexOf('/'));
      directoryPath = LocalFileSystem.getInstance().findFileByPath(directory);
      fileName = defaultUri.substring(defaultUri.lastIndexOf('/') + 1);
    }

    AtomicReference<File> resultRef = new AtomicReference<>();

    String finalFileName = fileName;
    VirtualFile finalDirectoryPath = directoryPath;
    ApplicationManager.getApplication().invokeAndWait(() -> {
      FileSaverDialog dialog = FileChooserFactory.getInstance().createSaveFileDialog(descriptor, project);
      var wrapper = dialog.save(finalDirectoryPath, finalFileName);
      if (wrapper != null) {
        resultRef.set(wrapper.getFile());
      }
    }, ModalityState.defaultModalityState());
    
    return resultRef.get();
  }

  /**
   * 弹出文件选择器
   */
  private static List<VirtualFile> showSelector(Project project,
      ExecuteShowFileSelectorParams params) {

    boolean allowFile = params.getAllowFile() == null || params.getAllowFile();
    boolean allowFolder = params.getAllowFolder() != null && params.getAllowFolder();
    boolean allowMultiple = params.getAllowMutiple() != null && params.getAllowMutiple();

    // 构造文件过滤器
    FileChooserDescriptor descriptor = new FileChooserDescriptor(
        allowFile,              // chooseFiles
        allowFolder,            // chooseFolders
        false,                  // chooseJars
        false,                  // chooseJarsAsFiles
        false,                  // chooseJarContents
        allowMultiple           // allowMultiple
    );
    // 设置文件过滤器（扩展名）
    Map<String, List<String>> filters = params.getFilters();
    if (filters != null && !filters.isEmpty()) {
      descriptor.withFileFilter(virtualFile -> {
        String name = virtualFile.getName().toLowerCase();
        for (List<String> extList : filters.values()) {
          for (String ext : extList) {
            // 兼容 .ext 和 ext
            String e = ext.startsWith(".") ? ext : ("." + ext);
            if (name.endsWith(e.toLowerCase())) {
              return true;
            }
          }
        }
        return false;
      });
    }

    // 选择器标题
    String title = params.getTitle();
    if (title != null) {
      descriptor.setTitle(title);
    }
    // 打开按钮文本
    String openLabel = params.getOpenLabel();
    if (openLabel != null) {
      descriptor.setDescription(openLabel);
    }

    // 默认目录
    VirtualFile defaultFile;
    String defaultUri = params.getDefaultUri();
    if (defaultUri != null && !defaultUri.isEmpty()) {
      defaultFile = LocalFileSystem.getInstance().findFileByPath(params.getDefaultUri());
    } else {
      defaultFile = null;
    }
    AtomicReference<VirtualFile[]> resultRef = new AtomicReference<>();
    ApplicationManager.getApplication().invokeAndWait(() -> {
      FileChooserDialog dialog = FileChooserFactory.getInstance().createFileChooser(descriptor, project, null);

      VirtualFile[] files = dialog.choose(project, defaultFile);
      resultRef.set(files);
    }, ModalityState.defaultModalityState());


    return Arrays.stream(resultRef.get()).toList();
  }
}

package com.xhs.codewiz.listener;

import com.intellij.codeInsight.lookup.LookupManager;
import com.intellij.codeInsight.template.TemplateManager;
import com.intellij.openapi.command.CommandEvent;
import com.intellij.openapi.command.CommandListener;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.VisualPosition;
import com.intellij.openapi.editor.actionSystem.DocCommandGroupId;
import com.intellij.openapi.editor.ex.DocumentEx;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.util.TextRange;
import com.intellij.util.ObjectUtils;
import com.xhs.codewiz.editor.CodeWizEditorManager;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import com.xhs.codewiz.editor.InlayDisposeContext;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.utils.DocumentUtils;
import com.xhs.codewiz.utils.LoggerUtil;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

public class CodewizCommandListener implements CommandListener {
    private static final Logger LOG = Logger.getInstance(CodewizCommandListener.class);
    private static final Key<CodewizCommandListener.CommandEditorState> COMMAND_STATE_KEY = Key.create("codewiz.commandState");
    private final Project project;
    private final AtomicInteger activeCommands = new AtomicInteger();
    private final AtomicBoolean startedWithEditor = new AtomicBoolean(false);
    private final AtomicReference<CodewizCommandListener.UndoTransparentActionState> undoTransparentActionStamp = new AtomicReference();
    //需要跳过的系统级操作
    private static final List<String> skipCommands = Arrays.asList("Undo", "Backspace", "Paste", "Redo", "Copy", "\u64a4\u6d88", "\u9000\u683c", "\u7c98\u8d34", "\u91cd\u505a\u7c98\u8d34", "\u590d\u5236");
    //需要跳过的插件自定义操作
    private static final List<String> ignoreCommands = Arrays.asList("Apply CodeWiz RCS Suggestion:WHOLE");
    private static final Set<String> ALLOW_COMMANDS = Set.of("Typing", "输入"); //通义仅允许输入触发补全

    public CodewizCommandListener(@NotNull Project project) {
        this.project = project;
    }

    public void commandStarted(@NotNull CommandEvent event) {
        if (this.activeCommands.getAndIncrement() > 0) {
            LoggerUtil.INSTANCE.logDebug(LOG, "Skipping nested commandStarted. Event: " + event);
        } else {
            Editor editor = CodeWizEditorUtil.getSelectedEditorSafely(this.project);
            if (editor != null) {
                this.startedWithEditor.set(true);
                COMMAND_STATE_KEY.set(editor, createCommandState(editor));
            } else {
                this.startedWithEditor.set(false);
            }

        }
    }

    public void commandFinished(@NotNull CommandEvent event) {
        if (this.activeCommands.decrementAndGet() > 0) {
            LoggerUtil.INSTANCE.logDebug(LOG, "Skipping nested commandFinished. Event: " + String.valueOf(event));
            return;
        }
        if (!this.startedWithEditor.get() || event.getCommandName() == null) {
            return;
        }
        Editor editor = CodeWizEditorUtil.getSelectedEditorSafely(this.project);
        if (editor == null) {
            return;
        }
        Document document = event.getDocument();

        if (document == null) {
            document = editor.getDocument();
        }
        if (!DocumentUtils.isValidEditorDocument(document)) {
            LoggerUtil.INSTANCE.logDebug(LOG, "invalid document:" + document.getClass().getName());
            return;
        }

        CodeWizEditorManager editorManager = CodeWizEditorManager.getInstance();
        if (!editorManager.isAvailable(editor) || editor.getCaretModel().getCaretCount() > 1) {
            return;
        }
        CommandEditorState commandStartState = COMMAND_STATE_KEY.get(editor);
        if (commandStartState == null) {
            return;
        }
        TemplateManager templateManager = TemplateManager.getInstance(this.project);
        if (templateManager.getActiveTemplate(editor) != null) {
            LoggerUtil.INSTANCE.logDebug(LOG, "Skipping template completion. Event: " + event.getCommandName());
            return;
        }
        if (StringUtils.isNotBlank(commandStartState.getSelectionText())) {
            return;
        }
        if (ignoreCommands.contains(event.getCommandName())) {
            return;
        }
        if (event.getCommandName() != null && this.isSkipCommand(event.getCommandName())) {
            LoggerUtil.INSTANCE.logDebug(LOG, "Skipping invalid command. Event: " + event.getCommandName());
            //自定义跳过不触发补全，需要额外判定是否需要拒绝补全
            editorManager.disposeInlays(editor, InlayDisposeContext.SKIP, getEventName(event));
            return;
        }
        CommandEditorState commandEndState = createCommandState(editor);
        if (isValidCommand(editor, event.getCommandName())
            && isDocumentModification(commandStartState, commandEndState)) {
            LoggerUtil.INSTANCE.logDebug(LOG, "command modified document: " + event.getCommandName());
            editorManager.editorModified(editor, EditEventTriggerKind.Automatic);
        } else if (isCaretPositionChange(commandStartState, commandEndState)) {
            //TODO lingma源码有一步预补全，待优化

            // 没有文本变更的命令，需要额外看是否需要拒绝补全
            editorManager.disposeInlays(editor, InlayDisposeContext.CaretChange, getEventName(event));
        }
    }
    private boolean isSkipCommand(String commandName) {
        boolean skip = skipCommands.stream().anyMatch(commandName::contains);
        if (!skip && commandName.toLowerCase(Locale.ROOT).contains("vim")) {
            skip = true;
        }
        return skip;
    }
    public void undoTransparentActionStarted() {
        Editor editor = CodeWizEditorUtil.getSelectedEditorSafely(this.project);
        this.undoTransparentActionStamp.set(editor != null ? createUndoTransparentState(editor) : null);
    }

    public void undoTransparentActionFinished() {
        /*UndoTransparentActionState currentEditorStamp = this.undoTransparentActionStamp.get();
        this.undoTransparentActionStamp.set(null);
        Editor editor = CodewizEditorUtil.getSelectedEditorSafely(this.project);
        if (editor == null || currentEditorStamp == null || editor != currentEditorStamp.editor) {
            return;
        }
        if (getDocumentStamp(editor.getDocument()) == currentEditorStamp.modificationStamp) {
            return;
        }
        CodewizEditorManager editorManager = CodewizEditorManager.getInstance();
        if (editorManager.isAvailable(editor) && editorManager.hasCompletionInlays(editor)) {
            editorManager.editorModified(editor, CompletionRequestType.Forced);
        }*/
    }
    private boolean isValidCommand(Editor editor, String commandName) {
      return StringUtils.isNotBlank(commandName) && LookupManager.getActiveLookup(editor) == null;
    }
    private static long getDocumentStamp(@NotNull Document document) {
        return document instanceof DocumentEx ? (long)((DocumentEx)document).getModificationSequence() : document.getModificationStamp();
    }

    @NotNull
    private static CodewizCommandListener.CommandEditorState createCommandState(@NotNull Editor editor) {
        int offset = editor.getCaretModel().getOffset();
        int line = editor.getDocument().getLineNumber(offset);
        int lineStartOffset = editor.getDocument().getLineStartOffset(line);
        int lineEndOffset = editor.getDocument().getLineEndOffset(line);
        String linePrefix = editor.getDocument().getText(new TextRange(lineStartOffset, offset));
        String lineSuffix = editor.getDocument().getText(new TextRange(offset, lineEndOffset));
        return new CommandEditorState(getDocumentStamp(editor.getDocument()), editor.getCaretModel().getVisualPosition(),
            linePrefix, lineSuffix, editor.getSelectionModel().getSelectedText(), editor.getCaretModel().getOffset());
    }

    @NotNull
    private static  CodewizCommandListener.UndoTransparentActionState createUndoTransparentState(@NotNull Editor editor) {
        return new CodewizCommandListener.UndoTransparentActionState(editor, getDocumentStamp(editor.getDocument()));
    }

    private static boolean isDocumentModification(@NotNull CodewizCommandListener.CommandEditorState first, @NotNull CodewizCommandListener.CommandEditorState second) {
        return first.modificationStamp != second.modificationStamp;
    }

    private static boolean isCaretPositionChange(@NotNull CodewizCommandListener.CommandEditorState first, @NotNull CodewizCommandListener.CommandEditorState second) {
        return !first.visualPosition.equals(second.visualPosition);
    }

    private String getEventName(CommandEvent event) {
        String eventName = event.getCommandName();
        //无法识别命令时，尝试获取其命令组
        if (StringUtils.isBlank(eventName)) {
            Object groupId = event.getCommandGroupId();
            if (groupId instanceof DocCommandGroupId) {
                eventName = "doc change";
            } else if (event.getCommandGroupId() instanceof Key<?>) {
                eventName = event.getCommandGroupId().toString();
            } else {
                try {
                    eventName = ObjectUtils.objectInfo(event.getCommandGroupId());
                } catch (Exception e) {
                    eventName = "unknown";
                }
            }
        }
        return eventName;
    }
    private static final class CommandEditorState {
        private final long modificationStamp;
        private final VisualPosition visualPosition;
        private final String lineSuffix;
        private final String linePrefix;
        private final String selectionText;
        private final int offset;

        public CommandEditorState(long modificationStamp, VisualPosition visualPosition, String linePrefix, String lineSuffix, String selectionText, int offset) {
            this.modificationStamp = modificationStamp;
            this.visualPosition = visualPosition;
            this.linePrefix = linePrefix;
            this.lineSuffix = lineSuffix;
            this.selectionText = selectionText;
            this.offset = offset;
        }

        public String getCaretPrevChar() {
            if (this.linePrefix != null && !this.linePrefix.isEmpty()) {
                return this.linePrefix.substring(this.linePrefix.length() - 1);
            }
            return null;
        }

        public String getCaretNextChar() {
            if (this.lineSuffix != null && !this.lineSuffix.isEmpty()) {
                return this.lineSuffix.substring(0, 1);
            }
            return null;
        }

        public long getModificationStamp() {
            return this.modificationStamp;
        }

        public VisualPosition getVisualPosition() {
            return this.visualPosition;
        }

        public String getLineSuffix() {
            return this.lineSuffix;
        }

        public String getLinePrefix() {
            return this.linePrefix;
        }

        public String getSelectionText() {
            return this.selectionText;
        }

        public int getOffset() {
            return this.offset;
        }
    }
    private static final class UndoTransparentActionState {
        private final @NotNull Editor editor;
        private final long modificationStamp;

        public UndoTransparentActionState(@NotNull Editor editor, long modificationStamp) {
            this.editor = editor;
            this.modificationStamp = modificationStamp;
        }

        public @NotNull Editor getEditor() {
            return this.editor;
        }

        public long getModificationStamp() {
            return this.modificationStamp;
        }
    }
}

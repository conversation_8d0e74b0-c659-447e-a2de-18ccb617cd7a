package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.global.ZeroBasedIndex;
/**
 * 删除 Tab
 */
public class DeleteTab {
    private String schemaProtocol = "workspace.delete.tab";
    private DeleteTabParams params;

    public DeleteTabParams getParams() {
        return params;
    }
    public void setParams(DeleteTabParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class DeleteTabParams {
        /** Tab 的索引 */
        private Integer tabIndex;
        /** 所在 TabGroup 的索引 */
        private Integer tabGroupIndex;
    
        public Integer getTabIndex() {
            return tabIndex;
        }
        public void setTabIndex(Integer tabIndex) {
            this.tabIndex = tabIndex;
        }
        public Integer getTabGroupIndex() {
            return tabGroupIndex;
        }
        public void setTabGroupIndex(Integer tabGroupIndex) {
            this.tabGroupIndex = tabGroupIndex;
        }
    }
}

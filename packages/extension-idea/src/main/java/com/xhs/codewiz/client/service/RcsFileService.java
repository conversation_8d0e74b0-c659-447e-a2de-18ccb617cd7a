package com.xhs.codewiz.client.service;

import com.intellij.diff.editor.ChainDiffVirtualFile;
import com.intellij.ide.projectView.ProjectView;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.ex.FileEditorManagerEx;
import com.intellij.openapi.fileEditor.impl.EditorWindow;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Computable;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.platform.DiffPanelUtil;
import com.xhs.codewiz.scheme.file.CreateFile;
import com.xhs.codewiz.scheme.file.CreateFile.CreateFileParams;
import com.xhs.codewiz.scheme.file.DeleteFile;
import com.xhs.codewiz.scheme.file.ExecuteClose;
import com.xhs.codewiz.scheme.file.ExecuteOpen;
import com.xhs.codewiz.scheme.file.ExecuteOpen.ExecuteOpenParams;
import com.xhs.codewiz.scheme.file.ExecuteRevert;
import com.xhs.codewiz.scheme.file.ExecuteRevert.ExecuteRevertParams;
import com.xhs.codewiz.scheme.file.ExecuteSave;
import com.xhs.codewiz.scheme.file.ExecuteSave.ExecuteSaveParams;
import com.xhs.codewiz.scheme.file.ReadDirectory;
import com.xhs.codewiz.scheme.file.ReadDirectoryResponse;
import com.xhs.codewiz.scheme.file.ReadDirectoryResponse.ReadDirectoryResponseResult;
import com.xhs.codewiz.scheme.file.ReadFile;
import com.xhs.codewiz.scheme.file.ReadFile.ReadFileParams;
import com.xhs.codewiz.scheme.file.ReadFileResponse;
import com.xhs.codewiz.scheme.file.ReadFileStat;
import com.xhs.codewiz.scheme.file.ReadFileStatResponse;
import com.xhs.codewiz.scheme.file.UpdateFileUri;
import com.xhs.codewiz.type.file.FileOpenOptions.FileOpenOptionsDidRevealInExplorer;
import com.xhs.codewiz.type.file.FileStat;
import com.xhs.codewiz.type.file.Permissions;
import com.xhs.codewiz.type.workspace.ViewColumn;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.FileUtil;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.List;
import javax.swing.SwingConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * Author: liukunpeng Date: 2025-07-16 Description:
 */
public class RcsFileService {
  private static final Logger logger = Logger.getInstance(RcsFileService.class);
  public static void fileCreate(String str, String channel) {
    fileCreateWithRes(str, channel);
  }

  public static Boolean fileCreateWithRes(String str, String channel) {
    CreateFile createFile = GsonUtil.fromJson(str, CreateFile.class);
    CreateFileParams params = createFile.getParams();
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    return WriteCommandAction.runWriteCommandAction(project, (Computable<Boolean>)() -> {
      try {
        Path path = getPathWithUri(params.getUri());
        Files.createDirectories(path.getParent());
        // 写文件
        Files.write(path, params.getContent().getText().getBytes());
        // 马上刷新使IDE识别
        LocalFileSystem.getInstance().refreshAndFindFileByPath(params.getUri());
        return true;
      } catch (IOException e) {
        LoggerUtil.INSTANCE.logWarn(logger, "Failed to write file: ", e);
        return false;
      }
    });
  }

  public static ReadFileResponse fileRead(String str, String channel) {
    ReadFile readFile = GsonUtil.fromJson(str, ReadFile.class);
    ReadFileParams params = readFile.getParams();
    Path path = getPathWithUri(params.getUri());
    VirtualFile file = VirtualFileManager.getInstance().findFileByNioPath(path);
    if (file != null) {
      Project project = RcsWebSocketManager.INSTANCE.getProjectByLocalChannel(channel);
      ReadFileResponse readFileResponse = new ReadFileResponse();;
      readFileResponse.setResult(FileUtil.buildContent(project, file));
      return readFileResponse;
    }
    return null;
  }

  public static void fileDelete(String str, String channel) {
    fileDeleteWithRes(str, channel);
  }

  public static Boolean fileDeleteWithRes(String str, String channel) {
    DeleteFile createFile = GsonUtil.fromJson(str, DeleteFile.class);
    if (null == createFile || StringUtils.isEmpty(createFile.getParams())) {
      return false;
    }
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    return WriteCommandAction.runWriteCommandAction(project, (Computable<Boolean>)() -> {
      Path path = getPathWithUri(createFile.getParams());
      VirtualFile file = VirtualFileManager.getInstance().findFileByNioPath(path);
      try {
        file.delete(null);
        return true;
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(logger, "LOCAL Failed to delete file: ", e);
        return false;
      }
    });
  }


  public static void fileOpen(String str, String channel) {
    ExecuteOpen excuteOpen = GsonUtil.fromJson(str, ExecuteOpen.class);
    if (null == excuteOpen || null == excuteOpen.getParams()) {
     return;
    }
    ExecuteOpenParams params = excuteOpen.getParams();
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    ApplicationManager.getApplication().invokeLater(() -> {
      VirtualFile file = RcsWorkspaceContentProviderService.getContentProviderFileByUri(params.getUri(), channel);
      //Path path = getPathWithUri(params.getUri());
      if (null == file || !file.exists()) {
        return;
      }
      FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);
      //是否需要展示文件
      boolean isShow = null != params.getOptions().getDidShow() && params.getOptions().getDidShow();

      if (isShow) {
        //是否需要聚焦文件
        boolean isFocs = null != params.getOptions().getDidFocus() && params.getOptions().getDidFocus();
        ViewColumn viewColumn = params.getOptions().getViewColumn();
        //是否拆分展示
        boolean isSplit = null != viewColumn && null != viewColumn.getIsBeside() && viewColumn.getIsBeside();
        //需要展示
        if (isSplit) {
          //这个是需要拆分的
          if (fileEditorManager instanceof FileEditorManagerEx ex) {
            // 获取当前的 EditorWindow（编辑区窗口，多分区时有多个）
            EditorWindow currentWindow = ex.getCurrentWindow();
            if (currentWindow != null) {
              //先关闭再展示
              FileEditorManagerEx managerEx = FileEditorManagerEx.getInstanceEx(project);
              for (EditorWindow window : managerEx.getWindows()) {
                if (window.isFileOpen(file)) {
                  window.closeFile(file);
                }
              }
              currentWindow.split(SwingConstants.RIGHT, !isFocs, file, isFocs);
            }
          }
        } else {
          Pair<ChainDiffVirtualFile, Long> pair = DiffPanelUtil.getEditorChainDiffFileByRightUri(params.getUri());
          if (null != pair) {
            Long now = System.currentTimeMillis();
            //如果dffPanel存在，并且5s内没有新变更，那么展示
            if (now - pair.getRight() > 5000) {
              fileEditorManager.openFile(file, isFocs);
            }
          } else {
            //直接展示
            fileEditorManager.openFile(file, isFocs);
          }
        }
        FileOpenOptionsDidRevealInExplorer didRevealInExplorer = params.getOptions().getDidRevealInExplorer();
        if (null == didRevealInExplorer
            || null == didRevealInExplorer.getExpandDirectory()
            || !didRevealInExplorer.getExpandDirectory()) {
          return;
        }
        //需要左侧目录定位
        ProjectView.getInstance(project).select(null, file, false);
      }
    });
  }
  public static void fileClose(String str, String channel) {
    ExecuteClose close = GsonUtil.fromJson(str, ExecuteClose.class);
    if (null == close || null == close.getParams()) {
      return;
    }
    String url = close.getParams().getUri();
    if (StringUtils.isEmpty(url)) {
      return;
    }
    ApplicationManager.getApplication().invokeLater(() -> {
      VirtualFile file = RcsWorkspaceContentProviderService.getContentProviderFileByUri(url, channel);
      if (null == file) {
        return;
      }
      Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
      FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);
      if (fileEditorManager.isFileOpen(file)) {
        fileEditorManager.closeFile(file);
      }
    });
  }
  public static ReadDirectoryResponse readDirectory(String str, String channel) {
    ReadDirectoryResponse response = new ReadDirectoryResponse();
    ReadDirectory readDirectory = GsonUtil.fromJson(str, ReadDirectory.class);
    if (null == readDirectory || null == readDirectory.getParams()) {
      response.setError("入参校验失败");
      return response;
    }
    List<ReadDirectoryResponseResult> results = new ArrayList<>();
    Path path = getPathWithUri(readDirectory.getParams().getUri());
    VirtualFile virtualDir = LocalFileSystem.getInstance().findFileByNioFile(path);
    if (virtualDir != null && virtualDir.isDirectory()) {
      for (VirtualFile file : virtualDir.getChildren()) {
        ReadDirectoryResponseResult resultItem = new ReadDirectoryResponseResult();
        resultItem.setName(file.getName());
        resultItem.setType(FileUtil.getFileType(file));
        results.add(resultItem);
      }
      response.setResult(results);
    } else {
      response.setError("目录不存在或者目标为文件");
    }
    return response;
  }

  public static ReadFileStatResponse readFileStat(String str, String channel) {
    ReadFileStatResponse response = new ReadFileStatResponse();
    ReadFileStat readFileStat = GsonUtil.fromJson(str, ReadFileStat.class);
    if (null == readFileStat || null == readFileStat.getParams()) {
      response.setError("参数校验失败");
      return response;
    }
    Path path = getPathWithUri(readFileStat.getParams().getUri());
    VirtualFile virtual = VirtualFileManager.getInstance().findFileByNioPath(path);
    if (null == virtual) {
      response.setError("目标为文件不存在");
      return response;
    }
    FileStat stat = new FileStat();
    if (Files.isReadable(path)) {
      stat.setPermissions(Permissions.Readonly);
    }
    try {
      BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);
      stat.setCtime((int)attrs.creationTime().toMillis());
      stat.setMtime((int)attrs.lastModifiedTime().toMillis());
    } catch (IOException e) {
      LoggerUtil.INSTANCE.logWarn(logger, "readFileStat attrs error", e);
    }
    stat.setType(FileUtil.getFileType(virtual));
    stat.setSize((int)virtual.getLength());
    response.setResult(stat);
    return response;
  }

  public static void fileRevert(String str, String channel) {
    ExecuteRevert executeRevert = GsonUtil.fromJson(str, ExecuteRevert.class);
    if (null == executeRevert || null == executeRevert.getParams()) {
      return;
    }
    ExecuteRevertParams params = executeRevert.getParams();
    try {
      Path path = getPathWithUri(params.getUri());
      VirtualFile file = VirtualFileManager.getInstance().findFileByNioPath(path);
      if (file != null) {
        WriteCommandAction.runWriteCommandAction(ApplicationUtil.findCurrentProject(), () -> {
          Document document = FileDocumentManager.getInstance().getDocument(file);
          // 下一步继续操作
          if (document != null && FileDocumentManager.getInstance().isDocumentUnsaved(document)) {
            FileDocumentManager.getInstance().reloadFromDisk(document);
          }
        });
      }
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(logger, "LOCAL Failed to revert file: ", e);
    }
  }

  public static Boolean fileSave(String str, String channel) {
    ExecuteSave save = GsonUtil.fromJson(str, ExecuteSave.class);
    if (null == save || null == save.getParams()) {
      return true;
    }
    ExecuteSaveParams params = save.getParams();
    try {
      Path path = getPathWithUri(params.getUri());
      VirtualFile file = VirtualFileManager.getInstance().findFileByNioPath(path);
      if (file != null) {
        WriteCommandAction.runWriteCommandAction(ApplicationUtil.findCurrentProject(), () -> {
          Document document = FileDocumentManager.getInstance().getDocument(file);
          // 下一步继续操作
          if (document != null && FileDocumentManager.getInstance().isDocumentUnsaved(document)) {
            FileDocumentManager.getInstance().saveDocument(document);
          }
        });
      }
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(logger, "LOCAL Failed to revert file: ", e);
    }
    return true;
  }

  public static void fileUpdateUri(String str, String channel) {
    UpdateFileUri updateUri = GsonUtil.fromJson(str, UpdateFileUri.class);
    if (null == updateUri || StringUtils.isEmpty(updateUri.getParams())) {
      return;
    }
    try {
      Path path = getPathWithUri(updateUri.getParams());
      VirtualFile file = LocalFileSystem.getInstance().refreshAndFindFileByNioFile(path);
      if (file == null) {
        VirtualFile parent = LocalFileSystem.getInstance()
            .refreshAndFindFileByPath(updateUri.getParams().substring(0, updateUri.getParams().lastIndexOf("/")));
        if (parent != null) {
          parent.refresh(false, true);
        }
      } else {
        file.refresh(false, false);
      }
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(logger, "local updateUri err: ", e);
    }
  }
  public static Path getPathWithUri(String uri) {
    return Path.of(URI.create(uri));
  }
}

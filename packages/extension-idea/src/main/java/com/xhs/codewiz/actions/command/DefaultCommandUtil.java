package com.xhs.codewiz.actions.command;

import static com.xhs.codewiz.constant.PluginCommonConstant.DEFAULT_COMMAND_GROUP_MENU;
import static com.xhs.codewiz.constant.PluginCommonConstant.DEFAULT_COMMAND_GROUP_VIEWS;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.intellij.openapi.actionSystem.ActionManager;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.client.service.RcsGlobalService;
import com.xhs.codewiz.scheme.global.ExecuteCommand;
import com.xhs.codewiz.scheme.global.ExecuteCommand.ExecuteCommandParams;
import com.xhs.codewiz.type.global.CommandType;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.swing.Icon;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-07-22 Description:
 * 注册初始命令
 */
public class DefaultCommandUtil {
  public static Map<Project, List<String>> actionIdMap = new ConcurrentHashMap<>();
  public static void registerChannelCommand(Project project, JsonObject requirment) {
    if (null == requirment || !requirment.has("contributes")) {
      return;
    }
    JsonObject contributes = requirment.getAsJsonObject("contributes");
    JsonArray commands = new JsonArray();
    if (contributes.has("commands")) {
      commands = contributes.getAsJsonArray("commands");
    }
    for (String key : contributes.keySet()) {
      switch (key) {
        case DEFAULT_COMMAND_GROUP_MENU :
          routerMenu(project, contributes.getAsJsonObject(key), commands);
          break;
        case DEFAULT_COMMAND_GROUP_VIEWS :
          ViewsCommandUtil.registerViewsCommand(contributes.getAsJsonObject(key));
          break;
        default:break;
      }
    }
  }

  /**
   * 分发到menus下的各个位置上去
   * @param menus
   * @param commands
   */
  private static void routerMenu(Project project, JsonObject menus, JsonArray commands) {
    if (null == menus) {
      return;
    }
    if (menus.has(EditorCommandUtil.editorNextGroupKey)) {
      EditorCommandUtil.registerEditorCommand(project, menus, commands);
    }
    if (menus.has(ChatWindowCommandUtil.chatWindowTitleNextGroupKey)) {
      ChatWindowCommandUtil.registerWindowViewsCommand(project, menus, commands);
    }
    if (menus.has(DiffCommentCommandUtil.commentTitleGroupKey)) {
      DiffCommentCommandUtil.registerDiffCommentCommand(project, menus, commands);
    }
  }
  public static BaseCommandAction createAction(Project project, JsonObject commandJson, String channel, Icon icon) {
    String title = commandJson.get("title").getAsString();
    String command = commandJson.get("command").getAsString();
    BaseCommandAction action = new BaseCommandAction(title, command, icon) {
      @Override
      public void update(@NotNull AnActionEvent e) {
        super.update(e);
        Project proj = e.getProject();
        //仅创建该action的项目可以展示
        if (null == proj || !proj.equals(project)) {
          e.getPresentation().setEnabledAndVisible(false);
        }
      }
      @Override
      public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        Pair<String, ExecuteCommandParams> pair = RcsGlobalService.getCommandParam(project, command);
        if (null !=  pair) {
          //这一类基本都是预设的
          ExecuteCommand executeCommand = new ExecuteCommand();
          executeCommand.setParams(pair.getRight());
          Object obj  = RcsWebSocketManager.INSTANCE.sendRequestWithChannelProvider(pair.getLeft(), "default",
              executeCommand, new TypeToken<Object>(){},  anActionEvent.getProject(), 1000);
        } else {
          ExecuteCommand executeCommand = new ExecuteCommand();
          ExecuteCommandParams params = new ExecuteCommandParams();
          params.setCommand(command);
          params.setType(CommandType.Default);
          Object obj  = RcsWebSocketManager.INSTANCE.sendRequestWithChannelProvider(channel, "default",
              executeCommand, new TypeToken<Object>(){},  anActionEvent.getProject(), 1000);
        }
      }
    };
    return action;
  }
  //action注册是全局的，关闭project的时候，需要清理当前项目注册的action
  public static void unRegisterAction(Project project) {
    EditorCommandUtil.unRegisterActionEditorAction(project);
    if (actionIdMap.containsKey(project)) {
      for (String actionId : actionIdMap.get(project)) {
        AnAction action = ActionManager.getInstance().getAction(actionId);
        if (null != action) {
          ActionManager.getInstance().unregisterAction(actionId);
        }
      }
    }
  }
}

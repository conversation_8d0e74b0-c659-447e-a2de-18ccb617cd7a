package com.xhs.codewiz.completion.request;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.util.Computable;
import com.intellij.openapi.util.TextRange;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

public class Measurements {
  Character charBeforeCaret;
  Character charAfterCaret;
  Character charTrimBeforeCaret;
  Character charTrimAfterCaret;
  Integer lastLineLengthBeforeCaret;
  Integer lastLineTrimEndLengthBeforeCaret;
  Integer documentLength;
  Integer caretCharOffset;
  Long completionDelayMs;

  public static Measurements build(@NotNull Editor editor) {
    return ApplicationManager.getApplication().runReadAction(
        (Computable<Measurements>) () -> {
          int caretOffset = editor.getCaretModel().getOffset();
          return Measurements.build(editor, caretOffset);
        });
  }

  public static Measurements build(@NotNull Editor editor, int caretOffset) {
    String text = editor.getDocument().getText();
    int lineNumber = editor.getDocument().getLineNumber(caretOffset);
    Measurements measurements = new Measurements();
    if (StringUtils.isEmpty((CharSequence)text)) {
      return measurements;
    }
    measurements.setCaretCharOffset(caretOffset);
    if (text.length() > measurements.getCaretCharOffset()) {
      measurements.setCharAfterCaret(Character.valueOf(text.charAt(measurements.getCaretCharOffset())));
      String suffix = text.substring(measurements.getCaretCharOffset()).trim();
      if (!suffix.isEmpty()) {
        measurements.setCharTrimAfterCaret(Character.valueOf(suffix.charAt(0)));
      }
    }
    if (measurements.getCaretCharOffset() > 1) {
      measurements.setCharBeforeCaret(Character.valueOf(text.charAt(measurements.getCaretCharOffset() - 2)));
      String prefix = text.substring(0, measurements.getCaretCharOffset()).trim();
      if (!prefix.isEmpty()) {
        measurements.setCharTrimBeforeCaret(Character.valueOf(prefix.charAt(prefix.length() - 1)));
      }
    }
    measurements.setDocumentLength(text.length());
    if (lineNumber > 0) {
      int lastLineStartOffset = editor.getDocument().getLineStartOffset(lineNumber - 1);
      int lastLineEndOffset = editor.getDocument().getLineEndOffset(lineNumber - 1);
      String lastLine = editor.getDocument().getText(new TextRange(lastLineStartOffset, lastLineEndOffset));
      measurements.setLastLineLengthBeforeCaret(lastLine.length());
      measurements.setLastLineTrimEndLengthBeforeCaret(StringUtils.stripEnd((String)lastLine, (String)" \t\r\n").length());
    }
    return measurements;
  }

  
  public Character getCharBeforeCaret() {
    return this.charBeforeCaret;
  }

  
  public Character getCharAfterCaret() {
    return this.charAfterCaret;
  }

  
  public Character getCharTrimBeforeCaret() {
    return this.charTrimBeforeCaret;
  }

  public Character getCharTrimAfterCaret() {
    return this.charTrimAfterCaret;
  }

  public Integer getLastLineLengthBeforeCaret() {
    return this.lastLineLengthBeforeCaret;
  }
  
  public Integer getLastLineTrimEndLengthBeforeCaret() {
    return this.lastLineTrimEndLengthBeforeCaret;
  }

  public Integer getDocumentLength() {
    return this.documentLength;
  }
  
  public Integer getCaretCharOffset() {
    return this.caretCharOffset;
  }
  
  public Long getCompletionDelayMs() {
    return this.completionDelayMs;
  }

  public void setCharBeforeCaret(Character charBeforeCaret) {
    this.charBeforeCaret = charBeforeCaret;
  }

  public void setCharAfterCaret(Character charAfterCaret) {
    this.charAfterCaret = charAfterCaret;
  }

  public void setCharTrimBeforeCaret(Character charTrimBeforeCaret) {
    this.charTrimBeforeCaret = charTrimBeforeCaret;
  }

  public void setCharTrimAfterCaret(Character charTrimAfterCaret) {
    this.charTrimAfterCaret = charTrimAfterCaret;
  }
  
  public void setLastLineLengthBeforeCaret(Integer lastLineLengthBeforeCaret) {
    this.lastLineLengthBeforeCaret = lastLineLengthBeforeCaret;
  }
  
  public void setLastLineTrimEndLengthBeforeCaret(Integer lastLineTrimEndLengthBeforeCaret) {
    this.lastLineTrimEndLengthBeforeCaret = lastLineTrimEndLengthBeforeCaret;
  }

  public void setDocumentLength(Integer documentLength) {
    this.documentLength = documentLength;
  }

  public void setCaretCharOffset(Integer caretCharOffset) {
    this.caretCharOffset = caretCharOffset;
  }

  public void setCompletionDelayMs(Long completionDelayMs) {
    this.completionDelayMs = completionDelayMs;
  }

  public Measurements() {
  }
}
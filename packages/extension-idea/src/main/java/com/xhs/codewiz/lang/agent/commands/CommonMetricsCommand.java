package com.xhs.codewiz.lang.agent.commands;

import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-06-03 Description:
 */
public class CommonMetricsCommand implements JsonRpcNotification {
  private String metrics_scene;
  private String metrics_key;
  private Object metrics_value;

  public String getMetrics_scene() {
    return metrics_scene;
  }

  public void setMetrics_scene(String metrics_scene) {
    this.metrics_scene = metrics_scene;
  }

  public String getMetrics_key() {
    return metrics_key;
  }

  public void setMetrics_key(String metrics_key) {
    this.metrics_key = metrics_key;
  }

  public Object getMetrics_value() {
    return metrics_value;
  }

  public void setMetrics_value(Object metrics_value) {
    this.metrics_value = metrics_value;
  }

  @Override
  public @NotNull String getCommandName() {
    return "doCommonMetrics";
  }
}

package com.xhs.codewiz.completion.request;


import com.xhs.codewiz.completion.CodewizCompletion;
import java.util.List;
import net.jcip.annotations.ThreadSafe;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@ThreadSafe
public interface CompletionCache {
    boolean isLatestPrefix(@NotNull String var1);

    @Nullable List<CodewizCompletion> get(@NotNull String var1, boolean var2);

    @Nullable List<CodewizCompletion> getLatest(@NotNull String var1);

    void add(@NotNull String var1, @NotNull String var2, boolean var3, @NotNull CodewizCompletion var4);

    void clear();
}


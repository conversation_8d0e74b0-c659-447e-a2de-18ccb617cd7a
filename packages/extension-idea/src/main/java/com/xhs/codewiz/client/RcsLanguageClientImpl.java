package com.xhs.codewiz.client;

import com.google.gson.JsonObject;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.client.model.ChannelCommonNotification;
import com.xhs.codewiz.client.model.ChannelCommonRequest;
import com.xhs.codewiz.client.service.RcsWorkspaceContentProviderService;
import com.xhs.codewiz.client.service.router.RcsListenerRouter;
import com.xhs.codewiz.constant.PluginCommonConstant;
import com.xhs.codewiz.scheme.global.ExecuteHeartbeat;
import com.xhs.codewiz.scheme.global.ExecuteHeartbeat.ExecuteHeartbeatParams;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.lang3.StringUtils;

public class RcsLanguageClientImpl implements RcsLanguageClient {
  private static final Logger logger = Logger.getInstance(RcsLanguageClientImpl.class);
  RcsLanguageServer server;
  Project project;
  public RcsLanguageClientImpl() {

  }
  public void setRcsLanguageServer(RcsLanguageServer server) {
    this.server = server;
  }

  public void setProject(Project project) {
    this.project = project;
  }

  @Override
  public CompletableFuture<Object> channelRequest(
      ChannelCommonRequest request) {
    JsonObject dataJson = GsonUtil.fromJson( GsonUtil.toJson(request));
    JsonObject params = dataJson.getAsJsonObject("params");
    if (params != null && params.has("schemaProtocol")) {
      RcsListenerRouter listenerEnum = RcsListenerRouter.getBySchemeProtocol(
          params.get("schemaProtocol").getAsString());
      if (null != listenerEnum) {
        return CompletableFuture.supplyAsync(() -> listenerEnum.executeRequest(params.toString(), request.getChannel()));
      }
      LoggerUtil.INSTANCE.logWarn(logger, "Unsupported operation: " + params.get("schemaProtocol").getAsString());
    }
    return CompletableFuture.completedFuture(null);
  }

  @Override
  public void channelNotification(ChannelCommonNotification request) {
    JsonObject dataJson = GsonUtil.fromJson(GsonUtil.toJson(request));
    JsonObject params = dataJson.getAsJsonObject("params");
    if (params != null && params.has("schemaProtocol")) {
      String schemeProtocol = params.get("schemaProtocol").getAsString();
      RcsListenerRouter listenerEnum = RcsListenerRouter.getBySchemeProtocol(schemeProtocol);
      if (null != listenerEnum) {
        CompletableFuture.runAsync(() -> {
          listenerEnum.executeNotification(params.toString(), request.getChannel());
        });
        return;
      } else if (StringUtils.equals(schemeProtocol, "global.execute.heartbeat")) {
        ping(params.toString());
        return;
      }
      LoggerUtil.INSTANCE.logWarn(logger, "Unsupported operation: " + params.get("schemaProtocol").getAsString());
    }
  }
  private void ping(String str) {
    ExecuteHeartbeat excuteHeartbeat = GsonUtil.fromJson(str, ExecuteHeartbeat.class);
    if (null == excuteHeartbeat || null == excuteHeartbeat.getParams()) {
      return;
    }
    ExecuteHeartbeatParams params = excuteHeartbeat.getParams();
    if (null != params.getPing()) {
      ExecuteHeartbeat returnHeart = new ExecuteHeartbeat();
      returnHeart.setParams(new ExecuteHeartbeatParams());
      ChannelCommonNotification notification = new ChannelCommonNotification(PluginCommonConstant.CHANNEL_PATH_SUFFIX,
          returnHeart);
      this.server.channelNotification(notification);
    } else if (null != params.getSubModules()) {
       //现在是一个project一个链接，所以下线的话是空的
      if (params.getSubModules().isEmpty()) {
        try {
          RcsWebSocketManager.INSTANCE.close(project);
          RcsWebSocketManager.INSTANCE.connectCosyServer(project);
        } catch (IOException e) {
          LoggerUtil.INSTANCE.logWarn(logger, "connectCosyServer err", e);
        }
      }
    }
  }
}

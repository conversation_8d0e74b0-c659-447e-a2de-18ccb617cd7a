package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 更新面板
 */
public class UpdatePanel {
    private String schemaProtocol = "platform.update.panel";
    private UpdatePanelParams params;

    public UpdatePanelParams getParams() {
        return params;
    }
    public void setParams(UpdatePanelParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdatePanelParams {
        /** 要更新的面板 ID */
        private String id;
        private List<UpdatePanelParamsUpdates> updates;
    
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public List<UpdatePanelParamsUpdates> getUpdates() {
            return updates;
        }
        public void setUpdates(List<UpdatePanelParamsUpdates> updates) {
            this.updates = updates;
        }
    }

        public static class UpdatePanelParamsUpdates {
        /** 面板属性值 */
        private Object value;
        /** 面板属性 path， 如 `options.enableScripts`. */
        private String key;
    
        public Object getValue() {
            return value;
        }
        public void setValue(Object value) {
            this.value = value;
        }
        public String getKey() {
            return key;
        }
        public void setKey(String key) {
            this.key = key;
        }
    }
}

package com.xhs.codewiz.listener;


import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectUtil;
import com.intellij.openapi.vfs.AsyncFileListener;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.newvfs.events.VFileContentChangeEvent;
import com.intellij.openapi.vfs.newvfs.events.VFileDeleteEvent;
import com.intellij.openapi.vfs.newvfs.events.VFileEvent;
import com.intellij.openapi.vfs.newvfs.events.VFileMoveEvent;
import com.intellij.openapi.vfs.newvfs.events.VFilePropertyChangeEvent;
import com.intellij.util.messages.Topic;
import java.util.Iterator;
import java.util.List;
import one.util.streamex.StreamEx;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class AsyncFileChangesListener implements AsyncFileListener {
    public static final Topic<Runnable> TOPIC_AFTER_VFS_CHANGE = Topic.create("afterVfsChange", Runnable.class);

    public AsyncFileChangesListener() {
    }

    @Nullable
    public  LSPChangeApplier prepareChange(@NotNull List<? extends VFileEvent> events) {
        if (LSPManager.isDisabled()) {
            return null;
        } else {
            List<? extends VFileEvent> refreshEvents = (StreamEx.ofReversed(events).filter(this::isSupportedEvent)).distinct(VFileEvent::getPath).toList();
            return refreshEvents.isEmpty() ? null : new LSPChangeApplier(refreshEvents);
        }
    }

    private boolean isSupportedEvent(@NotNull VFileEvent e) {
        return e.isFromRefresh() || e instanceof VFileMoveEvent || e instanceof VFilePropertyChangeEvent || e instanceof VFileDeleteEvent;
    }

    static class LSPChangeApplier implements ChangeApplier {
        private static final Logger LOG = Logger.getInstance(LSPChangeApplier.class);
        private final List<? extends VFileEvent> refreshEvents;

        public void afterVfsChange() {
            ApplicationManager.getApplication().executeOnPooledThread(this::afterVfsChangeAsync);
        }

        void afterVfsChangeAsync() {
            LOG.debug("afterVfsChange:", new Object[]{this.refreshEvents.size()});
            LSPManager manager = LSPManager.getInstance();
            Iterator var2 = this.refreshEvents.iterator();

            while(var2.hasNext()) {
                VFileEvent event = (VFileEvent)var2.next();
                this.handleEvent(manager, event);
            }

            ApplicationManager.getApplication().getMessageBus().syncPublisher(AsyncFileChangesListener.TOPIC_AFTER_VFS_CHANGE).run();
        }

        private void handleEvent(@NotNull LSPManager manager, @NotNull VFileEvent event) {
            if (event instanceof VFileContentChangeEvent) {
                this.handleContentChangeEvent(manager, (VFileContentChangeEvent)event);
            } else if (event instanceof VFileDeleteEvent) {
                this.handleDeleteEvent(manager, (VFileDeleteEvent)event);
            } else if (event instanceof VFileMoveEvent) {
                this.handleMoveEvent(manager, (VFileMoveEvent)event);
            } else if (event instanceof VFilePropertyChangeEvent) {
                this.handlePropertyChangeEvent(manager, (VFilePropertyChangeEvent)event);
            }

        }

        private void handleDeleteEvent(@NotNull LSPManager manager, @NotNull VFileDeleteEvent event) {
            manager.notifyDidDelete(event.getFile());
        }

        private void handleContentChangeEvent(@NotNull LSPManager manager, @NotNull VFileContentChangeEvent event) {
            //manager.notifyDidChange(event.getFile());
        }

        private void handleMoveEvent(@NotNull LSPManager manager, @NotNull VFileMoveEvent event) {
            VirtualFile file = event.getFile();
            manager.notifyDidClose(file, event.getOldPath());
            if (file.isValid()) {
                Project project = ProjectUtil.guessProjectForFile(file);
                if (project != null) {
                    manager.notifyDidOpen(project, file);
                }
            }

        }

        private void handlePropertyChangeEvent(@NotNull LSPManager manager, @NotNull VFilePropertyChangeEvent event) {
            if ("name".equals(event.getPropertyName())) {
                VirtualFile file = event.getFile();
                manager.notifyDidClose(file, event.getOldPath());
                if (file.isValid()) {
                    Project project = ProjectUtil.guessProjectForFile(file);
                    if (project != null) {
                        manager.notifyDidOpen(project, file);
                    }
                }

            }
        }

        public LSPChangeApplier(List<? extends VFileEvent> refreshEvents) {
            this.refreshEvents = refreshEvents;
        }

        public List<? extends VFileEvent> getRefreshEvents() {
            return this.refreshEvents;
        }
    }
}


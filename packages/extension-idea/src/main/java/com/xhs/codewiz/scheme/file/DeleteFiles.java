package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 批量删除文件
 */
public class DeleteFiles {
    private String schemaProtocol = "file.delete.files";
    /** 要删除的文件 URI 列表 */
    private List<String> params;

    public List<String> getParams() {
        return params;
    }
    public void setParams(List<String> params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

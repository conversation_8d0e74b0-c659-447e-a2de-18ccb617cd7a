package com.xhs.codewiz.completion;

import com.xhs.codewiz.completion.request.CodewizInlayList;
import com.xhs.codewiz.completion.request.CompletionCache;
import com.xhs.codewiz.completion.util.CompletionUtil;
import com.xhs.codewiz.editor.request.EditorRequest;
import com.xhs.codewiz.editor.request.LineInfo;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class TypingAsSuggestedCompletionUtil {
    private TypingAsSuggestedCompletionUtil() {
    }

    public static @Nullable List<CodewizInlayList> handleTypeAheadCaching(@NotNull EditorRequest request, @NotNull CompletionCache cache) {
        if (!isValidLineSuffix(request.getLineInfo())) {
            return null;
        } else {
            String prefix = request.getCurrentDocumentPrefix();
            List<CodewizCompletion> items = cache.getLatest(prefix);
            if (items == null) {
                return null;
            } else {
                boolean dropLinePrefix = cache.isLatestPrefix(prefix);
                List<CodewizInlayList> inlays = (List)items.stream().map((item) -> {
                    return CompletionUtil.createEditorCompletion(request, item, dropLinePrefix);
                }).filter(Objects::nonNull).collect(Collectors.toList());
                return inlays.isEmpty() ? null : inlays;
            }
        }
    }

    private static boolean isValidLineSuffix(@NotNull LineInfo line) {
        String lineSuffix = line.getLineSuffix();
        return lineSuffix.isEmpty() || CompletionUtil.isValidMiddleOfTheLinePosition(lineSuffix);
    }
}


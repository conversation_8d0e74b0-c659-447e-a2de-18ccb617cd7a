package com.xhs.codewiz.editor.request;


import com.google.gson.annotations.SerializedName;

import com.xhs.codewiz.type.content.Position;
import org.jetbrains.annotations.NotNull;

public class Document {
    @SerializedName("position")
    private Position position;
    @SerializedName("insertSpaces")
    private boolean useSpaces;
    @SerializedName("tabSize")
    private int tabSize;
    @SerializedName("uri")
    private VirtualFileUri uri;
    @SerializedName("version")
    private int version;

    private String fileContent;

    public @NotNull Position getPosition() {
        return this.position;
    }

    public boolean isUseSpaces() {
        return this.useSpaces;
    }

    public int getTabSize() {
        return this.tabSize;
    }

    public VirtualFileUri getUri() {
        return this.uri;
    }

    public int getVersion() {
        return this.version;
    }

    public void setPosition(@NotNull Position position) {
        this.position = position;
    }

    public void setUseSpaces(boolean useSpaces) {
        this.useSpaces = useSpaces;
    }

    public void setTabSize(int tabSize) {
        this.tabSize = tabSize;
    }

    public void setUri(VirtualFileUri uri) {
        this.uri = uri;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }

    public Document(@NotNull Position position, boolean useSpaces, int tabSize, VirtualFileUri uri, int version, String documentContent) {
        this.position = position;
        this.useSpaces = useSpaces;
        this.tabSize = tabSize;
        this.uri = uri;
        this.version = version;
        this.fileContent = documentContent;
    }
}


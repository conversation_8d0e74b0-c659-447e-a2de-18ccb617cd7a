package com.xhs.codewiz.type.platform;

import java.util.*;

/**
 * Webview Provider
 */
public class WebviewProvider {
    private WebviewProviderOptions options;

    /** Webview Provider ID */
    private String id;

    public WebviewProviderOptions getOptions() {
        return options;
    }

    public void setOptions(WebviewProviderOptions options) {
        this.options = options;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

public static class WebviewProviderOptions {
    /** 在 Webview 隐藏时是否保留上下文 */
    private Boolean retainContextWhenHidden;

    public Boolean getRetainContextWhenHidden() {
        return retainContextWhenHidden;
    }

    public void setRetainContextWhenHidden(Boolean retainContextWhenHidden) {
        this.retainContextWhenHidden = retainContextWhenHidden;
    }

}
}

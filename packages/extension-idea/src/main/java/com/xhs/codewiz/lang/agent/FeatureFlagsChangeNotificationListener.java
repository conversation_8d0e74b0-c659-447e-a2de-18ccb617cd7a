package com.xhs.codewiz.lang.agent;

import com.google.gson.JsonElement;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.lang.agent.notifications.FeatureFlagsNotification;
import com.xhs.codewiz.lang.agent.rpc.JsonRPC;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import com.xhs.codewiz.lang.featureflags.CodeWizFeatureFlags;
import com.xhs.codewiz.lang.featureflags.CodeWizFeatureFlagsService;
import org.jetbrains.annotations.NotNull;

public class FeatureFlagsChangeNotificationListener implements JsonRpcNotificationListener {
    private static final Logger AGENT_LOG = Logger.getInstance("#codewiz");

    public FeatureFlagsChangeNotificationListener() {
    }

    public boolean handleMessage(@NotNull String name, @NotNull JsonElement message) {
        if (!"featureFlagsNotification".equals(name)) {
            return false;
        } else {
            FeatureFlagsNotification featureFlags = (FeatureFlagsNotification) JsonRPC.parseResponse(message, FeatureFlagsNotification.class);
            Logger var10000 = AGENT_LOG;
            boolean var10001 = featureFlags.isSsc();
            var10000.debug("FeatureFlags: ssc=" + var10001 + ": rt=" + featureFlags.isRt() + ": chat=" + featureFlags.isChat());
            CodeWizFeatureFlags flags = new CodeWizFeatureFlags(featureFlags.isSsc(), featureFlags.isRt(), featureFlags.isChat());
     //       FeatureFlagManager.INSTANCE.setFeatureFlags(flags);
            CodeWizFeatureFlagsService.notifyApplication(flags);
            return true;
        }
    }
}

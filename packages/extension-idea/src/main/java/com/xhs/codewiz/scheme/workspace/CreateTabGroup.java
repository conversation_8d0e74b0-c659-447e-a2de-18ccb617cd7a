package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.TabGroup;
/**
 * 创建 TabGroup
 */
public class CreateTabGroup {
    private String schemaProtocol = "workspace.create.tabgroup";
    private CreateTabGroupParams params;

    public CreateTabGroupParams getParams() {
        return params;
    }
    public void setParams(CreateTabGroupParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateTabGroupParams {
        private TabGroup tabGroup;
        /** TabGroup 的索引 */
        private Integer tabGroupIndex;
    
        public TabGroup getTabGroup() {
            return tabGroup;
        }
        public void setTabGroup(TabGroup tabGroup) {
            this.tabGroup = tabGroup;
        }
        public Integer getTabGroupIndex() {
            return tabGroupIndex;
        }
        public void setTabGroupIndex(Integer tabGroupIndex) {
            this.tabGroupIndex = tabGroupIndex;
        }
    }
}

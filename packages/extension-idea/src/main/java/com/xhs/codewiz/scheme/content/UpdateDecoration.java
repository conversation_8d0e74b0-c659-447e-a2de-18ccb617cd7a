package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.file.Uri;
/**
 * 为选区设置装饰器
 */
public class UpdateDecoration {
    private String schemaProtocol = "content.update.decoration";
    private UpdateDecorationParams params;

    public UpdateDecorationParams getParams() {
        return params;
    }
    public void setParams(UpdateDecorationParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateDecorationParams {
        private List<Range> ranges;
        /** 装饰器 ID */
        private String id;
        private String uri;
    
        public List<Range> getRanges() {
            return ranges;
        }
        public void setRanges(List<Range> ranges) {
            this.ranges = ranges;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

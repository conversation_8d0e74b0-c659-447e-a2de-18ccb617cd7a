package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.Terminal;
/**
 * 创建终端
 */
public class CreateTerminal {
    private String schemaProtocol = "platform.create.terminal";
    private CreateTerminalParams params;

    public CreateTerminalParams getParams() {
        return params;
    }
    public void setParams(CreateTerminalParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateTerminalParams {
        /** 终端 ID */
        private String id;
        private Terminal terminal;
    
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public Terminal getTerminal() {
            return terminal;
        }
        public void setTerminal(Terminal terminal) {
            this.terminal = terminal;
        }
    }
}

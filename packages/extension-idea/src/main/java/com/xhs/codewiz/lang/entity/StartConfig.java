package com.xhs.codewiz.lang.entity;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import com.intellij.openapi.application.ApplicationInfo;
import com.xhs.codewiz.listener.LSPManager;
import com.xhs.codewiz.utils.PluginUtil;

/**
 * Agent 启动配置结构
 */
public class StartConfig {
    
    @JSONField(name = "lifecycle_id")
    private String lifecycleId;
    
    @J<PERSON>NField(name = "plugin_version")
    private String pluginVersion;
    
    @JSONField(name = "ide_series")
    private String ideSeries;
    
    @JSONField(name = "ide_version")
    private String ideVersion;
    
    @JSONField(name = "plugin_name")
    private String pluginName;
    
    @JSONField(name = "ide_platform")
    private String idePlatform;
    
    @JSONField(name = "local_test")
    private boolean localTest;

    public StartConfig() {
    }

    public StartConfig(String lifecycleId, String pluginVersion, String ideSeries, 
                        String ideVersion, String pluginName, String idePlatform, 
                        boolean localTest) {
        this.lifecycleId = lifecycleId;
        this.pluginVersion = pluginVersion;
        this.ideSeries = ideSeries;
        this.ideVersion = ideVersion;
        this.pluginName = pluginName;
        this.idePlatform = idePlatform;
        this.localTest = localTest;
    }

    // Getter 和 Setter 方法
    public String getLifecycleId() {
        return lifecycleId;
    }

    public void setLifecycleId(String lifecycleId) {
        this.lifecycleId = lifecycleId;
    }

    public String getPluginVersion() {
        return pluginVersion;
    }

    public void setPluginVersion(String pluginVersion) {
        this.pluginVersion = pluginVersion;
    }

    public String getIdeSeries() {
        return ideSeries;
    }

    public void setIdeSeries(String ideSeries) {
        this.ideSeries = ideSeries;
    }

    public String getIdeVersion() {
        return ideVersion;
    }

    public void setIdeVersion(String ideVersion) {
        this.ideVersion = ideVersion;
    }

    public String getPluginName() {
        return pluginName;
    }

    public void setPluginName(String pluginName) {
        this.pluginName = pluginName;
    }

    public String getIdePlatform() {
        return idePlatform;
    }

    public void setIdePlatform(String idePlatform) {
        this.idePlatform = idePlatform;
    }

    public boolean isLocalTest() {
        return localTest;
    }

    public void setLocalTest(boolean localTest) {
        this.localTest = localTest;
    }

    /**
     * 创建默认的启动配置实例
     */
    public static StartConfig createDefault() {
        ApplicationInfo appInfo = ApplicationInfo.getInstance();
        
        StartConfig config = new StartConfig();
        config.setLifecycleId(LSPManager.getInstance().getLifecycleId());
        config.setPluginVersion(PluginUtil.getVersion()); // 从插件描述符中获取
        config.setIdeSeries("jetbrains");
        config.setIdeVersion(appInfo.getFullVersion());
        config.setPluginName("codewiz-agent");
        config.setIdePlatform(appInfo.getVersionName());
        config.setLocalTest(PluginUtil.isDevEnv());
        
        return config;
    }

    /**
     * 将对象转换为 JSON 字符串
     */
    public String toJson() {
        return JSON.toJSONString(this);
    }
} 
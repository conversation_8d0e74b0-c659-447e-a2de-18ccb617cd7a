package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.DocumentFilter;
/**
 * 注册内容联想
 */
public class CreateCompletionProvider {
    private String schemaProtocol = "workspace.create.completionprovider";
    private CreateCompletionProviderParams params;

    public CreateCompletionProviderParams getParams() {
        return params;
    }
    public void setParams(CreateCompletionProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateCompletionProviderParams {
        private CreateCompletionProviderParamsProvider provider;
        private List<DocumentFilter> selector;
    
        public CreateCompletionProviderParamsProvider getProvider() {
            return provider;
        }
        public void setProvider(CreateCompletionProviderParamsProvider provider) {
            this.provider = provider;
        }
        public List<DocumentFilter> getSelector() {
            return selector;
        }
        public void setSelector(List<DocumentFilter> selector) {
            this.selector = selector;
        }
    }

        public static class CreateCompletionProviderParamsProvider {
        /** 触发补全的字符 */
        private List<String> triggerCharacters; // optional
        /** completion provider 的 ID */
        private String id;
    
        public List<String> getTriggerCharacters() {
            return triggerCharacters;
        }
        public void setTriggerCharacters(List<String> triggerCharacters) {
            this.triggerCharacters = triggerCharacters;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

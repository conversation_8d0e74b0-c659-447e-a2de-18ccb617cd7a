package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
import com.xhs.codewiz.type.file.Content;
/**
 * 更新文件
 */
public class UpdateFile {
    private String schemaProtocol = "file.update.file";
    private UpdateFileParams params;

    public UpdateFileParams getParams() {
        return params;
    }
    public void setParams(UpdateFileParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateFileParams {
        private String uri;
        private Content content;
    
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
        public Content getContent() {
            return content;
        }
        public void setContent(Content content) {
            this.content = content;
        }
    }
}

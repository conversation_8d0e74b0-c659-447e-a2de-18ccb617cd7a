package com.xhs.codewiz.scheme.content;

import java.util.*;
/**
 * 删除 Comments 合集
 */
public class DeleteCommentCollection {
    private String schemaProtocol = "content.delete.commentcollection";
    private DeleteCommentCollectionParams params;

    public DeleteCommentCollectionParams getParams() {
        return params;
    }
    public void setParams(DeleteCommentCollectionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class DeleteCommentCollectionParams {
        /** Comment Provider ID，表示该集合属于哪个 Comment Provider */
        private String provider;
        /** 要删除的 Comments 合集 ID */
        private String collectionId;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public String getCollectionId() {
            return collectionId;
        }
        public void setCollectionId(String collectionId) {
            this.collectionId = collectionId;
        }
    }
}

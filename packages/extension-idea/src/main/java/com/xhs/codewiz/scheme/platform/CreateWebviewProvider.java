package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.WebviewProvider;
/**
 * 创建 Webview Provider
 */
public class CreateWebviewProvider {
    private String schemaProtocol = "platform.create.webviewprovider";
    private WebviewProvider params;

    public WebviewProvider getParams() {
        return params;
    }
    public void setParams(WebviewProvider params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

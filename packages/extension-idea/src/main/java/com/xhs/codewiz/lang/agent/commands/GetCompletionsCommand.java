package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.completion.request.CompletionExtraContext;
import com.xhs.codewiz.editor.request.Document;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class GetCompletionsCommand implements CompletionsCommand<GetCompletionsResult> {
    @SerializedName("doc")
    private final @NotNull Document doc;
    @SerializedName("options")
    private final @Nullable Map<Object, Object> options;
    @SerializedName("extraContext")
    private CompletionExtraContext extraContext;

    public @NotNull String getCommandName() {
        return "getCompletions";
    }

    public @NotNull Class<GetCompletionsResult> getResponseType() {
        return GetCompletionsResult.class;
    }

    public GetCompletionsCommand(@NotNull Document doc, @Nullable Map<Object, Object> options,
        @NotNull CompletionExtraContext extraContext) {
        this.doc = doc;
        this.options = options;
        this.extraContext = extraContext;
    }

    public @NotNull Document getDoc() {
        return this.doc;
    }

    public @Nullable Map<Object, Object> getOptions() {
        return this.options;
    }

    public CompletionExtraContext getExtraContext() {
        return extraContext;
    }

    public void setExtraContext(CompletionExtraContext extraContext) {
        this.extraContext = extraContext;
    }
}


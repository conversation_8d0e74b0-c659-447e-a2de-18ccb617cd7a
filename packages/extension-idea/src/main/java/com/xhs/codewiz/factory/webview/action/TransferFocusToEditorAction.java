package com.xhs.codewiz.factory.webview.action;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import com.xhs.codewiz.factory.webview.util.FocusTransferUtil;

/**
 * <AUTHOR>
 * @date 2025/8/6 21:38
 */
public class TransferFocusToEditorAction extends AnAction {

    public TransferFocusToEditorAction() {
        super("Transfer Focus to Editor");
        getTemplatePresentation().setDescription("Transfer focus from ToolWindow to Editor");
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            return;
        }

        // 获取当前活动的ToolWindow
        ToolWindow activeToolWindow = getCurrentActiveToolWindow(project);
        if (activeToolWindow != null) {
            System.out.println("将焦点从ToolWindow转移到编辑器: " + activeToolWindow.getId());
            FocusTransferUtil.transferFocusToEditor(project);
        }
    }

    @Override
    public void update(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        boolean enabled = false;

        if (project != null) {
            ToolWindow activeToolWindow = getCurrentActiveToolWindow(project);
            enabled = activeToolWindow != null;
        }

        e.getPresentation().setEnabled(enabled);
        e.getPresentation().setText(enabled ? "Transfer Focus to Editor" : "No Active ToolWindow");
    }

    private ToolWindow getCurrentActiveToolWindow(Project project) {
        ToolWindowManager manager = ToolWindowManager.getInstance(project);

        for (String id : manager.getToolWindowIds()) {
            ToolWindow toolWindow = manager.getToolWindow(id);
            if (toolWindow != null && toolWindow.isActive()) {
                return toolWindow;
            }
        }
        return null;
    }
}


package com.xhs.codewiz.platform.comment;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.Service;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import org.jetbrains.annotations.Nullable;

/**
 * 评论状态持久化管理
 *
 * <AUTHOR>
 */
@Service
public final class CommentPersistState {
    private final Map<String, Comment> idMap = new ConcurrentHashMap<>();
    private final Map<String, NavigableMap<Integer, Comment>> filePathMap = new ConcurrentHashMap<>();
    private final CommentHistory commentHistory = new CommentHistory();

    public static CommentPersistState getInstance() {
        return ApplicationManager.getApplication().getService(CommentPersistState.class);
    }

    /**
     * 重置评论列表
     */
    public void resetComments(List<Comment> comments) {
        clearAllComments();
        if (comments == null || comments.isEmpty()) {
            return;
        }

        for (int i = 0; i < comments.size(); i++) {
            Comment comment = comments.get(i);
            comment.setNoteId(comment.getNoteId());
            addComment(comment);
        }
    }

    /**
     * 添加单个评论
     */
    public void addComment(Comment comment) {
        if (this.idMap.containsKey(comment.getNoteId())) {
            return;
        }
        this.idMap.put(comment.getNoteId().toString(), comment);
        this.filePathMap.computeIfAbsent(comment.getFilePath(), k -> new TreeMap<>())
                .put(comment.getEndLine(), comment);
        this.commentHistory.addComment(comment);
    }

    /**
     * 清除所有评论
     */
    public void clearAllComments() {
        this.idMap.clear();
        this.filePathMap.clear();
        this.commentHistory.clear();
    }

    /**
     * 根据ID获取评论
     */
    @Nullable
    public Comment getCommentById(String id) {
        return this.idMap.get(id);
    }

    /**
     * 获取所有文件路径映射
     */
    public Map<String, List<Comment>> getAllFilePathMap() {
        Map<String, List<Comment>> resultMap = new HashMap<>();

        for (Map.Entry<String, NavigableMap<Integer, Comment>> entry : this.filePathMap.entrySet()) {
            String filePath = entry.getKey();
            NavigableMap<Integer, Comment> commentMap = entry.getValue();
            List<Comment> commentList = new ArrayList<>(commentMap.values());
            resultMap.put(filePath, commentList);
        }

        return resultMap;
    }

    /**
     * 根据文件路径获取评论列表
     */
    public List<Comment> getCommentsByFilePath(String filePath) {
        NavigableMap<Integer, Comment> map = this.filePathMap.get(filePath);
        return (map != null) ? new ArrayList<>(map.values()) : Collections.emptyList();
    }

    /**
     * 获取评论总数
     */
    public int getCommentSize() {
        return this.commentHistory.comments.size();
    }

    /**
     * 获取文件列表大小
     */
    public int getFileListSize() {
        return this.filePathMap.size();
    }

    /**
     * 获取上一个评论
     */
    public Comment getPreviousComment(String id) {
        return getAdjacentComment(id, false);
    }

    /**
     * 获取下一个评论
     */
    public Comment getNextComment(String id) {
        return getAdjacentComment(id, true);
    }

    private Comment getAdjacentComment(String id, boolean isNext) {
        Comment comment = this.idMap.get(id);
        if (comment == null) {
            return null;
        }

        String filePath = comment.getFilePath();
        NavigableMap<Integer, Comment> map = this.filePathMap.get(filePath);
        if (map == null || map.isEmpty()) {
            return null;
        }

        int currentLine = comment.getEndLine();

        Map.Entry<Integer, Comment> entry = isNext ?
                map.higherEntry(currentLine) :
                map.lowerEntry(currentLine);

        return (entry != null) ? entry.getValue() :
                (isNext ? map.firstEntry().getValue() : map.lastEntry().getValue());
    }

    /**
     * 根据ID移除评论
     */
    public void removeCommentById(String id) {
        Comment comment = this.idMap.remove(id);
        if (comment == null) {
            return;
        }

        NavigableMap<Integer, Comment> map = this.filePathMap.get(comment.getFilePath());
        if (map != null) {
            map.remove(comment.getEndLine());
        }
        if (map == null || map.isEmpty()) {
            this.filePathMap.remove(comment.getFilePath());
        }

        this.commentHistory.removeById(comment.getNoteId().toString());
    }

    /**
     * 根据文件路径移除评论
     */
    public void removeCommentsByFilePath(String filePath) {
        NavigableMap<Integer, Comment> comments = this.filePathMap.remove(filePath);
        if (comments != null) {
            for (Comment comment : comments.values()) {
                this.idMap.remove(comment.getNoteId());
            }
        }
        this.commentHistory.removeByFilePath(filePath);
    }

    /**
     * 获取评论数量（同一文件）
     */
    public int getCommentCount(Comment comment) {
        return comment.getCount();
    }

    /**
     * 获取评论索引（同一文件中的位置）
     */
    public int getCommentIndex(Comment comment) {
        return comment.getIndex();
    }

    /**
     * 评论历史记录
     */
    private static class CommentHistory {
        List<Comment> comments = new ArrayList<>();

        public void clear() {
            this.comments.clear();
        }

        public void removeById(String id) {
            this.comments.removeIf(comment -> comment.getNoteId().equals(id));
        }

        public void removeByFilePath(String filePath) {
            this.comments.removeIf(comment -> comment.getFilePath().equals(filePath));
        }

        public void addComment(Comment comment) {
            this.comments.add(comment);
        }
    }
} 
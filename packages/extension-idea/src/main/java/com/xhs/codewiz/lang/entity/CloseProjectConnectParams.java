package com.xhs.codewiz.lang.entity;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;

/**
 * Author: liukunpeng Date: 2025-03-18 Description:
 */
public class CloseProjectConnectParams implements JsonRpcCommand<Boolean> {
  @SerializedName("projectUri")
  private String projectUri;

  public String getProjectUri() {
    return projectUri;
  }

  public void setProjectUri(String projectUri) {
    this.projectUri = projectUri;
  }

  @Override
  public String getCommandName() {
    return "tylm/closeProjectConnectStatus";
  }

  @Override
  public Class<Boolean> getResponseType() {
    return Boolean.class;
  }
}

package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 获取当前 Lens 内容
 */
public class ReadLens {
    private String schemaProtocol = "workspace.read.lens";
    private ReadLensParams params;

    public ReadLensParams getParams() {
        return params;
    }
    public void setParams(ReadLensParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadLensParams {
        /** Lens Provider 的 ID */
        private String provider;
        private String uri;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

package com.xhs.codewiz.node;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.progress.ProgressIndicator;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.MetricsUtil;
import com.xhs.codewiz.utils.PluginUtil;
import com.xhs.codewiz.utils.ProcessUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * Author: liukunpeng Date: 2025-07-25 Description:
 */
public class NodeServerRunner {
  private static final Logger LOG = Logger.getInstance(NodeServerRunner.class);

  private static ServerRunnerStepEnum currentStep = ServerRunnerStepEnum.UNSTART;
  private static String titleMsg; //启动失败时的提示信息

  private static Lock lock = new ReentrantLock();

  private static Integer waitMaxCount = 120;

  private static String serverPort;

  private static String pid;
  
  // 是否需要在后续命令中使用source
  private static boolean needSourceCommand = false;

  public static void startServer() {
    // 异步执行初始化逻辑，避免阻塞IDE主线程
    new Thread(() -> {
      if (checkServerStatus() || !lock.tryLock()) {
        return;
      }
      try {
        MetricsUtil.reportRcsEnvStart();
        if (!checkAndSetupNvmEnvironment()) {
          setupNvmEnvironment();
        }
        if (!checkNode22160()) {
          installNode22160();
        }
        MetricsUtil.reportRcsEnvSuccess();
      } catch (Throwable e) {
        setTitleMsg();
        LoggerUtil.INSTANCE.logWarn(LOG, "Node server initialization env failed", e);
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        String causeStackTrace = sw.toString();
        MetricsUtil.reportRcsEnvError(currentStep.stepDesc, causeStackTrace);
        //这里异常，提前终止跳出
        lock.unlock();
        return;
      }

      try{
        MetricsUtil.reportRcsStart();
        startServerJsAndGetPort();
        LoggerUtil.INSTANCE.logInfo(LOG, "Node server initialization started. port = " + serverPort);
        MetricsUtil.reportRcsSuccess();
      } catch (Throwable e) {
        setTitleMsg();
        LoggerUtil.INSTANCE.logWarn(LOG, "Node server initialization start failed", e);
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        String causeStackTrace = sw.toString();
        MetricsUtil.reportRcsError(currentStep.stepDesc, causeStackTrace);
      } finally {
        lock.unlock();
      }
    }, "NodeServer-Init-Thread").start();
  }

  /**
   * 检测服务是否可用
   * @return
   */
  public static boolean checkServerStatus() {
    return null != pid && ProcessUtil.isProcessAlive(Long.parseLong(pid));
  }

  /**
   * 查询当前服务所处步骤以及发生错误时的错误信息
   * @return
   */
  public static Pair<ServerRunnerStepEnum, String> getCurrentStep() {
    return Pair.of(currentStep, titleMsg);
  }

  /**
   * 最多等待一分钟
   */
  public static void waitForServer(ProgressIndicator indicator) {
    int tryCount = waitMaxCount;
    while (tryCount-- > 0 ) {
      if (checkServerStatus()) {
        return;
      }
      if (null != indicator && null != currentStep) {
        indicator.setText2("当前任务：" + currentStep.stepDesc);
      }
      ThreadUtil.sleep(1000);
    }
  }
  
  public static String getServerPort() {
    return serverPort;
  }

  /**
   * 检查并设置nvm环境
   * 优先使用source方式: 首先检查source文件，存在时优先执行source和nvm -v，不存在或失败时再尝试直接nvm -v
   * @return true if nvm is available, false otherwise
   */
  private static boolean checkAndSetupNvmEnvironment() throws Exception {
    currentStep = ServerRunnerStepEnum.CHECK_NVM_INSTALLED;
    LoggerUtil.INSTANCE.logInfo(LOG, "Checking nvm environment...");
    
    // 第一步：检查source文件是否存在，优先使用source方式
    File nvmSourceFile = new File(System.getProperty("user.home"), ".nvm/nvm.sh");
    if (nvmSourceFile.exists()) {
      LoggerUtil.INSTANCE.logInfo(LOG, "nvm source file found, trying source command first");
      // 优先执行 source ~/.nvm/nvm.sh && nvm -v
      if (executeNvmVersionCheck(true)) {
        LoggerUtil.INSTANCE.logInfo(LOG, "nvm is available with source command");
        needSourceCommand = true;
        return true;
      }
      
      // source方式失败，source文件存在但nvm不可用，删除现有nvm
      LoggerUtil.INSTANCE.logInfo(LOG, "nvm source file exists but nvm not working, removing existing nvm");
      removeExistingNvm();
      return false;
    }
    
    // 第二步：source文件不存在，尝试直接执行 nvm -v
    if (executeNvmVersionCheck(false)) {
      LoggerUtil.INSTANCE.logInfo(LOG, "nvm is directly available");
      needSourceCommand = false;
      return true;
    }
    
    // 第三步：都不可用，需要安装nvm
    LoggerUtil.INSTANCE.logInfo(LOG, "nvm not found, need to install nvm");
    return false;
  }

  /**
   * 执行nvm版本检查
   * @param useSource 是否使用source命令
   * @return true if nvm is available
   */
  private static boolean executeNvmVersionCheck(boolean useSource) throws Exception {
    String cmd = useSource ? "source ~/.nvm/nvm.sh && nvm -v" : "nvm -v";
    Process process = new ProcessBuilder("bash", "-c", cmd).start();
    BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
    String output = reader.readLine();
    int exitCode = process.waitFor();
    
    LoggerUtil.INSTANCE.logInfo(LOG, "nvm version check output: " + output + ", exit code: " + exitCode);
    return exitCode == 0 && output != null && !output.isEmpty();
  }

  /**
   * 移除现有的nvm安装
   */
  private static void removeExistingNvm() throws Exception {
    LoggerUtil.INSTANCE.logInfo(LOG, "Removing existing nvm installation...");
    String homeDir = System.getProperty("user.home");
    Process process = new ProcessBuilder("bash", "-c", 
        "rm -rf " + homeDir + "/.nvm").start();
    process.waitFor();
  }

  /**
   * 设置nvm环境（安装nvm）
   */
  private static void setupNvmEnvironment() throws Exception {
    installNvm();
    needSourceCommand = true;
  }

  private static void installNvm() throws Exception {
    currentStep = ServerRunnerStepEnum.NVM_INSTALL;
    LoggerUtil.INSTANCE.logInfo(LOG, "Installing nvm...");
    String cmd = "curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash";
    Process process = new ProcessBuilder("bash", "-c", cmd).inheritIO().start();
    process.waitFor();
  }

  /**
   * 检查Node 22.16.0是否安装
   * 使用 nvm ls 22.16.0 命令
   */
  private static boolean checkNode22160() throws Exception {
    currentStep = ServerRunnerStepEnum.CHECK_NODE_VERSION;
    LoggerUtil.INSTANCE.logInfo(LOG, "Checking node 22.16.0 installation...");
    
    String baseCmd = "nvm which 22.16.0";
    String cmd = needSourceCommand ? "source ~/.nvm/nvm.sh && " + baseCmd : baseCmd;
    
    Process process = new ProcessBuilder("bash", "-c", cmd).start();
    BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
    String output = reader.readLine();
    int exitCode = process.waitFor();
    
    LoggerUtil.INSTANCE.logInfo(LOG, "nvm ls 22.16.0 output: " + output + ", exit code: " + exitCode);
    
    // 如果输出包含版本号且不包含"N/A"，说明已安装
    return output != null && output.contains("22.16.0") && !output.contains("N/A");
  }

  private static void installNode22160() throws Exception {
    currentStep = ServerRunnerStepEnum.NODE_INSTALL;
    LoggerUtil.INSTANCE.logInfo(LOG, "Installing node 22.16.0...");
    
    String baseCmd = "nvm install 22.16.0";
    String cmd = needSourceCommand ? "source ~/.nvm/nvm.sh && " + baseCmd : baseCmd;
    
    Process process = new ProcessBuilder("bash", "-c", cmd).inheritIO().start();
    process.waitFor();
  }

  private static void startServerJsAndGetPort() throws Exception {
    currentStep = ServerRunnerStepEnum.SERVER_START;
    LoggerUtil.INSTANCE.logInfo(LOG, "Starting node server...");
    String serverPath = PluginUtil.getPluginBasePath().resolve("server_lib/server.js").toString();
    
    String baseCmd = "nvm use 22.16.0 && node \"" + serverPath + "\"";
    String cmd = needSourceCommand ? "source ~/.nvm/nvm.sh && " + baseCmd : baseCmd;
    
    LoggerUtil.INSTANCE.logWarn(LOG,"start server.js cmd: " + cmd);
    Process process = new ProcessBuilder("bash", "-c", cmd).redirectErrorStream(true).start();
    
    // 读取输出流
    BufferedReader reader = new BufferedReader(
        new InputStreamReader(process.getInputStream()));
    BufferedReader errorReader = new BufferedReader(
        new InputStreamReader(process.getErrorStream()));
    String line;
    while ((line = reader.readLine()) != null) {
      LoggerUtil.INSTANCE.logDebug(LOG,"Server output: " + line);
      if (StringUtils.startsWith(line, "rcs-port-")) {
        currentStep = ServerRunnerStepEnum.SERVER_START_SUCCESS;
        LoggerUtil.INSTANCE.logInfo(LOG, "Node server started successfully.");
        serverPort = StringUtils.substringAfter(line, "rcs-port-");
      }
      if (StringUtils.startsWith(line, "rcs-pid-")) {
        currentStep = ServerRunnerStepEnum.SERVER_START_SUCCESS;
        LoggerUtil.INSTANCE.logInfo(LOG, "Node server started successfully.");
        pid = StringUtils.substringAfter(line, "rcs-pid-");
      }
    }
    if (null != serverPort && null != pid) {
      return;
    }
    /*boolean isError = false;
    while ((line = errorReader.readLine()) != null) {
      isError = true;
      LoggerUtil.INSTANCE.logInfo(LOG,"Server error: " + line);
      LoggerUtil.INSTANCE.logWarn(LOG, "Node server start failed.");
      currentStep = ServerRunnerStepEnum.SERVER_START_FAIL;
      setTitleMsg();
    }
    
    // 如果没有错误但也没有获取到port和pid，尝试不使用source的方式启动
    if (!isError && (null == serverPort || null == pid)) {
      LoggerUtil.INSTANCE.logInfo(LOG, "Retrying server start without source command...");
      String fallbackCmd = "nvm use 22.16.0 && node \"" + serverPath + "\"";
      LoggerUtil.INSTANCE.logWarn(LOG,"fallback server.js cmd: " + fallbackCmd);
      process = new ProcessBuilder("bash", "-c", fallbackCmd).redirectErrorStream(true).start();
      
      reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
      errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
      while ((line = reader.readLine()) != null) {
        LoggerUtil.INSTANCE.logWarn(LOG,"Fallback server output: " + line);
        if (StringUtils.startsWith(line, "rcs-port-")) {
          currentStep = ServerRunnerStepEnum.SERVER_START_SUCCESS;
          LoggerUtil.INSTANCE.logInfo(LOG, "Node server started successfully.");
          serverPort = StringUtils.substringAfter(line, "rcs-port-");
        }
        if (StringUtils.startsWith(line, "rcs-pid-")) {
          currentStep = ServerRunnerStepEnum.SERVER_START_SUCCESS;
          LoggerUtil.INSTANCE.logInfo(LOG, "Node server started successfully.");
          pid = StringUtils.substringAfter(line, "rcs-pid-");
        }
      }
      if (null != serverPort && null != pid) {
        return;
      }
      while ((line = errorReader.readLine()) != null) {
        LoggerUtil.INSTANCE.logInfo(LOG,"Fallback server error: " + line);
        LoggerUtil.INSTANCE.logWarn(LOG, "Node server start failed.");
        currentStep = ServerRunnerStepEnum.SERVER_START_FAIL;
        setTitleMsg();
      }
    }*/
  }
  
  private static void setTitleMsg() {
    titleMsg = currentStep.errorTitle;
  }
}

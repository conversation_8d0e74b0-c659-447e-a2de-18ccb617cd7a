package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.DecorationRenderOptions;
/**
 * 新增装饰器，用于在编辑器中渲染特定内容，如添加装饰线、背景色等
 */
public class CreateDecoration {
    private String schemaProtocol = "content.create.decoration";
    private CreateDecorationParams params;

    public CreateDecorationParams getParams() {
        return params;
    }
    public void setParams(CreateDecorationParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateDecorationParams {
        private DecorationRenderOptions options;
        /** 装饰器 ID */
        private String id;
    
        public DecorationRenderOptions getOptions() {
            return options;
        }
        public void setOptions(DecorationRenderOptions options) {
            this.options = options;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

package com.xhs.codewiz.actions.command;

import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.factory.webview.util.HighBuildVersionReflectUtil;
import com.xhs.codewiz.utils.IconsUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.RegisterUtil;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import javax.swing.Icon;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * Author: liukunpeng Date: 2025-07-27 Description:
 */
public class ChatWindowCommandUtil {
  private static Logger log = Logger.getInstance(ChatWindowCommandUtil.class);
  public static String chatWindowTitleNextGroupKey = "view/title";
  //所有的可用titleAction,value->when,actions
  private static Map<String, Map<String, List<BaseCommandAction>>> titleActions = new HashMap<>();
  //当前可用的titleAction:包含default+when的
  private static Map<String, List<BaseCommandAction>> currentShowingActions = new HashMap<>();

  /**
   * @param menus
   * @param commands
   */
  public static void registerWindowViewsCommand(Project project, JsonObject menus, JsonArray commands) {
    titleActions.remove(project.getBasePath());
    currentShowingActions.remove(project.getBasePath());
    JsonArray titleMenus = menus.getAsJsonArray(chatWindowTitleNextGroupKey);
    Map<String, List<JsonObject>> whenMap = new HashMap<>();
    for (JsonElement element : titleMenus) {
      if (!(element instanceof JsonObject context)) {
        continue;
      }
      //带when条件的时候需要判断是否需要跳过
      if (context.has("when")) {
        String when = context.get("when").getAsString();
        List<JsonObject> whenChild = whenMap.computeIfAbsent(when, k -> new ArrayList<>());
        whenChild.add(context);
      } else {
        //没有when条件的，默认都展示
        List<JsonObject> whenChild = whenMap.computeIfAbsent("default", k -> new ArrayList<>());
        whenChild.add(context);
      }
    }
    if (whenMap.isEmpty()) {
      LoggerUtil.INSTANCE.logWarn(log, "registerWindowViewsCommand 没有注册view/title的命令");
      return;
    }
    Map<String, List<BaseCommandAction>> projectWhenMap = titleActions.computeIfAbsent(project.getBasePath(), k -> new HashMap<>());
    for (Map.Entry<String, List<JsonObject>> entry : whenMap.entrySet()) {
      if (CollectionUtils.isEmpty(entry.getValue())) {
        continue;
      }
      List<BaseCommandAction> actions = projectWhenMap.computeIfAbsent(entry.getKey(), k -> new ArrayList<>());
      for (JsonElement element : commands) {
        if (!(element instanceof JsonObject command)) {
          continue;
        }
        List<JsonObject> titleList = entry.getValue();
        JsonObject order = null;

        for (JsonObject title : titleList) {
          String commandName = title.get("command").getAsString();
          if (StringUtils.equals(commandName, command.get("command").getAsString())) {
            order= title;
            break;
          }
        }
        if (null == order) {
          continue;
        }
        //这些操作放在EditorPopupMenu下
        Icon icon = null;
        if (command.has("icon")) {
          String iconPath = command.get("icon").getAsString();
          icon = IconsUtil.getRcsIcon(iconPath);
        }
        //预设的都走localChannel/default，所以channel传空
        BaseCommandAction action = DefaultCommandUtil.createAction(project, command, null, icon);
        //建立与webview的映射
        String provider = "", group = "";
        if (order.has("provider")) {
          provider = order.get("provider").getAsString();
        }
        if (order.has("group")) {
          group = order.get("group").getAsString();
        }
        action.setExt("provider", provider);
        action.setExt("group", group);
        actions.add(action);
      }
    }
    //TODO 临时
    refreshChatTitleView(project, whenMap.keySet(), true);
  }
  public static void refreshChatTitleView(Project project, Set<String> newWhen, boolean isAll) {
    //对于view/title来说，没有注册动作，所以只关心new的即可
    Map<String, List<BaseCommandAction>> projectAction = titleActions.computeIfAbsent(project.getBasePath(), k -> new ConcurrentHashMap<>());
    if (projectAction.isEmpty()) {
      LoggerUtil.INSTANCE.logWarn(log, "refreshChatTitleView 没有注册view/title的命令");
      return;
    }
    List<BaseCommandAction> allActions = projectAction.getOrDefault("default", Lists.newArrayList());
    for (String when : newWhen) {
        if ("default".equalsIgnoreCase(when)) {
            continue;
        }
      if (!isAll && !projectAction.containsKey(when) ) {
        continue;
      }
      List<BaseCommandAction> whenList = projectAction.getOrDefault(when, Lists.newArrayList());
      allActions.addAll(whenList);
    }
    currentShowingActions.put(project.getBasePath(), allActions);
  }

  public static Pair<List<BaseCommandAction>, List<BaseCommandAction>> getTitleActions(Project project, String webviewProviderId) {
    List<BaseCommandAction> currentActionIds = currentShowingActions.get(project.getBasePath());
    if (CollectionUtils.isEmpty(currentActionIds)) {
      return Pair.of(Lists.newArrayList(), Lists.newArrayList());
    }
    List<BaseCommandAction> providerActions = currentActionIds.stream().filter(action -> {
      return StringUtils.equals(webviewProviderId, (String) action.getExt("provider"));
    }).collect(Collectors.toList());

    if (CollectionUtils.isEmpty(providerActions)) {
      return Pair.of(Lists.newArrayList(), Lists.newArrayList());
    }

    List<BaseCommandAction> navigationActions = providerActions.stream().filter(action -> {
      String group = (String) action.getExt("group");
      return StringUtils.startsWith(group, "navigation");
    }).toList();
    List<BaseCommandAction> overflowActions = providerActions.stream().filter(action -> {
      String group = (String) action.getExt("group");
      return StringUtils.startsWith(group, "overflow");
    }).toList();
    return Pair.of(navigationActions, overflowActions);
  }
  public static void clearProjectActions(Project project) {
    titleActions.remove(project.getBasePath());
    currentShowingActions.remove(project.getBasePath());
  }
}

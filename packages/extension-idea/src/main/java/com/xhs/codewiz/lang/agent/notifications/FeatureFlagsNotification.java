package com.xhs.codewiz.lang.agent.notifications;

import com.google.gson.annotations.SerializedName;
import org.jetbrains.annotations.NotNull;

public class FeatureFlagsNotification {
    public static final @NotNull String NAME = "featureFlagsNotification";
    @SerializedName("ssc")
    boolean ssc;
    @SerializedName("rt")
    boolean rt;
    @SerializedName("chat")
    boolean chat;

    public FeatureFlagsNotification() {
    }

    public boolean isSsc() {
        return this.ssc;
    }

    public boolean isRt() {
        return this.rt;
    }

    public boolean isChat() {
        return this.chat;
    }

    public void setSsc(boolean ssc) {
        this.ssc = ssc;
    }

    public void setRt(boolean rt) {
        this.rt = rt;
    }

    public void setChat(boolean chat) {
        this.chat = chat;
    }
}


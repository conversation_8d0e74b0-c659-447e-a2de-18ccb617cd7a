package com.xhs.codewiz.utils;

import java.nio.file.Files;
import java.nio.file.Paths;

import javax.swing.Icon;

import org.apache.commons.lang3.StringUtils;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.util.IconLoader;
import com.intellij.ui.AnimatedIcon;
import com.xhs.codewiz.factory.webview.util.HighBuildVersionReflectUtil;

public final class IconsUtil {
    public static final Icon CODEWIZ = IconLoader.getIcon("icons/codewiz.svg", IconsUtil.class);
    public static final Icon StatusBarIcon;
    public static final Icon StatusBarIconDisabled;
    public static final Icon StatusBarIconError;
    public static final Icon StatusBarIconWarning;
    public static final Icon StatusBarCompletionInProgress;
    public static final Icon ToolWindowIcon;

    private static final Logger log = Logger.getInstance(IconsUtil.class);

    private IconsUtil() {
    }

    static {
        StatusBarIcon = CODEWIZ;
        StatusBarIconDisabled = IconLoader.getIcon("icons/codewiz_disabled.svg", IconsUtil.class);
        StatusBarIconError = IconLoader.getIcon("icons/codewiz_error.svg", IconsUtil.class);
        StatusBarIconWarning = IconLoader.getIcon("icons/codewiz_warning.svg", IconsUtil.class);
        StatusBarCompletionInProgress = new AnimatedIcon.Default();
        ToolWindowIcon = IconLoader.getIcon("icons/copilot_toolwindow.svg", IconsUtil.class);
    }

    public static Icon getIcon(String content) {
        return HighBuildVersionReflectUtil.loadWebTypesSvgIcon(content);
    }

    public static Icon getRcsIconForToolWindows(String iconPath) {
        Icon icon = null;
        if (StringUtils.isNotEmpty(iconPath)) {
            String newIconPath = iconPath;
            boolean isDark = HighBuildVersionReflectUtil.isUnderDarcula();
            if (isDark) {
                String[] split = iconPath.split(".svg");
                newIconPath = split[0] + "_dark.svg";
            }
            try {
                String content = new String(Files.readAllBytes(Paths.get(newIconPath)));
                icon = IconsUtil.getIcon(content);
            } catch (Exception e) {
                LoggerUtil.INSTANCE.logWarn(log, "读取图标失败", e);
                try {
                    String content = new String(Files.readAllBytes(Paths.get(iconPath)));
                    icon = IconsUtil.getIcon(content);
                } catch (Exception e1) {
                    LoggerUtil.INSTANCE.logWarn(log, "重试后，读取图标失败", e1);
                }
            }
        }
        return icon;
    }

    public static Icon getRcsIcon(String iconPath) {
        Icon icon = null;
        if (StringUtils.isNotEmpty(iconPath)) {
            try {
                boolean isDark = HighBuildVersionReflectUtil.isUnderDarcula();
                if (isDark && StringUtils.contains(iconPath, "@vscode")) {
                    String[] split = iconPath.split(".svg");
                    iconPath = split[0] + "_dark.svg";
                }
            } catch (Exception e) {
                LoggerUtil.INSTANCE.logWarn(log, "getRcsIcon 读取图标失败", e);
            }

            try {
                String content = new String(Files.readAllBytes(Paths.get(iconPath)));
                icon = IconsUtil.getIcon(content);
            } catch (Exception e) {
                LoggerUtil.INSTANCE.logWarn(log, "读取图标失败", e);
            }
        }
        return icon;
    }
}

package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import org.jetbrains.annotations.NotNull;

public final class NotifyShownCommand implements JsonRpcCommand<String> {
    @SerializedName("sessionId")
    private final @NotNull String sessionId;
    @SerializedName("uuid")
    private final @NotNull String uuid;
    private final int start;
    private final int end;
    private final int shownLineCount;
    private final long timestamp;
    private final String shownContent;
    private final String filePath;

    public @NotNull String getCommandName() {
        return "notifyShown";
    }

    public @NotNull Class<String> getResponseType() {
        return String.class;
    }

    public @NotNull String getUuid() {
        return this.uuid;
    }

    public @NotNull String getSessionId() {
        return this.sessionId;
    }
    public int getStart() {
        return this.start;
    }
    public int getEnd() {
        return this.end;
    }
    public int getShownLineCount() {
        return this.shownLineCount;
    }
    public long getTimestamp() {
        return this.timestamp;
    }
    public String getShownContent() {
        return this.shownContent;
    }
    public String getFilePath() {
        return this.filePath;
    }

    public NotifyShownCommand(@NotNull String sessionId, @NotNull String uuid, int start, int end, int shownLineCount, long timestamp, String shownContent, String filePath) {
        this.sessionId = sessionId;
        this.uuid = uuid;
        this.start = start;
        this.end = end;
        this.shownLineCount = shownLineCount;
        this.timestamp = timestamp;
        this.shownContent = shownContent;
        this.filePath = filePath;
    }
}

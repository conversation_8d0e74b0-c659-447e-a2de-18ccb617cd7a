package com.xhs.codewiz.lang.agent.notifications;


import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.NotNull;

public interface LogMessage {
    String getMessage();

    boolean isDebug();

    boolean isInfo();

    boolean isWarn();

    boolean isError();

    default void outputMessage(@NotNull Logger logger) {
        if (this.isDebug()) {
            if (ApplicationManager.getApplication().isUnitTestMode()) {
                logger.debug(this.getMessage());
            } else if (logger.isTraceEnabled()) {
                logger.trace(this.getMessage());
            }
        } else if (this.isInfo()) {
            logger.info(this.getMessage());
        } else if (this.isWarn()) {
            logger.warn(this.getMessage());
        } else if (this.isError()) {
            logger.warn(this.getMessage());
        }

    }
}


package com.xhs.codewiz.factory.editor;

import javax.swing.Icon;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.intellij.ide.IconProvider;
import com.intellij.openapi.util.IconLoader;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;

/**
 * 自定义IconProvider，用于TopicEditor的图标显示
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class TopicIconProvider extends IconProvider {

    // 使用项目中已有的图标，或者创建新的图标文件
    private static final Icon TOPIC_ICON = IconLoader.getIcon("icons/codewiz.svg", TopicIconProvider.class);

    @Override
    public @Nullable Icon getIcon(@NotNull PsiElement element, int flags) {
        if (element instanceof PsiFile) {
            PsiFile psiFile = (PsiFile) element;
            if (psiFile.getVirtualFile() instanceof TopicEditorVirtualFile) {
                return TOPIC_ICON;
            }
        }
        return null;
    }
}

package com.xhs.codewiz.type.content;

import java.util.*;

/**
 * Different kinds of actions
 */
public enum ActionKind {
    /** 快速修复操作，通常用于自动修复代码问题 */
    QuickFix("QuickFix"),
    /** 空操作，通常用于占位或未定义的操作 */
    Empty("Empty"),
    /** 组织导入操作，通常用于整理代码中的导入语句 */
    SourceOrganizeImports("SourceOrganizeImports"),
    /** 重写操作，通常用于对现有代码进行重大修改 */
    RefactorRewrite("RefactorRewrite"),
    /** 重构操作，通常用于改善代码结构而不改变其外部行为 */
    Refactor("Refactor"),
    /** 内联操作，通常用于将单独的函数或模块内联到调用处 */
    RefactorInline("RefactorInline"),
    /** 提取操作，通常用于将代码提取到单独的函数或模块 */
    RefactorExtract("RefactorExtract"),
    /** 源操作，通常用于表示代码的来源 */
    Source("Source"),
    /** 修复所有操作，通常用于一次性修复所有问题 */
    SourceFixAll("SourceFixAll");

    private final String value;

    ActionKind(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}

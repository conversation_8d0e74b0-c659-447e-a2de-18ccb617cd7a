package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.LensItem;
/**
 * 获取 Lens 的详细信息
 */
public class ReadLensDetail {
    private String schemaProtocol = "workspace.read.lensdetail";
    private ReadLensDetailParams params;

    public ReadLensDetailParams getParams() {
        return params;
    }
    public void setParams(ReadLensDetailParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadLensDetailParams {
        private LensItem item;
        /** Lens Provider 的 ID */
        private String provider;
    
        public LensItem getItem() {
            return item;
        }
        public void setItem(LensItem item) {
            this.item = item;
        }
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
    }
}

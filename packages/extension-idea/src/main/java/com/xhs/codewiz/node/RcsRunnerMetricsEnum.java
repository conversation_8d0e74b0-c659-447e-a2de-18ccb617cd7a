package com.xhs.codewiz.node;

/**
 * Author: liukunpeng Date: 2025-08-20 Description:
 */
public enum RcsRunnerMetricsEnum {
    //defineTracePoint((machineId: string)=>[machineId]),
    RCS_SERVER_CODER_CREATE_ENV_START(1001, "rcs.server.coder.create.env.start", "开始启动前的环境创建"),
    //defineTracePoint((machineId: string, error: Error)=>[machineId, error.message]),
    RCS_SERVER_CODER_CREATE_ENV_ERROR(1002, "rcs.server.coder.create.env.error", "环境创建错误"),
    //'rcs.server.coder.create.env.succeed': defineTracePoint((machineId: string)=>[machineId]),
    RCS_SERVER_CODER_CREATE_ENV_SUCCEED(1003, "rcs.server.coder.create.env.succeed", "环境创建成功"),
    //'rcs.server.coder.create.start': defineTracePoint((machineId: string)=>[machineId]),
    RCS_SERVER_CODER_CREATE_START(1004, "rcs.server.coder.create.start", "开始启动 rcs"),
    //'rcs.server.coder.create.error': defineTracePoint((machineId: string, error: Error)=>[machineId, error.message]),
    RCS_SERVER_CODER_CREATE_ERROR(1005, "rcs.server.coder.create.error", "启动错误"),
    //'rcs.server.coder.create.succeed': defineTracePoint((machineId: string)=>[machineId]),
    RCS_SERVER_CODER_CREATE_SUCCEED(1006, "rcs.server.coder.create.succeed", "启动成功");

    public final int code;
    public final String name;
    public final String desc;

    RcsRunnerMetricsEnum(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

package com.xhs.codewiz.listener;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.LogicalPosition;
import com.intellij.openapi.editor.event.SelectionEvent;
import com.intellij.openapi.editor.event.SelectionListener;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.util.Alarm;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.editor.request.ActiveFileInfo;
import com.xhs.codewiz.scheme.content.UpdateRanges;
import com.xhs.codewiz.scheme.content.UpdateRanges.UpdateRangesParams;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.Range;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-06-18 Description:
 */
public class CurrentFileSelectionListener implements SelectionListener, Disposable {
  private final Alarm myAlarm = new Alarm(Alarm.ThreadToUse.SWING_THREAD);
  private static final int DELAY = 300;
  //public static Key<Boolean> KEY_TEMP_FILE_SHOULD_NOT_QUOTE = Key.create("KEY_TEMP_FILE_SHOULD_NOT_QUOTE");

  @Override
  public void selectionChanged(@NotNull SelectionEvent e) {
    this.myAlarm.cancelAllRequests();
    this.myAlarm.addRequest(() -> ApplicationManager.getApplication().invokeLater(() -> this.processSelection(e)), DELAY);
  }

  private void processSelection(@NotNull SelectionEvent e) {
    Editor editor = e.getEditor();
    Project project = editor.getProject();
    if (editor.isDisposed() || project == null) {
      return;
    }
    VirtualFile virtualFile = FileDocumentManager.getInstance().getFile(editor.getDocument());
    //如果是chat生成的临时文件，不处理
    /*Boolean isTempFile = virtualFile.getUserData(TEMP_FILE_FLAG);
    if (null != isTempFile && isTempFile) {
      return;
    }*/
    //!FileUtil.isValidProjectFile(project, virtualFile)
    if (virtualFile.getFileType().isBinary()) {
      return;
    }
    ApplicationManager.getApplication().runReadAction(() -> {
      /*if (null != virtualFile && null != virtualFile.getUserData(KEY_TEMP_FILE_SHOULD_NOT_QUOTE) && Boolean.TRUE.equals(virtualFile.getUserData(KEY_TEMP_FILE_SHOULD_NOT_QUOTE))) {
        return;
      }*/
      ActiveFileInfo fileInfo = ActiveFileInfo.getFromEditor(editor);
      UpdateRanges ranges = new UpdateRanges();
      UpdateRangesParams params = new UpdateRangesParams();
      params.setUri(virtualFile.getUrl());
      List<Range> rangeList = new ArrayList<>();
      Range range = new Range();
      LogicalPosition caretPosition = editor.getCaretModel().getLogicalPosition();
      Position position = new Position();
      position.setLine(caretPosition.line);
      position.setCharacter(caretPosition.column);
      if (null != fileInfo.getStart()) {
        range.setStart(fileInfo.getStart());
      } else {
        range.setStart(position);
      }
      if (null != fileInfo.getEnd()) {
        range.setEnd(fileInfo.getEnd());
      } else {
        range.setEnd(position);
      }
      rangeList.add(range);
      params.setRanges(rangeList);
      ranges.setParams(params);
      RcsWebSocketManager.INSTANCE.sendNotification(ranges, project);
    });
  }

  @Override
  public void dispose() {
    this.myAlarm.cancelAllRequests();
    this.myAlarm.dispose();
  }
}

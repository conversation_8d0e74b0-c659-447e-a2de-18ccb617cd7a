package com.xhs.codewiz.editor.request;

import com.xhs.codewiz.completion.enums.CodewizCompletionType;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public interface CodewizEditorInlay {
    @NotNull CodewizCompletionType getType();

    @NotNull List<String> getLines();

    int getEditorOffset();

    default boolean isEmptyCompletion() {
        List<String> completion = this.getLines();
        return completion.isEmpty() || completion.size() == 1 && completion.get(0).isEmpty();
    }
}


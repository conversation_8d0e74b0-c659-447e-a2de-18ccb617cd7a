package com.xhs.codewiz.setting;

import com.intellij.lang.Language;
import com.intellij.util.xmlb.annotations.OptionTag;
import com.intellij.util.xmlb.annotations.XCollection;
import com.intellij.util.xmlb.annotations.XCollection.Style;
import java.awt.Color;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

public class CodeWizApplicationState {
    @OptionTag("enableCompletions")
    public boolean enableCompletions = true;
    @XCollection(
            style = Style.v2,
            propertyElementName = "disabledLanguages",
            elementTypes = {String.class}
    )
    private final Set<String> disabledLanguageIds = new HashSet();
    @XCollection(
            style = Style.v2,
            propertyElementName = "languageAllowList",
            elementTypes = {String.class, Boolean.class}
    )
    private final Map<String, Boolean> languageAllowList = new HashMap();
    @OptionTag("languageAllowListReadOnly")
    public boolean isLanguageAllowListReadOnly = false;
    @OptionTag("showIdeCompletions")
    private boolean showIdeCompletions = false;
    @OptionTag("disableTypingAsSuggestedCache")
    public transient boolean disableTypingAsSuggestedCache = false;
    @OptionTag("kerberosServicePrincipal")
    public String kerberosServicePrincipal = "";
    public int debounce = 75;


    /**
     * 以下为setting配置可配置的项
     */
    @OptionTag("autoUpdate")
    public AutoUpdate autoUpdate = new AutoUpdate();
    @OptionTag(
        value = "inlayTextColor",
        converter = ColorConverter.class
    )
    public Color inlayTextColor = null;

    public CodeWizApplicationState() {
    }

    public boolean isShowIdeCompletions() {
        return this.showIdeCompletions;
    }

    public void setShowIdeCompletions(boolean showIdeCompletions) {
        if (this.showIdeCompletions != showIdeCompletions) {
            this.showIdeCompletions = showIdeCompletions;
        }

    }

    public boolean isEnabled(@NotNull Language language) {
        return true;
    }

    public static class AutoUpdate {
        public boolean checkForUpdate = true; //默认开启自动更新
        public boolean userApply = false;
    }
}


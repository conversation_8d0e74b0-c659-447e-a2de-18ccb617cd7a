package com.xhs.codewiz.lang;

import com.xhs.codewiz.utils.reference.FeatureService;

/**
 * Author: liukunpeng Date: 2025-03-28 Description:
 */
public enum Features {
  COMPLETION_AUTO_DELAY("completion.auto.delay", 250L),
  COMPLETION_AUTO_COMMENT_DELAY("completion.auto.comment.delay", 500L),
  COMPLETION_AUTO_MIN_DELAY("completion.auto.min.delay", 75L),
  COMPLETION_AUTO_MAX_DELAY("completion.auto.max.delay", 500L),
  QUERY_REF_FROM_IDE_INHERIT_MAX_COUNT("query.ref.from.ide.inherit.max.count", 5),
  QUERY_REF_FROM_IDE_INHERIT_MAX_DEPTH("query.ref.from.ide.inherit.max.depth", 2);
  String key;
  Object defaultValue;

  private Features(String key, Object defaultValue) {
    this.key = key;
    this.defaultValue = defaultValue;
  }

  public Integer intValue() {
    try {
      return FeatureService.getInstance().getIntegerFeature(this.key, this.defaultIntValue());
    }
    catch (Exception e) {
      return this.defaultIntValue();
    }
  }

  public Long longValue() {
    try {
      return FeatureService.getInstance().getLongFeature(this.key, this.defaultLongValue());
    }
    catch (Exception e) {
      return this.defaultLongValue();
    }
  }

  public String stringValue() {
    try {
      return FeatureService.getInstance().getStringFeature(this.key, this.defaultStringValue());
    }
    catch (Exception e) {
      return this.defaultStringValue();
    }
  }

  public Boolean booleanValue() {
    try {
      return FeatureService.getInstance().getBooleanFeature(this.key, this.defaultBooleanValue());
    }
    catch (Exception e) {
      return this.defaultBooleanValue();
    }
  }

  public Double doubleValue() {
    try {
      return FeatureService.getInstance().getDoubleFeature(this.key, this.defaultDoubleValue());
    }
    catch (Exception e) {
      return this.defaultDoubleValue();
    }
  }

  public Integer defaultIntValue() {
    return (Integer)this.defaultValue;
  }

  public Long defaultLongValue() {
    return (Long)this.defaultValue;
  }

  public Boolean defaultBooleanValue() {
    return (Boolean)this.defaultValue;
  }

  public String defaultStringValue() {
    return (String)this.defaultValue;
  }

  public Double defaultDoubleValue() {
    return (Double)this.defaultValue;
  }

  public String getKey() {
    return this.key;
  }
  public Object getDefaultValue() {
    return this.defaultValue;
  }
}

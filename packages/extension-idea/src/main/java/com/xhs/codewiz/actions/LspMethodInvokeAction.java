package com.xhs.codewiz.actions;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.utils.IdeNotificationUtil;

/**
 * <AUTHOR>
 * @date 2025/6/6 15:53
 */
public class LspMethodInvokeAction extends AnAction {
    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        Project project = anActionEvent.getProject();

        // 构建包含交互按钮的HTML
        // String interactiveHtml = buildInteractiveHtml();
        //
        // String webviewUpdateStr = String.format("""
        //         {
        //                 "schemaProtocol": "platform.update.webview",
        //                 "params": {
        //                     "provider": "rcsdebugger.webviewProvider",
        //                     "changes": [
        //                         {
        //                             "key": "html",
        //                             "value": "%s"
        //                         }
        //                     ]
        //                 }
        //             }
        //         """, escapeJson(interactiveHtml));
        //
        // WebviewService.updateWebview(webviewUpdateStr, "rcsdebugger.webviewProvider");
        // System.out.println("Interactive HTML loaded with postMessage buttons");

        IdeNotificationUtil.showMessage("info", "Interactive HTML loaded with postMessage buttons", project);
    }

    /**
     * 构建包含交互按钮的HTML页面
     */
    private String buildInteractiveHtml() {
        return """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>CodeWiz Interactive Test</title>
                    <style>
                        body {
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            margin: 20px;
                            background-color: var(--vscode-editor-background, #1e1e1e);
                            color: var(--vscode-editor-foreground, #d4d4d4);
                            line-height: 1.6;
                        }
                        .container {
                            max-width: 600px;
                            margin: 0 auto;
                        }
                        h1 {
                            color: var(--vscode-titleBar-activeForeground, #cccccc);
                            border-bottom: 1px solid var(--vscode-panel-border, #3c3c3c);
                            padding-bottom: 10px;
                        }
                        .button-group {
                            display: flex;
                            flex-wrap: wrap;
                            gap: 10px;
                            margin: 20px 0;
                        }
                        .btn {
                            padding: 8px 16px;
                            border: 1px solid var(--vscode-button-border, #0e639c);
                            background-color: var(--vscode-button-background, #0e639c);
                            color: var(--vscode-button-foreground, #ffffff);
                            cursor: pointer;
                            border-radius: 2px;
                            font-size: 13px;
                            transition: background-color 0.2s;
                        }
                        .btn:hover {
                            background-color: var(--vscode-button-hoverBackground, #1177bb);
                        }
                        .btn-secondary {
                            background-color: var(--vscode-button-secondaryBackground, #3c3c3c);
                            border-color: var(--vscode-button-secondaryBorder, #3c3c3c);
                            color: var(--vscode-button-secondaryForeground, #cccccc);
                        }
                        .btn-secondary:hover {
                            background-color: var(--vscode-button-secondaryHoverBackground, #555555);
                        }
                        .message-log {
                            background-color: var(--vscode-terminal-background, #0c0c0c);
                            border: 1px solid var(--vscode-panel-border, #3c3c3c);
                            padding: 15px;
                            margin-top: 20px;
                            border-radius: 3px;
                            font-family: 'Consolas', 'Monaco', monospace;
                            max-height: 300px;
                            overflow-y: auto;
                        }
                        .message-item {
                            margin-bottom: 8px;
                            padding: 5px;
                            border-left: 3px solid var(--vscode-editorInfo-foreground, #3794ff);
                            background-color: var(--vscode-editor-selectionBackground, rgba(55, 148, 255, 0.1));
                        }
                        .timestamp {
                            color: var(--vscode-descriptionForeground, #cccccc99);
                            font-size: 11px;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🚀 CodeWiz Interactive Test Panel</h1>
                        <p>点击下面的按钮测试与 ListenerJsRouteHandle 的交互通信：</p>
                
                        <div class="button-group">
                            <button class="btn" onclick="sendMessage('test', 'basic-test')">
                                基础测试
                            </button>
                            <button class="btn" onclick="sendMessage('action', 'button-click', {buttonId: 'btn1', timestamp: Date.now()})">
                                发送动作
                            </button>
                            <button class="btn btn-secondary" onclick="sendMessage('query', 'get-status')">
                                查询状态
                            </button>
                            <button class="btn btn-secondary" onclick="sendMessage('command', 'execute-task', {task: 'refresh', params: {force: true}})">
                                执行命令
                            </button>
                            <button class="btn" onclick="sendComplexMessage()">
                                复杂消息
                            </button>
                            <button class="btn btn-secondary" onclick="clearMessages()">
                                清空日志
                            </button>
                        </div>
                
                        <div class="message-log" id="messageLog">
                            <div class="message-item">
                                <div class="timestamp">[Ready]</div>
                                <div>Ready to send messages...</div>
                            </div>
                        </div>
                    </div>
                
                                         <script>
                         // acquireVsCodeApi 将通过注入方式提供
                         let vscode = null;
                
                         // 记录消息的函数
                        function logMessage(message, type = 'sent') {
                            const messageLog = document.getElementById('messageLog');
                            const messageItem = document.createElement('div');
                            messageItem.className = 'message-item';
                
                            const timestamp = new Date().toLocaleTimeString();
                            messageItem.innerHTML = `
                                <div class="timestamp">[${timestamp}] ${type.toUpperCase()}</div>
                                <div>${JSON.stringify(message, null, 2)}</div>
                            `;
                
                            messageLog.appendChild(messageItem);
                            messageLog.scrollTop = messageLog.scrollHeight;
                        }
                
                                                 // 发送消息的通用函数
                         function sendMessage(type, action, data = null) {
                             if (!vscode) {
                                 console.error('❌ VSCode API not ready yet');
                                 return;
                             }
                
                             const message = {
                                 type: type,
                                 action: action,
                                 data: data,
                                 timestamp: new Date().toISOString(),
                                 source: 'interactive-panel'
                             };
                
                             logMessage(message, 'sent');
                             vscode.postMessage(message);
                         }
                
                        // 发送复杂消息
                        function sendComplexMessage() {
                            const complexData = {
                                user: {
                                    id: 'user123',
                                    name: 'Test User',
                                    preferences: {
                                        theme: 'dark',
                                        language: 'zh-CN'
                                    }
                                },
                                session: {
                                    id: 'session-' + Math.random().toString(36).substr(2, 9),
                                    startTime: new Date().toISOString(),
                                    features: ['lsp', 'chat', 'completion']
                                },
                                request: {
                                    method: 'complex-operation',
                                    params: {
                                        mode: 'advanced',
                                        options: {
                                            enableLogging: true,
                                            maxRetries: 3,
                                            timeout: 30000
                                        }
                                    }
                                }
                            };
                
                            sendMessage('complex', 'multi-part-request', complexData);
                        }
                
                        // 清空消息日志
                        function clearMessages() {
                            const messageLog = document.getElementById('messageLog');
                            messageLog.innerHTML = `
                                <div class="message-item">
                                    <div class="timestamp">[Cleared]</div>
                                    <div>Message log cleared.</div>
                                </div>
                            `;
                        }
                
                                                                         // 初始化API完成后的回调
                         function onApiReady() {
                             console.log('✅ VSCode API ready, initializing...');
                             vscode = window.acquireVsCodeApi();
                
                             // 发送初始化消息
                             sendMessage('init', 'panel-loaded', {
                                 userAgent: navigator.userAgent,
                                 timestamp: Date.now(),
                                 ready: true
                             });
                         }
                
                         // 监听API就绪事件
                         window.addEventListener('vsCodeApiReady', onApiReady);
                
                         // 页面加载完成后检查API是否已经可用
                         window.addEventListener('load', function() {
                             console.log('🚀 Page loaded, waiting for API injection...');
                             if (typeof window.acquireVsCodeApi === 'function') {
                                 console.log('⚡ API already available, initializing immediately');
                                 onApiReady();
                             } else {
                                 console.log('⏳ Waiting for vsCodeApiReady event...');
                             }
                         });
                
                        // 监听可能的返回消息（如果有的话）
                        window.addEventListener('message', function(event) {
                            logMessage(event.data, 'received');
                        });
                    </script>
                </body>
                </html>
                """;
    }

    /**
     * 转义JSON字符串中的特殊字符
     */
    private String escapeJson(String input) {
        return input.replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }
}

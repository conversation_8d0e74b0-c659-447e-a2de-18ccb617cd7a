package com.xhs.codewiz.utils;

import java.awt.Cursor;
import java.util.List;
import java.util.Objects;

import javax.swing.JComponent;
import javax.swing.SwingUtilities;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.cef.browser.CefBrowser;
import org.cef.browser.CefFrame;
import org.cef.browser.CefMessageRouter;
import org.cef.callback.CefContextMenuParams;
import org.cef.callback.CefMenuModel;
// import org.cef.callback.CefRunContextMenuCallback;
import org.cef.handler.CefContextMenuHandlerAdapter;
import org.cef.handler.CefDisplayHandlerAdapter;
import org.cef.handler.CefLifeSpanHandlerAdapter;
import org.cef.handler.CefRequestHandlerAdapter;
import org.cef.network.CefRequest;
import org.jetbrains.annotations.Nullable;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.ui.jcef.JBCefApp;
import com.intellij.ui.jcef.JBCefBrowser;
import com.xhs.codewiz.client.service.RcsGlobalService;
import com.xhs.codewiz.factory.webview.WebviewBuilder;
import com.xhs.codewiz.listener.ListenerJsRouteHandle;

/**
 * <AUTHOR>
 * @date 2025/8/4 15:56
 */
public class JBCefBrowserUtil {

    private static final Logger logger = Logger.getInstance(JBCefBrowserUtil.class);

    private static final List<WebviewBuilder> WEBVIEW_BUILDERS = Lists.newArrayList();

    public static JBCefBrowser buildBrowserPanel(Project project, WebviewBuilder webviewBuilder) {
        LoggerUtil.INSTANCE.logInfo(logger,
                "[Stage4-Browser] buildBrowserPanel started - project: " + project.getName() + ", providerId: " +
                        (webviewBuilder.getWebviewProvider() != null ? webviewBuilder.getWebviewProvider().getId() : "null"));
        if (!JBCefApp.isSupported()) {
            LoggerUtil.INSTANCE.logWarn(logger, "[Stage4-Browser] JBCef not supported in current environment");
            IdeNotificationUtil.showMessage("warn", "当前环境不支持CEF，请联系CodeWiz同学查看", project);
            return null;
        }
        LoggerUtil.INSTANCE.logInfo(logger, "[Stage4-Browser] JBCef support confirmed, creating browser instance");
        JBCefBrowser htmlPanel;
        webviewBuilder.setProject(project);
        WEBVIEW_BUILDERS.add(webviewBuilder);
        try {
            // 移除离屏渲染,这个功能会有兼容性问题,比如输入汉字会一直输入中效果,部分机器无法输入中文问号
            htmlPanel = JBCefBrowser.createBuilder().setOffScreenRendering(true).build();
            LoggerUtil.INSTANCE.logInfo(logger, "[Stage4-Browser] JBCefBrowser created successfully using builder pattern");
        } catch (Exception e) {
            logger.error("JBCefBrowser not support builder model. err = ", e);
            LoggerUtil.INSTANCE.logWarn(logger, "[Stage4-Browser] Fallback to default JBCefBrowser constructor");
            htmlPanel = new JBCefBrowser();
        }
        // 设置指针样式映射，可以正常由swing显示UI光标样式
        JComponent uiComp = htmlPanel.getComponent();
        htmlPanel.getJBCefClient().addDisplayHandler(new CefDisplayHandlerAdapter() {
            @Override
            public boolean onCursorChange(CefBrowser browser, int cursorType) {
                if (cursorType < Cursor.DEFAULT_CURSOR || cursorType > Cursor.MOVE_CURSOR) {
                    SwingUtilities
                            .invokeLater(() -> uiComp.setCursor(Cursor.getPredefinedCursor(Cursor.DEFAULT_CURSOR)));
                } else {
                    SwingUtilities.invokeLater(() -> uiComp.setCursor(Cursor.getPredefinedCursor(cursorType)));
                }
                return true;
            }
        }, htmlPanel.getCefBrowser());
        htmlPanel.getJBCefClient().addLifeSpanHandler(new CefLifeSpanHandlerAdapter() {
            public boolean onBeforePopup(CefBrowser browser, CefFrame frame, String target_url,
                                         String target_frame_name) {
                // 这里 target_url就是要打开的新链接，在这里创建新的JBCefBrowser窗口
                // JDialog popupFrame = new JDialog((Frame) null, "新窗口", false);
                // popupFrame.setType(Window.Type.POPUP); // 设置为弹出类型
                // popupFrame.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
                // JBCefBrowser popupBrowser = new JBCefBrowser(target_url);
                // popupFrame.add(popupBrowser.getComponent());
                // popupFrame.setSize(800, 600);
                // popupFrame.setVisible(true);
                // return true阻止Cef自动弹窗；false允许。返回true，表示你自己处理了弹窗
                RcsGlobalService.openExtLink(target_url);
                return true;
            }
        }, htmlPanel.getCefBrowser());
        htmlPanel.getJBCefClient().addRequestHandler(new CefRequestHandlerAdapter() {
            @Override
            public boolean onBeforeBrowse(CefBrowser cefBrowser,
                                          CefFrame cefFrame,
                                          CefRequest cefRequest,
                                          boolean userGesture,
                                          boolean isRedirect) {
                String url = cefRequest.getURL();
                if (StringUtils.containsAny(url, "localhost", "127.0.0.1")) {
                    // 返回false则放行
                    return false;
                }
                RcsGlobalService.openExtLink(url);
                return true;
            }
        }, htmlPanel.getCefBrowser());

        // 绑定 browser ui 到 toolwindow 的 component
        // this.setCodeWizBrowser(htmlPanel, project);

        // 清理右键菜单栏
        clearMenuBar(htmlPanel);

        // cef 设置
        CefMessageRouter.CefMessageRouterConfig config = new CefMessageRouter.CefMessageRouterConfig("codeWizQuery",
                "codeWizCancel");
        CefMessageRouter cefMessageRouter = CefMessageRouter.create(config);
        cefMessageRouter.addHandler(new ListenerJsRouteHandle(project, webviewBuilder), true);

        // 设置消息路由
        htmlPanel.getJBCefClient().getCefClient().addMessageRouter(cefMessageRouter);

        webviewBuilder.setJbCefBrowser(htmlPanel);
        return htmlPanel;
    }

    public static JBCefBrowser getJcefBrowserByChannel(String channel) {
        WebviewBuilder webviewBuilder = getWebviewBuilderByChannel(channel);
        if (webviewBuilder == null) {
            return null;
        }

        return webviewBuilder.getJbCefBrowser();
    }

    public static @Nullable WebviewBuilder getWebviewBuilderByChannel(String channel) {
        Project project = ApplicationUtil.findCurrentProject();
        if (project == null) {
            return null;
        }

        String remoteChannel = getRemoteChannel(channel);
        String providerId = getProviderId(channel);

        WebviewBuilder webviewBuilder = WEBVIEW_BUILDERS.stream()
                .filter(builder -> builder.getRemoteChannel() != null &&
                        builder.getRemoteChannel().equals(remoteChannel) &&
                        Objects.equals(builder.getWebviewProvider().getId(), providerId))
                .findFirst()
                .orElse(null);

        if (webviewBuilder == null) {
            LoggerUtil.INSTANCE.logWarn(logger, "getJcefBrowserByChannel: webviewBuilder is null");
            return null;
        }
        return webviewBuilder;
    }

    public static @Nullable WebviewBuilder getWebviewBuilderByChannel(String channel, String providerId) {
        Project project = ApplicationUtil.findCurrentProject();
        if (project == null) {
            return null;
        }

        WebviewBuilder webviewBuilder = WEBVIEW_BUILDERS.stream()
                .filter(builder -> builder.getRemoteChannel() != null &&
                        builder.getRemoteChannel().equals(channel) &&
                        Objects.equals(builder.getWebviewProvider().getId(), providerId))
                .findFirst()
                .orElse(null);

        if (webviewBuilder == null) {
            LoggerUtil.INSTANCE.logWarn(logger, "getJcefBrowserByChannel: webviewBuilder is null");
            return null;
        }
        return webviewBuilder;
    }

    private static String getRemoteChannel(String channel) {
        String[] channelSplit = channel.split("/");
        return channelSplit[1];
    }

    private static String getProviderId(String channel) {
        String[] channelSplit = channel.split("/");
        return channelSplit[2];
    }

    public static void clearBuilder(Project project) {
        WEBVIEW_BUILDERS.removeIf(builder -> builder.getProject().getName().equals(project.getName()));
    }

    private static void clearMenuBar(JBCefBrowser jbCefBrowser) {
        jbCefBrowser.getJBCefClient().addContextMenuHandler(new CefContextMenuHandlerAdapter() {
            @Override
            public void onBeforeContextMenu(CefBrowser browser, CefFrame frame,
                                            CefContextMenuParams params, CefMenuModel model) {
                model.clear(); // 清空所有右键菜单项
            }

//            @Override
//            public boolean runContextMenu(CefBrowser browser, CefFrame frame,
//                                          CefContextMenuParams params, CefMenuModel model,
//                                          CefRunContextMenuCallback callback) {
//                // return super.runContextMenu(browser, frame, params, model, callback);
//                return true; // 禁用右键菜单
//            }
        }, jbCefBrowser.getCefBrowser());
    }
}

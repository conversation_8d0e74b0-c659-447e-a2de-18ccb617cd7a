package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 读取文件
 */
public class ReadFile {
    private String schemaProtocol = "file.read.file";
    private ReadFileParams params;

    public ReadFileParams getParams() {
        return params;
    }
    public void setParams(ReadFileParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadFileParams {
        private String uri;
    
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

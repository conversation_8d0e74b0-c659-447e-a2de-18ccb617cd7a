package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 创建 Uri Handler，处理自定义 URI。\n可以让外部网站、终端、插件甚至命令行通过特定链接唤起并让你的扩展响应这个链接。
 */
public class CreateUriHandler {
    private String schemaProtocol = "platform.create.urihandler";
    /** Uri Handler Id */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

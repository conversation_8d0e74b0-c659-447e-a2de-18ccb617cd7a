package com.xhs.codewiz.scheme.workspace;

import java.util.*;
/**
 * Lens Provider 内容发生变更
 */
public class UpdateLensProvider {
    private String schemaProtocol = "workspace.update.lensprovider";
    /** Lens Provider 的 ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

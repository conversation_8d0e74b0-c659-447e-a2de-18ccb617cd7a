package com.xhs.codewiz.lang.agent.notifications;

import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import java.util.List;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-07-02 Description:
 */
public class DoLogToLsNotification implements JsonRpcNotification {
  private List<ClientLogData> logs;

  public DoLogToLsNotification(List<ClientLogData> logs) {
    this.logs = logs;
  }

  public List<ClientLogData> getLogs() {
    return logs;
  }

  public void setLogs(List<ClientLogData> logs) {
    this.logs = logs;
  }
  @Override
  public @NotNull String getCommandName() {
    return "doLogs";
  }


  public static class ClientLogData {
    // info / warn / error / debug
    private String level;
    private String message;
    private String timestamp;
    // webview / plugin
    private String module;
    private String traceId;
    private String sessionId;
    private String requestId;
    // 是否上报xray:仅info需要设置，warn/error默认上报
    private boolean is_upload_xray;

    public String getLevel() {
      return level;
    }

    public void setLevel(String level) {
      this.level = level;
    }

    public String getMessage() {
      return message;
    }

    public void setMessage(String message) {
      this.message = message;
    }

    public String getTimestamp() {
      return timestamp;
    }

    public void setTimestamp(String timestamp) {
      this.timestamp = timestamp;
    }

    public String getModule() {
      return module;
    }

    public void setModule(String module) {
      this.module = module;
    }

    public String getTraceId() {
      return traceId;
    }

    public void setTraceId(String traceId) {
      this.traceId = traceId;
    }

    public String getSessionId() {
      return sessionId;
    }

    public void setSessionId(String sessionId) {
      this.sessionId = sessionId;
    }

    public String getRequestId() {
      return requestId;
    }

    public void setRequestId(String requestId) {
      this.requestId = requestId;
    }

    public boolean getIs_upload_xray() {
      return is_upload_xray;
    }

    public void setIs_upload_xray(boolean is_upload_xray) {
      this.is_upload_xray = is_upload_xray;
    }
  }
}

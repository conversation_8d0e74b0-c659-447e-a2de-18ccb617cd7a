package com.xhs.codewiz.utils.reference.inheritance;

import com.intellij.psi.PsiClass;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiJavaFile;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public class JavaInheritanceResolver
    implements InheritanceResolver {
  private static void collectSupers(PsiClass aClass, int depth, Filter filter, Set<PsiClass> supers) {
    if (filter.filterDepth(depth) || filter.filterCount(supers.size())) {
      return;
    }
    ArrayList<PsiClass> supersToCollect = new ArrayList<PsiClass>();
    for (PsiClass superClass : aClass.getSupers()) {
      if (filter.filterClass(superClass) || !supers.add(superClass)) continue;
      supersToCollect.add(superClass);
    }
    for (PsiClass superClass : supersToCollect) {
      JavaInheritanceResolver.collectSupers(superClass, depth + 1, filter, supers);
    }
  }

  @Override
  public PsiElement[] getSupers(PsiElement[] elements, Filter filter) {
    PsiClass[] classes = this.collectClasses(elements);
    HashSet<PsiClass> supers = new HashSet<PsiClass>();
    for (PsiClass aClass : classes) {
      JavaInheritanceResolver.collectSupers(aClass, 0, filter, supers);
    }
    return (PsiElement[])supers.toArray(PsiClass[]::new);
  }

  private PsiClass[] collectClasses(PsiElement[] elements) {
    HashSet<PsiClass> aClassSet = new HashSet<PsiClass>();
    for (PsiElement element : elements) {
      if (element instanceof PsiClass) {
        aClassSet.add((PsiClass)element);
        continue;
      }
      if (!(element instanceof PsiJavaFile)) continue;
      PsiJavaFile aFile = (PsiJavaFile)element;
      Collections.addAll(aClassSet, aFile.getClasses());
    }
    return aClassSet.toArray(new PsiClass[0]);
  }
}
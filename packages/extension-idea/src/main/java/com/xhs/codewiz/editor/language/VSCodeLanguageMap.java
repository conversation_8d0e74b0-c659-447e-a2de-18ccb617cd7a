package com.xhs.codewiz.editor.language;


import com.intellij.lang.Language;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class VSCodeLanguageMap {
    public static final String PLAIN_TEXT = "plaintext";
    private static final Map<String, String> INTELLIJ_VSCODE_MAP = Collections.unmodifiableMap(new ConcurrentHashMap<String, String>() {
        {
            this.put("CoffeeScript", "coffeescript");
            this.put("ObjectiveC", "c");
            this.put("c#", "csharp");
            this.put("C#", "csharp");
            this.put("C++", "cpp");
            this.put("CSS", "css");
            this.put("f#", "fsharp");
            this.put("go", "go");
            this.put("Groovy", "groovy");
            this.put("Handlebars", "handlebars");
            this.put("HTML", "html");
            this.put("XHTML", "html");
            this.put("JAVA", "java");
            this.put("JavaScript", "javascript");
            this.put("ECMAScript 6", "javascript");
            this.put("JavaScript JSX", "javascriptreact");
            this.put("TypeScript JSX", "typescriptreact");
            this.put("LESS", "less");
            this.put("Makefile", "makefile");
            this.put("Markdown", "markdown");
            this.put("PHP", "php");
            this.put("Jade", "pug");
            this.put("Python", "python");
            this.put("R", "r");
            this.put("ruby", "ruby");
            this.put("Rust", "rust");
            this.put("SCSS", "scss");
            this.put("Shell Script", "shellscript");
            this.put("SQL", "sql");
            this.put("SQL92", "sql");
            this.put("GenericSQL", "sql");
            this.put("Stylus", "stylus");
            this.put("TypeScript", "typescript");
            this.put("Vue", "vue");
            this.put("XML", "xml");
            this.put("yaml", "yaml");
        }
    });

    private VSCodeLanguageMap() {
    }

    public static @Nullable String getId(@NotNull Language language) {
        return (String)INTELLIJ_VSCODE_MAP.get(language.getID());
    }
}


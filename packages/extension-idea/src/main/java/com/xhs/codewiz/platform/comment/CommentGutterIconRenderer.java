package com.xhs.codewiz.platform.comment;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.editor.markup.GutterIconRenderer;
import com.xhs.codewiz.utils.IconsUtil;
import javax.swing.Icon;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 评论gutter图标渲染器
 *
 * <AUTHOR>
 */
public class CommentGutterIconRenderer extends GutterIconRenderer {
    private int line;
    private Runnable onClick;

    public CommentGutterIconRenderer(int line, Runnable onClick) {
        this.line = line;
        this.onClick = onClick;
    }

    @NotNull
    @Override
    public Icon getIcon() {
        return IconsUtil.CODEWIZ; // 使用codewiz图标
    }

    @Nullable
    @Override
    public String getTooltipText() {
        return "AI 代码评论 - 点击查看详情";
    }

    @Nullable
    @Override
    public AnAction getClickAction() {
        return new AnAction() {
            @Override
            public void actionPerformed(@NotNull AnActionEvent e) {
                if (onClick != null) {
                    onClick.run();
                }
            }
        };
    }

    @Override
    public boolean isNavigateAction() {
        return true;
    }

    // Getters and Setters
    public int getLine() {
        return line;
    }

    public void setLine(int line) {
        this.line = line;
    }

    public Runnable getOnClick() {
        return onClick;
    }

    public void setOnClick(Runnable onClick) {
        this.onClick = onClick;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CommentGutterIconRenderer)) {
            return false;
        }
        CommentGutterIconRenderer that = (CommentGutterIconRenderer) o;
        return line == that.line &&
                ((onClick == null && that.onClick == null) ||
                        (onClick != null && onClick.equals(that.onClick)));
    }

    @Override
    public int hashCode() {
        int result = line;
        result = 31 * result + (onClick != null ? onClick.hashCode() : 0);
        return result;
    }
} 
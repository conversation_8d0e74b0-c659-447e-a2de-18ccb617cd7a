package com.xhs.codewiz.actions;

import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.StatusBar;
import com.intellij.openapi.wm.WindowManager;
import com.intellij.psi.PsiFile;
import com.xhs.codewiz.actions.statusBar.CodeWizStatusBarWidget;
import com.xhs.codewiz.editor.CodeWizEditorManager;
import com.xhs.codewiz.editor.InlayDisposeContext;
import com.xhs.codewiz.setting.CodeWizApplicationSettings;
import com.xhs.codewiz.utils.BundleUtil;
import org.jetbrains.annotations.NotNull;

abstract class AbstractDisableCodewizCompletionsAction extends AnAction {
    private final boolean forCurrentFile;

    public void update(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            e.getPresentation().setEnabledAndVisible(false);
        } else {
            PsiFile file = e.getData(CommonDataKeys.PSI_FILE);
            boolean enabledGlobally = CodeWizApplicationSettings.settings().enableCompletions;
            boolean enabledForFile = file != null && CodeWizApplicationSettings.settings().isEnabled(file.getLanguage());
            e.getPresentation().setEnabledAndVisible(enabledGlobally && enabledForFile);
        }
    }

    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project != null && !project.isDisposed()) {
            PsiFile file =e.getData(CommonDataKeys.PSI_FILE);
            boolean global = !this.forCurrentFile || file == null;
            if (global) {
                CodeWizApplicationSettings.settings().enableCompletions = false;
            } else {
                //CodeWizApplicationSettings.settings().disableLanguage(file.getLanguage());
            }

            Editor editor = e.getData(CommonDataKeys.EDITOR);
            if (editor != null) {
                CodeWizEditorManager.getInstance().disposeInlays(editor, InlayDisposeContext.SettingsChange);
            }

            StatusBar bar = WindowManager.getInstance().getStatusBar(project);
            if (bar != null) {
                bar.setInfo(BundleUtil.get("action.codewiz.disableCodewiz.statusEnabled"));
            }

            CodeWizStatusBarWidget.update(project);
        }
    }

    public boolean isDumbAware() {
        return true;
    }

    public AbstractDisableCodewizCompletionsAction(boolean forCurrentFile) {
        this.forCurrentFile = forCurrentFile;
    }
    @Override
    public @NotNull ActionUpdateThread getActionUpdateThread() {
        return ActionUpdateThread.BGT;
    }
}


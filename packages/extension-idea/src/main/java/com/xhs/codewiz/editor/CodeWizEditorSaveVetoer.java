package com.xhs.codewiz.editor;

import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentSynchronizationVetoer;
import org.jetbrains.annotations.NotNull;

class CodeWizEditorSaveVetoer extends FileDocumentSynchronizationVetoer {
    CodeWizEditorSaveVetoer() {
    }

    public boolean maySaveDocument(@NotNull Document document, boolean isSaveExplicit) {
        return !CodeWizEditorManagerImpl.KEY_DOCUMENT_SAVE_VETO.isIn(document);
    }
}

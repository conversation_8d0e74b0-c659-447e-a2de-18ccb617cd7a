package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.DocumentFilter;
/**
 * 注册在内容上方显示的 Lens 提供者
 */
public class CreateLensProvider {
    private String schemaProtocol = "workspace.create.lensprovider";
    private CreateLensProviderParams params;

    public CreateLensProviderParams getParams() {
        return params;
    }
    public void setParams(CreateLensProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateLensProviderParams {
        private CreateLensProviderParamsProvider provider;
        private List<DocumentFilter> selector;
    
        public CreateLensProviderParamsProvider getProvider() {
            return provider;
        }
        public void setProvider(CreateLensProviderParamsProvider provider) {
            this.provider = provider;
        }
        public List<DocumentFilter> getSelector() {
            return selector;
        }
        public void setSelector(List<DocumentFilter> selector) {
            this.selector = selector;
        }
    }

        public static class CreateLensProviderParamsProvider {
        /** Lens 提供者的 ID */
        private String id;
    
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

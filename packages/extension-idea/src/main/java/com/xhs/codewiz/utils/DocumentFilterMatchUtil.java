package com.xhs.codewiz.utils;

import com.xhs.codewiz.enums.ExtensionEnum;
import com.xhs.codewiz.type.file.DocumentFilter;
import java.nio.file.FileSystems;
import java.nio.file.PathMatcher;
import java.nio.file.Paths;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-08-03 Description:
 */
public class DocumentFilterMatchUtil {
  /**
   * 判断给定的文件路径是否匹配此过滤器
   * @param filePath 文件路径
   * @return 如果匹配返回true，否则返回false
   */
  public static boolean matches(DocumentFilter filter, String filePath) {
    if (filePath == null || filePath.isEmpty()) {
      return false;
    }

    // 如果设置了language，检查语言匹配（假设你已经实现了这部分）
    if (StringUtils.isNotEmpty(filter.getLanguage())) {
      String language = ExtensionEnum.getLanguageByPath(filePath);
      return StringUtils.equals(language, filter.getLanguage());
    }
    // 如果设置了pattern，检查pattern匹配
    if (filter.getPattern() != null) {
      return matchesPattern(filter, filePath);
    }

    // 如果都没有设置，默认匹配
    return true;
  }

  /**
   * 基于GlobPattern判断文件路径是否匹配
   * @param filePath 文件路径
   * @return 如果匹配返回true，否则返回false
   */
  private static boolean matchesPattern(DocumentFilter filter,String filePath) {
    String glob = filter.getPattern().getGlob();
    String base = filter.getPattern().getBase();

    if (glob == null || glob.isEmpty()) {
      return true; // 如果没有设置glob模式，默认匹配
    }

    try {
      // 应用base路径过滤
      if (base != null && !base.isEmpty()) {
        // 检查文件路径是否包含base路径
        if (!filePath.contains(base)) {
          return false;
        }
        // 计算相对路径
        String relativePath = filePath.substring(base.length());
        if (relativePath.startsWith("/")) {
          relativePath = relativePath.substring(1);
        }
        filePath = relativePath;
      }

      // 创建PathMatcher用于glob模式匹配
      PathMatcher pathMatcher = FileSystems.getDefault().getPathMatcher("glob:" + glob);

      // 使用PathMatcher进行匹配
      return pathMatcher.matches(Paths.get(filePath));

    } catch (Exception e) {
      // 如果模式无效或其他异常，返回false
      return false;
    }
  }

  /**
   * 静态方法，用于快速判断文件路径是否匹配过滤器列表中的任意一个
   * @param filePath 文件路径
   * @param filters 过滤器列表
   * @return 如果匹配任意一个过滤器返回true，否则返回false
   */
  public static boolean matchesAny(String filePath, List<DocumentFilter> filters) {
    if (filters == null || filters.isEmpty()) {
      return true;
    }

    return filters.stream().anyMatch(filter -> {
      return matches(filter, filePath);
    });
  }

  /**
   * 静态方法，用于判断文件路径是否不匹配过滤器列表中的任意一个（用于排除列表）
   * @param filePath 文件路径
   * @param excludeFilters 排除过滤器列表
   * @return 如果不匹配任何一个排除过滤器返回true，否则返回false
   */
  public static boolean notMatchesAny(String filePath, List<DocumentFilter> excludeFilters) {
    if (excludeFilters == null || excludeFilters.isEmpty()) {
      return true;
    }

    return excludeFilters.stream().noneMatch(filter -> {
      return matches(filter, filePath);
    });
  }
}

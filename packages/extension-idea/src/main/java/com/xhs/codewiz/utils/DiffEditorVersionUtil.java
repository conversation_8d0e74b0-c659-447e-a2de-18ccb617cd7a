package com.xhs.codewiz.utils;

import com.intellij.diff.FrameDiffTool;
import com.intellij.diff.impl.CacheDiffRequestChainProcessor;
import com.intellij.diff.tools.fragmented.UnifiedDiffViewer;
import com.intellij.diff.tools.simple.SimpleDiffViewer;
import com.intellij.diff.util.Side;
import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.util.BuildNumber;
import java.lang.reflect.Method;

/**
 * Diff编辑器版本兼容性工具类
 * 处理不同版本IntelliJ IDEA中diff编辑器API的变化
 * <p>
 * 版本支持：
 * - 2023版本：使用 DiffRequestProcessorEditor
 * - 2024/2025版本：使用 DiffEditorViewerFileEditor
 */
public class DiffEditorVersionUtil {

    private static final Logger LOG = Logger.getInstance(DiffEditorVersionUtil.class);

    // 版本判断阈值
    private static final int VERSION_2024_THRESHOLD = 241; // 2024.1对应的版本号

    /**
     * 获取diff编辑器的处理器
     *
     * @param fileEditor 文件编辑器
     * @return CacheDiffRequestChainProcessor或null
     */
    public static CacheDiffRequestChainProcessor getDiffProcessor(FileEditor fileEditor) {
        if (fileEditor == null) {
            return null;
        }

        BuildNumber buildNumber = ApplicationInfo.getInstance().getBuild();
        try {
            if (buildNumber.getBaselineVersion() >= VERSION_2024_THRESHOLD) {
                // 2024/2025版本使用DiffEditorViewerFileEditor
                return getDiffProcessorFromDiffEditorViewer(fileEditor);
            } else {
                // 2023版本使用DiffRequestProcessorEditor
                return getDiffProcessorFromDiffRequestProcessor(fileEditor);
            }
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed to get diff processor", e);
            return null;
        }
    }

    /**
     * 从DiffEditorViewerFileEditor获取处理器 (2024/2025版本)
     */
    private static CacheDiffRequestChainProcessor getDiffProcessorFromDiffEditorViewer(FileEditor fileEditor) throws Exception {
        // 检查是否是DiffEditorViewerFileEditor
        Class<?> diffEditorViewerClass = Class.forName("com.intellij.diff.editor.DiffEditorViewerFileEditor");
        if (!diffEditorViewerClass.isInstance(fileEditor)) {
            return null;
        }

        // 获取EditorViewer
        Method getEditorViewerMethod = diffEditorViewerClass.getMethod("getEditorViewer");
        Object editorViewer = getEditorViewerMethod.invoke(fileEditor);

        // 检查是否是CacheDiffRequestChainProcessor
        if (editorViewer instanceof CacheDiffRequestChainProcessor) {
            return (CacheDiffRequestChainProcessor) editorViewer;
        }

        return null;
    }

    /**
     * 从DiffRequestProcessorEditor获取处理器 (2023版本)
     */
    private static CacheDiffRequestChainProcessor getDiffProcessorFromDiffRequestProcessor(FileEditor fileEditor) throws Exception {
        // 检查是否是DiffRequestProcessorEditor
        Class<?> diffRequestProcessorEditorClass = Class.forName("com.intellij.diff.editor.DiffRequestProcessorEditor");
        if (!diffRequestProcessorEditorClass.isInstance(fileEditor)) {
            return null;
        }

        // 获取processor
        Method getProcessorMethod = diffRequestProcessorEditorClass.getMethod("getProcessor");
        Object processor = getProcessorMethod.invoke(fileEditor);

        // 检查是否是CacheDiffRequestChainProcessor
        if (processor instanceof CacheDiffRequestChainProcessor) {
            return (CacheDiffRequestChainProcessor) processor;
        }

        return null;
    }

    /**
     * 获取右侧编辑器
     *
     * @param fileEditor 文件编辑器
     * @return 右侧编辑器或null
     */
    public static Editor getRightEditor(FileEditor fileEditor) {
        try {
            CacheDiffRequestChainProcessor processor = getDiffProcessor(fileEditor);
            if (processor == null) {
                return null;
            }

            FrameDiffTool.DiffViewer activeViewer = processor.getActiveViewer();
            if (activeViewer == null) {
                return null;
            }

            if (activeViewer instanceof SimpleDiffViewer simpleDiffViewer) {
                return simpleDiffViewer.getEditor(Side.RIGHT);
            }

            if (activeViewer instanceof UnifiedDiffViewer unifiedDiffViewer) {
                return unifiedDiffViewer.getEditor();
            }

            return null;
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed to get right editor", e);
            return null;
        }
    }

    /**
     * 获取左侧编辑器
     *
     * @param fileEditor 文件编辑器
     * @return 左侧编辑器或null
     */
    public static Editor getLeftEditor(FileEditor fileEditor) {
        try {
            CacheDiffRequestChainProcessor processor = getDiffProcessor(fileEditor);
            if (processor == null) {
                return null;
            }

            FrameDiffTool.DiffViewer activeViewer = processor.getActiveViewer();
            if (activeViewer == null) {
                return null;
            }

            if (activeViewer instanceof SimpleDiffViewer) {
                SimpleDiffViewer simpleDiffViewer = (SimpleDiffViewer) activeViewer;
                return simpleDiffViewer.getEditor(Side.LEFT);
            }

            // UnifiedDiffViewer只有一个编辑器
            return null;
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed to get left editor", e);
            return null;
        }
    }

    /**
     * 获取活动的diff查看器
     *
     * @param fileEditor 文件编辑器
     * @return DiffViewer或null
     */
    public static FrameDiffTool.DiffViewer getActiveDiffViewer(FileEditor fileEditor) {
        try {
            CacheDiffRequestChainProcessor processor = getDiffProcessor(fileEditor);
            if (processor == null) {
                return null;
            }

            return processor.getActiveViewer();
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed to get active diff viewer", e);
            return null;
        }
    }

    /**
     * 判断是否是diff编辑器
     *
     * @param fileEditor 文件编辑器
     * @return true如果是diff编辑器
     */
    public static boolean isDiffEditor(FileEditor fileEditor) {
        if (fileEditor == null) {
            return false;
        }

        BuildNumber buildNumber = ApplicationInfo.getInstance().getBuild();
        try {
            if (buildNumber.getBaselineVersion() >= VERSION_2024_THRESHOLD) {
                // 2024/2025版本检查DiffEditorViewerFileEditor
                Class<?> diffEditorViewerClass = Class.forName("com.intellij.diff.editor.DiffEditorViewerFileEditor");
                return diffEditorViewerClass.isInstance(fileEditor);
            } else {
                // 2023版本检查DiffRequestProcessorEditor
                Class<?> diffRequestProcessorEditorClass = Class.forName("com.intellij.diff.editor.DiffRequestProcessorEditor");
                return diffRequestProcessorEditorClass.isInstance(fileEditor);
            }
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed to check if diff editor", e);
            return false;
        }
    }

    /**
     * 判断是否是SimpleDiffViewer
     *
     * @param fileEditor 文件编辑器
     * @return true如果是SimpleDiffViewer
     */
    public static boolean isSimpleDiffViewer(FileEditor fileEditor) {
        try {
            FrameDiffTool.DiffViewer activeViewer = getActiveDiffViewer(fileEditor);
            return activeViewer instanceof SimpleDiffViewer;
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed to check if simple diff viewer", e);
            return false;
        }
    }

    /**
     * 判断是否是UnifiedDiffViewer
     *
     * @param fileEditor 文件编辑器
     * @return true如果是UnifiedDiffViewer
     */
    public static boolean isUnifiedDiffViewer(FileEditor fileEditor) {
        try {
            FrameDiffTool.DiffViewer activeViewer = getActiveDiffViewer(fileEditor);
            return activeViewer instanceof UnifiedDiffViewer;
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed to check if unified diff viewer", e);
            return false;
        }
    }

    /**
     * 获取当前IntelliJ IDEA版本信息
     *
     * @return 版本信息字符串
     */
    public static String getVersionInfo() {
        BuildNumber buildNumber = ApplicationInfo.getInstance().getBuild();
        return String.format("Build: %s, Baseline: %d",
                buildNumber.asString(), buildNumber.getBaselineVersion());
    }
} 
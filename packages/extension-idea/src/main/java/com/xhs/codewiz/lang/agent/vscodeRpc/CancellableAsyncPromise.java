package com.xhs.codewiz.lang.agent.vscodeRpc;


import com.intellij.openapi.diagnostic.Logger;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.AsyncPromise;

class CancellableAsyncPromise<T> extends AsyncPromise<T> {
    private final @Nullable Runnable onCancel;

    CancellableAsyncPromise(@Nullable Runnable onCancel) {
        this.onCancel = onCancel;
    }

    public boolean cancel(boolean mayInterruptIfRunning) {
        boolean isDone = this.isDone();
        boolean nowCancelled = super.cancel(mayInterruptIfRunning);
        if (!isDone && nowCancelled && this.onCancel != null) {
            try {
                this.onCancel.run();
            } catch (Exception var5) {
                Logger.getInstance(this.getClass()).debug("Error calling onCancel", var5);
            }
        }

        return nowCancelled;
    }
}


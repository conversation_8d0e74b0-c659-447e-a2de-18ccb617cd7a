package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 获取当前文档的链接
 */
public class ReadLink {
    private String schemaProtocol = "workspace.read.link";
    private ReadLinkParams params;

    public ReadLinkParams getParams() {
        return params;
    }
    public void setParams(ReadLinkParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadLinkParams {
        /** Link Provider 的 ID */
        private String provider;
        private String uri;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

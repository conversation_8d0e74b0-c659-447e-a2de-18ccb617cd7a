package com.xhs.codewiz.type.content;

import java.util.*;
import com.xhs.codewiz.type.global.Command;

/**
 * Meta information for action providers
 */
public class ActionProviderMeta {
    /** 解释当前 Provider 提供的各种功能 */
    private List<ActionProviderMetaDocumentationItem> documentation;

    private String id;

    private List<ActionKind> providedCodeActionKinds;

    public List<ActionProviderMetaDocumentationItem> getDocumentation() {
        return documentation;
    }

    public void setDocumentation(List<ActionProviderMetaDocumentationItem> documentation) {
        this.documentation = documentation;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<ActionKind> getProvidedCodeActionKinds() {
        return providedCodeActionKinds;
    }

    public void setProvidedCodeActionKinds(List<ActionKind> providedCodeActionKinds) {
        this.providedCodeActionKinds = providedCodeActionKinds;
    }

public class ActionProviderMetaDocumentationItem {
    private ActionKind kind;

    private Command command;

    public ActionKind getKind() {
        return kind;
    }

    public void setKind(ActionKind kind) {
        this.kind = kind;
    }

    public Command getCommand() {
        return command;
    }

    public void setCommand(Command command) {
        this.command = command;
    }

}
}

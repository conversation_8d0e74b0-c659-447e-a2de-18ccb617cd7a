package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.Session;
/**
 * 更新 Session
 */
public class UpdateSession {
    private String schemaProtocol = "platform.update.session";
    private UpdateSessionParams params;

    public UpdateSessionParams getParams() {
        return params;
    }
    public void setParams(UpdateSessionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateSessionParams {
        /** Session Provider ID */
        private String provider;
        /** 更新后的 Session 信息。在无法确定具体变更的 Session 时，可以不传该参数 */
        private Session session; // optional
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public Session getSession() {
            return session;
        }
        public void setSession(Session session) {
            this.session = session;
        }
    }
}

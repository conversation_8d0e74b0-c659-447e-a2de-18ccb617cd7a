package com.xhs.codewiz.type.platform;

import java.util.*;

/**
 * Session
 */
public class Session {
    /** Session ID */
    private String id;

    private List<String> scopes;

    /** Session 访问令牌 */
    private String accessToken;

    private SessionAccountInformation account;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<String> getScopes() {
        return scopes;
    }

    public void setScopes(List<String> scopes) {
        this.scopes = scopes;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public SessionAccountInformation getAccount() {
        return account;
    }

    public void setAccount(SessionAccountInformation account) {
        this.account = account;
    }

}

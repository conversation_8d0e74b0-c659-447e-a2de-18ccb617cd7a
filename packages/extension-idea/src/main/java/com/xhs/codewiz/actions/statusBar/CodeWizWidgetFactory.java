package com.xhs.codewiz.actions.statusBar;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.StatusBarWidget;
import com.intellij.openapi.wm.impl.status.widget.StatusBarEditorBasedWidgetFactory;
import com.xhs.codewiz.utils.BundleUtil;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;

public class CodeWizWidgetFactory extends StatusBarEditorBasedWidgetFactory {
    public CodeWizWidgetFactory() {
    }

    public @NonNls @NotNull String getId() {
        return CodeWizStatusBarWidget.WIDGET_ID;
    }

    public @Nls @NotNull String getDisplayName() {
        return BundleUtil.get("statusBar.displayName");
    }

    public @NotNull StatusBarWidget createWidget(@NotNull Project project) {
        return new CodeWizStatusBarWidget(project);
    }

    public void disposeWidget(@NotNull StatusBarWidget widget) {
    }
}


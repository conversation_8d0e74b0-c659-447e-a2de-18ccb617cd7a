package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
import com.xhs.codewiz.type.file.Content;
/**
 * 保存文件
 */
public class ExecuteSave {
    private String schemaProtocol = "file.execute.save";
    private ExecuteSaveParams params;

    public ExecuteSaveParams getParams() {
        return params;
    }
    public void setParams(ExecuteSaveParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteSaveParams {
        /** 要保存的文件 */
        private String uri;
        /** 文件内容 */
        private Content content; // optional
    
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
        public Content getContent() {
            return content;
        }
        public void setContent(Content content) {
            this.content = content;
        }
    }
}

package com.xhs.codewiz.type.platform;

import java.util.*;

/**
 * 平台名称
 */
public enum PlatformName {
    AndroidStudio("Android Studio"),
    PyCharm("PyCharm"),
    /** VSCode Remote 平台 */
    VscodeRemote("VSCode Remote"),
    /** VSCode Local 平台 */
    VscodeLocal("VSCode"),
    IntelliJIDEA("IntelliJ IDEA"),
    WebStorm("WebStorm"),
    Cursor("Cursor"),
    Clion("Clion");

    private final String value;

    PlatformName(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}

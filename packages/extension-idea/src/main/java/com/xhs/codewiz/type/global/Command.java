package com.xhs.codewiz.type.global;

import java.util.*;

/**
 * 指令
 */
public class Command {
    /** 指令提示 */
    private String tooltip;

    /** 指令描述 */
    private String description;

    /** 指令参数 */
    private List<Object> arguments;

    /** 指令标题 */
    private String title;

    /** 指令名称 */
    private String command;

    public String getTooltip() {
        return tooltip;
    }

    public void setTooltip(String tooltip) {
        this.tooltip = tooltip;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Object> getArguments() {
        return arguments;
    }

    public void setArguments(List<Object> arguments) {
        this.arguments = arguments;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

}

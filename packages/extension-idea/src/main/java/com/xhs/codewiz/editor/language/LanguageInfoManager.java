package com.xhs.codewiz.editor.language;


import com.intellij.lang.Language;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.LanguageFileType;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;
import com.xhs.codewiz.editor.request.LanguageInfo;
import java.util.Iterator;
import org.jetbrains.annotations.NotNull;

public final class LanguageInfoManager {
    private static final Key<LanguageInfo> CACHE_KEY = Key.create("codewiz.languageInfo");

    public LanguageInfoManager() {
    }

    public static @NotNull LanguageInfo findLanguageMapping(@NotNull PsiFile file) {
        LanguageInfo cachedLanguageInfo = CACHE_KEY.get(file);
        if (cachedLanguageInfo != null) {
            return cachedLanguageInfo;
        } else {
            Iterator<LanguageInfoSupport> supportIterator = LanguageInfoSupport.EP.getExtensionList().iterator();
            LanguageInfo mapping;
            do {
                if (!supportIterator.hasNext()) {
                    throw new IllegalStateException("Default implementation of languageMapping extension point not defined");
                }

                LanguageInfoSupport mappingSupport = supportIterator.next();
                mapping = mappingSupport.findVSCodeLanguageMapping(file);
            } while(mapping == null);

            CACHE_KEY.set(file, mapping);
            return mapping;
        }
    }

    public static @NotNull LanguageInfo findFallback(@NotNull VirtualFile file) {
        Language language = Language.ANY;
        FileType fileType = file.getFileType();
        if (fileType instanceof LanguageFileType) {
            language = ((LanguageFileType)fileType).getLanguage();
        }

        return new LanguageInfo(language, VSCodeLanguageMap.getId(language));
    }
}


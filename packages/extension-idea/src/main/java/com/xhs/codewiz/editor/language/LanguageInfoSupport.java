package com.xhs.codewiz.editor.language;


import com.intellij.openapi.extensions.ExtensionPointName;
import com.intellij.psi.PsiFile;
import com.xhs.codewiz.editor.request.LanguageInfo;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface LanguageInfoSupport {
    ExtensionPointName<LanguageInfoSupport> EP = new ExtensionPointName("com.xhs.codewiz.languageIdSupport");

    @Nullable LanguageInfo findVSCodeLanguageMapping(@NotNull PsiFile var1);
}


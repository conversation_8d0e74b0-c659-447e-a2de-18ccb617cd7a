package com.xhs.codewiz.lang.agent;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.intellij.execution.ExecutionException;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread;
import com.intellij.util.messages.MessageBus;
import com.xhs.codewiz.actions.status.CodeWizStatus;
import com.xhs.codewiz.actions.status.CodeWizStatusService;
import com.xhs.codewiz.lang.LspServiceForTylm;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcClientResponse;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcRequestListener;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.MetricsUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.annotations.TestOnly;
import org.jetbrains.concurrency.CancellablePromise;
import org.jetbrains.concurrency.Promise;

public class RestartableCodeWizAgentProcessService implements CodeWizAgentProcessService, Disposable {
    private static final Logger LOG = Logger.getInstance(RestartableCodeWizAgentProcessService.class);
    protected final @NotNull AtomicInteger restartAttempts = new AtomicInteger();
    private final Object lock = new Object();
    private @NotNull CodeWizAgentProcessServiceEx delegate;
    private boolean isInitialized = false; // 插件是否已成功初始化过 LSP

    public RestartableCodeWizAgentProcessService() {
        // 插件启动开始埋点 - 在最开始的地方
        MetricsUtil.reportBeforeStart();

        try {
            this.delegate = this.createInitializedDelegate(0);
            this.isInitialized = true;
            this.delegate.startNotify();
        } catch (Exception var2) {
            // 捕获错误信息并上报
            String errorMessage = var2.getMessage() != null ? var2.getMessage() : "Failed to initialize agent";
            MetricsUtil.reportPluginLaunchFailed("initialization_failed", errorMessage);
            this.delegate = new UnavailableAgentProcessService("error in constructor");
            //ErrorReportService.getInstance().reportException(var2, Collections.emptyMap(), 0);
        }
        // 插件启动成功
        MetricsUtil.reportAfterStart();

    }

    @RequiresBackgroundThread
    private static void sendRestartNotifications(int attempts) {
        if (attempts < 10) {
            MessageBus bus = ApplicationManager.getApplication().getMessageBus();
            (bus.syncPublisher(CodeWizAgentProcessStatusListener.TOPIC)).onAgentProcessRestart();
        } else if (attempts == 10) {
            LoggerUtil.INSTANCE.logWarn(LOG, "too many restart attempts");
//            TelemetryService.getInstance().track("editor.intellij.tooManyAgentRestarts", Map.of("maxAttempts", String.valueOf(10)));
            CodeWizStatusService.notifyApplication(CodeWizStatus.AgentBroken);
        } else {
            LoggerUtil.INSTANCE.logDebug(LOG, "too many restart attempts");
        }

    }

    /**
     * 根据exit code确定LSP错误类型，暂时只捕获exit code用于调试分析
     *
     * @param exitCode 进程退出码
     * @param message  异常消息
     * @return 错误类型代码
     */
    private String determineLspErrorType(String exitCode, String message) {
        // 如果有exit code，直接使用
        if (exitCode != null) {
            return exitCode;
        }

        // 没有exit code时使用通用的LSP启动失败
        return "lsp_launch_failed";
    }

    public boolean isRunning() {
        return this.getDelegate().isRunning();
    }

    public <T> @NotNull CancellablePromise<T> executeCommand(@NotNull JsonRpcCommand<T> command) {
        return this.getDelegate().executeCommand(command);
    }

    public <T> @NotNull CancellablePromise<T> executeCommand(@NotNull JsonRpcCommand<T> command,
                                                             @Nullable JsonObject additionalProperties) {
        return this.getDelegate().executeCommand(command, additionalProperties);
    }

    public void executeResponse(@NotNull JsonRpcClientResponse response) {
        this.getDelegate().executeResponse(response);
    }

    @Override
    public CancellablePromise<Object> executeCommonLsp(JsonObject request, String commandName) {
        return this.getDelegate().executeCommonLsp(request, commandName);
    }

    @Override
    public void commonNotification4UI(JsonObject request, String commandName) {
        this.getDelegate().commonNotification4UI(request, commandName);
    }

    public void executeNotification(@NotNull JsonRpcNotification notification) {
        this.getDelegate().executeNotification(notification);
    }

    public void executeNotification(@NotNull JsonRpcNotification notification, @Nullable JsonObject additionalProperties) {
        this.getDelegate().executeNotification(notification, additionalProperties);
    }

    public void addNotificationListener(@NotNull Disposable parentDisposable, @NotNull JsonRpcNotificationListener listener) {
        CodeWizAgentProcessServiceEx currentDelegate = this.getDelegate();
        BoundJsonRpcNotificationListener boundWrapper = new BoundJsonRpcNotificationListener(currentDelegate, listener);
        currentDelegate.addNotificationListener(parentDisposable, boundWrapper);
    }

    public <I, O> void addRequestListener(@NotNull String lspCommand, final @NotNull JsonRpcRequestListener<I, O> listener) {
        final CodeWizAgentProcessServiceEx currentDelegate = this.getDelegate();
        JsonRpcRequestListener<I, O> boundWrapper = new JsonRpcRequestListener<I, O>() {
            public @NotNull Promise<O> handleMessage(@NotNull I request) {
                if (currentDelegate != RestartableCodeWizAgentProcessService.this.getDelegate()) {
                    throw new RuntimeException("Agent is shutting down");
                } else {
                    return listener.handleMessage(request);
                }
            }

            public Class<I> getRequestType() {
                return listener.getRequestType();
            }

            public Class<O> getResponseType() {
                return listener.getResponseType();
            }
        };
        currentDelegate.addRequestListener(lspCommand, boundWrapper);
    }

    public void dispose() {
        synchronized (this.lock) {
            try {
                this.delegate.shutdown();
            } finally {
                this.delegate = new UnavailableAgentProcessService("disposed");
            }

        }
    }

    @TestOnly
    protected @NotNull CodeWizAgentProcessServiceEx getDelegate() {
        synchronized (this.lock) {
            return this.delegate;
        }
    }

    protected void forceRestart() {
        synchronized (this.lock) {
            try {
                this.delegate.shutdown();
            } finally {
                this.setNewDelegateLocked();
            }

        }
    }

    protected CodeWizAgentProcessServiceEx createInitializedDelegate(int restartAttempts) throws Exception {
        BaseDelegateImplementation delegate = new BaseDelegateImplementation(restartAttempts, !this.isInitialized);
        this.initializeDelegate(delegate);
        return delegate;
    }

    protected void initializeDelegate(BaseDelegateImplementation delegate) {
        BoundJsonRpcNotificationListener wrappedStatusListener =
                new BoundJsonRpcNotificationListener(delegate, new StatusChangeNotificationListener());
        BoundJsonRpcNotificationListener wrappedFeatureFlagsListener =
                new BoundJsonRpcNotificationListener(delegate, new FeatureFlagsChangeNotificationListener());
        BoundJsonRpcNotificationListener wrappedLogListener =
                new BoundJsonRpcNotificationListener(delegate, new LogMessageNotificationListener());

        BoundJsonRpcNotificationListener tylmListener = new BoundJsonRpcNotificationListener(delegate, new CodeWizNotificationListener());
        delegate.initialize(List.of(wrappedStatusListener, wrappedFeatureFlagsListener, wrappedLogListener, tylmListener));
        delegate.addRequestListener("window/showMessageRequest", new ShowMessageRequestHandler());
        delegate.addRequestListener("textDocument/queryReference", new TextDocumentReferenceHandler());
    }

    private void setNewDelegateLocked() {
        int attempts = this.restartAttempts.get();
        boolean var7 = false;

        try {
            var7 = true;
            if (attempts >= 10) {
                this.delegate = new UnavailableAgentProcessService("too many restarts");
                var7 = false;
            } else {
                try {
                    this.delegate = this.createInitializedDelegate(attempts);
                    //异步重新初始化socket-connect
                    ThreadUtil.execute(this::initConnectAfterRestart);
                    var7 = false;
                } catch (Exception var8) {
                    LoggerUtil.INSTANCE.logError(LOG, "error initializing agent", var8);
                    this.delegate = new UnavailableAgentProcessService("exception");
                    var7 = false;
                }
            }
        } finally {
            if (var7) {
                CodeWizAgentProcessServiceEx var4 = this.delegate;
                var4.startNotify();
                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    sendRestartNotifications(attempts);
                });
            }
        }

        CodeWizAgentProcessServiceEx current = this.delegate;
        current.startNotify();
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            sendRestartNotifications(attempts);
        });
    }

    private void initConnectAfterRestart() {
        //重置状态
        CodeWizStatusService.notifyApplication(CodeWizStatus.Ready, "agent init");
        //重启后，需要重新初始化已打开项目
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            Iterable<Project> allProject = ApplicationUtil.findValidProjects();
            allProject.forEach(pro -> {
                try {
                    LspServiceForTylm.initializeParams(pro);
                } catch (Exception e) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "initializeParamsAfterRestart err, msg = {}", e);
                }
            });
        });
    }

    private class BoundJsonRpcNotificationListener implements JsonRpcNotificationListener {
        private final CodeWizAgentProcessService associatedService;
        private final JsonRpcNotificationListener delegate;

        private BoundJsonRpcNotificationListener(@NotNull CodeWizAgentProcessService associatedService,
                                                 JsonRpcNotificationListener delegate) {
            this.delegate = delegate;
            this.associatedService = associatedService;
        }

        public boolean handleMessage(@NotNull String name, @NotNull JsonElement message) {
            if (this.associatedService != RestartableCodeWizAgentProcessService.this.getDelegate()) {
                LoggerUtil.INSTANCE.logDebug(LOG, "JSON-RPC listener called for shutdown agent: " + name + ", message: " + message);
                return false;
            } else {
                return this.delegate.handleMessage(name, message);
            }
        }
    }

    protected class BaseDelegateImplementation extends CodeWizAgentProcessServiceImpl {
        public BaseDelegateImplementation(int restartAttempts, boolean isInit) throws ExecutionException {
            super(restartAttempts, isInit);
        }

        void beforeCommand(@NotNull JsonRpcCommand<?> command) {
        }

        void afterCommand(@NotNull JsonRpcCommand<?> command) {
            afterCommand();
        }

        void afterCommand() {
            if (!this.isShutdown()) {
                RestartableCodeWizAgentProcessService.this.restartAttempts.set(0);
            }
        }

        @Override
        void afterNotification() {
            if (!this.isShutdown()) {
                RestartableCodeWizAgentProcessService.this.restartAttempts.set(0);
            }
        }

        void beforeNotification(@NotNull JsonRpcNotification notification) {
        }

        void afterNotification(@NotNull JsonRpcNotification notification) {
            if (!this.isShutdown()) {
                RestartableCodeWizAgentProcessService.this.restartAttempts.set(0);
            }
        }

        void beforeResponse(@NotNull JsonRpcClientResponse response) {
        }

        void onRestartException(@NotNull Exception exception, @NotNull String commandName, @NotNull String recentOutput,
                                @Nullable Integer exitCode) {
            if (!this.isShutdown()) {
                RestartableCodeWizAgentProcessService.this.restartAttempts.incrementAndGet();

                boolean var9 = false;

                try {
                    var9 = true;
                    RestartableCodeWizAgentProcessService.this.forceRestart();
                    var9 = false;
                } finally {
                    if (var9) {
                        //Attachment[] attachments = new Attachment[]{new Attachment("agent-output.txt", recentOutput)};
                        LoggerUtil.INSTANCE.logWarn(RestartableCodeWizAgentProcessService.LOG,
                                "CodeWiz Agent terminated unexpectedly. Exit code: " + exitCode,
                                new RuntimeException("CodeWiz Agent terminated unexpectedly. Exit code: " + exitCode, exception));
                    }
                }

                //Attachment[] attachmentsx = new Attachment[]{new Attachment("agent-output.txt", recentOutput)};
                LoggerUtil.INSTANCE.logWarn(RestartableCodeWizAgentProcessService.LOG,
                        "CodeWiz Agent terminated unexpectedly. Exit code: " + exitCode,
                        new RuntimeException("CodeWiz Agent terminated unexpectedly. Exit code: " + exitCode, exception));
            }
        }
    }
}


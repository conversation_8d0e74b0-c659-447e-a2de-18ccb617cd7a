package com.xhs.codewiz.utils;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Author: liukunpeng Date: 2025-03-06 Description:
 */
public class ReflectUtil {
  private static Map<String, CacheClass> classMap = new ConcurrentHashMap<String, CacheClass>();

  /**
   * 根据类名获取类：由于要解析所有文件，所以做缓存
   * @param className
   * @return
   * @throws ClassNotFoundException
   */
  public static Class<?> classForName(String className) throws ClassNotFoundException {
    Class<?> result = null;
    CacheClass target = classMap.get(className);
    if (target == null) {
      target = new CacheClass(null);
      try {
        result = Class.forName(className);
        target.setClassObject(result);
      }
      catch (Exception exception) {
        // empty catch block
      }
      classMap.put(className, target);
    }
    return target.getClassObject();
  }

  public static <T> T getStaticField(Class<?> targetClass, String fieldName) throws NoSuchFieldException, IllegalAccessException {
    if (targetClass == null) {
      throw new IllegalArgumentException("targetClass must not be null");
    }
    Field field = targetClass.getDeclaredField(fieldName);
    field.setAccessible(true);
    return (T)field.get(null);
  }

  public static <T> T getField(Object obj, String fieldName) throws NoSuchFieldException, IllegalAccessException {
    Field field = obj.getClass().getDeclaredField(fieldName);
    field.setAccessible(true);
    return (T)field.get(obj);
  }

  static class CacheClass {
    private Class<?> classObject;

    public Class<?> getClassObject() {
      return this.classObject;
    }

    public void setClassObject(Class<?> classObject) {
      this.classObject = classObject;
    }

    public CacheClass(Class<?> classObject) {
      this.classObject = classObject;
    }
  }
}

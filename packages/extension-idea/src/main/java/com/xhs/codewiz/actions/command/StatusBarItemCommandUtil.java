package com.xhs.codewiz.actions.command;

import com.google.gson.JsonObject;
import com.intellij.openapi.actionSystem.ActionManager;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.Constraints;
import com.intellij.openapi.actionSystem.DefaultActionGroup;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.scheme.platform.CreateStatusBarItem;
import com.xhs.codewiz.scheme.platform.DeleteStatusBarItem;
import com.xhs.codewiz.scheme.platform.ExecuteHideStatusBar;
import com.xhs.codewiz.scheme.platform.ExecuteShowStatusBar;
import com.xhs.codewiz.scheme.platform.UpdateStatusBarItem;
import com.xhs.codewiz.type.global.Command;
import com.xhs.codewiz.utils.GsonUtil;
import java.util.ArrayList;

/**
 * Author: liukunpeng Date: 2025-07-25 Description:
 * 状态栏item命令处理,这个是固定协议处理
 */
public class StatusBarItemCommandUtil {
  public static void createStatusBarItem(String str, String channel) {
    CreateStatusBarItem statusBarItem = GsonUtil.fromJson(str, CreateStatusBarItem.class);
    if (null == statusBarItem
        || null == statusBarItem.getParams()
        || null == statusBarItem.getParams().getCommand()) {
      return;
    }
    Command command = statusBarItem.getParams().getCommand();
    JsonObject jsonObject = new JsonObject();
    jsonObject.addProperty("title", command.getTitle());
    jsonObject.addProperty("command", command.getCommand());
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    BaseCommandAction action = DefaultCommandUtil.createAction(project, jsonObject, channel, null);
    String actionId = channel + statusBarItem.getParams().getId();
    addActionToStatusBar(action, RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel), actionId);
  }

  public static void updateStatusBarItem(String str, String channel) {
    UpdateStatusBarItem statusBarItem = GsonUtil.fromJson(str, UpdateStatusBarItem.class);
    if (null == statusBarItem
        || null == statusBarItem.getParams()
        || null == statusBarItem.getParams().getItem()
        || null == statusBarItem.getParams().getItem().getCommand()) {
      return;
    }
    Command command = statusBarItem.getParams().getItem().getCommand();
    JsonObject jsonObject = new JsonObject();
    jsonObject.addProperty("title", command.getTitle());
    jsonObject.addProperty("command", command.getCommand());
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    BaseCommandAction action = DefaultCommandUtil.createAction(project, jsonObject, channel, null);
    String actionId = channel + statusBarItem.getParams().getId();
    replaceActionToStatusBar(action, RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel), actionId);
  }

  public static void deleteStatusBarItem(String str, String channel) {
    DeleteStatusBarItem statusBarItem = GsonUtil.fromJson(str, DeleteStatusBarItem.class);
    if (null == statusBarItem
        || null == statusBarItem.getParams()) {
      return;
    }
    String actionId = channel + statusBarItem.getParams();
    ActionManager am = ActionManager.getInstance();
    AnAction action = am.getAction(actionId);
    if (null != action) {
      DefaultActionGroup statusBarMenu = (DefaultActionGroup) am.getAction("codewiz.rcs.CodeWizStatusBarItem");
      statusBarMenu.remove(action);
      am.unregisterAction(actionId);
      Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
      if (DefaultCommandUtil.actionIdMap.containsKey(project)) {
        DefaultCommandUtil.actionIdMap.get(project).remove(actionId);
      }
    }
  }

  public static void hideStatusBarItem(String str, String channel) {
    ExecuteHideStatusBar hide = GsonUtil.fromJson(str, ExecuteHideStatusBar.class);
    if (null == hide
        || null == hide.getParams()) {
      return;
    }
    String actionId = channel + hide.getParams();
    ActionManager am = ActionManager.getInstance();
    AnAction action = am.getAction(actionId);
    if (null == action) {
      return;
    }
    DefaultActionGroup statusBarMenu = (DefaultActionGroup) am.getAction("codewiz.rcs.CodeWizStatusBarItem");
    //从statusBar的操作组中移除该操作
    statusBarMenu.remove(action);
  }

  public static void showStatusBarItem(String str, String channel) {
    ExecuteShowStatusBar show = GsonUtil.fromJson(str, ExecuteShowStatusBar.class);
    if (null == show
        || null == show.getParams()) {
      return;
    }
    String actionId = channel + show.getParams();
    ActionManager am = ActionManager.getInstance();
    AnAction action = am.getAction(actionId);
    if (null == action) {
      return;
    }
    DefaultActionGroup statusBarMenu = (DefaultActionGroup) am.getAction("codewiz.rcs.CodeWizStatusBarItem");
    //从statusBar的操作组中添加该操作
    if (!statusBarMenu.containsAction(action)) {
      statusBarMenu.add(statusBarMenu, Constraints.LAST);
    }
  }


  public static void addActionToStatusBar(AnAction action, Project project, String actionId) {
    ActionManager am = ActionManager.getInstance();
    DefaultActionGroup statusBarMenu = (DefaultActionGroup) am.getAction("codewiz.rcs.CodeWizStatusBarItem");
    // 用唯一ID注册新action（防止重复）
    if (am.getAction(actionId) == null) {
      am.registerAction(actionId, action);
      statusBarMenu.add(action, Constraints.LAST);
    }
    if (!DefaultCommandUtil.actionIdMap.containsKey(project)) {
      DefaultCommandUtil.actionIdMap.put(project, new ArrayList<>());
    }
    DefaultCommandUtil.actionIdMap.get(project).add(actionId);
  }

  public static void replaceActionToStatusBar(AnAction action, Project project, String actionId) {
    ActionManager am = ActionManager.getInstance();
    DefaultActionGroup statusBarMenu = (DefaultActionGroup) am.getAction("codewiz.rcs.CodeWizStatusBarItem");
    // 用唯一ID注册新action（防止重复）
    if (am.getAction(actionId) == null) {
      am.registerAction(actionId, action);
      statusBarMenu.add(action, Constraints.LAST);
    } else {
      //获取老的action
      AnAction old = am.getAction(actionId);
      //替换ID映射的action
      am.replaceAction(actionId, action);
      //替换绑定action
      statusBarMenu.replaceAction(old, action);
    }
    if (!DefaultCommandUtil.actionIdMap.containsKey(project)) {
      DefaultCommandUtil.actionIdMap.put(project, new ArrayList<>());
    }
    if (!DefaultCommandUtil.actionIdMap.get(project).contains(actionId)) {
      DefaultCommandUtil.actionIdMap.get(project).add(actionId);
    }
  }
}

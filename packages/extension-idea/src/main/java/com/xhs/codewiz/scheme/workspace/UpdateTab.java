package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.Tab;
import com.xhs.codewiz.type.global.ZeroBasedIndex;
/**
 * 更新 Tab 信息
 */
public class UpdateTab {
    private String schemaProtocol = "workspace.update.tab";
    private UpdateTabParams params;

    public UpdateTabParams getParams() {
        return params;
    }
    public void setParams(UpdateTabParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateTabParams {
        /** 更新后的 Tab 信息 */
        private Tab tab;
        /** Tab 索引 */
        private Integer tabIndex;
        /** TabGroup 索引 */
        private Integer tabGroupIndex;
    
        public Tab getTab() {
            return tab;
        }
        public void setTab(Tab tab) {
            this.tab = tab;
        }
        public Integer getTabIndex() {
            return tabIndex;
        }
        public void setTabIndex(Integer tabIndex) {
            this.tabIndex = tabIndex;
        }
        public Integer getTabGroupIndex() {
            return tabGroupIndex;
        }
        public void setTabGroupIndex(Integer tabGroupIndex) {
            this.tabGroupIndex = tabGroupIndex;
        }
    }
}

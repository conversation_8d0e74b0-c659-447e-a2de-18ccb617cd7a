package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.ActionProviderMeta;
import com.xhs.codewiz.type.file.DocumentFilter;
/**
 * 注册 Action Provider
 */
public class CreateActionProvider {
    private String schemaProtocol = "content.create.actionprovider";
    private CreateActionProviderParams params;

    public CreateActionProviderParams getParams() {
        return params;
    }
    public void setParams(CreateActionProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateActionProviderParams {
        private ActionProviderMeta provider;
        private List<DocumentFilter> selector;
    
        public ActionProviderMeta getProvider() {
            return provider;
        }
        public void setProvider(ActionProviderMeta provider) {
            this.provider = provider;
        }
        public List<DocumentFilter> getSelector() {
            return selector;
        }
        public void setSelector(List<DocumentFilter> selector) {
            this.selector = selector;
        }
    }
}

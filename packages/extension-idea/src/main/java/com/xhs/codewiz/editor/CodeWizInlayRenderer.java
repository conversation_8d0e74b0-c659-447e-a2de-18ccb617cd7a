package com.xhs.codewiz.editor;

import com.intellij.openapi.editor.EditorCustomElementRenderer;
import com.intellij.openapi.editor.Inlay;
import com.xhs.codewiz.completion.enums.CodewizCompletionType;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface CodeWizInlayRenderer extends EditorCustomElementRenderer {
    @NotNull List<String> getContentLines();

    @Nullable Inlay<CodeWizInlayRenderer> getInlay();

    @NotNull CodewizCompletionType getType();
}

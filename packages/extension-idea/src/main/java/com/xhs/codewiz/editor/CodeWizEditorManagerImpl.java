package com.xhs.codewiz.editor;

import com.intellij.codeInsight.lookup.LookupManager;
import com.intellij.injected.editor.EditorWindow;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.command.CommandProcessor;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.Inlay;
import com.intellij.openapi.editor.InlayModel;
import com.intellij.openapi.editor.ScrollType;
import com.intellij.openapi.editor.ex.EditorEx;
import com.intellij.openapi.editor.impl.ImaginaryEditor;
import com.intellij.openapi.extensions.ExtensionPoint;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileDocumentSynchronizationVetoer;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.util.KeyWithDefaultValue;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread;
import com.intellij.util.concurrency.annotations.RequiresEdt;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.completion.CodewizCompletion;
import com.xhs.codewiz.completion.CodewizCompletionService;
import com.xhs.codewiz.completion.CompletionSplitter;
import com.xhs.codewiz.completion.enums.CodeWizApplyInlayStrategy;
import com.xhs.codewiz.completion.enums.CompletionType;
import com.xhs.codewiz.completion.request.AgentCompletion;
import com.xhs.codewiz.completion.request.CodewizInlayList;
import com.xhs.codewiz.completion.request.CompletionExtraContext;
import com.xhs.codewiz.completion.request.Measurements;
import com.xhs.codewiz.editor.request.AgentEditorRequest;
import com.xhs.codewiz.editor.request.CodewizEditorInlay;
import com.xhs.codewiz.editor.request.EditorRequest;
import com.xhs.codewiz.editor.request.EditorRequestResultList;
import com.xhs.codewiz.editor.request.ProjectEditorSession;
import com.xhs.codewiz.editor.request.RequestId;
import com.xhs.codewiz.editor.util.CodewizEditorSupport;
import com.xhs.codewiz.enums.CompletionStatusEnum;
import com.xhs.codewiz.lang.Features;
import com.xhs.codewiz.lang.LspServiceForTylm;
import com.xhs.codewiz.lang.agent.commands.NotifyAcceptedCommand;
import com.xhs.codewiz.listener.LSPManager;
import com.xhs.codewiz.listener.topic.CodewizInlayListener;
import com.xhs.codewiz.listener.topic.EditorRequestsCancelledMessage;
import com.xhs.codewiz.listener.topic.InlaysReceivedMessage;
import com.xhs.codewiz.setting.CodeWizApplicationSettings;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.utils.CancellableAlarm;
import com.xhs.codewiz.utils.CodeWizPsiUtil;
import com.xhs.codewiz.utils.CodewizStringUtil;
import com.xhs.codewiz.utils.LogContextUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.PluginUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Flow;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CodeWizEditorManagerImpl implements CodeWizEditorManager {
    private static final Logger LOG = Logger.getInstance(CodeWizEditorManagerImpl.class);
    public static final Key<EditorRequestResultList> KEY_LAST_REQUEST = Key.create("copilot.editorRequest");
    private static final Key<Boolean> KEY_PROCESSING = KeyWithDefaultValue.create("copilot.processing", false);
    private static final Key<Boolean> KEY_EDITOR_SUPPORTED = Key.create("copilot.editorSupported");

    private static final Key<Boolean> KEY_NO_CONSIST_CREATE = Key.create("copilot.noConsistCreate"); // 用于标记不再继续创建新的请求

    static final Key<Boolean> KEY_DOCUMENT_SAVE_VETO = Key.create("copilot.docSaveVeto");
    private static final int REQUEST_DELAY_MILLIS = 75;
    private static final Set<String> COMMAND_BLACKLIST = Set.of("Expand Live Template by Tab");
    protected final CancellableAlarm requestAlarm = new CancellableAlarm(this);

    private static final Key<ProjectEditorSession> KEY_EDITOR_SESSION = Key.create("copilot.editorSession");

    public CodeWizEditorManagerImpl() {
    }

    @RequiresEdt
    public void setNoConsistCreate(Editor editor, boolean value) {
        KEY_NO_CONSIST_CREATE.set(editor, value);
    }

    private boolean isNoConsistCreate(Editor editor) {
        return KEY_NO_CONSIST_CREATE.get(editor, false);
    }


    @RequiresEdt
    public boolean isAvailable(@NotNull Editor editor) {
        Boolean isAvailable = KEY_EDITOR_SUPPORTED.get(editor);
        if (isAvailable == null) {
            isAvailable = !(editor instanceof EditorWindow) && !(editor instanceof ImaginaryEditor) && (!(editor instanceof EditorEx) || !((EditorEx)editor).isEmbeddedIntoDialogWrapper()) && !editor.isViewer() && !editor.isOneLineMode() && PluginUtil.isSupportedIDE(editor.getProject()) && CodewizCompletionService.getInstance().isAvailable(editor);
            KEY_EDITOR_SUPPORTED.set(editor, isAvailable);
        }

        return isAvailable && !editor.isDisposed();
    }

    @RequiresEdt
    public void disposeInlays(@NotNull Editor editor, @NotNull InlayDisposeContext disposeContext) {
        disposeInlays(editor, disposeContext, null);
    }

    @RequiresEdt
    public void disposeInlays(@NotNull Editor editor, @NotNull InlayDisposeContext disposeContext, String eventName) {
        if (!this.isAvailable(editor) || this.isProcessing(editor)) {
            return;
        }
        EditorRequestResultList request = KEY_LAST_REQUEST.get(editor);

        if (null != request) {
            if (disposeContext.isResetLastRequest()) {
                KEY_LAST_REQUEST.set(editor, null);
            }
            //尝试发送拒绝Telemetry
            sendRejectedTelemetry(editor, request, disposeContext, eventName);
        }

        if (request == null || request.getRequest().getOffset() != editor.getCaretModel().getOffset()) {
            this.cancelCompletionRequests(editor);
        }

        this.wrapProcessing(editor, () -> {
            this.disposeInlays(this.collectInlays(editor, 0, editor.getDocument().getTextLength()));
        });
    }

    private void sendRejectedTelemetry(@NotNull Editor editor, EditorRequestResultList request, @NotNull InlayDisposeContext disposeContext, String eventName) {
        //请求无效 或者 不存在待补全 或者 没有上屏，则不需要触发拒绝/取消动作
        if (null == request || request.getCurrentCompletion() == null
            || !((AgentEditorRequest) request.getRequest()).getCompletionStatus().equals(
            CompletionStatusEnum.SHOWN)) {
            return;
        }
        if (disposeContext.equals(InlayDisposeContext.Applied)) {
            //采纳不需要拒绝上报
            return;
        }
        //凡是上屏，且进入销毁流程的，都需要触发拒绝动作
        this.sendRejectedTelemetry(request, editor, eventName);
        //拒绝销毁场景且命令事件不为空，需要触发拒绝动作
        /*if (disposeContext.isSendRejectedTelemetry() && StringUtils.isNotBlank(eventName)) {
            //存在补全，并且执行了非采纳事件，需要触发拒绝
            this.sendRejectedTelemetry(request, editor, eventName);
            return;
        }*/
        //非拒绝的销毁，需要触发上屏后取消动作
        /*if (((AgentEditorRequest) request.getRequest()).getCompletionStatus().equals(CompletionStatusEnum.SHOWN)
            && disposeContext.isSendCancelAfterShownTelemetry()) {
            this.sendCancelAfterShownTelemetry(request, editor, eventName);
        }*/
    }
    @RequiresEdt
    @Override
    public void editorModified(@NotNull Editor editor, int offset, @NotNull EditEventTriggerKind requestType) {
        LogContextUtil.get().setSessionId(getEditorSessionId(editor));
        LoggerUtil.INSTANCE.logDebug(LOG, "editorModified");

        Project project = editor.getProject();
        if (project == null) {
            return;
        }
        EditorRequestResultList lastRequest = KEY_LAST_REQUEST.get(editor);
        if (this.isDuplicateRequest(editor.getDocument(), offset, requestType, lastRequest)) {
            LoggerUtil.INSTANCE.logDebug(LOG, "Ignoring duplicate editorModified request");
            return;
        }
        if (this.isLookupUnsupported(requestType, editor)) {
            LoggerUtil.INSTANCE.logDebug(LOG, "completion disabled because of a visible popup");
            return;
        }
        this.cancelCompletionRequests(editor);
        if (this.isBlacklistedCommand()) {
            return;
        }
        if (this.isUnsupportedEditorState(editor)) {
            return;
        }
        //后边开发了配置之后可以打开
        if (EditEventTriggerKind.Automatic.equals(requestType) && !CodeWizApplicationSettings.isCodeWizEnabled(project, editor)) {
            this.disposeInlays(editor, InlayDisposeContext.Typing);
            return;
        }
        //后边接了登陆以后可以打开
        /*if (CodewizStatusService.getCurrentStatus().getFirst() == CodewizStatus.NotSignedIn
            && CodewizApplicationSettings.settings().enableCompletions) {
            LoggerUtil.INSTANCE.logDebug(LOG, "Completions unavailable because user isn't authorized for codewiz");
            //未登录不可使用代码补全
            CodeWizCompetionActionUtil.disabledCodewiz(project, editor);
            return;
        }*/
        EditorRequest request = CodewizCompletionService.getInstance().createRequest(editor, offset, CompletionType.GhostText, requestType);
        if (request == null) {
            return;
        }
        boolean debounce = this.shouldDebounce(editor, offset, lastRequest);

        this.disposeInlays(editor, debounce ? InlayDisposeContext.Typing : InlayDisposeContext.TypingAsSuggested);

        CodeWizEditorUtil.addEditorRequest(editor, request);
        KEY_LAST_REQUEST.set(editor, new EditorRequestResultList(request));

        /*boolean typingAsSuggestedCompletionIsShown = this.considerShowingTypingAsSuggestedInlays(editor, requestType, lastRequest, request);
        if (!typingAsSuggestedCompletionIsShown) {
            this.sendRejectedTelemetry(lastRequest);
            this.disposeInlays(editor, InlayDisposeContext.Typing);
        }*/
        if (CodewizEditorSupport.isEditorCompletionsSupported(editor)) {
            this.queueCompletionRequest(editor, request, (Integer)null, false, (first) -> {
                this.insertInlays(first, request, editor, true, InlayDisposeContext.Typing);
            }, debounce);
        }
    }
    private void syncCheckConnect() {

    }

    boolean shouldDebounce(@NotNull Editor editor, int offset, EditorRequestResultList lastRequest) {
        try {
            if (lastRequest != null) {
                @Nullable CodewizInlayList completion = lastRequest.getCurrentCompletion();
                int lastOffset = lastRequest.getRequest().getOffset();
                if (completion != null && offset > lastOffset) {
                    CodewizCompletion copilot = completion.getCodewizCompletion();
                    String completionText = copilot.getCompletion().stream().reduce("", (a, b) -> a + b);
                    if (completionText.isEmpty()) {
                        return true;
                    }
                    String typed = editor.getDocument().getText(TextRange.from((int)lastOffset, (int)(offset - lastOffset)));
                    return !completionText.startsWith(typed);
                }
            }
            return true;
        }
        catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "error computing debounce" + e);
            return true;
        }
    }
    public void cancelCompletionRequests(@NotNull Editor editor) {
        this.requestAlarm.cancelAllRequests();
        List<EditorRequest> requests = (List) CodeWizEditorUtil.KEY_REQUESTS.get(editor);
        if (requests != null && !requests.isEmpty()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "cancelCompletionRequests: " + requests.size());
            int count = requests.size();
            Iterator<EditorRequest> requestIterator = requests.iterator();

            while(requestIterator.hasNext()) {
                EditorRequest request = (EditorRequest)requestIterator.next();
                requestIterator.remove();
                request.cancel();
            }

            (ApplicationManager.getApplication().getMessageBus().syncPublisher(
                EditorRequestsCancelledMessage.TOPIC)).requestsCancelled(count);
        }
    }

    @Override
    public int countCompletionInlays(@NotNull Editor editor, @NotNull TextRange searchRange, boolean inlineInlays,
        boolean afterLineEndInlays, boolean blockInlays, boolean matchInLeadingWhitespace) {
        if (!this.isAvailable(editor)) {
            return 0;
        } else {
            int startOffset = searchRange.getStartOffset();
            int endOffset = searchRange.getEndOffset();
            InlayModel inlayModel = editor.getInlayModel();
            int totalCount = 0;
            if (inlineInlays) {
                totalCount = (int)((long)totalCount + inlayModel.getInlineElementsInRange(startOffset, endOffset).stream().filter((inlay) -> {
                    if (!(inlay.getRenderer() instanceof CodeWizInlayRenderer)) {
                        return false;
                    } else if (matchInLeadingWhitespace) {
                        return true;
                    } else {
                        List<String> lines = ((CodeWizInlayRenderer)inlay.getRenderer()).getContentLines();
                        if (lines.isEmpty()) {
                            return false;
                        } else {
                            int whitespaceEnd = inlay.getOffset() + CodewizStringUtil.leadingWhitespaceLength((String)lines.get(0));
                            return searchRange.getEndOffset() >= whitespaceEnd;
                        }
                    }
                }).count());
            }

            if (blockInlays) {
                totalCount = (int)((long)totalCount + inlayModel.getBlockElementsInRange(startOffset, endOffset).stream().filter((inlay) -> {
                    return inlay.getRenderer() instanceof CodeWizInlayRenderer;
                }).count());
            }

            if (afterLineEndInlays) {
                totalCount = (int)((long)totalCount + inlayModel.getAfterLineEndElementsInRange(startOffset, endOffset).stream().filter((inlay) -> {
                    return inlay.getRenderer() instanceof CodeWizInlayRenderer;
                }).count());
            }

            return totalCount;
        }
    }

    @Override
    public boolean applyCompletion(@NotNull Editor editor, CodeWizApplyInlayStrategy applyStrategy) {
        LogContextUtil.get().setSessionId(getEditorSessionId(editor));
        if (editor.isDisposed()) {
            LoggerUtil.INSTANCE.logWarn(LOG, "editor already disposed");
            return false;
        }
        Project project = editor.getProject();
        if (project == null || project.isDisposed()) {
            LoggerUtil.INSTANCE.logWarn(LOG, "project disposed or null: " + project);
            return false;
        }

        if (this.isProcessing(editor)) {
            LoggerUtil.INSTANCE.logWarn(LOG, "can't apply inlays while processing");
            return false;
        }
        EditorRequestResultList request = KEY_LAST_REQUEST.get(editor);
        if (request == null) {
            return false;
        }
        CodewizInlayList currentCompletion = request.getCurrentCompletion();
        if (currentCompletion == null) {
            return false;
        }
        if (applyStrategy == CodeWizApplyInlayStrategy.WHOLE) {
            this.disposeInlays(editor, InlayDisposeContext.Applied);
        } else {
            this.setNoConsistCreate(editor, true);
            this.disposeInlays(editor, InlayDisposeContext.TypingAsSuggested);
        }
        this.applyCompletion(project, editor, request.getRequest(), currentCompletion, applyStrategy);
        return true;
    }

    @Override
    public void applyCompletion(@NotNull Project project, @NotNull Editor editor,
        @NotNull EditorRequest request, @NotNull CodewizInlayList completion,
        @NotNull CodeWizApplyInlayStrategy applyStrategy) {
        TextRange range = completion.getReplacementRange();
        String text = (new CompletionSplitter(applyStrategy)).split(completion);
        //如果当前采纳的内容和补全返回一致，则直接按照Whole触发采纳:CopilotCommandListener.ignoreCommands
        if (StringUtils.equals(text, completion.getReplacementText())) {
            //此时需要按照全采纳去清理一下
            this.disposeInlays(editor, InlayDisposeContext.Applied);
            applyStrategy = CodeWizApplyInlayStrategy.WHOLE;
        }
        WriteCommandAction.runWriteCommandAction(project, "Apply CodeWiz RCS Suggestion:" + applyStrategy, "CodeWiz", () -> {
            if (project.isDisposed()) {
                return;
            }
            Document document = editor.getDocument();
            try {
                KEY_DOCUMENT_SAVE_VETO.set(document, true);
                this.wrapWithTemporarySaveVetoHandler(() -> {
                    if (range.getEndOffset() <= document.getTextLength()) {
                        document.replaceString(range.getStartOffset(), range.getEndOffset(), text);
                        editor.getCaretModel().moveToOffset(range.getStartOffset() + text.length());
                        editor.getScrollingModel().scrollToCaret(ScrollType.MAKE_VISIBLE);
                    } else {
                        LoggerUtil.INSTANCE.logWarn(LOG, "Attempt to apply completions out of document bounds: " + range + ", document: " + document.getTextLength());
                    }

                });
            } finally {
                KEY_DOCUMENT_SAVE_VETO.set(document, null);
            }
        }, new PsiFile[0]);

        //修改状态为已采纳
        ((AgentEditorRequest) request).setCompletionStatus(CompletionStatusEnum.ACCEPTED);

        //只有当采纳方式看做为Whole时，才进行取消缓存(逐行&&逐字符需要使用缓存返回)
        if (applyStrategy == CodeWizApplyInlayStrategy.WHOLE) {
            LSPManager.getInstance().notifyCancelCache(editor);
        }
        //开始上报上报采纳事件
        try {
            String uuid = ((AgentCompletion) completion.getCodewizCompletion()).getAgentData().getUuid();
            NotifyAcceptedCommand command = new NotifyAcceptedCommand(getEditorSessionId(editor), uuid, text.length(), applyStrategy.name(), range.getStartOffset());
            command.setStartLine(editor.getDocument().getLineNumber(range.getStartOffset()));
            int acceptEndLine = editor.getDocument().getLineNumber(range.getStartOffset() + text.length());
            command.setLineCount(acceptEndLine - command.getStartLine() + 1);
            Pair<String, String> content = getBeforeAndAfter4Accepted(editor.getDocument(), range.getStartOffset(), range.getStartOffset() + text.length());
            command.setBeforeContent(content.getLeft());
            command.setAfterContent(content.getRight());
            command.setAcceptContent(text);
            CodewizCompletionService.getInstance().sendAcceptedTelemetry(command);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "sendAcceptedTelemetry error", e);
        }
    }
    private Pair<String, String> getBeforeAndAfter4Accepted(Document document, int startOffset, int endOffset) {
        try {
            String beforeText = "", afterText = "";
            int line = document.getLineNumber(startOffset); //获取采纳开始的行号
            int lineCount = document.getLineCount();
            if (line > 0) { //补全在首行的话，那么没有上N行的内容
                int beforeStartLine = Math.max(0, line - 3); //获取上N行的第一行
                int beforeEndLine = Math.max(0, line - 1); //获取上N行的最后一行
                int beforeStart = document.getLineStartOffset(beforeStartLine);
                int beforeEnd = document.getLineEndOffset(beforeEndLine);
                beforeText = document.getText(new TextRange(beforeStart, beforeEnd));
            }

            line = document.getLineNumber(endOffset); //获取采纳结束的行号
            if (line < lineCount - 1) {
                int afterBeforeLine = Math.min(lineCount - 1, line + 1);
                int afterEndLine = Math.min(lineCount - 1, line + 3);
                int afterStart = document.getLineStartOffset(afterBeforeLine);
                int afterEnd = document.getLineEndOffset(afterEndLine);
                afterText = document.getText(new TextRange(afterStart, afterEnd));
            }
            return Pair.of(beforeText, afterText);
        } catch (Throwable var4) {
            return Pair.of("", "");
        }
    }
    public void dispose() {
    }

    @RequiresEdt
    public @NotNull List<CodeWizInlayRenderer> collectInlays(@NotNull Editor editor, int startOffset, int endOffset) {
        InlayModel model = editor.getInlayModel();
        ArrayList<Inlay<?>> inlays = new ArrayList();
        inlays.addAll(model.getInlineElementsInRange(startOffset, endOffset));
        inlays.addAll(model.getAfterLineEndElementsInRange(startOffset, endOffset));
        inlays.addAll(model.getBlockElementsInRange(startOffset, endOffset));
        ArrayList<CodeWizInlayRenderer> renderers = new ArrayList();
        Iterator var7 = inlays.iterator();

        while(var7.hasNext()) {
            Inlay<?> inlay = (Inlay)var7.next();
            if (inlay.getRenderer() instanceof CodeWizInlayRenderer) {
                renderers.add((CodeWizInlayRenderer)inlay.getRenderer());
            }
        }

        return renderers;
    }

    public boolean hasNextInlaySet(@NotNull Editor editor) {
        EditorRequestResultList request = (EditorRequestResultList)KEY_LAST_REQUEST.get(editor);
        if (request == null) {
            return false;
        } else {
            CodewizCompletionService service = CodewizCompletionService.getInstance();
            return request.hasNext() || service.isSupportingOnDemandCycling(editor) && !request.hasOnDemandCompletions();
        }
    }

    public boolean hasPreviousInlaySet(@NotNull Editor editor) {
        EditorRequestResultList request = (EditorRequestResultList)KEY_LAST_REQUEST.get(editor);
        return request != null && request.hasPrev();
    }

    public void showNextInlaySet(@NotNull Editor editor) {
        EditorRequestResultList request = KEY_LAST_REQUEST.get(editor);
        if (request == null) {
            LoggerUtil.INSTANCE.logDebug(LOG, "current request unavailable");
        } else {
            CodewizInlayList set = request.getNextCompletion();
            if (set != null) {
                this.insertInlays(set, request.getRequest(), editor, true, InlayDisposeContext.Cycling);
            } else {
                CodewizCompletionService service = CodewizCompletionService.getInstance();
                if (service.isSupportingOnDemandCycling(editor) && this.isActiveRequest(request.getRequest(), editor)) {
                    request.setHasOnDemandCompletions();
                    this.fetchOnDemandCompletions(editor, request);
                }

            }
        }
    }

    public void showPreviousInlaySet(@NotNull Editor editor) {
        EditorRequestResultList request = (EditorRequestResultList)KEY_LAST_REQUEST.get(editor);
        if (request != null) {
            CodewizInlayList set = request.getPrevCompletion();
            if (set != null) {
                this.insertInlays(set, request.getRequest(), editor, true, InlayDisposeContext.Cycling);
            }
        }
    }

    private void queueCompletionRequest(@NotNull Editor editor, @NotNull EditorRequest contentRequest, @Nullable Integer maxCompletions, boolean cycling, @Nullable Consumer<CodewizInlayList> onFirstCompletion, boolean debounceRequest) {
        this.requestAlarm.cancelAllAndAddRequest(() -> {
            if (!contentRequest.isCancelled()) {
                this.requestCodewizCompletions(editor, contentRequest, maxCompletions, cycling, onFirstCompletion);
            }
        }, 75);
    }

    @RequiresBackgroundThread
    private void requestCodewizCompletions(final @NotNull Editor editor, final @NotNull EditorRequest request, @Nullable Integer maxCompletions, boolean cycling, final @Nullable Consumer<CodewizInlayList> onFirstCompletion) {
        if (!CodeWizEditorUtil.isFocusedEditor(editor)) {
            LoggerUtil.INSTANCE.logDebug(LOG, "skipping completions for unfocused editor: " + editor);
            return;
        }
        //如果没有准备好，后台启动，本次不触发补全
        /*if (!RcsWebSocketManager.INSTANCE.checkWebsocketAgent(editor.getProject(), true)) {
            return;
        }*/
        //lingma不可用时需要重启，但是不等待
        if (!LspServiceForTylm.checkProjectConnectStatus(editor.getProject(), true)) {
            return;
        }
        final AtomicBoolean resetCompletions = new AtomicBoolean(cycling);
        CompletionExtraContext extraContext = getLingmaExtParam(editor);
        CodewizCompletionService.getInstance().fetchCompletionsFromLs(extraContext, request, maxCompletions, cycling, new Flow.Subscriber<List<CodewizInlayList>>() {
            private volatile Flow.Subscription subscription;
            private volatile boolean hasFirstCompletion;

            public void onSubscribe(Flow.Subscription subscription) {
                this.subscription = subscription;
                this.subscription.request(1L);
                Disposer.tryRegister(request.getDisposable(), this.subscription::cancel);
            }

            public void onNext(List<CodewizInlayList> inlaySets) {
                EditorRequestResultList stored;
                LoggerUtil.INSTANCE.logDebug(LOG, "received inlay");
                if (!CodeWizEditorManagerImpl.this.isActiveRequest(request, editor)) {
                    LoggerUtil.INSTANCE.logDebug(LOG, "skipping inlay because request already cancelled");
                    return;
                }
                if (resetCompletions.compareAndSet(true, false)) {
                    stored = CodeWizEditorManagerImpl.KEY_LAST_REQUEST.get(editor);
                    if (stored != null) {
                        stored.resetInlays();
                    }
                }
                if (!CodeWizEditorManagerImpl.this.addInlays(editor, inlaySets)) {
                    LoggerUtil.INSTANCE.logDebug(LOG, "failed to add inlays");
                    return;
                }
                this.subscription.request(1L);
                (ApplicationManager.getApplication().getMessageBus().syncPublisher(
                    InlaysReceivedMessage.TOPIC)).inlaysReceived(request, inlaySets);
                if (!this.hasFirstCompletion && onFirstCompletion != null && !inlaySets.isEmpty()) {
                    this.hasFirstCompletion = true;
                    CodewizInlayList firstSet = inlaySets.get(0);

                    assert firstSet != null && !firstSet.isEmpty();

                    ApplicationManager.getApplication().invokeLater(() -> {
                        onFirstCompletion.accept(firstSet);
                    });
                }
            }

            public void onError(Throwable throwable) {
                throwable.printStackTrace();
                if (LOG.isTraceEnabled() || ApplicationManager.getApplication().isUnitTestMode()) {
                    LoggerUtil.INSTANCE.logDebug(LOG, "onError", throwable);
                } else if (LOG.isDebugEnabled()) {
                    LoggerUtil.INSTANCE.logDebug(LOG, "onError: " + throwable.getMessage());
                }
            }

            public void onComplete() {
            }
        });
    }
    private CompletionExtraContext getLingmaExtParam(Editor editor) {
        try {
            boolean isComment = false;
            PsiElement element = CodeWizPsiUtil.getCaratElement(editor);
            if (element != null && CodeWizPsiUtil.isCommentElement(element, editor)) {
                isComment = true;
            }
            //普通代码delay250ms，comment delay500ms：lingma同款逻辑
            long delay = isComment ? Features.COMPLETION_AUTO_COMMENT_DELAY.longValue()
                : Features.COMPLETION_AUTO_DELAY.longValue();
            Measurements measurements = Measurements.build(editor);
            measurements.setCompletionDelayMs(delay);
            Document document = editor.getDocument();
            int offset = CodeWizEditorUtil.getCaretOffset(editor);
            String lineSuffixCode = document.getText(TextRange.create(offset, document.getLineEndOffset(document.getLineNumber(offset))));
            int tabWidth = ReadAction.compute(() -> {
                return editor.getSettings().getTabSize(editor.getProject());
            });
            return new CompletionExtraContext(getEditorSessionId(editor), isComment,
                "document-change", measurements, lineSuffixCode, tabWidth);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "getLingmaExtParam err, msg = " + e);
        }
        return null;
    }
    private void insertInlays(@NotNull CodewizInlayList inlays, @NotNull EditorRequest request, @NotNull Editor editor, boolean disposeExistingInlays, @NotNull InlayDisposeContext disposeContext) {
        LogContextUtil.get().setSessionId(getEditorSessionId(editor));
        if (!this.isActiveRequest(request, editor)) {
            LoggerUtil.INSTANCE.logDebug(LOG, "skipping insertion of inlay because request was cancelled");
        } else {
            if (LOG.isDebugEnabled()) {
                int var10001 = RequestId.currentRequestId();
                LoggerUtil.INSTANCE.logDebug(LOG, "inserting completion inlay into editor, current request ID: " + var10001 + ", request ID: " + request.getRequestId() + ", caret: " + editor.getCaretModel().getOffset() + ", request offset: " + request.getOffset());
            }

            if (!CodeWizEditorUtil.isFocusedEditor(editor)) {
                LoggerUtil.INSTANCE.logDebug(LOG, "Not inserting inlays into editor without focus.");
            } else {
                this.doInsertInlays(inlays, request, editor, disposeExistingInlays, disposeContext);
            }
        }
    }

    private void  doInsertInlays(@NotNull CodewizInlayList inlays, @NotNull EditorRequest request, @NotNull Editor editor, boolean disposeExistingInlays, @NotNull InlayDisposeContext context) {
        if (disposeExistingInlays) {
            this.disposeInlays(editor, context);
        }
        if (LookupManager.getActiveLookup(editor) != null) {
            return;
        }
        if (StringUtils.isEmpty(inlays.getReplacementText())) {
            return;
        }
        ArrayList<Inlay<CodeWizInlayRenderer>> insertedInlays = new ArrayList();
        InlayModel inlayModel = editor.getInlayModel();
        int index = 0;
        int shownLineCount = 0;
        Iterator var9 = inlays.iterator();

        while(var9.hasNext()) {
            CodewizEditorInlay inlay = (CodewizEditorInlay)var9.next();
            if (!inlay.isEmptyCompletion()) {
                CodeWizDefaultInlayRenderer renderer = new CodeWizDefaultInlayRenderer(editor, request, inlay.getType(), inlay.getLines());
                Inlay<CodeWizInlayRenderer> editorInlay = null;
                switch (inlay.getType()) {
                    case Inline:
                        editorInlay = inlayModel.addInlineElement(inlay.getEditorOffset(), true, Integer.MAX_VALUE - index, renderer);
                        break;
                    case AfterLineEnd:
                        editorInlay = inlayModel.addAfterLineEndElement(inlay.getEditorOffset(), true, renderer);
                        break;
                    case Block:
                        editorInlay = inlayModel.addBlockElement(inlay.getEditorOffset(), true, false, Integer.MAX_VALUE - index, renderer);
                }

                if (editorInlay != null) {
                    renderer.setInlay(editorInlay);
                }

                insertedInlays.add(editorInlay);
                ++index;
                if (CollectionUtils.isNotEmpty(inlay.getLines())) {
                    shownLineCount += inlay.getLines().size();
                }
            }
        }

        if (context != InlayDisposeContext.TypingAsSuggested && shownLineCount > 0) {
            int start = inlays.getReplacementRange().getStartOffset();
            int end = start + inlays.getReplacementText().length();
            String filePath = "";
            if (null != editor.getVirtualFile()) {
                filePath = editor.getVirtualFile().getUrl();
            }
            CodewizCompletionService.getInstance().sendShownTelemetry(inlays.getCodewizCompletion(), getEditorSessionId(editor), start, end, shownLineCount, inlays.getReplacementText(), filePath);
            ((AgentEditorRequest) request).setCompletionStatus(CompletionStatusEnum.SHOWN);
        }

        LoggerUtil.INSTANCE.logDebug(LOG, "publishing inlaysUpdated");
        ApplicationManager.getApplication().getMessageBus().syncPublisher(CodewizInlayListener.TOPIC).inlaysUpdated(request, context, editor, insertedInlays);
    }

    private boolean isProcessing(@NotNull Editor editor) {
        return (Boolean)KEY_PROCESSING.get(editor);
    }

    private void disposeInlays(List<CodeWizInlayRenderer> renderers) {
        LoggerUtil.INSTANCE.logDebug(LOG, "Disposing inlays: " + renderers.size());
        Iterator var2 = renderers.iterator();

        while(var2.hasNext()) {
            CodeWizInlayRenderer renderer = (CodeWizInlayRenderer)var2.next();
            Inlay<CodeWizInlayRenderer> inlay = renderer.getInlay();
            if (inlay != null) {
                Disposer.dispose(inlay);
            }
        }

    }

    private void wrapProcessing(@NotNull Editor editor, @NotNull Runnable block) {
        assert !(Boolean)KEY_PROCESSING.get(editor);

        try {
            KEY_PROCESSING.set(editor, true);
            block.run();
        } finally {
            KEY_PROCESSING.set(editor, null);
        }

    }

    private boolean isActiveRequest(@NotNull EditorRequest request, @NotNull Editor editor) {
        if (request.getRequestId() != RequestId.currentRequestId()) {
            return false;
        } else {
            EditorRequestResultList stored = (EditorRequestResultList)KEY_LAST_REQUEST.get(editor);
            return stored != null && stored.getRequest().equalsRequest(request);
        }
    }

    private boolean addInlays(@NotNull Editor editor, @NotNull List<CodewizInlayList> inlaySets) {
        EditorRequestResultList stored = (EditorRequestResultList) KEY_LAST_REQUEST.get(editor);
        if (stored != null) {
            for (CodewizInlayList inlays : inlaySets) {
                stored.addInlays(inlays);
            }
        }

        return stored != null;
    }

    private boolean addInlaysToFirst(@NotNull Editor editor, @NotNull List<CodewizInlayList> inlaySets) {
        List<CodewizInlayList> copy = new ArrayList<>(inlaySets);
        Collections.reverse(copy);
        inlaySets = copy;
        EditorRequestResultList stored = (EditorRequestResultList) KEY_LAST_REQUEST.get(editor);
        if (stored != null) {
            Iterator var4 = inlaySets.iterator();
            while (var4.hasNext()) {
                CodewizInlayList inlays = (CodewizInlayList) var4.next();
                stored.addInlaysToFirst(inlays);
            }
        }
        return stored != null;
    }


    private void fetchOnDemandCompletions(@NotNull Editor editor, @NotNull EditorRequestResultList request) {
        EditorRequest currentRequest = request.getRequest();
        LoggerUtil.INSTANCE.logDebug(LOG, "Fetching on-demand completions for cycling");
        this.queueCompletionRequest(editor, currentRequest, 2, true, (first) -> {
            CodewizInlayList nextSet = request.getNextCompletion();
            if (nextSet != null) {
                LoggerUtil.INSTANCE.logDebug(LOG, "Received first on-demand completion");
                this.insertInlays(nextSet, currentRequest, editor, true, InlayDisposeContext.Cycling);
            }
        }, true);
    }

    private void sendRejectedTelemetry(@Nullable EditorRequestResultList lastRequest, @NotNull Editor editor, String eventName) {
        if (lastRequest != null) {
            List<CodewizInlayList> allShownCompletion = lastRequest.getAllShownCompletion();
            if (allShownCompletion != null) {
                List<CodewizCompletion> shown = allShownCompletion.stream().filter(Objects::nonNull).map(CodewizInlayList::getCodewizCompletion).collect(Collectors.toList());
                CodewizCompletionService.getInstance().sendRejectedTelemetry(shown, getEditorSessionId(editor), eventName);
                ((AgentEditorRequest) lastRequest.getRequest()).setCompletionStatus(CompletionStatusEnum.REJECTED);
            }

        }
    }

    private boolean isDuplicateRequest(@NotNull Document document, int requestOffset, @NotNull EditEventTriggerKind requestType, @Nullable EditorRequestResultList lastRequest) {
        if (lastRequest == null || EditEventTriggerKind.Invoke.equals(requestType)) {
            return false;
        }
        if (lastRequest.getRequest().getOffset() != requestOffset) {
            return false;
        }
        long lastStamp = lastRequest.getRequest().getDocumentModificationSequence();
        return lastStamp == CodeWizEditorUtil.getDocumentModificationStamp(document);
    }

    private boolean isBlacklistedCommand() {
        String commandName = CommandProcessor.getInstance().getCurrentCommandName();
        return commandName != null && COMMAND_BLACKLIST.contains(commandName);
    }

    private boolean isUnsupportedEditorState(@NotNull Editor editor) {
        return editor.getCaretModel().getCaretCount() > 1 ? true : editor.getSelectionModel().hasSelection();
    }

    private boolean isLookupUnsupported(@NotNull EditEventTriggerKind requestType, @NotNull Editor editor) {
        return EditEventTriggerKind.Automatic.equals(requestType) && LookupManager.getActiveLookup(editor) != null;
    }

    private void wrapWithTemporarySaveVetoHandler(@NotNull Runnable runnable) {
        Disposable disposable = Disposer.newDisposable();

        try {
            ExtensionPoint<FileDocumentSynchronizationVetoer> extensionPoint = ApplicationManager.getApplication().getExtensionArea().getExtensionPoint(FileDocumentSynchronizationVetoer.EP_NAME);
            extensionPoint.registerExtension(new CodeWizEditorSaveVetoer(), disposable);
            runnable.run();
        } finally {
            Disposer.dispose(disposable);
        }
    }

    /**
     * 获取编辑会话ID
     * @param editor
     * @return
     */
    private String getEditorSessionId(@NotNull Editor editor) {
        ProjectEditorSession session = KEY_EDITOR_SESSION.get(editor.getProject());
        if (null == session) {
            session = new ProjectEditorSession();
            KEY_EDITOR_SESSION.set(editor.getProject(), session);
        }
        return session.getSessionId();
    }
}


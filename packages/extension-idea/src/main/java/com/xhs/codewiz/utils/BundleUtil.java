package com.xhs.codewiz.utils;


import com.intellij.DynamicBundle;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.PropertyKey;

public final class BundleUtil extends DynamicBundle {
    public static final BundleUtil INSTANCE = new BundleUtil();

    private BundleUtil() {
        super("codewiz");
    }

    public static String get(@NotNull @PropertyKey(
            resourceBundle = "codewiz"
    ) String key) {
        return INSTANCE.getMessage(key, new Object[0]);
    }

    public static String get(@NotNull @PropertyKey(
            resourceBundle = "codewiz"
    ) String key, Object... params) {
        return INSTANCE.getMessage(key, params);
    }
}


package com.xhs.codewiz.lang.agent;

import com.google.gson.JsonElement;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.util.text.StringUtil;
import com.xhs.codewiz.actions.status.CodeWizStatus;
import com.xhs.codewiz.actions.status.CodeWizStatusService;
import com.xhs.codewiz.lang.agent.notifications.StatusNotification;
import com.xhs.codewiz.lang.agent.rpc.JsonRPC;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import org.jetbrains.annotations.NotNull;

class StatusChangeNotificationListener implements JsonRpcNotificationListener {
    private static final Logger AGENT_LOG = Logger.getInstance("#codewiz");

    StatusChangeNotificationListener() {
    }

    public boolean handleMessage(@NotNull String name, @NotNull JsonElement message) {
        if (!"statusNotification".equals(name)) {
            return false;
        } else {
            StatusNotification status =  JsonRPC.parseResponse(message, StatusNotification.class);
            StatusNotification.Status notifyStatus = status.getStatus();
            AGENT_LOG.debug("StatusChange " + notifyStatus + ": " + status.getMessage());
            CodeWizStatus pluginStatus;
            switch (status.getStatus()) {
                case Normal:
                    pluginStatus = CodeWizStatus.Ready;
                    break;
                case InProgress:
                    pluginStatus = CodeWizStatus.CompletionInProgress;
                    break;
                case Warning:
                    pluginStatus = CodeWizStatus.AgentWarning;
                    break;
                case Error:
                    pluginStatus = CodeWizStatus.AgentError;
                    break;
                default:
                    throw new IllegalStateException("Unexpected status: " + status.getStatus());
            }

            CodeWizStatusService.notifyApplication(pluginStatus, StringUtil.nullize(status.getMessage()));
            return true;
        }
    }
}


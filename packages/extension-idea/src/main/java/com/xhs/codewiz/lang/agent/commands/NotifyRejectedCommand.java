package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public final class NotifyRejectedCommand implements JsonRpcCommand<String> {
    @SerializedName("sessionId")
    private final @NotNull String sessionId;
    @SerializedName("uuids")
    private final @NotNull List<String> uuids;
    private final String eventName;
    private final long timestamp;

    public @NotNull String getCommandName() {
        return "notifyRejected";
    }

    public @NotNull Class<String> getResponseType() {
        return String.class;
    }

    public @NotNull List<String> getUuids() {
        return this.uuids;
    }

    public @NotNull String getSessionId() {
        return this.sessionId;
    }
    public @NotNull String getEventName() {
        return this.eventName;
    }
    public long getTimestamp() {
        return this.timestamp;
    }

    public NotifyRejectedCommand(@NotNull String sessionId, @NotNull List<String> uuids, String eventName, long timestamp) {
        this.sessionId = sessionId;
        this.uuids = uuids;
        this.eventName = eventName;
        this.timestamp = timestamp;
    }
}


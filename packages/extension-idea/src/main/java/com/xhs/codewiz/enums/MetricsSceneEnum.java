package com.xhs.codewiz.enums;

/**
 * Author: liukunpeng Date: 2025-04-23 Description:
 */
public enum MetricsSceneEnum {
  COMPLETE(1, "complete", "补全"),
  EDITOR(2, "editor", "编辑动作"),
  CHAT(3, "chat", "智能问答"),
  PLUGIN(4, "plugin", "插件侧上报");

  private final Integer type;
  private final String name;
  private final String desc;

  public Integer getType() {
    return type;
  }

  public String getDesc() {
    return desc;
  }

  public String getName() {
    return name;
  }

  MetricsSceneEnum(Integer type, String name, String desc) {
    this.type = type;
    this.name = name;
    this.desc = desc;
  }
  public static MetricsSceneEnum getByType(Integer type) {
    for (MetricsSceneEnum metricsSceneEnum : MetricsSceneEnum.values()) {
      if (metricsSceneEnum.getType().equals(type)) {
        return metricsSceneEnum;
      }
    }
    return null;
  }
  public static MetricsSceneEnum getByName(String name) {
    for (MetricsSceneEnum metricsSceneEnum : MetricsSceneEnum.values()) {
      if (metricsSceneEnum.getName().equals(name)) {
        return metricsSceneEnum;
      }
    }
    return null;
  }
}

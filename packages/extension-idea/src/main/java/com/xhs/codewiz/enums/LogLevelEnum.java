package com.xhs.codewiz.enums;

/**
 * Author: liukunpeng Date: 2025-07-02 Description:
 */
public enum LogLevelEnum {
    DEBUG(0, "DEBUG"),
    INFO(1, "INFO"),
    WARN(2, "WARN"),
    ERROR(3, "ERROR");

    private int level;
    private String name;

    LogLevelEnum(int level, String name) {
        this.level = level;
        this.name = name;
    }

    public int getLevel() {
        return level;
    }

    public String getName() {
        return name;
    }
}

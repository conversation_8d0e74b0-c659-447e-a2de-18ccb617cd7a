package com.xhs.codewiz.update;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import java.util.List;

public class Plugins {

    @JacksonXmlElementWrapper(useWrapping = false)
    @JacksonXmlProperty(localName = "plugin")
    private List<Plugin> pluginList;

    public List<Plugin> getPluginList() {
        return pluginList;
    }

    public void setPluginList(List<Plugin> pluginList) {
        this.pluginList = pluginList;
    }

    public static class Plugin {

        @JacksonXmlProperty(isAttribute = true)
        private String id;

        @JacksonXmlProperty(isAttribute = true)
        private String url;

        @JacksonXmlProperty(isAttribute = true)
        private String version;

        @JacksonXmlProperty(localName = "require-restart", isAttribute = true)
        private boolean requireRestart;

        @JacksonXmlProperty(localName = "automatic-update", isAttribute = true)
        private boolean automaticUpdate;

        private IdeaVersion ideaVersion;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }


        public boolean getRequireRestart() {
            return requireRestart;
        }

        public void setRequireRestart(boolean requireRestart) {
            this.requireRestart = requireRestart;
        }


        public boolean getAutomaticUpdate() {
            return automaticUpdate;
        }

        public void setAutomaticUpdate(boolean automaticUpdate) {
            this.automaticUpdate = automaticUpdate;
        }


        public IdeaVersion getIdeaVersion() {
            return ideaVersion;
        }

        public void setIdeaVersion(IdeaVersion ideaVersion) {
            this.ideaVersion = ideaVersion;
        }
    }

    public static class IdeaVersion {

        @JacksonXmlProperty(localName = "since-build", isAttribute = true)
        private String sinceBuild;

        public String getSinceBuild() {
            return sinceBuild;
        }

        public void setSinceBuild(String sinceBuild) {
            this.sinceBuild = sinceBuild;
        }
    }
}

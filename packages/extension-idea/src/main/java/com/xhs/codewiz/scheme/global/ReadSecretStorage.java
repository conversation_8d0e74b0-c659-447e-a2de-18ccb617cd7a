package com.xhs.codewiz.scheme.global;

import java.util.*;
/**
 * 读取全局敏感信息存储
 */
public class ReadSecretStorage {
    private String schemaProtocol = "global.read.secretstorage";
    /** Key */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

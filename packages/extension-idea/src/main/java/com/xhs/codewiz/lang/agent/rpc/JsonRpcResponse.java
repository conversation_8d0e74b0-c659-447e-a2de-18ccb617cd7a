package com.xhs.codewiz.lang.agent.rpc;

import com.google.gson.JsonElement;

public final class JsonRpcResponse {
    private final Integer requestId;

    public JsonRpcResponse( Integer requestId,  String notificationName,  JsonElement response) {
        if (response == null) throw new NullPointerException("response is marked non-null but is null");
        this.requestId = requestId;
        this.notificationName = notificationName;
        this.response = response;
    }

    private final String notificationName;
    private final JsonElement response;

    public Integer getRequestId() {
        return this.requestId;
    }

    public String getNotificationName() {
        return this.notificationName;
    }

    public JsonElement getResponse() {
        return this.response;
    }

    public boolean isNotification() {
        return (this.requestId == null && this.notificationName != null);
    }

    public boolean isServerRequest() {
        return (this.requestId != null && this.notificationName != null);
    }
}



package com.xhs.codewiz.editor;

import com.intellij.codeInsight.editorActions.TypedHandlerDelegate;
import com.intellij.openapi.command.CommandProcessor;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.psi.PsiFile;
import org.jetbrains.annotations.NotNull;

public class TypeOverHandler extends TypedHandlerDelegate {
    private static final Key<Long> TYPE_OVER_STAMP = Key.create("copilot.typeOverStamp");

    public TypeOverHandler() {
    }

    public static boolean getPendingTypeOverAndReset(@NotNull Editor editor) {
        Long stamp = (Long)TYPE_OVER_STAMP.get(editor);
        if (stamp == null) {
            return false;
        } else {
            TYPE_OVER_STAMP.set(editor, null);
            return stamp == editor.getDocument().getModificationStamp();
        }
    }

    @NotNull
    public TypedHandlerDelegate.Result beforeCharTyped(char c, @NotNull Project project, @NotNull Editor editor, @NotNull PsiFile file, @NotNull FileType fileType) {
        boolean validTypeOver = c == ')' || c == ']' || c == '}' || c == '"' || c == '\'' || c == '>' || c == ';';
        if (validTypeOver && CommandProcessor.getInstance().getCurrentCommand() != null) {
            TYPE_OVER_STAMP.set(editor, editor.getDocument().getModificationStamp());
        } else {
            TYPE_OVER_STAMP.set(editor, null);
        }

        return Result.CONTINUE;
    }
}

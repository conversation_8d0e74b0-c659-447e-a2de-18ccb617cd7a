package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.Range;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public class GetCompletionsResult {
    @SerializedName("completions")
    @NotNull List<Completion> completions;

    public GetCompletionsResult(@NotNull List<Completion> completions) {
        this.completions = completions;
    }

    public @NotNull List<Completion> getCompletions() {
        return this.completions;
    }

    public void setCompletions(@NotNull List<Completion> completions) {
        this.completions = completions;
    }

    public static final class Completion {
        @SerializedName("uuid")
        private final @NotNull String uuid;
        @SerializedName("text")
        private final @NotNull String text;
        @SerializedName("range")
        private final @NotNull Range range;
        @SerializedName("displayText")
        private final @NotNull String displayText;
        @SerializedName("position")
        private final @NotNull Position position;
        @SerializedName("docVersion")
        private final int docVersion;

        public Completion(@NotNull String uuid, @NotNull String text, @NotNull Range range, @NotNull String displayText, @NotNull Position position, int docVersion) {
            this.uuid = uuid;
            this.text = text;
            this.range = range;
            this.displayText = displayText;
            this.position = position;
            this.docVersion = docVersion;
        }

        public @NotNull String getUuid() {
            return this.uuid;
        }

        public @NotNull String getText() {
            return this.text;
        }

        public @NotNull Range getRange() {
            return this.range;
        }

        public @NotNull String getDisplayText() {
            return this.displayText;
        }

        public @NotNull Position getPosition() {
            return this.position;
        }

        public int getDocVersion() {
            return this.docVersion;
        }
    }
}


package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 终端状态
 */
public class UpdateTerminalState {
    private String schemaProtocol = "platform.update.terminalstate";
    private UpdateTerminalStateParams params;

    public UpdateTerminalStateParams getParams() {
        return params;
    }
    public void setParams(UpdateTerminalStateParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateTerminalStateParams {
        /** 要更新的终端 ID */
        private String terminal;
        private UpdateTerminalStateParamsState state;
    
        public String getTerminal() {
            return terminal;
        }
        public void setTerminal(String terminal) {
            this.terminal = terminal;
        }
        public UpdateTerminalStateParamsState getState() {
            return state;
        }
        public void setState(UpdateTerminalStateParamsState state) {
            this.state = state;
        }
    }

        public static class UpdateTerminalStateParamsState {
        /** 终端是否被用户交互过 */
        private Boolean isInteractedWith;
        /** 终端的 shell 名称。未明确给出或不支持时为空。\n主要可用类型如下：\n'bash', 'cmd', 'csh', 'fish', 'gitbash', 'julia', 'ksh', 'node', 'nu', 'pwsh', 'python'\n'sh', 'wsl', 'zsh' */
        private String shell; // optional
        /** 增强 Shell 是否可用。\n为 True 表明 Terminal 支持进行执行和结果读写。 */
        private Boolean shellIntegrationAvailable;
    
        public Boolean getIsInteractedWith() {
            return isInteractedWith;
        }
        public void setIsInteractedWith(Boolean isInteractedWith) {
            this.isInteractedWith = isInteractedWith;
        }
        public String getShell() {
            return shell;
        }
        public void setShell(String shell) {
            this.shell = shell;
        }
        public Boolean getShellIntegrationAvailable() {
            return shellIntegrationAvailable;
        }
        public void setShellIntegrationAvailable(Boolean shellIntegrationAvailable) {
            this.shellIntegrationAvailable = shellIntegrationAvailable;
        }
    }
}

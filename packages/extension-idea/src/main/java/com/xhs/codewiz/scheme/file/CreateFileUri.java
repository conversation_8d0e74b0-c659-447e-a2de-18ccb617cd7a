package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 创建文件 uri，用于通知远端创建的文件 Uri。\n仅用于 `workspace.create.fileSystemWatcher`
 */
public class CreateFileUri {
    private String schemaProtocol = "file.create.fileuri";
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

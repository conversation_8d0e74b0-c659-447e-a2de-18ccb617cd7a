package com.xhs.codewiz.type.workspace;

import java.util.*;
import com.xhs.codewiz.type.content.Change;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.content.TextOrSnippet;
import com.xhs.codewiz.type.global.Command;
import com.xhs.codewiz.type.global.MarkdownOrString;

/**
 * 联想项
 */
public class CompletionItem {
    private CompletionItemKind kind;

    /** 排序文本，用于控制联想项的排序 */
    private String sortText;

    private MarkdownOrString documentation;

    private Range range;

    /** 联想项的标签 */
    private String label;

    /** 过滤文本，用于控制联想项的过滤 */
    private String filterText;

    /** 当用户在接受补全前输入了某个字符，如果这个字符在 commitCharacters 列表中，则该补全项会被自动接受，并且该字符会追加到补全结果后面 */
    private List<String> commitCharacters;

    /** 在插入联想项时执行的命令 */
    private Command command;

    private TextOrSnippet insertText;

    /** 是否预选此联想项 */
    private Boolean preselect;

    /** 联想项的详细描述 */
    private String detail;

    /** 是否保留空白字符，默认为 false */
    private Boolean keepWhitespace;

    /** 附加文本编辑，用于在插入联想项时执行额外的编辑操作 */
    private List<Change> additionalTextEdits;

    public CompletionItemKind getKind() {
        return kind;
    }

    public void setKind(CompletionItemKind kind) {
        this.kind = kind;
    }

    public String getSortText() {
        return sortText;
    }

    public void setSortText(String sortText) {
        this.sortText = sortText;
    }

    public MarkdownOrString getDocumentation() {
        return documentation;
    }

    public void setDocumentation(MarkdownOrString documentation) {
        this.documentation = documentation;
    }

    public Range getRange() {
        return range;
    }

    public void setRange(Range range) {
        this.range = range;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getFilterText() {
        return filterText;
    }

    public void setFilterText(String filterText) {
        this.filterText = filterText;
    }

    public List<String> getCommitCharacters() {
        return commitCharacters;
    }

    public void setCommitCharacters(List<String> commitCharacters) {
        this.commitCharacters = commitCharacters;
    }

    public Command getCommand() {
        return command;
    }

    public void setCommand(Command command) {
        this.command = command;
    }

    public TextOrSnippet getInsertText() {
        return insertText;
    }

    public void setInsertText(TextOrSnippet insertText) {
        this.insertText = insertText;
    }

    public Boolean getPreselect() {
        return preselect;
    }

    public void setPreselect(Boolean preselect) {
        this.preselect = preselect;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public Boolean getKeepWhitespace() {
        return keepWhitespace;
    }

    public void setKeepWhitespace(Boolean keepWhitespace) {
        this.keepWhitespace = keepWhitespace;
    }

    public List<Change> getAdditionalTextEdits() {
        return additionalTextEdits;
    }

    public void setAdditionalTextEdits(List<Change> additionalTextEdits) {
        this.additionalTextEdits = additionalTextEdits;
    }

}

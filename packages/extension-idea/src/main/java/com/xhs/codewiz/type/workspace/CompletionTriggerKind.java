package com.xhs.codewiz.type.workspace;

import java.util.*;

/**
 * 触发联想的方式
 */
public enum CompletionTriggerKind {
    /** 输入触发字符时触发 */
    TriggerCharacter(1),
    /** 当之前提供的补全不完整时触发 */
    TriggerForIncompleteCompletions(2),
    /** 手动触发 */
    Invoke(0);

    private final int value;

    CompletionTriggerKind(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}

package com.xhs.codewiz.type.content;

import java.util.*;

/**
 * Comment Collection 项
 */
public class CommentCollection {
    /** Comment Collection 内 Comment 的覆盖范围，不存在时针对当前文件。 */
    private Range range;

    /** Comment Collection ID */
    private String id;

    /** Comment Collection 的描述。 */
    private String label;

    private String uri;

    /** 打开文档时是否默认折叠 */
    private Boolean collapsible;

    public Range getRange() {
        return range;
    }

    public void setRange(Range range) {
        this.range = range;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public Boolean getCollapsible() {
        return collapsible;
    }

    public void setCollapsible(Boolean collapsible) {
        this.collapsible = collapsible;
    }

}

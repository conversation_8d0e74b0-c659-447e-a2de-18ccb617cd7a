package com.xhs.codewiz.type.platform;

import java.util.*;

/**
 * Webview
 */
public class Webview {
    /** Webview 图标右上角数字提示，如消息数提示等 */
    private WebviewBadge badge;

    private WebviewOptions options;

    /** Webview 的描述 */
    private String description;

    /** Webview 的 HTML 内容 */
    private String html;

    /** Webview 的标题 */
    private String title;

    public WebviewBadge getBadge() {
        return badge;
    }

    public void setBadge(WebviewBadge badge) {
        this.badge = badge;
    }

    public WebviewOptions getOptions() {
        return options;
    }

    public void setOptions(WebviewOptions options) {
        this.options = options;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

/**
 * Webview 图标右上角数字提示，如消息数提示等
 */
public static class WebviewBadge {
    /** 数字提示的提示信息 */
    private String tooltip;

    /** 数字提示的值 */
    private Integer value;

    public String getTooltip() {
        return tooltip;
    }

    public void setTooltip(String tooltip) {
        this.tooltip = tooltip;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

}
public static class WebviewOptions {
    /** 本地资源根目录列表，Webview 可以访问这些目录下的资源。\n默认为workspace根目录 */
    private List<String> localResourceRoots;

    /** webview 内允许使用的 localhost 端口映射 */
    private List<WebviewOptionsPortMappingItem> portMapping;

    /** 是否启用命令 URI，默认为 `false` */
    private Boolean enableCommandUris;

    /** 是否启用脚本，默认为 `false` */
    private Boolean enableScripts;

    /** 是否启用表单，默认为 `true` */
    private Boolean enableForms;

    public List<String> getLocalResourceRoots() {
        return localResourceRoots;
    }

    public void setLocalResourceRoots(List<String> localResourceRoots) {
        this.localResourceRoots = localResourceRoots;
    }

    public List<WebviewOptionsPortMappingItem> getPortMapping() {
        return portMapping;
    }

    public void setPortMapping(List<WebviewOptionsPortMappingItem> portMapping) {
        this.portMapping = portMapping;
    }

    public Boolean getEnableCommandUris() {
        return enableCommandUris;
    }

    public void setEnableCommandUris(Boolean enableCommandUris) {
        this.enableCommandUris = enableCommandUris;
    }

    public Boolean getEnableScripts() {
        return enableScripts;
    }

    public void setEnableScripts(Boolean enableScripts) {
        this.enableScripts = enableScripts;
    }

    public Boolean getEnableForms() {
        return enableForms;
    }

    public void setEnableForms(Boolean enableForms) {
        this.enableForms = enableForms;
    }

public static class WebviewOptionsPortMappingItem {
    /** Webview 端口 */
    private Integer webviewPort;

    /** 扩展主机端口 */
    private Integer extensionHostPort;

    public Integer getWebviewPort() {
        return webviewPort;
    }

    public void setWebviewPort(Integer webviewPort) {
        this.webviewPort = webviewPort;
    }

    public Integer getExtensionHostPort() {
        return extensionHostPort;
    }

    public void setExtensionHostPort(Integer extensionHostPort) {
        this.extensionHostPort = extensionHostPort;
    }

}
}
}

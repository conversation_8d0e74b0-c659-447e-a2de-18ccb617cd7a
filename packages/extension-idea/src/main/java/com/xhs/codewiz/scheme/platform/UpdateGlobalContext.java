package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 更新全局上下文
 */
public class UpdateGlobalContext {
    private String schemaProtocol = "platform.update.globalcontext";
    private List<UpdateGlobalContextParams> params;

    public List<UpdateGlobalContextParams> getParams() {
        return params;
    }
    public void setParams(List<UpdateGlobalContextParams> params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateGlobalContextParams {
        /** 上下文值 */
        private Object value;
        /** 上下文键 */
        private String key;
    
        public Object getValue() {
            return value;
        }
        public void setValue(Object value) {
            this.value = value;
        }
        public String getKey() {
            return key;
        }
        public void setKey(String key) {
            this.key = key;
        }
    }
}

package com.xhs.codewiz.completion;

import com.intellij.openapi.util.TextRange;
import com.xhs.codewiz.completion.enums.CodeWizApplyInlayStrategy;
import com.xhs.codewiz.completion.request.CodewizInlayList;

public class CompletionSplitter {
    private final CodeWizApplyInlayStrategy applyStrategy;

    public CompletionSplitter(CodeWizApplyInlayStrategy boundaryStrategy) {
        this.applyStrategy = boundaryStrategy;
    }

    public String split(CodewizInlayList completion) {
        TextRange range = completion.getReplacementRange();
        String fullCompletion = completion.getReplacementText();
        switch (this.applyStrategy) {
            case NEXT_WORD:
                return this.splitNextWord(fullCompletion, range.getEndOffset() - range.getStartOffset());
            case NEXT_LINE:
                int nextNewline = fullCompletion.indexOf(10, range.getEndOffset() - range.getStartOffset());
                return fullCompletion.substring(0, nextNewline == -1 ? fullCompletion.length() : nextNewline + 1);
            default:
                return completion.getReplacementText();
        }
    }

    private String splitNextWord(String fullCompletion, int offset) {
        int whitespaceBlock = this.findContinuousBlock(fullCompletion, offset, Character::isWhitespace);
        if (whitespaceBlock != -1) {
            return fullCompletion.substring(0, whitespaceBlock);
        } else {
            int symbolBlock = this.findContinuousBlock(fullCompletion, offset, this::isBoundaryCharacter);
            if (symbolBlock != -1) {
                return fullCompletion.substring(0, symbolBlock);
            } else {
                int otherBlock = this.findContinuousBlock(fullCompletion, offset, (c) -> {
                    return !this.isBoundaryCharacter(c) && !Character.isWhitespace(c);
                });
                return otherBlock != -1 ? fullCompletion.substring(0, otherBlock) : fullCompletion;
            }
        }
    }

    private int findContinuousBlock(String fullCompletion, int offset, BoundaryFinder isBoundaryCharacter) {
        if (fullCompletion.length() <= offset) {
            return -1;
        }
        int endIndex = offset;
        if (!isBoundaryCharacter.isBoundaryCharacter(fullCompletion.charAt(offset))) {
            return -1;
        } else {
            while(endIndex < fullCompletion.length() && isBoundaryCharacter.isBoundaryCharacter(fullCompletion.charAt(endIndex))) {
                ++endIndex;
            }

            return endIndex;
        }
    }

    private boolean isBoundaryCharacter(char c) {
        return "~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?".indexOf(c) != -1;
    }

    interface BoundaryFinder {
        boolean isBoundaryCharacter(char var1);
    }
}

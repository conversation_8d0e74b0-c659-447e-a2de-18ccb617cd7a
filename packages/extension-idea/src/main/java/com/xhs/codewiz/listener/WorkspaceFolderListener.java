package com.xhs.codewiz.listener;

import com.intellij.openapi.module.Module;
import com.intellij.openapi.project.ModuleListener;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManagerListener;
import com.xhs.codewiz.lang.agent.commands.AgentWorkspaceFolders;
import com.xhs.codewiz.lang.agent.commands.DidChangeWorkspaceFolders;
import org.jetbrains.annotations.NotNull;

public class WorkspaceFolderListener implements ProjectManagerListener, ModuleListener {
    public WorkspaceFolderListener() {
    }

    public void projectClosed(@NotNull Project project) {
        if (this.isListenerEnabled()) {
            this.notify(AgentWorkspaceFolders.projectRemovedNotification(project));
        }

    }

    public void projectClosing(@NotNull Project project) {
        if (this.isListenerEnabled()) {
            this.notify(AgentWorkspaceFolders.projectRemovedNotification(project));
        }

    }

    @SuppressWarnings("removal")
    @Override
    public void projectOpened(Project project) {
        if (this.isListenerEnabled()) {
            this.notify(AgentWorkspaceFolders.projectAddedNotification(project));
        }
    }

    public void moduleAdded(@NotNull Project project, @NotNull Module module) {
        if (this.isListenerEnabled()) {
            this.notify(AgentWorkspaceFolders.moduleAddedNotification(module));
        }

    }

    public void moduleRemoved(@NotNull Project project, @NotNull Module module) {
        if (this.isListenerEnabled()) {
            this.notify(AgentWorkspaceFolders.moduleRemovedNotification(module));
        }

    }

    private void notify(DidChangeWorkspaceFolders notification) {
        LSPManager.getInstance().notify(notification);
    }

    private boolean isListenerEnabled() {
        return !LSPManager.isDisabled();
    }
}


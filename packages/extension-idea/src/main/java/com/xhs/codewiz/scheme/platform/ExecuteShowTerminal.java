package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 显示终端
 */
public class ExecuteShowTerminal {
    private String schemaProtocol = "platform.execute.showterminal";
    private ExecuteShowTerminalParams params;

    public ExecuteShowTerminalParams getParams() {
        return params;
    }
    public void setParams(ExecuteShowTerminalParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteShowTerminalParams {
        private ExecuteShowTerminalParamsOptions options; // optional
        /** 终端 ID */
        private String terminal;
    
        public ExecuteShowTerminalParamsOptions getOptions() {
            return options;
        }
        public void setOptions(ExecuteShowTerminalParamsOptions options) {
            this.options = options;
        }
        public String getTerminal() {
            return terminal;
        }
        public void setTerminal(String terminal) {
            this.terminal = terminal;
        }
    }

        public static class ExecuteShowTerminalParamsOptions {
        /** 是否需要聚焦终端 */
        private Boolean didFocus; // optional
    
        public Boolean getDidFocus() {
            return didFocus;
        }
        public void setDidFocus(Boolean didFocus) {
            this.didFocus = didFocus;
        }
    }
}

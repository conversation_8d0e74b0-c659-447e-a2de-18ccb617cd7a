package com.xhs.codewiz.lang.agent;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.PluginUtil;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CodeWizAgentUtil {
    private static final Logger LOG = Logger.getInstance(CodeWizAgentUtil.class);

    public CodeWizAgentUtil() {
    }

    public static @Nullable Path getAgentDirectoryPath() {
        return findAssetDirectory("GITHUB_COPILOT_AGENTDIR", "copilot-agent/dist");
    }

    public static @Nullable Path getAgentBinaryDirectoryPath() {
        //return findAssetDirectory("GITHUB_COPILOT_AGENTBINDIR", "copilot-agent/bin");
        return findAssetDirectory("GITHUB_COPILOT_AGENTBINDIR", "server_lib/");
    }

    private static @Nullable Path findAssetDirectory(@NotNull String unitTestEnvVar, @NotNull String directoryName) {
        Path envPath;
        if (ApplicationManager.getApplication().isUnitTestMode()) {
            String envValue = System.getenv(unitTestEnvVar);
            if (envValue != null) {
                envPath = Paths.get(envValue);
                if (!Files.exists(envPath, new LinkOption[0])) {
                    LoggerUtil.INSTANCE.logDebug(LOG, unitTestEnvVar + " path doesn't exist: " + envPath);
                    return null;
                }

                return envPath;
            }
        }

        Path basePath = PluginUtil.getPluginBasePath();
        envPath = basePath.resolve(directoryName);
        //首次进来时不存在，需要去执行文件复制操作
        if (!Files.exists(envPath, new LinkOption[0])) {
            //LSInitialize.extractLanguageServer();
            return null;
        }
        return envPath;
    }
}


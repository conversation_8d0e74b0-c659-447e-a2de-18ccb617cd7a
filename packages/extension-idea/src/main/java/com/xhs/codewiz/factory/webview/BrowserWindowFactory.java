package com.xhs.codewiz.factory.webview;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.util.registry.Registry;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.openapi.wm.ex.ToolWindowManagerListener;
import com.intellij.ui.jcef.JBCefBrowser;
import com.intellij.util.messages.MessageBusConnection;
import com.xhs.codewiz.utils.LoggerUtil;


/**
 * <AUTHOR>
 * @description 浏览器窗口工厂 - 负责创建和管理 ToolBrowserWindow 实例
 */
public class BrowserWindowFactory implements ToolWindowFactory, DumbAware {
    static Logger logger = Logger.getInstance(BrowserWindowFactory.class);

    private static final BrowserWindowFactory instance = new BrowserWindowFactory();

    /**
     * 缓存 ToolBrowserWindow 实例
     * key: project.getName() + "#" + toolWindowId，value: ToolBrowserWindow
     */
    private final Map<String, ToolBrowserWindow> windowInstances = new ConcurrentHashMap<>();

    /**
     * 保存项目的连接实例，避免重复监听
     */
    private final Map<Project, MessageBusConnection> projectConnections = new ConcurrentHashMap<>();

    private static final Map<String, ToolWindow> LAST_ACTIVATE_WINDOW = new HashMap<>();

    private BrowserWindowFactory() {
        // ide启动需要初始化一次cef的配置，设置背景色与当前theme相同
        initCefAppBackgroundColor();
    }

    /**
     * 获取单例实例
     */
    public static BrowserWindowFactory getInstance() {
        // 单例模式
        return instance;
    }

    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        LoggerUtil.INSTANCE.logInfo(logger,
                "[Stage4-Browser] createToolWindowContent started - project: " + project.getName() + ", toolWindowId: " +
                        toolWindow.getId());
        String windowKey = generateWindowKey(project, toolWindow.getId());

        // 为每个 ToolWindow 创建独立的 ToolBrowserWindow 实例
        ToolBrowserWindow window = createToolBrowserWindow(project, toolWindow);
        windowInstances.put(windowKey, window);

        if (StringUtils.isNotBlank(toolWindow.getStripeTitle())) {
            toolWindow.setStripeTitle(toolWindow.getStripeTitle().replace(" ", ""));
        }

        // 初始化浏览器配置
        window.initBrowser(project);

        LoggerUtil.INSTANCE.logInfo(logger,
                "Created ToolBrowserWindow for project: " + project.getName() + ", toolWindow: " + toolWindow.getId());
    }

    /**
     * 创建新的 ToolBrowserWindow 实例
     */
    public ToolBrowserWindow createToolBrowserWindow(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        ToolBrowserWindow window = new ToolBrowserWindow();
        toolWindow.setAutoHide(false);
        window.setActiveWindow(toolWindow);
        return window;
    }

    /**
     * 生成窗口缓存的 key
     */
    private String generateWindowKey(@NotNull Project project, @NotNull String toolWindowId) {
        return project.getBasePath() + "#" + toolWindowId;
    }

    private String generateActivateWindowKey(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        return project.getBasePath() + "#" + toolWindow.getAnchor() + "#";
    }


    private void initCefAppBackgroundColor() {
        // 仅配置一次，改变主题后需要重启ide才会重新配置这个颜色
        try {
            // 主动关闭remote模式，可以解决渲染问题
            Registry.get("ide.browser.jcef.out-of-process.enabled").setValue(false);
        } catch (Throwable e) {
            LoggerUtil.INSTANCE.logWarn(logger, "initBrowser setSettings err, msg = {}", e);
        }
    }

    /**
     * 获取指定项目和 ToolWindow 的浏览器实例
     */
    public JBCefBrowser getJBCefBrowser(@NotNull Project project, @NotNull String toolWindowId) {
        ToolBrowserWindow window = getToolBrowserWindow(project, toolWindowId);
        return window != null ? window.getCodeWizBrowser() : null;
    }

    /**
     * 获取指定项目和 ToolWindow 的 ToolBrowserWindow 实例
     */
    public ToolBrowserWindow getToolBrowserWindow(@NotNull Project project, @NotNull String toolWindowId) {
        String windowKey = generateWindowKey(project, toolWindowId);
        return windowInstances.get(windowKey);
    }

    /**
     * 根据 remoteChannelId 和 providerId 获取 ToolBrowserWindow 实例
     */
    public ToolBrowserWindow getToolBrowserWindowByProvider(@NotNull Project project,
                                                            @NotNull String remoteChannelId,
                                                            @NotNull String providerId) {
        return windowInstances.values().stream()
                .filter(window -> window.getRemoteChannel() != null &&
                        window.getRemoteChannel().equals(remoteChannelId) &&
                        Objects.equals(window.getWebviewProvider().getId(), providerId))
                .findFirst()
                .orElse(null);
    }

    /**
     * @deprecated 使用 getInstance().getToolBrowserWindow(project, toolWindowId) 替代
     */
    @Deprecated
    public static ToolBrowserWindow getToolBrowserWindow(Project project) {
        return getInstance().getFirstToolBrowserWindow(project);
    }

    /**
     * 获取项目的第一个 ToolBrowserWindow（兼容性方法）
     */
    private ToolBrowserWindow getFirstToolBrowserWindow(@NotNull Project project) {
        String projectName = project.getName();
        return windowInstances.values().stream()
                .filter(window -> window.getActiveWindow() != null &&
                        window.getActiveWindow().toString().contains(projectName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 为项目添加工具窗口监听器
     */
    public void projectAddToolWindowListener(Project project) {
        if (project == null) {
            return;
        }
        // 检查是否已经有连接，避免重复订阅
        if (projectConnections.containsKey(project)) {
            LoggerUtil.INSTANCE.logDebug(logger, "Project " + project.getName() + " already has tool window listener, skipping.");
            return;
        }

        MessageBusConnection connection = project.getMessageBus().connect();
        projectConnections.put(project, connection);

        connection.subscribe(ToolWindowManagerListener.TOPIC, new ToolWindowManagerListener() {
            @Override
            public void stateChanged(ToolWindowManager mgr, @NotNull ToolWindow toolWindow,
                                     @NotNull ToolWindowManagerEventType changeType) {
                // 查找对应的 ToolBrowserWindow 实例
                ToolBrowserWindow toolBrowserWindow = getToolBrowserWindow(project, toolWindow.getId());
                if (toolBrowserWindow != null
                        && Objects.equals(toolBrowserWindow.getActiveWindow().getId(), toolWindow.getId())) {
                    hideLastActivateWindow(toolWindow, project, changeType);

                    toolBrowserWindow.toolWindowStateChange(mgr, toolWindow, changeType, project);

                    System.out.println("tool window stripe == " + toolWindow.getStripeTitle()
                            + " change type == " + changeType.name()
                    );
                }
            }

            @Override
            public void toolWindowShown(@NotNull ToolWindow toolWindow) {
                // 查找对应的 ToolBrowserWindow 实例
                ToolBrowserWindow toolBrowserWindow = getToolBrowserWindow(project, toolWindow.getId());
                if (toolBrowserWindow != null
                        && Objects.equals(toolBrowserWindow.getActiveWindow().getId(), toolWindow.getId())) {
                    toolBrowserWindow.toolWindowShown(toolWindow, project);
                }
            }
        });

        LoggerUtil.INSTANCE.logInfo(logger, "Tool window listener added for project: " + project.getName());
    }

    /**
     * 激活新window时，隐藏同侧上一次打开的window
     * 处理打开第一个window，在打开同侧第二个window时的图标闪烁问题
     *
     * @param toolWindow
     * @param project
     */
    private void hideLastActivateWindow(@NotNull ToolWindow toolWindow,
                                        Project project,
                                        ToolWindowManagerListener.ToolWindowManagerEventType changeType) {
        ApplicationManager.getApplication().invokeLater(() -> {
            if (changeType != ToolWindowManagerListener.ToolWindowManagerEventType.ActivateToolWindow) {
                // 如果不是激活事件，则不计入
                return;
            }
            String activateWindowKey = generateActivateWindowKey(project, toolWindow);
            ToolWindow lastActivateWindow = LAST_ACTIVATE_WINDOW.get(activateWindowKey);
            if (lastActivateWindow != null
                    // && lastActivateWindow.isVisible()
                    && !lastActivateWindow.getId().equals(toolWindow.getId())) {
                LoggerUtil.INSTANCE.logInfo(logger, "hideLastActivateWindow, lastActivateWindow: "
                        + lastActivateWindow.getId() + " toolWindow: " + toolWindow.getId());
                lastActivateWindow.hide();
            }
            LAST_ACTIVATE_WINDOW.put(activateWindowKey, toolWindow);
        });
    }

    /**
     * 移除项目的工具窗口监听器
     */
    public void projectRemoveToolWindowListener(Project project) {
        String projectName = project.getName();
        MessageBusConnection connection = projectConnections.remove(project);

        if (connection != null) {
            try {
                connection.disconnect();
                LoggerUtil.INSTANCE.logInfo(logger, "Tool window listener removed for project: " + projectName);
            } catch (Exception e) {
                LoggerUtil.INSTANCE.logWarn(logger, "Error disconnecting tool window listener for project: " + projectName, e);
            }
        }

        // 清理项目的所有窗口实例
        clearProjectWindows(project);
    }

    /**
     * 清理项目的所有 ToolBrowserWindow 实例
     */
    public void clearProjectWindows(@NotNull Project project) {
        String projectName = project.getBasePath();
        windowInstances.entrySet().removeIf(entry -> {
            String key = entry.getKey();
            if (key.startsWith(projectName + "#")) {
                // 释放资源
                ToolBrowserWindow window = entry.getValue();
                if (window != null) {
                    addDisposer(project, window);
                    window.dispose();
                }
                LoggerUtil.INSTANCE.logInfo(logger, "Removed ToolBrowserWindow for key: " + key);
                return true;
            }
            return false;
        });
    }

    private static void addDisposer(Project project, ToolBrowserWindow browserWindow) {
        Disposer.register(browserWindow, () -> {
            if (browserWindow.getWebviewProvider() != null
                    && StringUtils.isNotEmpty(browserWindow.getWebviewProvider().getId())) {
                // ChatWindowCommandUtil.clearProviderTitleActions(project, browserWindow.getWebviewProvider().getId());
            }
        });
    }

    /**
     * 移除特定的 ToolBrowserWindow 实例
     */
    public void removeToolBrowserWindow(@NotNull Project project, @NotNull String toolWindowId) {
        ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
        ToolWindow toolWindow = toolWindowManager.getToolWindow(toolWindowId);
        if (toolWindow != null) {
            // 卸载 ToolWindow
            toolWindowManager.unregisterToolWindow(toolWindow.getId());
        }

        String windowKey = generateWindowKey(project, toolWindowId);
        ToolBrowserWindow window = windowInstances.remove(windowKey);
        if (window != null) {
            addDisposer(project, window);
            window.dispose();
            LoggerUtil.INSTANCE.logInfo(logger,
                    "Removed ToolBrowserWindow for project: " + project.getName() + ", toolWindow: " + toolWindowId);
        }
    }

    /**
     * 获取项目的所有 ToolBrowserWindow 实例
     */
    public java.util.List<ToolBrowserWindow> getProjectWindows(@NotNull Project project) {
        String projectName = project.getBasePath();
        return windowInstances.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(projectName + "#"))
                .map(Map.Entry::getValue)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取项目中第一个有 WebviewProvider 的 ToolBrowserWindow
     */
    public ToolBrowserWindow getFirstAvailableWindow(@NotNull Project project) {
        return getProjectWindows(project).stream()
                .filter(window -> window.getWebviewProvider() != null && StringUtils.isNotEmpty(window.getWebviewProvider().getId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取当前项目的 JBCefBrowser 实例（兼容性方法）
     * 优先返回第一个可用的浏览器实例
     *
     * @param project 项目实例
     * @return JBCefBrowser 实例，如果没有找到返回 null
     */
    public static JBCefBrowser getCurrentJBCefBrowser(Project project) {
        if (project == null) {
            return null;
        }

        BrowserWindowFactory factory = getInstance();

        // 首先尝试获取有 WebviewProvider 的窗口
        ToolBrowserWindow availableWindow = factory.getFirstAvailableWindow(project);
        if (availableWindow != null && availableWindow.getCodeWizBrowser() != null) {
            return availableWindow.getCodeWizBrowser();
        }

        // 如果没有找到，尝试获取项目的任意一个窗口
        java.util.List<ToolBrowserWindow> projectWindows = factory.getProjectWindows(project);
        for (ToolBrowserWindow window : projectWindows) {
            if (window.getCodeWizBrowser() != null) {
                return window.getCodeWizBrowser();
            }
        }

        LoggerUtil.INSTANCE.logWarn(logger, "No JBCefBrowser found for project: " + project.getName());
        return null;
    }
}

package com.xhs.codewiz.scheme.workspace;

import java.util.*;
/**
 * 删除链接提供者
 */
public class DeleteLinkProvider {
    private String schemaProtocol = "workspace.delete.linkprovider";
    /** 删除的链接提供者 ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

package com.xhs.codewiz.editor.language;

import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.utils.LoggerUtil;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

public class LanguageUtil {
  private static Logger log = Logger.getInstance(LanguageUtil.class);
  public static final String PLAINTEXT = "plaintext";
  private static LanguageMapping languageMapping = LanguageMapping.buildLanguageMapping();
  public static Map<String, String> LANGUAGE_TO_EXT = new HashMap<String, String>(){
    {
      this.put("java", "java");
      this.put("python", "py");
      this.put("javascript", "js");
      this.put("typescript", "ts");
      this.put("tsx", "tsx");
      this.put("go", "go");
      this.put("c", "c");
      this.put("cpp", "cpp");
      this.put("c++", "cpp");
      this.put("xml", "xml");
      this.put("html", "html");
      this.put("sql", "sql");
      this.put("sqlite", "sql");
      this.put("php", "php");
      this.put("kotlin", "kt");
      this.put("yaml", "yml");
      this.put("properties", "properties");
      this.put("csharp", "cs");
      this.put("c_sharp", "cs");
      this.put("css", "css");
      this.put("scss", "scss");
      this.put("vue", "vue");
    }
  };

  public static String guessExtensionByMarkdownLanguage(String language) {
    if (language == null) {
      return "";
    }
    return LANGUAGE_TO_EXT.getOrDefault(language.toLowerCase(), language.toLowerCase());
  }

  public static String getLanguageByFilePath(String filePath) {
    String name = filePath.toLowerCase(Locale.ROOT);
    String fileName = FilenameUtils.getName(name);
    if (StringUtils.isBlank(fileName)) {
      return PLAINTEXT;
    }
    return languageMapping.getLanguages(fileName);
  }

  public static String getExtensionByFileName(String name) {
    try {
      String fileExt = FilenameUtils.getExtension(name);
      if (StringUtils.isBlank(fileExt)) {
        return fileExt;
      }
      return fileExt;
    }
    catch (Exception e) {
      int index = name.lastIndexOf(".");
      LoggerUtil.INSTANCE.logWarn(log, "getExtensionByFileName name is " + name);
      if (index == -1) {
        return "";
      }
      LoggerUtil.INSTANCE.logWarn(log, "getExtensionByFileName suffix is " + name.substring(index + 1));
      return name.substring(index + 1);
    }
  }
}
package com.xhs.codewiz.lang.agent;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.DumbService;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcRequestListener;
import com.xhs.codewiz.lang.entity.CodeReferenceResult;
import com.xhs.codewiz.lang.entity.QueryReferenceParams;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import com.xhs.codewiz.utils.reference.ReferenceResolver;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.concurrency.AsyncPromise;
import org.jetbrains.concurrency.Promise;

public class TextDocumentReferenceHandler implements
    JsonRpcRequestListener<QueryReferenceParams, CodeReferenceResult> {
    private static final Logger log = Logger.getInstance(TextDocumentReferenceHandler.class);

    private static final CodeReferenceResult DEFAULT_REFERENCE_FAIL_RESULT = new CodeReferenceResult(new ArrayList<>(), false);
    private static final CodeReferenceResult DEFAULT_REFERENCE_SUCCESS_RESULT = new CodeReferenceResult(new ArrayList<>(), true);

    // 不同语言的内容优化器
    private static final Map<String, LanguageContentOptimizer> CONTENT_OPTIMIZERS = new HashMap<>();

    static {
        // 注册不同语言的优化器
        CONTENT_OPTIMIZERS.put("java", new JavaContentOptimizer());
        CONTENT_OPTIMIZERS.put("default", new DefaultContentOptimizer());
    }

    @Override
    public @NotNull Promise<CodeReferenceResult> handleMessage(@NotNull QueryReferenceParams request) {
        AsyncPromise<CodeReferenceResult> promise = new AsyncPromise<>();
        ThreadUtil.execute(() -> {
            try {
                Project project = ApplicationUtil.getProjectByPath(request.getWorkspace());
                if (project == null) {
                    LoggerUtil.INSTANCE.logWarn(log, "invalid project for querying reference");
                    promise.setResult(DEFAULT_REFERENCE_FAIL_RESULT);
                    return;
                }
                if (DumbService.isDumb(project)) {
                    promise.setResult(DEFAULT_REFERENCE_FAIL_RESULT);
                    return;
                }
                Editor editor = CodeWizEditorUtil.getSelectedEditorSafely(project);
                if (editor == null) {
                    LoggerUtil.INSTANCE.logWarn(log, "invalid editor for querying reference");
                    promise.setResult(DEFAULT_REFERENCE_FAIL_RESULT);
                    return;
                }
                long startTime = System.currentTimeMillis();
                CodeReferenceResult result = resolveDeclarationWithOptimization(editor, request);
                long end = System.currentTimeMillis();
                LoggerUtil.INSTANCE.logDebug(log, "query reference cost:" + (end - startTime));
                promise.setResult(result);
            } catch (Throwable e) {
                LoggerUtil.INSTANCE.logWarn(log, "query reference error", e);
                promise.setResult(DEFAULT_REFERENCE_FAIL_RESULT);
            }
        });
        return promise;
    }

    /**
     * 带优化的引用解析
     */
    private CodeReferenceResult resolveDeclarationWithOptimization(Editor editor, QueryReferenceParams request) {
        // 首先使用原始的解析方法
        CodeReferenceResult result = ReferenceResolver.resolveDeclaration(editor, request);

        if (!result.isSuccess() || result.getItems() == null) {
            return result;
        }

        // 对每个引用项进行语言特定的优化
        for (CodeReferenceResult.CodeReferenceItem item : result.getItems()) {
            optimizeReferenceItem(item);
        }

        return result;
    }

    /**
     * 优化单个引用项
     */
    private void optimizeReferenceItem(CodeReferenceResult.CodeReferenceItem item) {
        if (item == null || item.getFilePath() == null || item.getContent() == null) {
            return;
        }

        String language = detectLanguageFromFilePath(item.getFilePath());
        LanguageContentOptimizer optimizer = CONTENT_OPTIMIZERS.getOrDefault(language, CONTENT_OPTIMIZERS.get("default"));

        try {
            String optimizedContent = optimizer.optimizeContent(item.getContent(), item.getFilePath());
            if (optimizedContent != null && !optimizedContent.equals(item.getContent())) {
                item.setContent(optimizedContent);
                LoggerUtil.INSTANCE.logDebug(log, "Optimized content for " + language + " file: " + item.getFilePath());
            }
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to optimize content for file: " + item.getFilePath(), e);
        }
    }

    /**
     * 根据文件路径检测语言类型
     */
    private String detectLanguageFromFilePath(String filePath) {
        if (filePath == null) {
            return "default";
        }

        String lowerPath = filePath.toLowerCase();
        // TODO 后续支持其他语言
        if (lowerPath.endsWith(".class") ||
                lowerPath.endsWith(".jar") ||
                lowerPath.endsWith(".war") ||
                lowerPath.endsWith(".java")
        ) {
            return "java";
        }

        return "default";
    }

    @Override
    public Class<QueryReferenceParams> getRequestType() {
        return QueryReferenceParams.class;
    }

    @Override
    public Class<CodeReferenceResult> getResponseType() {
        return CodeReferenceResult.class;
    }

    /**
     * 语言内容优化器接口
     */
    interface LanguageContentOptimizer {
        String optimizeContent(String content, String filePath);
    }

    /**
     * Java 内容优化器
     */
    public static class JavaContentOptimizer implements LanguageContentOptimizer {
        
        // 编译好的正则表达式，提高性能
        private static final Pattern PUBLIC_CLASS_PATTERN = Pattern.compile(
            "public\\s+class\\s+(\\w+)\\s*\\{", 
            Pattern.MULTILINE
        );
        
        private static final Pattern PUBLIC_STATIC_INTERFACE_IFACE_PATTERN = Pattern.compile(
            "public\\s+static\\s+interface\\s+Iface\\s*\\{", 
            Pattern.MULTILINE
        );

        @Override
        public String optimizeContent(String content, String filePath) {
            if (content == null || content.isEmpty()) {
                return content;
            }

            // 快速检查：如果不包含目标接口，直接返回原内容
            if (!content.contains("public static interface Iface")) {
                return content;
            }

            try {
                String extractedContent = extractClassAndInterface(content);
                return extractedContent != null ? extractedContent.trim() : content.trim();
            } catch (Exception e) {
                // 如果提取失败，返回原内容
                LoggerUtil.INSTANCE.logWarn(log, "Failed to extract class and interface from Java file: " + filePath, e);
                return content.trim();
            }
        }

        /**
         * 高性能提取第一个 public class 及其内部的 public static interface Iface
         */
        private String extractClassAndInterface(String content) {
            // 1. 找到第一个 public class 的位置
            Matcher classMatcher = PUBLIC_CLASS_PATTERN.matcher(content);
            if (!classMatcher.find()) {
                return null;
            }

            String className = classMatcher.group(1);
            int classStart = classMatcher.start();
            int classBodyStart = classMatcher.end() - 1; // '{'的位置

            // 2. 找到 class 的结束位置（匹配花括号）
            int classEnd = findMatchingBrace(content, classBodyStart);
            if (classEnd == -1) {
                return null;
            }

            // 3. 在 class 内容中查找 public static interface Iface
            String classContent = content.substring(classBodyStart + 1, classEnd);
            Matcher ifaceMatcher = PUBLIC_STATIC_INTERFACE_IFACE_PATTERN.matcher(classContent);
            if (!ifaceMatcher.find()) {
                return null;
            }

            int ifaceStart = ifaceMatcher.start();
            int ifaceBodyStart = ifaceMatcher.end() - 1; // '{'的位置

            // 4. 找到 interface 的结束位置
            int ifaceEnd = findMatchingBrace(classContent, ifaceBodyStart);
            if (ifaceEnd == -1) {
                return null;
            }

            // 5. 构造结果
            StringBuilder result = new StringBuilder();
            result.append("public class ").append(className).append(" {\n");
            
            // 提取接口内容，保持原有缩进
            String ifaceContent = classContent.substring(ifaceStart, ifaceEnd + 1);
            // 为接口内容添加适当的缩进
            String[] ifaceLines = ifaceContent.split("\n");
            for (int i = 0; i < ifaceLines.length; i++) {
                String line = ifaceLines[i];
                if (i == 0) {
                    // 接口声明行
                    result.append("    ").append(line.trim()).append("\n");
                } else if (line.trim().isEmpty()) {
                    // 空行
                    result.append("\n");
                } else if (line.trim().equals("}")) {
                    // 接口结束花括号
                    result.append("    ").append(line.trim()).append("\n");
                } else {
                    // 接口内的方法，需要双重缩进
                    result.append("        ").append(line.trim()).append("\n");
                }
            }
            result.append("}");
            return result.toString();
        }

        /**
         * 高性能花括号匹配算法
         * 找到与指定位置的开花括号匹配的闭花括号位置
         */
        private int findMatchingBrace(String content, int startPos) {
            if (startPos >= content.length() || content.charAt(startPos) != '{') {
                return -1;
            }

            int braceCount = 1;
            boolean inString = false;
            boolean inChar = false;
            boolean inLineComment = false;
            boolean inBlockComment = false;
            char prev = '\0';

            for (int i = startPos + 1; i < content.length(); i++) {
                char current = content.charAt(i);

                // 处理注释和字符串，避免在注释/字符串中的花括号被误算
                if (!inString && !inChar && !inBlockComment) {
                    if (current == '/' && i + 1 < content.length()) {
                        char next = content.charAt(i + 1);
                        if (next == '/') {
                            inLineComment = true;
                            i++; // 跳过下一个字符
                            continue;
                        } else if (next == '*') {
                            inBlockComment = true;
                            i++; // 跳过下一个字符
                            continue;
                        }
                    }
                }

                if (inLineComment) {
                    if (current == '\n') {
                        inLineComment = false;
                    }
                    continue;
                }

                if (inBlockComment) {
                    if (current == '*' && i + 1 < content.length() && content.charAt(i + 1) == '/') {
                        inBlockComment = false;
                        i++; // 跳过下一个字符
                    }
                    continue;
                }

                if (!inChar && current == '"' && prev != '\\') {
                    inString = !inString;
                } else if (!inString && current == '\'' && prev != '\\') {
                    inChar = !inChar;
                } else if (!inString && !inChar) {
                    if (current == '{') {
                        braceCount++;
                    } else if (current == '}') {
                        braceCount--;
                        if (braceCount == 0) {
                            return i;
                        }
                    }
                }

                prev = current;
            }

            return -1; // 没有找到匹配的花括号
        }
    }

    /**
     * 默认内容优化器
     */
    static class DefaultContentOptimizer implements LanguageContentOptimizer {
        @Override
        public String optimizeContent(String content, String filePath) {
            // 默认不做优化，直接返回原内容
            return content;
        }
    }
}
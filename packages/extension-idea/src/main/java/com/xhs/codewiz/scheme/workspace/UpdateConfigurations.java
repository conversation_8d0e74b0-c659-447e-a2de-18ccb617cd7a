package com.xhs.codewiz.scheme.workspace;

import java.util.*;
/**
 * Workspace 配置项。以下为部分默认配置:  \n `workbench.colorTheme`: 颜色主题,`dark`|`light`|`dark-high-contrast`|`light-high-contrast`
 */
public class UpdateConfigurations {
    private String schemaProtocol = "workspace.update.configurations";
    /** 配置项的键值对 */
    private List<UpdateConfigurationsParams> params;

    public List<UpdateConfigurationsParams> getParams() {
        return params;
    }
    public void setParams(List<UpdateConfigurationsParams> params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateConfigurationsParams {
        /** 配置项路径 */
        private String path;
        /** 配置项的值 */
        private Object value;
    
        public String getPath() {
            return path;
        }
        public void setPath(String path) {
            this.path = path;
        }
        public Object getValue() {
            return value;
        }
        public void setValue(Object value) {
            this.value = value;
        }
    }
}

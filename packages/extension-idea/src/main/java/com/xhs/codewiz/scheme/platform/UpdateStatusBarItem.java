package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.StatusBarItem;
/**
 * 更新状态栏项
 */
public class UpdateStatusBarItem {
    private String schemaProtocol = "platform.update.statusbaritem";
    private UpdateStatusBarItemParams params;

    public UpdateStatusBarItemParams getParams() {
        return params;
    }
    public void setParams(UpdateStatusBarItemParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateStatusBarItemParams {
        /** 更新后的状态栏项数据 */
        private StatusBarItem item;
        /** 要更新的状态栏项 ID */
        private String id;
    
        public StatusBarItem getItem() {
            return item;
        }
        public void setItem(StatusBarItem item) {
            this.item = item;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

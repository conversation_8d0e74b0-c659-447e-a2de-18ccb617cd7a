package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.StatusBarItem;
/**
 * 创建状态栏项
 */
public class CreateStatusBarItem {
    private String schemaProtocol = "platform.create.statusbaritem";
    private StatusBarItem params;

    public StatusBarItem getParams() {
        return params;
    }
    public void setParams(StatusBarItem params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public final class WorkspaceFoldersChangeEvent {
    @SerializedName("added")
    private final @NotNull List<WorkspaceFolder> added;
    @SerializedName("removed")
    private final @NotNull List<WorkspaceFolder> removed;

    public @NotNull List<WorkspaceFolder> getAdded() {
        return this.added;
    }

    public @NotNull List<WorkspaceFolder> getRemoved() {
        return this.removed;
    }

    public WorkspaceFoldersChangeEvent(@NotNull List<WorkspaceFolder> added, @NotNull List<WorkspaceFolder> removed) {
        this.added = added;
        this.removed = removed;
    }
}

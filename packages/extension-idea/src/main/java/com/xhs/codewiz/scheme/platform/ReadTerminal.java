package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 读取 Terminal。\n收到这个请求时，你需要判断 Terminal 是否可读，\n如果可读，先使用 `global.create.streamString` 通过 `terminalId/executeId` 通道发送字符串 chunks，发送完成后返回 `chunksCounts`；\n如果不可读，直接返回。
 */
public class ReadTerminal {
    private String schemaProtocol = "platform.read.terminal";
    private ReadTerminalParams params;

    public ReadTerminalParams getParams() {
        return params;
    }
    public void setParams(ReadTerminalParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadTerminalParams {
        /** 返回时使用的 Stream ID */
        private String streamId;
        /** Terminal 执行的 ID */
        private String executeId;
        /** Terminal 的 ID */
        private String terminalId;
    
        public String getStreamId() {
            return streamId;
        }
        public void setStreamId(String streamId) {
            this.streamId = streamId;
        }
        public String getExecuteId() {
            return executeId;
        }
        public void setExecuteId(String executeId) {
            this.executeId = executeId;
        }
        public String getTerminalId() {
            return terminalId;
        }
        public void setTerminalId(String terminalId) {
            this.terminalId = terminalId;
        }
    }
}

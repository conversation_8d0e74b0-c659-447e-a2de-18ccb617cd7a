package com.xhs.codewiz.actions.util;

import com.intellij.openapi.project.Project;
import com.xhs.codewiz.actions.command.ChatWindowCommandUtil;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.scheme.platform.UpdateGlobalContext;
import com.xhs.codewiz.scheme.platform.UpdateGlobalContext.UpdateGlobalContextParams;
import com.xhs.codewiz.utils.GsonUtil;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.compress.utils.Lists;

/**
 * Author: liukunpeng Date: 2025-08-01 Description:
 */
public class WhenGlobalContextUtil {
  public static Map<Project, Map<String, UpdateGlobalContext>> currentContextMap = new ConcurrentHashMap<>();
  public static void registerWhenGlobalContext(String str, String channel) {
    UpdateGlobalContext context = GsonUtil.fromJson(str, UpdateGlobalContext.class);
    if (null == context || null == context.getParams()) {
      return;
    }
    Project project = RcsWebSocketManager.INSTANCE.getProjectByLocalChannel(channel);
    Map<String, UpdateGlobalContext> projectMap = currentContextMap.computeIfAbsent(project, k -> new ConcurrentHashMap<>());
    //替换新的channel
    projectMap.put(channel, context);
    Set<String> newWhen = new HashSet<>();
    for (UpdateGlobalContext when : projectMap.values()) {
      for (UpdateGlobalContextParams param : when.getParams()) {
        if (null != param.getValue() && (Boolean) param.getValue()) {
          newWhen.add(param.getKey());
        }
      }
    }
    ChatWindowCommandUtil.refreshChatTitleView(project, newWhen, false);

  }
}

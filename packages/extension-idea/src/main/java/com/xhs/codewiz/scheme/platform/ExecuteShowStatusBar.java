package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 显示状态栏按钮
 */
public class ExecuteShowStatusBar {
    private String schemaProtocol = "platform.execute.showstatusbar";
    /** statusBar ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

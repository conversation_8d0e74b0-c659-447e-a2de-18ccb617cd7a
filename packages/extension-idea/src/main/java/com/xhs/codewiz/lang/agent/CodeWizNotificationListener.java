package com.xhs.codewiz.lang.agent;

import static com.xhs.codewiz.lang.agent.rpc.JsonRPC.GSON;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.constant.PluginCommonConstant;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes.XhsUserInfo;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.IdeNotificationUtil;
import com.xhs.codewiz.utils.LocalStorageUtil;
import java.util.List;
import java.util.Map;
import javax.security.auth.message.AuthStatus;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-03-18 Description:
 */
public class CodeWizNotificationListener implements JsonRpcNotificationListener {
  static Logger logger = Logger.getInstance(CodeWizNotificationListener.class);
  @Override
  public boolean handleMessage(@NotNull String name, @NotNull JsonElement paramJsonElement) {
    switch (name) {
      case "xhsLoginInfo":
        xhsLoginInfo(paramJsonElement);
        return true;
      case "showMessageDialog":
        IdeNotificationUtil.showMessage("info", paramJsonElement.getAsString(), ApplicationUtil.findCurrentProject());
        return true;
      default:
    }
    return false;
  }
  private void xhsLoginInfo(JsonElement paramJsonElement) {
    XhsUserInfo xhsUserInfo = new Gson().fromJson(paramJsonElement.toString(), XhsUserInfo.class);
    if (null != xhsUserInfo) {
      XhsUserInfoRes res = new XhsUserInfoRes();
      res.setCode(200);
      res.setData(xhsUserInfo);
      //临时缓存登陆信息
      LocalStorageUtil.setProperty(PluginCommonConstant.LOGIN_TOKEN_TEMP_KEY, new Gson().toJson(res));
    }
  }
}

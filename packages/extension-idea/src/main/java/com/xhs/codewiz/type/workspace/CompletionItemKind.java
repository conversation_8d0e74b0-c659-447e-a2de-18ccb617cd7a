package com.xhs.codewiz.type.workspace;

import java.util.*;

/**
 * 联想项类型
 */
public enum CompletionItemKind {
    /** 枚举 */
    Enum(12),
    /** 运算符 */
    Operator(23),
    /** 类型参数 */
    TypeParameter(24),
    /** 变量 */
    Variable(5),
    /** 用户 */
    User(25),
    /** 问题 */
    Issue(26),
    /** 常量 */
    Constant(20),
    /** 单位 */
    Unit(10),
    /** 文件夹 */
    Folder(18),
    /** 结构体 */
    Struct(21),
    /** 枚举成员 */
    EnumMember(19),
    /** 函数 */
    Function(2),
    /** 字段 */
    Field(4),
    /** 属性 */
    Property(9),
    /** 关键字 */
    Keyword(13),
    /** 构造函数 */
    Constructor(3),
    /** 引用 */
    Reference(17),
    /** 颜色 */
    Color(15),
    /** 文本 */
    Text(0),
    /** 方法 */
    Method(1),
    /** 代码片段 */
    Snippet(14),
    /** 值 */
    Value(11),
    /** 事件 */
    Event(22),
    /** 类 */
    Class(6),
    /** 模块 */
    Module(8),
    /** 文件 */
    File(16),
    /** 接口 */
    Interface(7);

    private final int value;

    CompletionItemKind(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}

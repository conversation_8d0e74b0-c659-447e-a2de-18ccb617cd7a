package com.xhs.codewiz.update;

import static java.util.concurrent.TimeUnit.MINUTES;

import com.google.gson.reflect.TypeToken;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.StartupActivity;
import com.intellij.util.concurrency.AppExecutorUtil;
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread;
import com.xhs.codewiz.enums.RemoteServiceEnum;
import com.xhs.codewiz.lang.LspServiceForTylm;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes.XhsUserInfo;
import com.xhs.codewiz.setting.CodeWizApplicationSettings;
import com.xhs.codewiz.update.model.GetPluginConfigRequest;
import com.xhs.codewiz.update.model.GetPluginConfigResponse;
import com.xhs.codewiz.utils.HttpUtils;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.PluginUtil;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * 启动一个线程轮询市场Code Wiz插件版本
 * 如果版本有diff（不关注版本先后）
 * 下载解压替换
 */
public class AutoUpdateActivityFromRemote implements StartupActivity, StartupActivity.DumbAware {
    private static final Logger LOG = Logger.getInstance(AutoUpdateActivityFromRemote.class);
    private volatile String lastUpdateVersion = "0";
    private final AtomicBoolean HAS_INIT = new AtomicBoolean(false);
    private int skipCount = 0;

    @Override
    @RequiresBackgroundThread
    public void runActivity(@NotNull Project project) {
        doUpdateCheck();
    }
    private void doUpdateCheck() {
        //仅允许触发一次
        if (HAS_INIT.getAndSet(true)) {
            return;
        }
        try {
            AppExecutorUtil.getAppScheduledExecutorService().scheduleWithFixedDelay(() -> {
                try {
                    //未开启自动强制升级时直接返回
                    if (!CodeWizApplicationSettings.settings().autoUpdate.checkForUpdate) {
                        return;
                    }
                    XhsUserInfo userInfo = LspServiceForTylm.getUserLoginInfo();
                    //未登录时直接返回
                    if (null == userInfo) {
                        return;
                    }
                    String url = PluginUtil.isDevEnv() ? UpdateConstant.UPDATE_CONFIG_SIT : UpdateConstant.UPDATE_CONFIG_PROD;
                    GetPluginConfigResponse response = HttpUtils.postBody(getPluginConfigRequest(userInfo.getEmail()), new TypeToken<GetPluginConfigResponse>(){}, url);
                    if (null == response || null == response.getData()) {
                        LoggerUtil.INSTANCE.logWarn(LOG, "plugin Code Wiz hot-patch failed, url = " + url);
                        return;
                    }
                    if (!response.getData().isUpdate()) {
                        LoggerUtil.INSTANCE.logInfo(LOG, "plugin Code Wiz un exec hot-patch, nowVersion = " + PluginUtil.getVersion());
                        return;
                    }
                    String localVersion = PluginUtil.getVersion();
                    //如本次待更新版本与上一次更新一致，那么不需要处理
                    if (StringUtils.equals(response.getData().getArtifactVersion(), getLastUpdateVersion(localVersion))) {
                        return;
                    }
                    if (MarketRequest.isDownloading.get()) {
                        return;
                    }
                    //正式下载前，需要判定是否交由Rcs处理
                    if (!RemoteServiceEnum.isLocalUpdate()) {
                        //如果已经存在rcs更新路径，则直接返回
                        if (StringUtils.isNotEmpty(MarketRequest.sourceDirectory.get())) {
                            return;
                        }
                        //不存在时，叠加跳过次数
                        skipCount++;
                        //允许5次空跑，不到5次时，等待rcs处理，本地不做更新
                        if (skipCount < 5) {
                            return;
                        }
                    }
                    skipCount = 0; //本地触发下载，清空rcs空跑次数
                    //开始下载
                    boolean res = MarketRequest.downloadPlugin(response.getData().getArtifactUrl());
                    if (res) {
                        UpdateNotificationManager.notification(localVersion, response.getData().getArtifactVersion());
                        //本地下载成功后，最新更新版本提升至此次市场版本
                        lastUpdateVersion = response.getData().getArtifactVersion();
                    }
                } catch (IOException e) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "plugin Code Wiz hot-patch chain exec failed", e);
                }
            }, 0, 20L, MINUTES);
        }
        catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed update", e);
        }
    }
    private static GetPluginConfigRequest getPluginConfigRequest(String userEmail) {
        GetPluginConfigRequest request = new GetPluginConfigRequest();
        request.setUserEmail(userEmail);
        request.setPlatform("jetbrains");
        request.setVersion(PluginUtil.getVersion());
        return request;
    }

    /**
     * 获取本地上次下载升级后的版本号
     * 未下载升级过时返回当前安装版本
     * @return
     */
    private String getLastUpdateVersion(String localVersion) {
        if (StringUtils.compare(lastUpdateVersion, localVersion)  < 0) {
            lastUpdateVersion = localVersion;
        }
        return lastUpdateVersion;
    }
}

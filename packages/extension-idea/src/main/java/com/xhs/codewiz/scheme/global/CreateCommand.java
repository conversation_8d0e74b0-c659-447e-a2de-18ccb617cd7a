package com.xhs.codewiz.scheme.global;

import java.util.*;
import com.xhs.codewiz.type.global.CommandType;
/**
 * 创建全局命令。一般情况下都是用于在本地注册远端命令，很少会向远端注册命令。
 */
public class CreateCommand {
    private String schemaProtocol = "global.create.command";
    private CreateCommandParams params;

    public CreateCommandParams getParams() {
        return params;
    }
    public void setParams(CreateCommandParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateCommandParams {
        private CommandType type;
        /** 命令 */
        private String command;
    
        public CommandType getType() {
            return type;
        }
        public void setType(CommandType type) {
            this.type = type;
        }
        public String getCommand() {
            return command;
        }
        public void setCommand(String command) {
            this.command = command;
        }
    }
}

package com.xhs.codewiz.type.platform;

import java.util.*;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.workspace.ViewColumn;

/**
 * 内置面板
 */
public abstract class BuildInPanel {
    /** 类型标识 */
    public abstract String getType();

    /** 问题面板 */
    public static class BuildInPanelProblems extends BuildInPanel {
        public static final String TYPE = "problems";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

    }
    /** 差异面板 */
    public static class BuildInPanelDiff extends BuildInPanel {
        public static final String TYPE = "diff";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        private ViewColumn viewColumn;

        /** 显示的选区内容 */
        private Range selection;

        /** 左侧文件 URI */
        private String left;

        /** 右侧文件 URI */
        private String right;

        /** 面板标题 */
        private String title;

        public ViewColumn getViewColumn() {
            return viewColumn;
        }

        public void setViewColumn(ViewColumn viewColumn) {
            this.viewColumn = viewColumn;
        }

        public Range getSelection() {
            return selection;
        }

        public void setSelection(Range selection) {
            this.selection = selection;
        }

        public String getLeft() {
            return left;
        }

        public void setLeft(String left) {
            this.left = left;
        }

        public String getRight() {
            return right;
        }

        public void setRight(String right) {
            this.right = right;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

    }
    /** views 面板，比如资源管理器、搜索等标准面板 */
    public static class BuildInPanelViews extends BuildInPanel {
        public static final String TYPE = "views";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        /** 视图 ID */
        private String viewId;

        public String getViewId() {
            return viewId;
        }

        public void setViewId(String viewId) {
            this.viewId = viewId;
        }

    }
    /** Webview 面板 */
    public static class BuildInPanelWebview extends BuildInPanel {
        public static final String TYPE = "webview";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        private ViewColumn viewColumn;

        /** Webview provider ID */
        private String webviewId;

        /** 面板标题 */
        private String title;

        public ViewColumn getViewColumn() {
            return viewColumn;
        }

        public void setViewColumn(ViewColumn viewColumn) {
            this.viewColumn = viewColumn;
        }

        public String getWebviewId() {
            return webviewId;
        }

        public void setWebviewId(String webviewId) {
            this.webviewId = webviewId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

    }
}

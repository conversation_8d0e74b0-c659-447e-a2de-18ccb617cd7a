package com.xhs.codewiz.listener;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.util.Pair;
import com.xhs.codewiz.lang.LspServiceForTylm;
import com.xhs.codewiz.lang.agent.commands.GitRepoInfo;
import com.xhs.codewiz.lang.entity.CloseProjectConnectParams;
import com.xhs.codewiz.utils.FileUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import git4idea.repo.GitRepository;
import git4idea.repo.GitRepositoryChangeListener;
import java.util.concurrent.atomic.AtomicBoolean;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManagerListener;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.factory.webview.BrowserWindowFactory;
import com.xhs.codewiz.utils.JBCefBrowserUtil;

public class MultiProjectManagerListener implements ProjectManagerListener {
    private static final Logger log = Logger.getInstance(MultiProjectManagerListener.class);
    public static final AtomicBoolean INITED = new AtomicBoolean(false);

    @SuppressWarnings("removal")
    public void projectOpened(@NotNull Project project) {
        try {
            log.info("Project opened: " + project.getName());

            // 初始化虚拟文件监听器，这个2019+无法在plugin.xml中注册
            VirtualFileManager.getInstance().addVirtualFileListener(new CodeWizVirtualFileListener());

            // 添加工具窗口监听器
            BrowserWindowFactory.getInstance().projectAddToolWindowListener(project);
            ApplicationManager.getApplication().executeOnPooledThread(() -> {
                try {
                    Pair<Integer, Long> infoPair = LspServiceForTylm.readCosyInfoFile(60);
                    if (infoPair == null) {
                        log.warn("Cannot read from .info, connect to Cosy server failed");
                    } else {
                        Integer port = infoPair.first;
                        Long pid = infoPair.second;
                        if (port == null || pid == null) {
                            log.warn("Cannot get port and pid from .info file, connect failed");
                        }
                    }
                    //尽量保证已经启动成功lingma才能init
                    LspServiceForTylm.initializeParams(project);

                    String projectBasePath = project.getBasePath();
                    project.getMessageBus().connect().subscribe(
                        GitRepository.GIT_REPO_CHANGE, new GitRepositoryChangeListener() {
                            @Override
                            public void repositoryChanged(GitRepository repository) {
                                String repoPath = repository.getRoot().getPath();
                                if (projectBasePath != null && projectBasePath.equals(repoPath)) {
                                    // 只处理project根目录仓库
                                    GitRepoInfo info = new GitRepoInfo(project);
                                    LSPManager.getInstance().notify(info);
                                }
                            }
                        }
                    );
                } catch (InterruptedException e) {
                    log.warn("Connect to Cosy server error, try to kill process and restart", (Throwable)e);
                }
            });
        } catch (Exception e) {
            log.warn("Failed to add tool window listener for project: " + project.getName(), e);
        }
    }

    public void projectClosed(@NotNull Project project) {
        log.info("Project closed: " + project.getName());

        try {
            // 移除工具窗口监听器
            BrowserWindowFactory.getInstance().projectRemoveToolWindowListener(project);

            // 移除builder
            JBCefBrowserUtil.clearBuilder(project);
        } catch (Exception e) {
            log.warn("Failed to remove tool window listener for project: " + project.getName(), e);
        }
        RcsWebSocketManager.INSTANCE.close(project);

        CloseProjectConnectParams closeProjectConnectParams = new CloseProjectConnectParams();
        closeProjectConnectParams.setProjectUri(FileUtil.getProjectBaseDir(project));
        LspServiceForTylm.closeProjectConnect(closeProjectConnectParams);
    }
}
package com.xhs.codewiz.type.content;

import java.util.*;

public class TextOrSnippet {
    /** 文本内容 */
    private String text;

    /** 是否为代码片段 */
    private Boolean isSnippet;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Boolean getIsSnippet() {
        return isSnippet;
    }

    public void setIsSnippet(Boolean isSnippet) {
        this.isSnippet = isSnippet;
    }

}

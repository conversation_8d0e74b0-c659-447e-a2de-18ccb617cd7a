package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.content.RevealType;
import com.xhs.codewiz.type.file.Uri;
/**
 * 滚动视图到内容
 */
public class ExecuteRevealRange {
    private String schemaProtocol = "content.execute.revealrange";
    private ExecuteRevealRangeParams params;

    public ExecuteRevealRangeParams getParams() {
        return params;
    }
    public void setParams(ExecuteRevealRangeParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteRevealRangeParams {
        private Range range;
        private RevealType type; // optional
        private String uri;
    
        public Range getRange() {
            return range;
        }
        public void setRange(Range range) {
            this.range = range;
        }
        public RevealType getType() {
            return type;
        }
        public void setType(RevealType type) {
            this.type = type;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

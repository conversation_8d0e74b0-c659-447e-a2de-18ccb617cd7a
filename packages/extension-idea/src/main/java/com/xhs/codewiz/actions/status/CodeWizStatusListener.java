package com.xhs.codewiz.actions.status;


import com.intellij.util.messages.Topic;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@FunctionalInterface
public interface CodeWizStatusListener {
    Topic<CodeWizStatusListener> TOPIC = Topic.create("codewiz.status", CodeWizStatusListener.class);

    void onCodeWizStatus(@NotNull CodeWizStatus var1, @Nullable String var2);
}


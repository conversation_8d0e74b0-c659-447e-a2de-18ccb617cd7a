package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.FileChanges;
/**
 * 更新工作区内容
 */
public class ExecuteChanges {
    private String schemaProtocol = "workspace.execute.changes";
    private ExecuteChangesParams params;

    public ExecuteChangesParams getParams() {
        return params;
    }
    public void setParams(ExecuteChangesParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteChangesParams {
        /** 工作区内容编辑列表 */
        private List<FileChanges> changes;
    
        public List<FileChanges> getChanges() {
            return changes;
        }
        public void setChanges(List<FileChanges> changes) {
            this.changes = changes;
        }
    }
}

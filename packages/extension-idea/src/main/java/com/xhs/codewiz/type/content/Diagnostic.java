package com.xhs.codewiz.type.content;

import java.util.*;
import com.xhs.codewiz.type.global.Severity;

/**
 * 诊断信息
 */
public class Diagnostic {
    /** 诊断严重性 */
    private Severity severity;

    /** 诊断代码 */
    private String code;

    private Range range;

    /** 诊断来源 */
    private String source;

    /** 诊断消息 */
    private String message;

    public Severity getSeverity() {
        return severity;
    }

    public void setSeverity(Severity severity) {
        this.severity = severity;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Range getRange() {
        return range;
    }

    public void setRange(Range range) {
        this.range = range;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}

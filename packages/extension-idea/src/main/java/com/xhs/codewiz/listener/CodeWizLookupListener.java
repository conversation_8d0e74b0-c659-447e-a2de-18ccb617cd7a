package com.xhs.codewiz.listener;

import com.intellij.codeInsight.lookup.Lookup;
import com.intellij.codeInsight.lookup.LookupManagerListener;
import com.intellij.codeInsight.template.TemplateManager;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.WriteIntentReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.util.Computable;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.xhs.codewiz.editor.CodeWizEditorManager;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import com.xhs.codewiz.editor.InlayDisposeContext;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.utils.CodeWizPsiUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;

public class CodeWizLookupListener implements LookupManagerListener {
    private static final Logger LOG = Logger.getInstance(CodeWizLookupListener.class);

    public CodeWizLookupListener() {
    }

    /**
     * 沿用copilot设计，在ide自动补全时不触发codewiz补全(取消期间触发的补全)
     * ide补全结束(补全框关闭后)，触发codewiz补全
     * @param oldLookup
     * @param newLookup
     */
    public void activeLookupChanged(@Nullable Lookup oldLookup, @Nullable Lookup newLookup) {
        LoggerUtil.INSTANCE.logDebug(LOG, "activeLookupChanged");
        Lookup validLookup = newLookup != null ? newLookup : oldLookup;
        PsiFile psiFile = validLookup != null ? validLookup.getPsiFile() : null;
        if (psiFile != null) {
            return;
        }
        CodeWizEditorManager editorManager = CodeWizEditorManager.getInstance();
        if (oldLookup != null && newLookup == null) {
            // ①补全框刚被关闭
            PsiFile file = oldLookup.getPsiFile();
            if (file != null) {
                Editor editor = oldLookup.getEditor();
                // ②IDE原生补全刚被关闭时，通知codewiz进行“补全过程”处理
                if (CodeWizEditorUtil.isSelectedEditor(editor) && editorManager.isAvailable(editor) && !editor.getDocument().isInBulkUpdate()) {
                    ApplicationManager.getApplication().invokeLater(() -> {
                        WriteIntentReadAction.run((Runnable) () -> {
                            if (this.isValidTriggerPosition(editor)) {
                                editorManager.editorModified(editor, EditEventTriggerKind.Invoke);
                            }
                        });
                    });
                }
            }
        } else if (newLookup != null && oldLookup == null) {
            // ③ide补全框刚刚被打开，且用户设置为“不展示到IDE自带补全”
            Editor editor = newLookup.getEditor();
            if (editorManager.isAvailable(editor)) {
                editorManager.cancelCompletionRequests(editor);
                editorManager.disposeInlays(editor, InlayDisposeContext.IdeCompletion);
            }
        }
    }

    private boolean isValidTriggerPosition(Editor editor) {
        return ApplicationManager.getApplication().runReadAction((Computable<Boolean>) () -> {
            char prefixChar;
            TemplateManager templateManager;
            if (editor == null || editor.getDocument().isInBulkUpdate() || editor.getCaretModel().getCaretCount() > 1) {
                return false;
            }
            if (!CodeWizPsiUtil.checkCaretAround(editor)) {
                LoggerUtil.INSTANCE.logDebug(LOG, "invalid caret around, ignore trigger");
                return false;
            }
            if (editor.getProject() != null && (templateManager = TemplateManager.getInstance(editor.getProject())) != null && templateManager.getActiveTemplate(editor) != null) {
                LoggerUtil.INSTANCE.logDebug(LOG, "Skipping template completion. There is an active template");
                return false;
            }
            try {
                PsiElement element = CodeWizPsiUtil.getCaratElement(editor);
                if (element != null) {
                    LoggerUtil.INSTANCE.logDebug(LOG, "close popup caret element class:" + element.getClass() + " parent:" + element.getParent().getClass());
                } else {
                    LoggerUtil.INSTANCE.logDebug(LOG, "close popup caret element is null");
                }
                if (CodeWizPsiUtil.isImportElement(element, editor)) {
                    LoggerUtil.INSTANCE.logDebug(LOG, "ignore trigger import statement");
                    return false;
                }
                if (CodeWizPsiUtil.isLiteralElement(editor, element)) {
                    LoggerUtil.INSTANCE.logDebug(LOG, "ignore trigger literal statement");
                    return false;
                }
            } catch (Throwable e) {
                LoggerUtil.INSTANCE.logWarn(LOG, "error when check trigger position", e);
                //进入这个catch，大概率是ide慢操作了，直接返回false
                return false;
            }
            int caretOffset = editor.getCaretModel().getOffset();
            Document document = editor.getDocument();
            int lineIndex = document.getLineNumber(caretOffset);
            TextRange lineSuffixRange = TextRange.create(caretOffset, document.getLineEndOffset(lineIndex));
            String lineSuffix = document.getText(lineSuffixRange).trim();
            TextRange linePrefixRange = TextRange.create(document.getLineStartOffset(lineIndex), caretOffset);
            String linePrefix = document.getText(linePrefixRange).trim();
            if (StringUtils.isNotBlank(lineSuffix)
                && StringUtils.isNotBlank(linePrefix)
                && (lineSuffix.charAt(0) == ')' || lineSuffix.charAt(0) == ']' || lineSuffix.charAt(0) == '=') && ((prefixChar = linePrefix.charAt(linePrefix.length() - 1)) >= 'a' && prefixChar <= 'z' || prefixChar >= 'A' && prefixChar <= 'Z' || prefixChar >= '0' && prefixChar <= '9' || prefixChar == '_' || prefixChar == ')')) {
                LoggerUtil.INSTANCE.logDebug(LOG, "invalid bracket char around caret, ignore trigger");
                return false;
            }
            return true;
        });
    }
}


package com.xhs.codewiz.utils.reference;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.intellij.codeInsight.TargetElementUtil;
import com.intellij.codeInsight.navigation.actions.GotoDeclarationAction;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.LogicalPosition;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.project.DumbService;
import com.intellij.openapi.project.IndexNotReadyException;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Pair;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiDirectory;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiReference;
import com.xhs.codewiz.lang.Features;
import com.xhs.codewiz.lang.entity.CodeReferenceResult;
import com.xhs.codewiz.lang.entity.QueryReferenceParams;
import com.xhs.codewiz.utils.StopWatch;
import com.xhs.codewiz.utils.reference.inheritance.InheritanceUtil;
import com.xhs.codewiz.utils.reference.signature.SignatureUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * Author: liukunpeng Date: 2025-03-28 Description:
 */
public class ReferenceResolver {
  private static final Logger logger = Logger.getInstance(ReferenceResolver.class);
  private static final Cache<String, CodeReferenceResult> REFERENCE_CACHE = Caffeine.newBuilder().expireAfterWrite(24L, TimeUnit.HOURS).maximumSize(1000L).build();

  public static CodeReferenceResult resolveDeclaration(Editor editor, QueryReferenceParams params) {
    CodeReferenceResult cacheResult;
    CodeReferenceResult result = new CodeReferenceResult();
    result.setItems(new ArrayList<>());
    result.setSuccess(false);
    if (editor == null || editor.getProject() == null) {
      return result;
    }
    LogicalPosition logicalPosition = new LogicalPosition(params.getTargetRow(), params.getTargetColumn());
    FileDocumentManager fileDocumentManager = FileDocumentManager.getInstance();
    VirtualFile virtualFile = fileDocumentManager.getFile(editor.getDocument());
    if (virtualFile == null) {
      return result;
    }
    String cacheKey = String.format("decl:%s:%s:%d:%d", editor.getProject().getBasePath(), virtualFile.getPresentableUrl(), params.getTargetRow(), params.getTargetColumn());
    if (StringUtils.isNotBlank((CharSequence)params.getKey())) {
      cacheKey = String.format("decl:%s:%s", editor.getProject().getBasePath(), params.getKey());
    }
    if ((cacheResult = REFERENCE_CACHE.getIfPresent(cacheKey)) != null) {
      logger.debug("Using reference cache: " + (cacheResult.getItems() == null ? 0 : cacheResult.getItems().size()) + " for " + cacheKey);
      return cacheResult;
    }
    logger.debug("query reference cache " + cacheKey);
    ReferenceResolver.findDeclarations(editor.getProject(), editor, logicalPosition, result);
    if (result.isSuccess()) {
      REFERENCE_CACHE.put(cacheKey, result);
    }
    return result;
  }
  public static void findDeclarations(@NotNull Project project, @NotNull Editor editor, LogicalPosition logicalPosition, CodeReferenceResult result) {
    DumbService.getInstance((Project)project).runWithAlternativeResolveEnabled(() -> ApplicationManager.getApplication().runReadAction(() -> {
      try {
        int offset = editor.logicalPositionToOffset(logicalPosition);
        StopWatch sw = new StopWatch();
        sw.start("doSelectCandidate");
        Pair<PsiElement[], PsiElement> pair = ReferenceResolver.doSelectCandidate(project, editor, offset);
        long doSelectCandidateCost = sw.stop();
        PsiElement[] elements = (PsiElement[])pair.first;
        PsiElement usage = (PsiElement)pair.second;
        if (elements.length == 0) {
          logger.debug(String.format("no elements found, time cost(ms): doSelectCandidate[%d]", doSelectCandidateCost));
          return;
        }
        HashSet<PsiElement> elementSet = new HashSet();
        Collections.addAll(elementSet, elements);
        sw.start("getSupers");
        PsiElement[] supers = InheritanceUtil.getSupers(elements);
        long getSupersCost = sw.stop();
        supers = Arrays.stream(supers).limit(
            Features.QUERY_REF_FROM_IDE_INHERIT_MAX_COUNT.intValue().intValue()).toArray(PsiElement[]::new);
        Collections.addAll(elementSet, supers);
        ArrayList<Long> parseElementCostList = new ArrayList<Long>();
        int elementIdx = 0;
        for (PsiElement element : elementSet) {
          CodeReferenceResult.CodeReferenceItem item;
          sw.start(String.format("element-%d", ++elementIdx));
          if (element instanceof PsiDirectory) {
            PsiDirectory dir = (PsiDirectory)element;
            String dirPath = dir.getVirtualFile().getPresentableUrl();
            logger.debug(String.format(String.format("find directory element, path: %s", dirPath), new Object[0]));
            item = new CodeReferenceResult.CodeReferenceItem();
            item.setFilePath(dirPath);
          } else {
            item = ReferenceResolver.parseElement(element);
          }
          parseElementCostList.add(sw.stop());
          if (item == null) continue;
          result.getItems().add(item);
        }
        result.setSuccess(true);
        logger.debug(String.format("find %d elements and %d supers, time cost(ms): doSelectCandidate[%d], getSupers[%d], parseElements[%s]", elements.length, supers.length, doSelectCandidateCost, getSupersCost, parseElementCostList));
      }
      catch (IndexNotReadyException e) {
        result.setSuccess(false);
        logger.warn("Index not ready");
      }
    }));
  }

  @NotNull
  private static Pair<PsiElement[], PsiElement> doSelectCandidate(@NotNull Project project, @NotNull Editor editor, int offset) {
    PsiElement[] elements = GotoDeclarationAction.findAllTargetElements(project, editor, offset);
    logger.debug(String.format("find %d target elements at offset[%d] in project[%s]", elements.length, offset, project.getBasePath()));
    if (elements.length == 0) {
      PsiElement usage = ReferenceResolver.suggestCandidates(
          TargetElementUtil.findReference(editor, offset)).isEmpty() ? GotoDeclarationAction.findElementToShowUsagesOf(editor, offset) : null;
      return new Pair(elements, usage);
    }
    return elements.length == 1 ? new Pair(elements, GotoDeclarationAction.findElementToShowUsagesOf(editor, offset)) : new Pair(elements, null);
  }

  @NotNull
  private static Collection<PsiElement> suggestCandidates(@Nullable PsiReference reference) {
    if (reference == null) {
      return Collections.emptyList();
    }
    return TargetElementUtil.getInstance().getTargetCandidates(reference);
  }
  public static CodeReferenceResult.CodeReferenceItem parseElement(PsiElement el) {
    StopWatch sw = new StopWatch();
    sw.start("getContainingFile");
    PsiFile psiFile = el.getContainingFile();
    long getContainingFileCost = sw.stop();
    if (psiFile == null) {
      return null;
    }
    sw.start("getVirtualFile");
    VirtualFile virtualFile = psiFile.getVirtualFile();
    long getVirtualFileCost = sw.stop();
    if (virtualFile == null) {
      return null;
    }
    sw.start("generateSignature");
    String content = SignatureUtil.generateSignature(psiFile);
    long generateSignatureCost = sw.stop();
    CodeReferenceResult.CodeReferenceItem item = new CodeReferenceResult.CodeReferenceItem();
    item.setContent(content);
    item.setFilePath(virtualFile.getPresentableUrl());
    logger.debug(String.format("parse element in file[%s], time cost(ms): getContainingFile[%d], getVirtualFile[%d], generateSignature[%d]", virtualFile.getPresentableUrl(), getContainingFileCost, getVirtualFileCost, generateSignatureCost));
    return item;
  }
}

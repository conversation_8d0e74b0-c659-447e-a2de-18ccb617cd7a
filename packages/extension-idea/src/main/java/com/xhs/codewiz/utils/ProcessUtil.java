package com.xhs.codewiz.utils;

import com.intellij.openapi.diagnostic.Logger;
import com.sun.jna.Pointer;
import com.sun.jna.platform.win32.Kernel32;
import com.sun.jna.platform.win32.WinNT;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Author: liukunpeng Date: 2025-03-17 Description:
 * ProcessUtils
 */
public class ProcessUtil {
  private static final Logger log = Logger.getInstance(ProcessUtil.class);
  public static final String WINDOWS_OS = "win";
  public static final String LINUX_OS = "nux";
  public static final String LINIX_OS = "nix";
  public static final String MAC_OS = "mac";
  public static List<Long> findLingmaPidList() {
    return ProcessUtil.getPidListFromName(".lingma-xhs/");
  }
  public static Long getPid(Process process) {
    Long pid = System.getProperty("os.name").toLowerCase().contains("windows") ? ProcessUtil.windowsProcessId(process) : ProcessUtil.unixLikeProcessId(process);
    return pid;
  }
  private static Long unixLikeProcessId(Process process) {
    Class<?> clazz = process.getClass();
    try {
      if ("java.lang.UNIXProcess".equals(clazz.getName()) || "java.lang.ProcessImpl".equals(clazz.getName())) {
        Field pidField = clazz.getDeclaredField("pid");
        pidField.setAccessible(true);
        Object value = pidField.get(process);
        if (value instanceof Integer) {
          log.debug("Detected pid: {}", new Object[]{value});
          return ((Integer)value).longValue();
        }
      }
    }
    catch (IllegalAccessException | IllegalArgumentException | NoSuchFieldException | SecurityException exception) {
      // empty catch block
    }
    return null;
  }

  private static Long windowsProcessId(Process process) {
    if (process.getClass().getName().equals("java.lang.Win32Process") || process.getClass().getName().equals("java.lang.ProcessImpl")) {
      try {
        Field f = process.getClass().getDeclaredField("handle");
        f.setAccessible(true);
        long handl = f.getLong(process);
        Kernel32 kernel = Kernel32.INSTANCE;
        WinNT.HANDLE handle = new WinNT.HANDLE();
        handle.setPointer(Pointer.createConstant((long)handl));
        int ret = kernel.GetProcessId(handle);
        log.debug("Detected windows pid: {}", new Object[]{ret});
        return (long)ret;
      }
      catch (Throwable e) {
        e.printStackTrace();
      }
    }
    return null;
  }
  public static List<Long> getPidListFromName(String name) {
    String osName = System.getProperty("os.name").toLowerCase();
    if (isWindowsPlatform()) {
      log.info(String.format("Get pid list Windows mode. Name: [%s]", name));
      String[] command = new String[]{"cmd", "/c", "tasklist /FI \"IMAGENAME eq " + name + ".exe\""};
      List<Long> pids = getPidListWindows(command);
      if ((pids == null || pids.isEmpty()) && ((pids = getPidListWindows(command = new String[]{"c:\\windows\\system32\\cmd.exe", "/c", "c:\\windows\\system32\\tasklist.exe /FI \"IMAGENAME eq " + name + ".exe\""})) == null || pids.isEmpty())) {
        command = new String[]{"cmd", "/c", "wmic process where \"name='" + name + "'\" get processid"};
        return getPidListByWmic(command);
      }
      return pids;
    }
    if (!osName.contains(LINIX_OS) && !osName.contains(LINUX_OS)) {
      if (osName.contains(MAC_OS)) {
        log.info(String.format("Get pid list MacOS mode. Name: [%s]", name));
        String[] command = new String[]{"/bin/sh", "-c", "ps -eo pid,command | grep " + name};
        List<Long> pids = getPidList(command);
        if (pids == null || pids.isEmpty()) {
          command = new String[]{"pgrep", name};
          pids = getPidListByGrep(command);
        }
        return pids;
      }
      log.info(String.format("Unsupported OS: Get pid list for Name: [%s] return empty list", name));
      return null;
    }
    log.info(String.format("Get pid list Linux/Unix mode. Name: [%s]", name));
    String[] command = new String[]{"/bin/sh", "-c", "ps -eo pid,command | grep " + name};
    return getPidList(command);
  }
  public static boolean isWindowsPlatform() {
    String osName = System.getProperty("os.name").toLowerCase();
    return osName.indexOf(WINDOWS_OS) >= 0;
  }

  private static List<Long> getPidList(String[] command) {
    log.info(String.format("getPidList Command [%s]", String.join((CharSequence)",", command)));
    try {
      String strLine;
      Runtime rt = Runtime.getRuntime();
      Process pr = rt.exec(command);
      InputStreamReader isReader = new InputStreamReader(pr.getInputStream());
      BufferedReader bReader = new BufferedReader(isReader);
      ArrayList<Long> pidList = new ArrayList<Long>();
      while ((strLine = bReader.readLine()) != null) {
        String[] outputs;
        if (!strLine.contains("Lingma") || !strLine.contains("start") || (outputs = strLine.trim().split("\\s+")).length <= 0) continue;
        try {
          pidList.add(Long.parseLong(outputs[0]));
        }
        catch (Exception e) {
          log.warn(String.format("Parse [%s] and add pid list encountered exception: %s", strLine, e.getMessage()));
        }
      }
      return pidList;
    }
    catch (Exception ex) {
      log.warn(String.format("Got exception using system command [%s].", String.join((CharSequence)",", command)), (Throwable)ex);
      return null;
    }
  }

  private static List<Long> getPidListWindows(String[] command) {
    log.info(String.format("getPidListWindows Command [%s]", String.join((CharSequence)",", command)));
    try {
      String strLine;
      Runtime rt = Runtime.getRuntime();
      Process pr = rt.exec(command);
      InputStreamReader isReader = new InputStreamReader(pr.getInputStream());
      BufferedReader bReader = new BufferedReader(isReader);
      ArrayList<Long> pidList = new ArrayList<Long>();
      while ((strLine = bReader.readLine()) != null) {
        String[] outputs;
        log.info("windows get pid output:" + strLine);
        if (!strLine.contains("Lingma") || (outputs = strLine.trim().split("[\\s\t]+")).length <= 0) continue;
        try {
          pidList.add(Long.parseLong(outputs[1]));
        }
        catch (Exception e) {
          log.warn(String.format("Parse [%s] and add pid list encountered exception: %s", strLine, e.getMessage()));
        }
      }
      return pidList;
    }
    catch (Exception ex) {
      log.warn(String.format("Got exception using system command [%s].", String.join((CharSequence)",", command)), (Throwable)ex);
      return null;
    }
  }
  private static List<Long> getPidListByWmic(String[] command) {
    log.info(String.format("getPidListByWmic Command [%s]", String.join((CharSequence)",", command)));
    ArrayList<Long> pids = new ArrayList<Long>();
    try {
      String line;
      ProcessBuilder pb = new ProcessBuilder(command);
      pb.redirectErrorStream(true);
      Process process = pb.start();
      BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
      ArrayList<String> lines = new ArrayList<String>();
      while ((line = in.readLine()) != null) {
        lines.add(line);
      }
      in.close();
      process.waitFor();
      if (lines.size() > 1) {
        try {
          pids.add(Long.parseLong(((String)lines.get(1)).trim()));
        }
        catch (Exception e) {
          log.warn(String.format("Parse [%s] and add pid list encountered exception: %s", lines, e.getMessage()));
        }
      }
    }
    catch (Exception ex) {
      log.warn(String.format("Got exception using system command [%s].", String.join((CharSequence)",", command)), (Throwable)ex);
    }
    return pids;
  }

  private static List<Long> getPidListByGrep(String[] command) {
    log.info(String.format("getPidListByGrep Command [%s]", String.join((CharSequence)",", command)));
    ArrayList<Long> pids = new ArrayList<Long>();
    try {
      String line;
      ProcessBuilder pb = new ProcessBuilder(command);
      pb.redirectErrorStream(true);
      Process process = pb.start();
      BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
      while ((line = in.readLine()) != null) {
        try {
          pids.add(Long.parseLong(line.trim()));
        }
        catch (Exception e) {
          log.warn(String.format("Parse [%s] and add pid list encountered exception: %s", line, e.getMessage()));
        }
      }
      in.close();
      process.waitFor();
    }
    catch (Exception ex) {
      log.warn(String.format("Got exception using system command [%s].", String.join((CharSequence)",", command)), (Throwable)ex);
    }
    return pids;
  }

  public static boolean isProcessAlive(long pid) {
    String command;
    String osName = System.getProperty("os.name").toLowerCase();
    if (isWindowsPlatform()) {
      log.info(String.format("Check alive Windows mode. Pid: [%d]", pid));
      command = "C:\\Windows\\System32\\cmd.exe /c C:\\Windows\\System32\\tasklist.exe /FI \"PID eq " + pid + "\"";
    } else if (osName.contains(LINIX_OS) || osName.contains(LINUX_OS) || osName.contains(MAC_OS)) {
      log.info(String.format("Check alive Linux/Unix mode. Pid: [%d]", pid));
      command = "ps -p " + pid;
    } else {
      log.info(String.format("Unsupported OS: Check alive for Pid: [%d] return false", pid));
      return false;
    }
    return isProcessIdRunning(pid, command);
  }
  private static boolean isProcessIdRunning(long pid, String command) {
    log.info(String.format("Command [%s]", command));
    try {
      String strLine;
      Runtime rt = Runtime.getRuntime();
      Process pr = rt.exec(command);
      InputStreamReader isReader = new InputStreamReader(pr.getInputStream());
      BufferedReader bReader = new BufferedReader(isReader);
      while ((strLine = bReader.readLine()) != null) {
        if (!strLine.contains(pid + " ")) continue;
        return true;
      }
      return false;
    }
    catch (Exception ex) {
      log.warn(String.format("Got exception using system command [%s].", command), (Throwable)ex);
      return true;
    }
  }

}

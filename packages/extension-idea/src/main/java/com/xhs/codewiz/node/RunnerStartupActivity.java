package com.xhs.codewiz.node;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.StartupActivity;
import com.intellij.openapi.wm.RegisterToolWindowTask;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowAnchor;
import com.intellij.openapi.wm.ToolWindowManager;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.factory.webview.BrowserWindowFactory;
import com.xhs.codewiz.utils.IconsUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import java.io.IOException;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-07-26 Description:
 */
public class RunnerStartupActivity implements StartupActivity.DumbAware{
  private static final Logger log = Logger.getInstance(RunnerStartupActivity.class);

  @Override
  public void runActivity(@NotNull Project project) {
    ProgressManager.getInstance().run(new Task.Backgroundable(project, "Rednote CodeWiz Init", false) {
      @Override
      public void run(@NotNull ProgressIndicator indicator) {
        if (!NodeServerRunner.checkServerStatus()) {
          //未启动时，需要先启动 server
          NodeServerRunner.startServer();
          //尝试等待启动成功
          NodeServerRunner.waitForServer(indicator);
          indicator.setText2("");
        }
        if (!RcsWebSocketManager.INSTANCE.checkWebsocketAgent(project, false)) {
          //未准备过，那么需要初始化
          try {
            indicator.setText2("插件依赖资源准备中");
            //初始化websocket
            RcsWebSocketManager.INSTANCE.connectCosyServer(project);
            indicator.setText2("");
          } catch (IOException e) {
            LoggerUtil.INSTANCE.logError(log, "Rcs Server Init err", e);
          }
        }
      }
    });
  }


}

package com.xhs.codewiz.actions;

import com.intellij.application.options.CodeStyle;
import com.intellij.codeInsight.lookup.LookupManager;
import com.intellij.codeInsight.template.TemplateManager;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.editor.Caret;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.actionSystem.EditorAction;
import com.intellij.openapi.editor.actionSystem.EditorActionHandler;
import com.intellij.openapi.keymap.KeymapUtil;
import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.TextRange;
import com.xhs.codewiz.completion.enums.CodeWizApplyInlayStrategy;
import com.xhs.codewiz.editor.CodeWizEditorManager;
import com.xhs.codewiz.editor.util.EditorUtilCopy;
import com.xhs.codewiz.setting.CodeWizApplicationSettings;
import com.xhs.codewiz.utils.CodewizStringUtil;
import java.awt.event.KeyEvent;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CodeWizApplyInlaysAction extends EditorAction implements DumbAware {
    public static final String ID = "codewiz.rcs.applyInlays";

    public CodeWizApplyInlaysAction() {
        super(new ApplyInlaysHandler());
        this.setInjectedContext(true);
    }

    public void update(@NotNull AnActionEvent e) {
        if (this.isIgnoredKeyboardEvent(e)) {
            e.getPresentation().setEnabled(false);
        } else {
            super.update(e);
        }
    }

    private boolean isIgnoredKeyboardEvent(@NotNull AnActionEvent e) {
        if (!(e.getInputEvent() instanceof KeyEvent)) {
            return false;
        }
        if (((KeyEvent)e.getInputEvent()).getKeyChar() != '\t') {
            return false;
        }
        Project project = e.getProject();
        if (project == null) {
            return false;
        }
        Editor editor = this.getEditor(e.getDataContext());
        if (editor == null) {
            return false;
        }
        Document document = editor.getDocument();
        int blockIndent = CodeStyle.getIndentOptions(project, document).INDENT_SIZE;
        int caretOffset = editor.getCaretModel().getOffset();
        int line = document.getLineNumber(caretOffset);
        if (isNonEmptyLinePrefix(document, line, caretOffset)) {
            return false;
        }
        if (isNonEmptyLinePrefix(document, line, caretOffset)) {
            return false;
        }
        int caretOffsetAfterTab = EditorUtilCopy.indentLine(project, editor, line, blockIndent, caretOffset);
        if (caretOffsetAfterTab < caretOffset) {
            return false;
        }
        TextRange tabRange = TextRange.create(caretOffset, caretOffsetAfterTab);
        CodeWizEditorManager editorManager = CodeWizEditorManager.getInstance();
        if (editorManager.countCompletionInlays(editor, tabRange, true, false, false, false) > 0) {
            return false;
        }
        int endOfLineInlays = editorManager.countCompletionInlays(editor, tabRange, false, true, false, false);
        if (endOfLineInlays > 0) {
            return false;
        }
        int blockInlays = editorManager.countCompletionInlays(editor, tabRange, false, false, true, false);
        if (blockInlays > 0) {
            TextRange caretToEndOfLineRange = TextRange.create(caretOffset, document.getLineEndOffset(line));
            return editorManager.countCompletionInlays(editor, caretToEndOfLineRange, true, true, false, true) > 0;
        }
        return true;
    }

    private static boolean isNonEmptyLinePrefix(Document document, int lineNumber, int caretOffset) {
        int lineStartOffset = document.getLineStartOffset(lineNumber);
        if (lineStartOffset == caretOffset) {
            return false;
        } else {
            String linePrefix = document.getText(TextRange.create(lineStartOffset, caretOffset));
            return !CodewizStringUtil.isSpacesOrTabs(linePrefix, false);
        }
    }

    public static boolean isSupported(@NotNull Editor editor) {
        Project project = editor.getProject();
        boolean ideCompletionsSupported = CodeWizApplicationSettings.settings().isShowIdeCompletions();
        return project != null
            && editor.getCaretModel().getCaretCount() == 1
            && (ideCompletionsSupported || LookupManager.getActiveLookup(editor) == null)
            && CodeWizEditorManager.getInstance().hasCompletionInlays(editor)
            && TemplateManager.getInstance(project).getActiveTemplate(editor) == null;
    }

    private static class ApplyInlaysHandler extends EditorActionHandler {
        private ApplyInlaysHandler() {
        }

        protected boolean isEnabledForCaret(@NotNull Editor editor, @NotNull Caret caret, DataContext dataContext) {
            return CodeWizApplyInlaysAction.isSupported(editor);
        }

        public boolean executeInCommand(@NotNull Editor editor, DataContext dataContext) {
            return false;
        }

        protected void doExecute(@NotNull Editor editor, @Nullable Caret caret, DataContext dataContext) {
            CodeWizEditorManager.getInstance().applyCompletion(editor, CodeWizApplyInlayStrategy.WHOLE);
        }
    }
}


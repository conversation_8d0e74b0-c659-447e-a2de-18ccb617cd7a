package com.xhs.codewiz.lang.agent.lsp;


import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import java.lang.reflect.Type;

public enum TextDocumentSyncKind {
    None(0),
    Full(1),
    Incremental(2);

    private final int id;

    private TextDocumentSyncKind(int id) {
        this.id = id;
    }

    public static final class TypeAdapter implements JsonSerializer<TextDocumentSyncKind> {
        public TypeAdapter() {
        }

        public JsonElement serialize(TextDocumentSyncKind syncKind, Type type, JsonSerializationContext context) {
            return new JsonPrimitive(syncKind.id);
        }
    }
}


package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 显示面板
 */
public class ExecuteHidePanel {
    private String schemaProtocol = "platform.execute.hidepanel";
    private ExecuteHidePanelParams params;

    public ExecuteHidePanelParams getParams() {
        return params;
    }
    public void setParams(ExecuteHidePanelParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteHidePanelParams {
        /** 是否销毁面板 */
        private Boolean dispose; // optional
        /** 面板 ID */
        private String id;
    
        public Boolean getDispose() {
            return dispose;
        }
        public void setDispose(Boolean dispose) {
            this.dispose = dispose;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

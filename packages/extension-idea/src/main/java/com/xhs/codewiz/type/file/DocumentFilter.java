package com.xhs.codewiz.type.file;

import java.util.*;

/**
 * 文档过滤器，用于指定哪些文档类型的内容可以被处理
 */
public class DocumentFilter {
    private String scheme;

    private GlobPattern pattern;

    private String language;

    public String getScheme() {
        return scheme;
    }

    public void setScheme(String scheme) {
        this.scheme = scheme;
    }

    public GlobPattern getPattern() {
        return pattern;
    }

    public void setPattern(GlobPattern pattern) {
        this.pattern = pattern;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

}

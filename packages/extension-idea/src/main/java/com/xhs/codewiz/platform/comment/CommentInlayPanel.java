package com.xhs.codewiz.platform.comment;

import static com.xhs.codewiz.utils.IdeNotificationUtil.showMessage;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.intellij.icons.AllIcons;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.actionSystem.ActionToolbar;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.Presentation;
import com.intellij.openapi.actionSystem.impl.ActionButton;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorCustomElementRenderer;
import com.intellij.openapi.editor.Inlay;
import com.intellij.openapi.editor.markup.TextAttributes;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.ui.JBColor;
import com.intellij.util.messages.MessageBus;
import com.intellij.util.ui.JBUI;
import com.intellij.util.ui.UIUtil;
import com.xhs.codewiz.actions.command.DiffCommentCommandUtil;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.platform.enums.CommentStatus;
import com.xhs.codewiz.scheme.global.ExecuteCommand;
import com.xhs.codewiz.scheme.global.ExecuteCommand.ExecuteCommandParams;
import com.xhs.codewiz.type.global.CommandType;
import com.xhs.codewiz.utils.DiffEditorVersionUtil;
import com.xhs.codewiz.utils.IconsUtil;
import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Cursor;
import java.awt.Desktop;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Graphics2D;
import java.awt.Rectangle;
import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;
import java.awt.geom.Rectangle2D;
import java.net.URI;
import javax.swing.JButton;
import javax.swing.JComponent;
import javax.swing.JEditorPane;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.SwingUtilities;
import javax.swing.border.Border;
import javax.swing.event.HyperlinkEvent;
import javax.swing.text.View;
import org.jetbrains.annotations.NotNull;

/**
 * 评论内联显示面板
 *
 * <AUTHOR>
 */
public class CommentInlayPanel extends JPanel implements EditorCustomElementRenderer, Disposable {
    // 静态常量作为默认值
    private static final int DEFAULT_PANEL_RIGHT_OFFSET = 93;
    private static final int DEFAULT_COMMENT_CONTENT_RIGHT_OFFSET = 113;

    // SimpleDiffViewer的offset值
    private static final int SIMPLE_DIFF_PANEL_RIGHT_OFFSET = 93;
    private static final int SIMPLE_DIFF_COMMENT_CONTENT_RIGHT_OFFSET = 113;

    // UnifiedDiffViewer的offset值
    private static final int UNIFIED_DIFF_PANEL_RIGHT_OFFSET = 133;
    private static final int UNIFIED_DIFF_COMMENT_CONTENT_RIGHT_OFFSET = 153;

    private Editor editor;
    private final JPanel mainPanel = new JPanel(new BorderLayout());
    private JEditorPane commentContent;
    private boolean isShowInlay = false;
    private final Comment comment;
    private Inlay<?> inlay;

    static Logger logger = Logger.getInstance(CommentInlayPanel.class);

    public CommentInlayPanel(Editor editor, Comment comment) {
        super(new BorderLayout());

        this.editor = editor;
        this.comment = comment;
        setBorder(createBorder());

        populateMainPanel();
        add(this.mainPanel, BorderLayout.CENTER);
        revalidate();
        repaint();

        // 监听编辑器大小变化
        editor.getComponent().addComponentListener(new ComponentAdapter() {
            @Override
            public void componentResized(ComponentEvent e) {
                redraw();
            }
        });

        // 监听滚动事件
        // editor.getScrollingModel().addVisibleAreaListener(e -> checkInlayVisibility(), this);

        // 订阅消息通知
        Project project = editor.getProject();
        if (project != null) {
            MessageBus messageBus = project.getMessageBus();
            // 这里可以添加消息订阅
        }
    }

    /**
     * 根据编辑器类型动态获取面板右侧偏移量
     */
    private int getPanelRightOffset() {
        try {
            return rightOffset(
                    DEFAULT_PANEL_RIGHT_OFFSET,
                    SIMPLE_DIFF_PANEL_RIGHT_OFFSET,
                    UNIFIED_DIFF_PANEL_RIGHT_OFFSET);
        } catch (Exception e) {
            logger.warn("Failed to determine panel right offset, using default", e);
            return DEFAULT_PANEL_RIGHT_OFFSET;
        }
    }

    /**
     * 根据编辑器类型动态获取评论内容右侧偏移量
     */
    private int getCommentContentRightOffset() {
        try {
            return rightOffset(
                    DEFAULT_COMMENT_CONTENT_RIGHT_OFFSET,
                    SIMPLE_DIFF_COMMENT_CONTENT_RIGHT_OFFSET,
                    UNIFIED_DIFF_COMMENT_CONTENT_RIGHT_OFFSET);
        } catch (Exception e) {
            logger.warn("Failed to determine comment content right offset, using default", e);
            return DEFAULT_COMMENT_CONTENT_RIGHT_OFFSET;
        }
    }

    private int rightOffset(int defaultCommentContentRightOffset,
                            int simpleDiffCommentContentRightOffset,
                            int unifiedDiffCommentContentRightOffset) {
        Project project = editor.getProject();
        if (project == null) {
            return defaultCommentContentRightOffset;
        }

        FileEditor selectedEditor = FileEditorManager.getInstance(project).getSelectedEditor();
        if (selectedEditor == null) {
            return defaultCommentContentRightOffset;
        }

        if (DiffEditorVersionUtil.isSimpleDiffViewer(selectedEditor)) {
            return simpleDiffCommentContentRightOffset;
        } else if (DiffEditorVersionUtil.isUnifiedDiffViewer(selectedEditor)) {
            return unifiedDiffCommentContentRightOffset;
        }

        return defaultCommentContentRightOffset;
    }

    private void checkInlayVisibility() {
        if (this.inlay == null) {
            return;
        }

        ApplicationManager.getApplication().runReadAction(() -> {
            Rectangle visibleArea = this.editor.getScrollingModel().getVisibleArea();
            Rectangle inlayBounds = this.inlay.getBounds();
            if (inlayBounds == null) {
                return;
            }

            boolean isInVisibleArea = visibleArea.intersects(inlayBounds);
            setVisible(isInVisibleArea);
        });
    }

    public void populateMainPanel() {
        this.mainPanel.removeAll();

        this.mainPanel.setBackground(getBackgroundColor());

        JPanel northPanel = new JPanel(new BorderLayout());
        northPanel.add(createHeaderPanel(), BorderLayout.NORTH);
        northPanel.add(createTitleAndFeedbackPanel(), BorderLayout.SOUTH);
        this.mainPanel.add(northPanel, BorderLayout.NORTH);

        // 添加内容面板
        this.mainPanel.add(createContentPanel(), BorderLayout.CENTER);

        // 添加操作面板
        //this.mainPanel.add(createOperatePanel(), BorderLayout.SOUTH);
    }

    private JPanel createOperatePanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));

        JButton likeButton = new JButton("点赞");
        JButton unlikeButton = new JButton("点踩");
        JButton delayButton = new JButton("延期修复");

        // 设置基础样式
        initializeButtonStyle(likeButton);
        initializeButtonStyle(unlikeButton);
        initializeButtonStyle(delayButton);

        // 根据评论状态设置按钮样式
        updateButtonStyles(likeButton, unlikeButton, delayButton);

        likeButton.addActionListener(e -> {
            handleLikeAction(likeButton, unlikeButton, delayButton);
        });

        unlikeButton.addActionListener(e -> {
            handleUnlikeAction(likeButton, unlikeButton, delayButton);
        });

        delayButton.addActionListener(e -> {
            handleDelayAction(likeButton, unlikeButton, delayButton);
        });

        likeButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        unlikeButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        //delayButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));

        panel.add(likeButton);
        panel.add(unlikeButton);
        //panel.add(delayButton);
        panel.setBorder(JBUI.Borders.emptyLeft(10));

        return panel;
    }

    /**
     * 初始化按钮基础样式
     */
    private void initializeButtonStyle(JButton button) {
        button.setOpaque(true);
        button.setContentAreaFilled(true);
        button.setFocusPainted(false);
        // button.setBorder(JBUI.Borders.empty(4, 8));
    }

    /**
     * 根据评论状态更新按钮样式
     */
    private void updateButtonStyles(JButton likeButton, JButton unlikeButton, JButton delayButton) {
        CommentStatus status = comment.getStatus();

        // 先重置所有按钮到默认状态
        setDefaultButtonStyle(likeButton, "点赞");
        setDefaultButtonStyle(unlikeButton, "点踩");
        setDefaultButtonStyle(delayButton, "延期修复");

        // 根据状态高亮对应按钮
        switch (status) {
            case LIKE:
                setActiveButtonStyle(likeButton, JBColor.GREEN.darker(), JBColor.BLACK, "已点赞");
                break;
            case UNLIKE:
                setActiveButtonStyle(unlikeButton, JBColor.RED.darker(), JBColor.BLACK, "已点踩");
                break;
            case DELAY:
                setActiveButtonStyle(delayButton, JBColor.YELLOW.darker(), JBColor.BLACK, "已延期");
                break;
            case UNKNOWN:
            default:
                // 默认状态已经在上面设置
                break;
        }
    }

    /**
     * 设置按钮默认样式
     */
    private void setDefaultButtonStyle(JButton button, String text) {
        button.setText(text);
        button.setBackground(UIUtil.getPanelBackground());
        button.setForeground(UIUtil.getLabelForeground());
        button.setOpaque(true);
        button.setContentAreaFilled(true);
        // button.setBorder(JBUI.Borders.empty(4, 8));
        button.repaint();
    }

    /**
     * 重置按钮样式（已弃用，改为setDefaultButtonStyle）
     */
    @Deprecated
    private void resetButtonStyle(JButton button) {
        button.setBackground(null);
        button.setForeground(null);
        button.setOpaque(false);
        button.setBorder(null);
        button.setContentAreaFilled(false);
    }

    /**
     * 设置激活状态的按钮样式
     */
    private void setActiveButtonStyle(JButton button, Color backgroundColor, Color foregroundColor, String text) {
        button.setBackground(backgroundColor);
        button.setForeground(foregroundColor);
        button.setOpaque(true);
        button.setContentAreaFilled(true);
        // button.setBorder(JBUI.Borders.empty(4, 8));
        button.setText(text);
        button.repaint();
    }

    /**
     * 处理点赞操作
     */
    private void handleLikeAction(JButton likeButton, JButton unlikeButton, JButton delayButton) {
        CommentCacheService cacheService = CommentCacheService.getInstance();
        CommentStatus newStatus = (comment.getStatus() == CommentStatus.LIKE) ? CommentStatus.UNKNOWN : CommentStatus.LIKE;

       /* // 调用 LS 接口
        String voteType = (newStatus == CommentStatus.LIKE) ? "UP" : "UNKNOWN";
        CommentVoteResponse response = CodeWizAgentLspService.commentVote(comment.getNoteId(), voteType);

        if (response != null && response.isSuccess()) {
            // 更新评论状态
            comment.setStatus(newStatus);
            cacheService.updateCommentStatus(comment.getNoteId(), newStatus);

            // 直接更新按钮样式，避免重新创建面板
            updateButtonStyles(likeButton, unlikeButton, delayButton);

            // 强制刷新按钮显示
            forceButtonRefresh(likeButton, unlikeButton, delayButton);

            logger.info("用户点赞评论: " + comment.getNoteId() + ", 状态: " + newStatus + ", LS响应: " + response.getMsg());
        } else {
            logger.warn("点赞请求失败: " + (response != null ? response.getMsg() : "响应为空"));
            showInfoMessage("点赞失败: " + (response != null ? response.getMsg() : "网络错误"));
        }*/
    }

    /**
     * 处理点踩操作
     */
    private void handleUnlikeAction(JButton likeButton, JButton unlikeButton, JButton delayButton) {
        CommentCacheService cacheService = CommentCacheService.getInstance();
        CommentStatus newStatus = (comment.getStatus() == CommentStatus.UNLIKE) ? CommentStatus.UNKNOWN : CommentStatus.UNLIKE;

        /*// 调用 LS 接口
        String voteType = (newStatus == CommentStatus.UNLIKE) ? "DOWN" : "UNKNOWN";
        CommentVoteResponse response = CodeWizAgentLspService.commentVote(comment.getNoteId(), voteType);

        if (response != null && response.isSuccess()) {
            // 更新评论状态
            comment.setStatus(newStatus);
            cacheService.updateCommentStatus(comment.getNoteId(), newStatus);

            // 直接更新按钮样式，避免重新创建面板
            updateButtonStyles(likeButton, unlikeButton, delayButton);

            // 强制刷新按钮显示
            forceButtonRefresh(likeButton, unlikeButton, delayButton);

            logger.info("用户点踩评论: " + comment.getNoteId() + ", 状态: " + newStatus + ", LS响应: " + response.getMsg());
        } else {
            logger.warn("点踩请求失败: " + (response != null ? response.getMsg() : "响应为空"));
            showInfoMessage("点踩失败: " + (response != null ? response.getMsg() : "网络错误"));
        }*/
    }

    /**
     * 处理延期修复操作
     */
    private void handleDelayAction(JButton likeButton, JButton unlikeButton, JButton delayButton) {
        CommentCacheService cacheService = CommentCacheService.getInstance();
        CommentStatus newStatus = (comment.getStatus() == CommentStatus.DELAY) ? CommentStatus.UNKNOWN : CommentStatus.DELAY;

        // 调用 LS 接口  
       /* String voteType = (newStatus == CommentStatus.DELAY) ? "DELAY" : "UNKNOWN";
        CommentVoteResponse response = CodeWizAgentLspService.commentVote(comment.getNoteId(), voteType);

        if (response != null && response.isSuccess()) {
            // 更新评论状态
            comment.setStatus(newStatus);
            cacheService.updateCommentStatus(comment.getNoteId(), newStatus);

            // 直接更新按钮样式，避免重新创建面板
            updateButtonStyles(likeButton, unlikeButton, delayButton);

            // 强制刷新按钮显示
            forceButtonRefresh(likeButton, unlikeButton, delayButton);

            logger.info("用户延期修复评论: " + comment.getNoteId() + ", 状态: " + newStatus + ", LS响应: " + response.getMsg());

            // if (newStatus == CommentStatus.DELAY) {
            //     showInfoMessage("评论已标记为延期修复");
            // }
        } else {
            logger.warn("延期修复请求失败: " + (response != null ? response.getMsg() : "响应为空"));
            showInfoMessage("延期修复失败: " + (response != null ? response.getMsg() : "网络错误"));
        }*/
    }

    /**
     * 强制刷新按钮显示
     */
    private void forceButtonRefresh(JButton... buttons) {
        SwingUtilities.invokeLater(() -> {
            for (JButton button : buttons) {
                button.invalidate();
                button.revalidate();
                button.repaint();
            }
            // 刷新整个面板
            this.revalidate();
            this.repaint();
        });
    }

    /**
     * 显示信息提示
     */
    public void showInfoMessage(String message) {
        // SwingUtilities.invokeLater(() -> {
        //     JLabel messageLabel = new JLabel(message);
        //     messageLabel.setForeground(JBColor.BLUE);
        //     // 这里可以添加更复杂的提示显示逻辑
        //     logger.info("Info: " + message);
        // });

        // get current project
        Project project = ProjectManager.getInstance().getDefaultProject();
        showMessage("warn", message, project);
    }

    private JPanel createHeaderPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        JLabel commentIndexLabel = new JLabel(String.format("AI 评论（第 %d 个 / 共 %d 个）",
                CommentPersistState.getInstance().getCommentIndex(this.comment),
                CommentPersistState.getInstance().getCommentCount(this.comment)));
        panel.add(commentIndexLabel, BorderLayout.WEST);

        // 导航按钮
        AnAction prevAction = new PreviousCommentAction(this.editor, this.comment);
        Presentation prevPresentation = prevAction.getTemplatePresentation().clone();
        ActionButton prevButton = new ActionButton(prevAction, prevPresentation, "unknown", ActionToolbar.DEFAULT_MINIMUM_BUTTON_SIZE);
        prevButton.setBorder(JBUI.Borders.empty());
        prevButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));

        AnAction nextAction = new NextCommentAction(this.editor, this.comment);
        Presentation nextPresentation = nextAction.getTemplatePresentation().clone();
        ActionButton nextButton = new ActionButton(nextAction, nextPresentation, "unknown", ActionToolbar.DEFAULT_MINIMUM_BUTTON_SIZE);
        nextButton.setBorder(JBUI.Borders.empty());
        nextButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));

        AnAction collapseAction = new CollapseCommentAction(this.editor, this.comment);
        Presentation collapsePresentation = collapseAction.getTemplatePresentation().clone();
        ActionButton closeButton =
                new ActionButton(collapseAction, collapsePresentation, "unknown", ActionToolbar.DEFAULT_MINIMUM_BUTTON_SIZE);
        closeButton.setBorder(JBUI.Borders.empty());
        closeButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));

        JPanel navigationPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        navigationPanel.add(prevButton);
        navigationPanel.add(nextButton);
        navigationPanel.add(closeButton);

        panel.add(navigationPanel, BorderLayout.EAST);

        return panel;
    }

    private JComponent createContentPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        this.commentContent = new JEditorPane();
        this.commentContent.setContentType("text/html");
        String htmlContent = buildCommentContentHtml(this.comment);
        this.commentContent.setText(htmlContent);
        this.commentContent.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        this.commentContent.setEditable(false);
        this.commentContent.setFocusable(false);
        this.commentContent.setCaretPosition(0);
        this.commentContent.setBackground(getBackgroundColor());
        // 设置JEditorPane为透明，避免HTML内容的背景色覆盖
        this.commentContent.setOpaque(false);
        this.commentContent.putClientProperty(JEditorPane.HONOR_DISPLAY_PROPERTIES, Boolean.TRUE);

        this.commentContent.addHyperlinkListener(event -> {
            if (HyperlinkEvent.EventType.ACTIVATED.equals(event.getEventType())) {
                try {
                    Desktop.getDesktop().browse(new URI(event.getURL().toString()));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        commentContentSetPreferredSize();

        JPanel textPanel = new JPanel(new BorderLayout());
        textPanel.add(this.commentContent, BorderLayout.CENTER);
        textPanel.setBorder(JBUI.Borders.empty(0, 2, 2, 8));
        panel.add(textPanel, BorderLayout.CENTER);

        panel.setBorder(JBUI.Borders.emptyLeft(2));
        return panel;
    }

    private void commentContentSetPreferredSize() {
        int availableWidth = this.editor.getComponent().getWidth() - JBUI.scale(getCommentContentRightOffset());
        View rootView = this.commentContent.getUI().getRootView(this.commentContent);
        rootView.setSize(availableWidth, Float.MAX_VALUE);

        // 添加额外高度补偿 (一行高度)
        float lineHeight = this.commentContent.getFontMetrics(this.commentContent.getFont()).getHeight();
        int preferredHeight = (int) rootView.getPreferredSpan(View.Y_AXIS) + (int) lineHeight;

        this.commentContent.setPreferredSize(new Dimension(availableWidth, preferredHeight));
    }

    private JPanel createTitleAndFeedbackPanel() {
        JLabel titleLabel = new JLabel(buildCommentTitleHtml());
        titleLabel.setIcon(IconsUtil.CODEWIZ); // 使用codewiz图标

        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.add(titleLabel, BorderLayout.CENTER);

        // 反馈按钮
        LikeCommentAction likeAction = new LikeCommentAction(this.comment, this);
        DislikeCommentAction dislikeAction = new DislikeCommentAction(this.comment, this);

        Presentation likePresentation = likeAction.getTemplatePresentation().clone();
        ActionButton likeButton = new ActionButton(likeAction, likePresentation, "unknown", ActionToolbar.DEFAULT_MINIMUM_BUTTON_SIZE);
        likeButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        likeButton.setBorder(JBUI.Borders.empty());

        Presentation dislikePresentation = dislikeAction.getTemplatePresentation().clone();
        ActionButton dislikeButton =
                new ActionButton(dislikeAction, dislikePresentation, "unknown", ActionToolbar.DEFAULT_MINIMUM_BUTTON_SIZE);
        dislikeButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        dislikeButton.setBorder(JBUI.Borders.empty());

        JPanel feedBackPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        feedBackPanel.add(likeButton);
        feedBackPanel.add(dislikeButton);

        JPanel panel = new JPanel(new BorderLayout());
        panel.add(titlePanel, BorderLayout.WEST);
        panel.add(feedBackPanel, BorderLayout.EAST);

        return panel;
    }

    private String buildCommentTitleHtml() {
        return "<html><b>CodeWiz</b></html>";
    }

    private String buildCommentContentHtml(Comment comment) {
        StringBuilder html = new StringBuilder("<html>");
        html.append("<head><style>body { font-family: 'PingFang SC', sans-serif; background-color: transparent; }</style></head>");
        html.append("<body>");

        // 添加分类标签
        // String category = comment.getCategory();
        // if (category != null && !category.isEmpty()) {
        //     String color = comment.isSeriousProblem() ? "#D32F2F" : "#FFA500";
        //     html.append("<span style=\"color: ").append(color).append(";\">【")
        //             .append(category).append("】</span><br/>");
        // }

        // 添加评论内容
        html.append("<span>").append(comment.getNoteContent()).append("</span>");

        // 添加规则链接
        // String ruleText = comment.getRuleText();
        // String ruleUrl = comment.getRuleUrl();
        // if (ruleText != null && !ruleText.isEmpty() && ruleUrl != null && !ruleUrl.isEmpty()) {
        //     html.append("<br/><a href=\"").append(ruleUrl).append("\" style=\"color:#2196F3;\">")
        //             .append(ruleText).append("</a>");
        // }

        html.append("<br/></body></html>");
        return html.toString();
    }

    public void createInlay(int offset) {
        // 使用 EditorCustomElementRenderer 接口
        this.inlay = this.editor.getInlayModel().addBlockElement(offset, false, false, 1, this);
    }

    private Border createBorder() {
        return JBUI.Borders.emptyLeft(5);
    }

    private Color getBackgroundColor() {
        return JBColor.namedColor("Editor.background", new JBColor(0xf7f7f7, 0x2b2b2b));
    }

    @Override
    public int calcWidthInPixels(@NotNull Inlay inlay) {
        int width = this.editor.getComponent().getWidth() - JBUI.scale(getPanelRightOffset());
        return Math.max(width, 1);
    }

    @Override
    public int calcHeightInPixels(@NotNull Inlay inlay) {
        int height = getPreferredSize().height;
        return this.isShowInlay ? Math.max(height, 1) : 0;
    }

    public void toggleVisibility() {
        this.isShowInlay = !this.isShowInlay;
        showInlay();
    }

    public void showInlay() {
        setVisible(this.isShowInlay);
        if (this.inlay != null) {
            // 在读取动作中执行inlay.update()以避免线程安全问题
            ApplicationManager.getApplication().runReadAction(() -> {
                this.inlay.update();
            });
        }
    }

    @Override
    public void paint(@NotNull Inlay inlay, @NotNull Graphics2D g, @NotNull Rectangle2D targetRegion,
                      @NotNull TextAttributes textAttributes) {
        try {
            Rectangle bounds = inlay.getBounds();
            if (bounds == null || getBounds().equals(bounds)) {
                return;
            }
            setBounds(bounds);
            revalidate();
            repaint();
        } catch (Exception e) {
            // 如果出现线程安全问题，静默处理避免影响渲染
            logger.warn("Error in paint method: " + e.getMessage());
        }
    }

    public void redraw() {
        SwingUtilities.invokeLater(() -> {
            commentContentSetPreferredSize();
            int newWidth = this.editor.getComponent().getWidth() - JBUI.scale(getPanelRightOffset());
            setSize(new Dimension(newWidth, getPreferredSize().height));

            Inlay<?> inlay = getInlay();
            if (inlay != null) {
                // 在读取动作中执行inlay.update()以避免线程安全问题
                ApplicationManager.getApplication().runReadAction(() -> {
                    inlay.update();
                });
            }
            revalidate();
            repaint();
        });
    }

    @Override
    public void dispose() {
        if (this.inlay != null) {
            this.inlay.dispose();
        }
    }

    // Getters
    public boolean isShowInlay() {
        return isShowInlay;
    }

    public void setShowInlay(boolean showInlay) {
        this.isShowInlay = showInlay;
    }

    public Comment getComment() {
        return comment;
    }

    public void setInlay(Inlay<?> inlay) {
        this.inlay = inlay;
    }

    public Inlay<?> getInlay() {
        return inlay;
    }

    public void setEditor(Editor editor) {
        this.editor = editor;
    }

    // Inner Action Classes
    public class CollapseCommentAction extends AnAction {
        private final Comment comment;

        public CollapseCommentAction(Editor editor, Comment comment) {
            super("折叠", "折叠评论", AllIcons.General.CollapseComponent);
            this.comment = comment;
        }

        @Override
        public void actionPerformed(@NotNull AnActionEvent e) {
            CommentInlayPanel.this.toggleVisibility();
        }
    }

    public static class PreviousCommentAction extends AnAction {
        private final Comment comment;

        public PreviousCommentAction(Editor editor, Comment comment) {
            super("上一个", "上一个评论", AllIcons.Actions.FindAndShowPrevMatches);
            this.comment = comment;
        }

        @Override
        public void actionPerformed(@NotNull AnActionEvent e) {
            CommentInlayService.getInstance().navigateToPrevious(this.comment);
        }
    }

    public static class NextCommentAction extends AnAction {
        private final Comment comment;

        public NextCommentAction(Editor editor, Comment comment) {
            super("下一个", "下一个评论", AllIcons.Actions.FindAndShowNextMatches);
            this.comment = comment;
        }

        @Override
        public void actionPerformed(@NotNull AnActionEvent e) {
            CommentInlayService.getInstance().navigateToNext(this.comment);
        }
    }

    public static class LikeCommentAction extends AnAction {
        private final Comment comment;
        private final CommentInlayPanel panel;

        public LikeCommentAction(Comment comment, CommentInlayPanel panel) {
            this.comment = comment;
            this.panel = panel;
            updatePresentation();
        }

        private void updatePresentation() {
            if (comment.getStatus() == CommentStatus.LIKE) {
                getTemplatePresentation().setText("取消点赞");
                getTemplatePresentation().setDescription("取消对此评论的点赞");
                getTemplatePresentation().setIcon(AllIcons.Ide.LikeSelected);
            } else {
                getTemplatePresentation().setText("点赞");
                getTemplatePresentation().setDescription("对此评论点赞");
                getTemplatePresentation().setIcon(AllIcons.Ide.Like);
            }
        }

        @Override
        public void actionPerformed(@NotNull AnActionEvent e) {
            Project project = e.getProject();
            if (null == project) {
                project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(comment.getChannel());
            }
            CommentStatus newStatus;
            String command;
            if (comment.getStatus() == CommentStatus.LIKE) {
                // 当前已点赞，点击后取消点赞
                newStatus = CommentStatus.CANCEL;
                command = DiffCommentCommandUtil.getCommandByStatus(e.getProject(), CommentStatus.CANCEL_UP);
            } else {
                // 当前未点赞，点击后点赞
                newStatus = CommentStatus.LIKE;
                command = DiffCommentCommandUtil.getCommandByStatus(e.getProject(), CommentStatus.LIKE);
            }

            // 调用 LS 接口
            // 调用 RCS 接口
            ExecuteCommand executeCommand = new ExecuteCommand();
            ExecuteCommandParams params = new ExecuteCommandParams();
            params.setCommand(command);
            params.setType(CommandType.Default);
            params.setArgs(Lists.newArrayList(comment.getNoteId(), comment.getCollectId()));
            executeCommand.setParams(params);
            Object obj  = RcsWebSocketManager.INSTANCE.sendRequestWithChannelProvider(comment.getChannel(), "default",
                executeCommand, new TypeToken<Object>(){},  project, 1000);

            if (obj != null) {
                // 更新评论状态
                comment.setStatus(newStatus);
                CommentCacheService.getInstance().updateCommentStatus(comment.getNoteId(), newStatus);

                logger.info("用户点赞操作: " + comment.getNoteId() + ", 新状态: " + newStatus + ", RCS响应: " +obj);

                // 触发面板刷新
                panel.refresh(project);
            } else {
                logger.warn("点赞操作失败: " + obj);
            }
        }
    }

    public static class DislikeCommentAction extends AnAction {
        private final Comment comment;
        private final CommentInlayPanel panel;

        public DislikeCommentAction(Comment comment, CommentInlayPanel panel) {
            this.comment = comment;
            this.panel = panel;
            updatePresentation();
        }

        private void updatePresentation() {
            if (comment.getStatus() == CommentStatus.UNLIKE) {
                getTemplatePresentation().setText("取消点踩");
                getTemplatePresentation().setDescription("取消对此评论的点踩");
                getTemplatePresentation().setIcon(AllIcons.Ide.DislikeSelected);
            } else {
                getTemplatePresentation().setText("点踩");
                getTemplatePresentation().setDescription("对此评论点踩");
                getTemplatePresentation().setIcon(AllIcons.Ide.Dislike);
            }
        }

        @Override
        public void actionPerformed(@NotNull AnActionEvent e) {
            Project project = e.getProject();
            if (null == project) {
                project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(comment.getChannel());
            }
            CommentStatus newStatus;
            String command;
            if (comment.getStatus() == CommentStatus.UNLIKE) {
                // 当前已点踩，点击后取消点踩
                newStatus = CommentStatus.CANCEL;
                command = DiffCommentCommandUtil.getCommandByStatus(e.getProject(), CommentStatus.CANCEL_DOWN);
            } else {
                // 当前未点踩，点击后点踩
                newStatus = CommentStatus.UNLIKE;
                command = DiffCommentCommandUtil.getCommandByStatus(e.getProject(), CommentStatus.UNLIKE);
            }

            // 调用 RCS 接口
            ExecuteCommand executeCommand = new ExecuteCommand();
            ExecuteCommandParams params = new ExecuteCommandParams();
            params.setCommand(command);
            params.setType(CommandType.Default);
            params.setArgs(Lists.newArrayList(comment.getNoteId(), comment.getCollectId()));
            executeCommand.setParams(params);
            Object obj  = RcsWebSocketManager.INSTANCE.sendRequestWithChannelProvider(comment.getChannel(), "default",
                executeCommand, new TypeToken<Object>(){},  project, 1000);
            if (obj != null) {
                // 更新评论状态
                comment.setStatus(newStatus);
                CommentCacheService.getInstance().updateCommentStatus(comment.getNoteId(), newStatus);

                logger.info("用户点踩操作: " + comment.getNoteId() + ", 新状态: " + newStatus + ", RCS响应: " + obj);

                // 触发面板刷新
                panel.refresh(e.getProject());
            } else {
                logger.warn("点踩操作失败");
            }
        }
    }

    /**
     * 延期修复Action
     */
    public static class DelayCommentAction extends AnAction {
        private final Comment comment;

        public DelayCommentAction(Comment comment) {
            super("延期修复", "标记此评论为延期修复", AllIcons.Actions.Suspend);
            this.comment = comment;
        }

        @Override
        public void actionPerformed(@NotNull AnActionEvent e) {
            CommentCacheService cacheService = CommentCacheService.getInstance();
            CommentStatus newStatus = (comment.getStatus() == CommentStatus.DELAY) ? CommentStatus.UNKNOWN : CommentStatus.DELAY;

            // 更新评论状态
            comment.setStatus(newStatus);
            cacheService.updateCommentStatus(comment.getNoteId(), newStatus);

            logger.info("用户延期修复评论: " + comment.getNoteId() + ", 状态: " + newStatus);
        }

        @Override
        public void update(@NotNull AnActionEvent e) {
            // 在read action中执行以避免线程问题
            ApplicationManager.getApplication().runReadAction(() -> {
                boolean isDelayed = comment.getStatus() == CommentStatus.DELAY;
                e.getPresentation().setText(isDelayed ? "已延期" : "延期修复");
                e.getPresentation().setDescription(isDelayed ? "取消延期" : "标记此评论为延期修复");
            });
        }
    }

    public void refresh(Project project) {
        if (project == null) {
            return;
        }
        if (this.inlay == null) {
            return;
        }

        // 在UI线程中执行刷新操作
        ApplicationManager.getApplication().invokeLater(() -> {
            populateMainPanel();
            SwingUtilities.updateComponentTreeUI(this);
            repaint();
            revalidate();
            redraw();
            showInlay();
        });
    }
} 
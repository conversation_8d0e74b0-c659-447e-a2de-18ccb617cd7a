package com.xhs.codewiz.factory.webview;

import org.apache.commons.lang3.StringUtils;

import com.intellij.openapi.project.Project;
import com.intellij.ui.jcef.JBCefBrowser;
import com.xhs.codewiz.type.platform.Webview;
import com.xhs.codewiz.type.platform.WebviewProvider;
import com.xhs.codewiz.utils.ThemeUtils;

/**
 * <AUTHOR>
 * @date 2025/8/4 16:41
 */
public class WebviewBuilder {
    private Webview webview = new Webview();
    private WebviewProvider webviewProvider;
    private String remoteChannel;
    private JBCefBrowser jbCefBrowser;
    private Project project;

    public WebviewBuilder() {
    }

    public WebviewBuilder(Webview webview,
                          WebviewProvider webviewProvider,
                          String remoteChannel,
                          JBCefBrowser jbCefBrowser,
                          Project project) {
        this.webview = webview;
        this.webviewProvider = webviewProvider;
        this.remoteChannel = remoteChannel;
        this.jbCefBrowser = jbCefBrowser;
        this.project = project;
    }

    public Webview getWebview() {
        if (webview == null) {
            webview = new Webview();
        }

        String html = webview.getHtml();
        if (isHttpUrl(html)) {
            webview.setHtml(appendThemeParameter(html));
        }

        return webview;
    }

    /**
     * 检查是否为HTTP URL
     */
    private boolean isHttpUrl(String url) {
        return StringUtils.isNotBlank(url) && url.startsWith("http");
    }

    /**
     * 为URL添加主题参数
     */
    private String appendThemeParameter(String url) {
        if (StringUtils.isBlank(url) || url.contains("theme=")) {
            return url;
        }

        String theme = ThemeUtils.isDark() ? "dark" : "light";
        String separator = url.contains("?") ? "&" : "?";

        return url + separator + "theme=" + theme;
    }

    public void setWebview(Webview webview) {
        this.webview = webview;
    }

    public WebviewProvider getWebviewProvider() {
        return webviewProvider;
    }

    public void setWebviewProvider(WebviewProvider webviewProvider) {
        this.webviewProvider = webviewProvider;
    }

    public String getRemoteChannel() {
        return remoteChannel;
    }

    public void setRemoteChannel(String remoteChannel) {
        this.remoteChannel = remoteChannel;
    }

    public JBCefBrowser getJbCefBrowser() {
        return jbCefBrowser;
    }

    public void setJbCefBrowser(JBCefBrowser jbCefBrowser) {
        this.jbCefBrowser = jbCefBrowser;
    }

    public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }
}

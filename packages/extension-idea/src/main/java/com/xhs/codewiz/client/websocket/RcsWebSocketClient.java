package com.xhs.codewiz.client.websocket;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.client.RcsLanguageClient;
import com.xhs.codewiz.client.RcsLanguageClientImpl;
import com.xhs.codewiz.client.RcsLanguageServer;
import com.xhs.codewiz.utils.ThreadUtil;
import java.io.IOException;
import java.io.Serializable;
import java.net.URI;
import javax.websocket.ClientEndpoint;
import javax.websocket.CloseReason;
import javax.websocket.ContainerProvider;
import javax.websocket.DeploymentException;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.Session;
import org.eclipse.lsp4j.jsonrpc.Launcher;

@ClientEndpoint
public class RcsWebSocketClient {
  private static final Logger logger = Logger.getInstance(RcsWebSocketClient.class);

  private Session session;
  RcsLanguageServer server;
  RcsLanguageClient client;

  public RcsWebSocketClient() {
    this.client = new RcsLanguageClientImpl();
  }

  public void connect(URI uri, Project project) throws DeploymentException, IOException {
    long connectSt = System.currentTimeMillis();
    ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
    try {
      Thread.currentThread().setContextClassLoader(RcsWebSocketClient.class.getClassLoader());
      //增加重试逻辑
      int retryTime = 5;
      while(retryTime-- > 0) {
        try {
          this.session = ContainerProvider.getWebSocketContainer().connectToServer(this, uri);
          if (null != this.session) {
            break;
          }
          ThreadUtil.sleep(1000);
        } catch (Exception e) {
          logger.warn("fail to connect copilot websocket, retry time = " + retryTime, e);
          ThreadUtil.sleep(1000);
        }
      }
      Launcher<RcsLanguageServer> launcher = new RcsSocketLauncherBuilder<RcsLanguageServer>()
          .setSession(this.session)
          .setLocalService(this.client)
          .setRemoteInterface(RcsLanguageServer.class)
          .validateMessages(true)
          .create();
      this.server = launcher.getRemoteProxy();
      this.client.setRcsLanguageServer(server);
      this.client.setProject(project);
      logger.info(String.format("copilot websocket startup succeed, project = %s, cost %d ms, session open = %s", project.getName(), System.currentTimeMillis() - connectSt, isSessionOpen()));
    } catch (Exception e) {
      logger.warn("Connecting websocket encountered IO Exception" + e);
    }
    finally {
      Thread.currentThread().setContextClassLoader(originalClassLoader);
    }
  }

  public boolean isSessionOpen() {
    return this.session != null && this.session.isOpen();
  }

  public void closeSession() {
    if (this.isSessionOpen()) {
      try {
        this.session.close();
        logger.info("Session closed");
      }
      catch (IOException e) {
        logger.warn("Session close encountered error" + e.getMessage());
      }
    } else {
      logger.warn("Session is already closed");
    }
  }

  public void sendMessage(String str) {
    this.session.getAsyncRemote().sendText(str);
  }

  public RcsLanguageServer getServer() {
    return this.server;
  }

  public RcsLanguageClient getClient() {
    return this.client;
  }

  @OnError
  public void error(Session session, Throwable t) {
    logger.warn("WebSocket encountered error" + t);
  }

  @OnClose
  public void close(Session session, CloseReason reason) {
    logger.info("WebSocket closed, reason: " + reason.getReasonPhrase() + " code:" + (Serializable)(reason.getCloseCode() != null ? Integer.valueOf(reason.getCloseCode().getCode()) : "unknown"));
  }

  public Session getSession() {
    return this.session;
  }
}

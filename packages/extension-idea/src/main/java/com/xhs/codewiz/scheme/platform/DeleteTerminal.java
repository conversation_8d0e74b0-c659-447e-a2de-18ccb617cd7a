package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.TerminalExitReason;
/**
 * 删除运行在发送方的终端。\n本地发送表示删除本地存在的终端，远程发送表示删除远程存在的终端。
 */
public class DeleteTerminal {
    private String schemaProtocol = "platform.delete.terminal";
    private DeleteTerminalParams params;

    public DeleteTerminalParams getParams() {
        return params;
    }
    public void setParams(DeleteTerminalParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class DeleteTerminalParams {
        /** 删除终端的原因 */
        private TerminalExitReason reason;
        /** 删除终端的原因代码 */
        private Integer code; // optional
        /** 要删除的终端 ID */
        private String terminalId;
    
        public TerminalExitReason getReason() {
            return reason;
        }
        public void setReason(TerminalExitReason reason) {
            this.reason = reason;
        }
        public Integer getCode() {
            return code;
        }
        public void setCode(Integer code) {
            this.code = code;
        }
        public String getTerminalId() {
            return terminalId;
        }
        public void setTerminalId(String terminalId) {
            this.terminalId = terminalId;
        }
    }
}

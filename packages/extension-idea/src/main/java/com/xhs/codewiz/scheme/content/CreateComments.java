package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.Comment;
/**
 * 添加 Comments
 */
public class CreateComments {
    private String schemaProtocol = "content.create.comments";
    private CreateCommentsParams params;

    public CreateCommentsParams getParams() {
        return params;
    }
    public void setParams(CreateCommentsParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateCommentsParams {
        private List<Comment> comments;
        /** Comment Provider ID，表示该 Comments 属于哪个 Comment Provider */
        private String provider;
        /** Comments 合集 ID */
        private String collectionId;
    
        public List<Comment> getComments() {
            return comments;
        }
        public void setComments(List<Comment> comments) {
            this.comments = comments;
        }
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public String getCollectionId() {
            return collectionId;
        }
        public void setCollectionId(String collectionId) {
            this.collectionId = collectionId;
        }
    }
}

package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 删除 Session Provider
 */
public class DeleteSessionProvider {
    private String schemaProtocol = "platform.delete.sessionprovider";
    /** 要删除的 Session Provider ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

package com.xhs.codewiz.type.content;

import java.util.*;
import com.xhs.codewiz.type.global.MarkdownOrString;

/**
 * Comment 内容
 */
public class Comment {
    private CommentAuthor author;

    /** Comment ID */
    private String id;

    /** Comment 的描述。 */
    private String label;

    private MarkdownOrString body;

    private String contextValue;

    /** Comment 创建时间，字符串格式的时间戳 */
    private String timestamp;

    public CommentAuthor getAuthor() {
        return author;
    }

    public void setAuthor(CommentAuthor author) {
        this.author = author;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public MarkdownOrString getBody() {
        return body;
    }

    public void setBody(MarkdownOrString body) {
        this.body = body;
    }

    public String getContextValue() {
        return contextValue;
    }

    public void setContextValue(String contextValue) {
        this.contextValue = contextValue;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

public static class CommentAuthor {
    /** 作者名称 */
    private String name;

    /** 作者图标的路径 */
    private String iconPath;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIconPath() {
        return iconPath;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

}
}

package com.xhs.codewiz.editor.language;

import com.intellij.lang.Language;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;
import com.xhs.codewiz.editor.request.LanguageInfo;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class ExtensionOverrideLanguageInfoSupport implements LanguageInfoSupport {
    private static final Map<Key, String> extensionLanguageOverrides = Map.of(new Key("ECMAScript 6", "jsx"), "javascriptreact");

    public ExtensionOverrideLanguageInfoSupport() {
    }

    public @Nullable LanguageInfo findVSCodeLanguageMapping(@NotNull PsiFile file) {
        VirtualFile virtualFile = file.getVirtualFile();
        if (virtualFile == null) {
            return null;
        } else {
            Language language = file.getLanguage();
            String extension = virtualFile.getExtension();
            String vsCodeId = (String)extensionLanguageOverrides.get(new Key(language.getID(), extension));
            return vsCodeId != null ? new LanguageInfo(language, vsCodeId) : null;
        }
    }

    private static final class Key {
        private final String languageId;
        private final String extension;

        public Key(String languageId, String extension) {
            this.languageId = languageId;
            this.extension = extension;
        }
        public String getLanguageId() {
            return this.languageId;
        }

        public String getExtension() {
            return this.extension;
        }
    }
}

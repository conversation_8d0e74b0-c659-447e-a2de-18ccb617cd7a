package com.xhs.codewiz.type.global;

import java.util.*;

/**
 * 终端退出原因
 */
public enum ExitReason {
    /** 扩展导致的退出 */
    Extension(4),
    /** 用户操作导致的退出 */
    User(3),
    /** 未知原因 */
    Unknown(0),
    /** 终端被关闭 */
    Shutdown(1),
    /** 终端进程退出 */
    Process(2);

    private final int value;

    ExitReason(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}

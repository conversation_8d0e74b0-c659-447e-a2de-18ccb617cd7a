package com.xhs.codewiz.listener;

import com.intellij.ide.AppLifecycleListener;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.node.NodeServerRunner;
import com.xhs.codewiz.update.UnzipAndReplace;
import com.xhs.codewiz.utils.LoggerUtil;
import java.io.IOException;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import org.jetbrains.annotations.ApiStatus.Internal;

import java.io.BufferedReader;
import java.io.InputStreamReader;

/**
 * Author: liukunpeng Date: 2025-07-15 Description:
 */
public class CodeWizAppLifecycleListener implements AppLifecycleListener {
  private static final Logger LOG = Logger.getInstance(CodeWizAppLifecycleListener.class);
  private static Lock lock = new ReentrantLock();
  @Override
  public void appClosing() {
    try {
      //虽然概率特别低，但是还是加个锁保险点
      if (!lock.tryLock()) {
        return;
      }
      UnzipAndReplace.replaceDirectory();
    } catch (IOException e) {
      LoggerUtil.INSTANCE.logWarn(LOG, "replaceDirectory err", e);
    } finally {
      lock.unlock();
    }
  }

  @Internal
  @Override
  public void appStarted() {
    
  }

}

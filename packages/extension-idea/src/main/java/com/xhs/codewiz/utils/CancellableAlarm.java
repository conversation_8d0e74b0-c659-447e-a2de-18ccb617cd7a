package com.xhs.codewiz.utils;

import com.intellij.openapi.Disposable;
import com.intellij.util.Alarm;
import com.intellij.util.Alarm.ThreadToUse;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.TestOnly;

public class CancellableAlarm {
    private final Object LOCK = new Object();
    private final Alarm alarm;

    public CancellableAlarm(@NotNull Disposable parentDisposable) {
        this.alarm = new Alarm(ThreadToUse.POOLED_THREAD, parentDisposable);
    }

    public void cancelAllRequests() {
        synchronized(this.LOCK) {
            this.alarm.cancelAllRequests();
        }
    }

    public void cancelAllAndAddRequest(@NotNull Runnable request, int delayMillis) {
        synchronized(this.LOCK) {
            this.alarm.cancelAllRequests();
            this.alarm.addRequest(request, delayMillis);
        }
    }

    @TestOnly
    public void waitForAllExecuted(int timeout, TimeUnit timeUnit) throws ExecutionException, InterruptedException, TimeoutException {
        synchronized(this.LOCK) {
            this.alarm.waitForAllExecuted((long)timeout, timeUnit);
        }
    }
}


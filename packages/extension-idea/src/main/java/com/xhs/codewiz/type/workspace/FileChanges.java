package com.xhs.codewiz.type.workspace;

import java.util.*;
import com.xhs.codewiz.type.content.Change;

/**
 * 工作区编辑内容集合
 */
public abstract class FileChanges {
    /** 类型标识 */
    public abstract String getType();

    public static class FileChangesFile extends FileChanges {
        public static final String TYPE = "file";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        /** to 不存在时为删除，存在时为重命名 */
        private String from;

        /** from 不存在时为新增，存在时为重命名 */
        private String to;

        public String getFrom() {
            return from;
        }

        public void setFrom(String from) {
            this.from = from;
        }

        public String getTo() {
            return to;
        }

        public void setTo(String to) {
            this.to = to;
        }

    }
    public static class FileChangesContent extends FileChanges {
        public static final String TYPE = "content";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        private List<Change> changes;

        private String uri;

        public List<Change> getChanges() {
            return changes;
        }

        public void setChanges(List<Change> changes) {
            this.changes = changes;
        }

        public String getUri() {
            return uri;
        }

        public void setUri(String uri) {
            this.uri = uri;
        }

    }
}

package com.xhs.codewiz.platform.comment;

import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.markup.RangeHighlighter;
import com.xhs.codewiz.platform.enums.CommentStatus;

/**
 * 代码评论数据模型
 *
 * <AUTHOR>
 */
public class Comment {
    private String noteId;
    private String noteContent;
    private String category;
    private int startLine;
    private int endLine;
    private int level; // 1-严重, 2-次要, 3-轻微
    private String filePath;
    private String ruleText;
    private String ruleUrl;
    private int index;
    private int count;

    private String collectId;
    private String channel;
    
    // 评论状态
    private CommentStatus status = CommentStatus.UNKNOWN;

    // UI相关
    private CommentInlayPanel commentInlay;
    private RangeHighlighter gutterIconRendererHighlighter;
    private Editor editor;
    private boolean visible = false;

    // 反馈状态（保持向后兼容）
    private boolean clickedUp = false;
    private boolean clickedDown = false;

    public Comment() {
    }

    public Comment(String noteId, String noteContent, String category, int startLine, int endLine, int level, String filePath) {
        this.noteId = noteId;
        this.noteContent = noteContent;
        this.category = category;
        this.startLine = startLine;
        this.endLine = endLine;
        this.level = level;
        this.filePath = filePath;
    }

    // Getters and Setters
    public String getNoteId() {
        return noteId;
    }

    public void setNoteId(String noteId) {
        this.noteId = noteId;
    }

    public String getNoteContent() {
        return noteContent;
    }

    public void setNoteContent(String noteContent) {
        this.noteContent = noteContent;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public int getStartLine() {
        return startLine;
    }

    public void setStartLine(int startLine) {
        this.startLine = startLine;
    }

    public int getEndLine() {
        return endLine;
    }

    public void setEndLine(int endLine) {
        this.endLine = endLine;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getRuleText() {
        return ruleText;
    }

    public void setRuleText(String ruleText) {
        this.ruleText = ruleText;
    }

    public String getRuleUrl() {
        return ruleUrl;
    }

    public void setRuleUrl(String ruleUrl) {
        this.ruleUrl = ruleUrl;
    }

    public CommentInlayPanel getCommentInlay() {
        return commentInlay;
    }

    public void setCommentInlay(CommentInlayPanel commentInlay) {
        this.commentInlay = commentInlay;
    }

    public RangeHighlighter getGutterIconRendererHighlighter() {
        return gutterIconRendererHighlighter;
    }

    public void setGutterIconRendererHighlighter(RangeHighlighter gutterIconRendererHighlighter) {
        this.gutterIconRendererHighlighter = gutterIconRendererHighlighter;
    }

    public Editor getEditor() {
        return editor;
    }

    public void setEditor(Editor editor) {
        this.editor = editor;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public boolean isClickedUp() {
        return clickedUp;
    }

    public void setClickedUp(boolean clickedUp) {
        this.clickedUp = clickedUp;
    }

    public boolean isClickedDown() {
        return clickedDown;
    }

    public void setClickedDown(boolean clickedDown) {
        this.clickedDown = clickedDown;
    }

    public String getLevelText() {
        switch (level) {
            case 1:
                return "严重问题";
            case 2:
                return "次要问题";
            case 3:
                return "轻微问题";
            default:
                return "一般问题";
        }
    }

    public boolean isSeriousProblem() {
        return level == 1;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public CommentStatus getStatus() {
        return status;
    }

    public void setStatus(CommentStatus status) {
        this.status = status != null ? status : CommentStatus.UNKNOWN;
        // 同步更新旧的反馈状态字段
        updateLegacyFeedbackStatus();
    }

    /**
     * 更新旧的反馈状态字段以保持向后兼容
     */
    private void updateLegacyFeedbackStatus() {
        this.clickedUp = (status == CommentStatus.LIKE);
        this.clickedDown = (status == CommentStatus.UNLIKE);
    }

    /**
     * 检查是否已经操作过
     */
    public boolean hasUserAction() {
        return status != CommentStatus.UNKNOWN;
    }

    public String getCollectId() {
        return collectId;
    }

    public void setCollectId(String collectId) {
        this.collectId = collectId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
}
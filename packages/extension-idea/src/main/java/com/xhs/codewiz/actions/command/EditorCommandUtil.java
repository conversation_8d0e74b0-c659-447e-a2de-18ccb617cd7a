package com.xhs.codewiz.actions.command;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.intellij.openapi.actionSystem.ActionManager;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.Constraints;
import com.intellij.openapi.actionSystem.DefaultActionGroup;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.client.model.RemoteServiceMeta;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-07-22 Description:
 * 菜单命令处理
 *  1.初始化时预设
 *  2.动态调整(这个还没有梳理)
 */
public class EditorCommandUtil {
  public static String editorNextGroupKey = "editor/context";
  public static Map<Project, List<String>> editActionMap = new ConcurrentHashMap<>();
  public static void registerEditorCommand(Project  project, JsonObject menus, JsonArray commands) {
    JsonArray editorMenu = menus.getAsJsonArray(editorNextGroupKey);
    //一期仅支持context菜单
    List<String> contextMenuNameList = Lists.newArrayList();
    for (JsonElement element : editorMenu) {
      if (!(element instanceof JsonObject subMenu)) {
        continue;
      }
      if (subMenu.has("submenu")) {
        contextMenuNameList.add(subMenu.get("submenu").getAsString());
      }
    }
    if (CollectionUtils.isEmpty(contextMenuNameList)) {
      return;
    }
    //找到了具体的子菜单，开始寻找子菜单下的command
    for (String contextMenuName : contextMenuNameList) {
      JsonArray contextMenus = menus.getAsJsonArray(contextMenuName);
      List<String> commandNames = new ArrayList<>();
      for (JsonElement element : contextMenus) {
        if (!(element instanceof JsonObject context)) {
          continue;
        }
        commandNames.add(context.get("command").getAsString());
      }
      if (CollectionUtils.isEmpty(commandNames)) {
        return;
      }
      for (JsonElement element : commands) {
        if (!(element instanceof JsonObject command)) {
          continue;
        }
        if (!commandNames.contains(command.get("command").getAsString())) {
          continue;
        }
        //这些操作放在EditorPopupMenu下
        BaseCommandAction action = DefaultCommandUtil.createAction(project, command, null, null);
        addActionToEditorPopupMenu(action, project);
      }
    }
  }
  public static void unRegisterActionEditorAction(Project project) {
    List<String> actionIds = editActionMap.remove(project);
    if (CollectionUtils.isNotEmpty(actionIds)) {
      ActionManager am = ActionManager.getInstance();
      DefaultActionGroup editorPopupMenu = (DefaultActionGroup) am.getAction("codewiz.rcs.editor.menu");
      for (String id : actionIds) {
        AnAction action = am.getAction(id);
        if (null != action) {
          editorPopupMenu.remove(action);
          am.unregisterAction(id);
        }
      }
    }
  }
  // 动态挂载到 EditorPopupMenu
  private static void addActionToEditorPopupMenu(AnAction action, Project project) {
    ActionManager am = ActionManager.getInstance();
    DefaultActionGroup editorPopupMenu = (DefaultActionGroup) am.getAction("codewiz.rcs.editor.menu");
    // 用唯一ID注册新action（防止重复）
    String actionId = action.getTemplatePresentation().getDescription() + project.getBasePath();
    if (am.getAction(actionId) == null) {
      am.registerAction(actionId, action);
      editorPopupMenu.add(action, Constraints.FIRST);
    } else {
      return;
    }
    if (!editActionMap.containsKey(project)) {
      editActionMap.put(project, new ArrayList<>());
    }
    editActionMap.get(project).add(actionId);
  }
}

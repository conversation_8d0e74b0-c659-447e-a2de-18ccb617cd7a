package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.file.Uri;
/**
 * 获取当前内联补全内容
 */
public class ReadInlineCompletion {
    private String schemaProtocol = "workspace.read.inlinecompletion";
    private ReadInlineCompletionParams params;

    public ReadInlineCompletionParams getParams() {
        return params;
    }
    public void setParams(ReadInlineCompletionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadInlineCompletionParams {
        /** 内联补全提供者的 ID */
        private String provider;
        private ReadInlineCompletionParamsContext context;
        private Position position;
        private String uri;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public ReadInlineCompletionParamsContext getContext() {
            return context;
        }
        public void setContext(ReadInlineCompletionParamsContext context) {
            this.context = context;
        }
        public Position getPosition() {
            return position;
        }
        public void setPosition(Position position) {
            this.position = position;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }

        public static class ReadInlineCompletionParamsContext {
        private EditEventTriggerKind triggerKind;
        /** 用户当前选中的联想信息 */
        private ReadInlineCompletionParamsContextSelectedCompletionInfo selectedCompletionInfo; // optional
    
        public EditEventTriggerKind getTriggerKind() {
            return triggerKind;
        }
        public void setTriggerKind(EditEventTriggerKind triggerKind) {
            this.triggerKind = triggerKind;
        }
        public ReadInlineCompletionParamsContextSelectedCompletionInfo getSelectedCompletionInfo() {
            return selectedCompletionInfo;
        }
        public void setSelectedCompletionInfo(ReadInlineCompletionParamsContextSelectedCompletionInfo selectedCompletionInfo) {
            this.selectedCompletionInfo = selectedCompletionInfo;
        }
    }

        /**
     * 用户当前选中的联想信息
     */
    public static class ReadInlineCompletionParamsContextSelectedCompletionInfo {
        private Range range;
        /** 选中的文本内容 */
        private String text;
    
        public Range getRange() {
            return range;
        }
        public void setRange(Range range) {
            this.range = range;
        }
        public String getText() {
            return text;
        }
        public void setText(String text) {
            this.text = text;
        }
    }
}

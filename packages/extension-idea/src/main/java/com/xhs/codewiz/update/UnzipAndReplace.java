package com.xhs.codewiz.update;

import com.intellij.openapi.application.PathManager;
import com.xhs.codewiz.utils.LoggerUtil;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

public class UnzipAndReplace {

    public static File unzip(String zipFilePath, String extractPath) throws IOException {
        try (ZipInputStream zipInputStream = new ZipInputStream(new FileInputStream(zipFilePath))) {
            byte[] buffer = new byte[1024];
            ZipEntry zipEntry = zipInputStream.getNextEntry();
            File directory = null;
            while (zipEntry != null) {
                String entryName = zipEntry.getName();
                File entryFile = new File(extractPath, entryName);

                if (zipEntry.isDirectory()) {
                    entryFile.mkdirs();
                    if (entryFile.getParent().equals(extractPath)) {
                        directory = entryFile;
                    }
                } else {
                    new File(entryFile.getParent()).mkdirs();

                    try (FileOutputStream outputStream = new FileOutputStream(entryFile)) {
                        int bytesRead;
                        while ((bytesRead = zipInputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                    }
                }

                zipInputStream.closeEntry();
                zipEntry = zipInputStream.getNextEntry();
            }
            return directory;
        }
    }

    public static void replaceDirectory() throws IOException {
        String sourcePath = MarketRequest.sourceDirectory.get();
        if (StringUtils.isEmpty(sourcePath)) {
            return;
        }
        String pluginDirectory = sourcePath.substring(sourcePath.lastIndexOf(File.separator) + 1);
        String targetPath = PathManager.getPluginsPath() + File.separator + pluginDirectory;
        FileUtils.cleanDirectory(new File(
            PathManager.getPluginsPath() + File.separator + pluginDirectory));
        Path source = new File(sourcePath).toPath();
        Path target = new File(targetPath).toPath();
        Files.move(source, target, StandardCopyOption.REPLACE_EXISTING);
        setPermissions(target);
    }
    private static void setPermissions(Path target) {
        Set<PosixFilePermission> dirPerms = PosixFilePermissions.fromString("rwxr-xr-x");
        Set<PosixFilePermission> execPerms = PosixFilePermissions.fromString("rwxr-xr-x");
        Set<PosixFilePermission> filePerms = PosixFilePermissions.fromString("rw-r--r--");

      try {
        Files.walkFileTree(target, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult preVisitDirectory(Path d, BasicFileAttributes attrs) throws IOException {
                Files.setPosixFilePermissions(d, dirPerms);
                return FileVisitResult.CONTINUE;
            }
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                String fname = file.getFileName().toString().toLowerCase();
                if (
                    !fname.contains(".") ||              // 无扩展名
                        fname.endsWith(".sh") ||
                        fname.endsWith(".run") ||
                        fname.endsWith(".command") ||
                        fname.endsWith(".bin") ||
                        fname.endsWith(".out") ||
                        fname.endsWith(".exe")
                ) {
                    Files.setPosixFilePermissions(file, execPerms);
                } else {
                    Files.setPosixFilePermissions(file, filePerms);
                }
                return FileVisitResult.CONTINUE;
            }
        });
      } catch (IOException e) {
          //LoggerUtil.INSTANCE.logWarn();
      }
    }
}

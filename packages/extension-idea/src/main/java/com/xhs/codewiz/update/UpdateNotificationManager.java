package com.xhs.codewiz.update;

import static com.xhs.codewiz.update.UpdateConstant.BALLOON_CONTENT;

import com.intellij.ide.DataManager;
import com.intellij.notification.Notification;
import com.intellij.notification.NotificationGroupManager;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.actionSystem.PlatformDataKeys;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.utils.IconsUtil;
import org.jetbrains.annotations.NotNull;

public class UpdateNotificationManager {

    public static volatile short doNotAsk = 0;

    public static void notification(String currentVersion, String orderVersion) {
        ApplicationManager.getApplication().invokeLater(() -> {
            DataContext dataContext = DataManager.getInstance().getDataContext();
            Project project = dataContext.getData(PlatformDataKeys.PROJECT);
            String versionChange = currentVersion + " -> " + orderVersion;
            String message = String.format(BALLOON_CONTENT, versionChange);
            Notification notification = NotificationGroupManager.getInstance()
                    .getNotificationGroup("rcs.notifications")
                    .createNotification(message, NotificationType.INFORMATION);
            notification.setTitle(UpdateConstant.BALLOON_TITLE);
            notification.setIcon(IconsUtil.CODEWIZ);
            notification.setImportant(true);
            notification.addAction(new AnAction("现在重启") {
                @Override
                public void actionPerformed(@NotNull AnActionEvent e) {
                    ApplicationManager.getApplication().exit(false, true, true);
                }
            });
            /*notification.addAction(new AnAction("不再提示") {
                @Override
                public void actionPerformed(@NotNull AnActionEvent e) {
                    notification.hideBalloon();
                }
            });*/
            doNotAsk = 1; //只通知一次
            notification.notify(project);
        });
    }
}

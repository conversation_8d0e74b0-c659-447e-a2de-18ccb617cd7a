package com.xhs.codewiz.lang.agent.vscodeRpc;


import com.intellij.execution.ExecutionException;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.execution.process.KillableProcessHandler;
import com.intellij.execution.process.ProcessAdapter;
import com.intellij.execution.process.ProcessEvent;
import com.intellij.execution.process.ProcessOutputTypes;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.util.Key;
import com.intellij.util.io.BaseDataReader.SleepingPolicy;
import com.intellij.util.io.BaseOutputReader;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcMessageHandler;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.MetricsUtil;
import java.util.UUID;
import org.jetbrains.annotations.NotNull;

public class AgentProcessHandler extends KillableProcessHandler {
    private static final Logger LOG = Logger.getInstance(AgentProcessHandler.class);
    private final LimitedStringBuilder recentOutput = new LimitedStringBuilder(2048);
    private final String requestId = UUID.randomUUID().toString(); // 启动请求ID

    public AgentProcessHandler(@NotNull GeneralCommandLine cmdline, @NotNull JsonRpcMessageHandler messageHandler, boolean isInit) throws ExecutionException {
        super(cmdline);
        //上报LSP启动开始，使用生成的requestId
        MetricsUtil.reportStartLSP(isInit, requestId);
        final VSCodeJsonRpcParser messageParser = new VSCodeJsonRpcParser(messageHandler);
        this.addProcessListener(new ProcessAdapter() {
            public void onTextAvailable(@NotNull ProcessEvent event, @NotNull Key outputType) {
                String output = event.getText();
                LoggerUtil.INSTANCE.logInfo(LOG, String.format("AgentProcessHandler [%s] %s", outputType, output));

                if (outputType.equals(ProcessOutputTypes.STDOUT)) {
                    messageParser.append(output);
                }

                AgentProcessHandler.this.recentOutput.append(output);
            }

            @Override
            public void processTerminated(ProcessEvent event) {
                String errMsg = "Process terminated with exit code: " + event.getExitCode();
                LoggerUtil.INSTANCE.logWarn(LOG, errMsg);
                //上报LSP启动失败，使用同一个requestId
                String errorCode = String.valueOf(event.getExitCode());
                MetricsUtil.reportStartLSPFailed(errorCode, errMsg, requestId);
            }
        });
    }

    @NotNull
    protected BaseOutputReader.Options readerOptions() {
        return new AgentOutputReaderOptions();
    }

    public @NotNull String getRecentOutput() {
        return this.recentOutput.toString();
    }

    private static class AgentOutputReaderOptions extends BaseOutputReader.Options {
        private AgentOutputReaderOptions() {
        }

        public SleepingPolicy policy() {
            return SleepingPolicy.BLOCKING;
        }

        public boolean splitToLines() {
            return false;
        }
    }
}


package com.xhs.codewiz.type.file;

import java.util.*;

/**
 * 文件内容，会优先读取 text 字段。\n注意，非文本内容或超过 50kb 大小的内容请设置 buffer 字段，避免直接传输长字符串影响性能。
 */
public class Content {
    private Eol eol;

    /** 制表符的大小，通常用于格式化文本内容 */
    private Integer tabSize;

    /** 是否使用空格替换制表符 */
    private Boolean insertSpaces;

    /** 文件的语言 ID，通常用于语法高亮和代码分析 */
    private String languageId;

    /** buffer header ID，用于标识 websocket buffer 传输对应的内容。\n使用该字段时，需要另外使用 websocket 传输 buffer，并在 buffer 开头按如下顺序添加协议头:\n 1. 4bytes 表示 headID 长度\n 2. headID。 */
    private String buffer;

    /** 文件内容的文本表示，通常用于小文件或简单文本内容 */
    private String text;

    /** 文件内容的编码格式，例如 'utf-8' 或 'base64' */
    private String encoding;

    /** 文件内容的版本号，用于跟踪内容的修改 */
    private Integer version;

    public Eol getEol() {
        return eol;
    }

    public void setEol(Eol eol) {
        this.eol = eol;
    }

    public Integer getTabSize() {
        return tabSize;
    }

    public void setTabSize(Integer tabSize) {
        this.tabSize = tabSize;
    }

    public Boolean getInsertSpaces() {
        return insertSpaces;
    }

    public void setInsertSpaces(Boolean insertSpaces) {
        this.insertSpaces = insertSpaces;
    }

    public String getLanguageId() {
        return languageId;
    }

    public void setLanguageId(String languageId) {
        this.languageId = languageId;
    }

    public String getBuffer() {
        return buffer;
    }

    public void setBuffer(String buffer) {
        this.buffer = buffer;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

}

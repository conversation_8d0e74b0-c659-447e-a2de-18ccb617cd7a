package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 显示文件选择器
 */
public class ExecuteShowFileSelector {
    private String schemaProtocol = "platform.execute.showfileselector";
    private ExecuteShowFileSelectorParams params;

    public ExecuteShowFileSelectorParams getParams() {
        return params;
    }
    public void setParams(ExecuteShowFileSelectorParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteShowFileSelectorParams {
        /** 是否允许选择文件 */
        private Boolean allowFile; // optional
        /** 默认选择的文件路径 */
        private String defaultUri; // optional
        /** 是否允许多选 */
        private Boolean allowMutiple; // optional
        /** 是否允许选择文件夹 */
        private Boolean allowFolder; // optional
        private Map<String, List<String>> filters; // optional
        /** 打开按钮的文本 */
        private String openLabel; // optional
        /** 选择器标题 */
        private String title; // optional
    
        public Boolean getAllowFile() {
            return allowFile;
        }
        public void setAllowFile(Boolean allowFile) {
            this.allowFile = allowFile;
        }
        public String getDefaultUri() {
            return defaultUri;
        }
        public void setDefaultUri(String defaultUri) {
            this.defaultUri = defaultUri;
        }
        public Boolean getAllowMutiple() {
            return allowMutiple;
        }
        public void setAllowMutiple(Boolean allowMutiple) {
            this.allowMutiple = allowMutiple;
        }
        public Boolean getAllowFolder() {
            return allowFolder;
        }
        public void setAllowFolder(Boolean allowFolder) {
            this.allowFolder = allowFolder;
        }
        public Map<String, List<String>> getFilters() {
            return filters;
        }
        public void setFilters(Map<String, List<String>> filters) {
            this.filters = filters;
        }
        public String getOpenLabel() {
            return openLabel;
        }
        public void setOpenLabel(String openLabel) {
            this.openLabel = openLabel;
        }
        public String getTitle() {
            return title;
        }
        public void setTitle(String title) {
            this.title = title;
        }
    }
}

package com.xhs.codewiz.listener.topic;


import com.intellij.util.messages.Topic;
import com.xhs.codewiz.completion.request.CodewizInlayList;
import com.xhs.codewiz.editor.request.EditorRequest;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public interface InlaysReceivedMessage {
    Topic<InlaysReceivedMessage> TOPIC = Topic.create("codewiz.inlaysReceived", InlaysReceivedMessage.class);

    void inlaysReceived(@NotNull EditorRequest var1, @NotNull List<CodewizInlayList> var2);
}


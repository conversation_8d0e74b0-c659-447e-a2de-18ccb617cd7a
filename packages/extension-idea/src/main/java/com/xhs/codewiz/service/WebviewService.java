package com.xhs.codewiz.service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.gson.reflect.TypeToken;
import com.intellij.ide.ui.LafManagerListener;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowAnchor;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.openapi.wm.ToolWindowType;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentManager;
import com.intellij.ui.jcef.JBCefBrowser;
import com.intellij.util.messages.MessageBusConnection;
import com.xhs.codewiz.client.ProviderTypeEnum;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.enums.WebviewField;
import com.xhs.codewiz.factory.editor.TopicPanelUtil;
import com.xhs.codewiz.factory.webview.ActivityBar;
import com.xhs.codewiz.factory.webview.BrowserWindowFactory;
import com.xhs.codewiz.factory.webview.IdeToWebviewMsg;
import com.xhs.codewiz.factory.webview.ToolBrowserWindow;
import com.xhs.codewiz.factory.webview.WebviewBuilder;
import com.xhs.codewiz.scheme.platform.CreateWebviewMessage;
import com.xhs.codewiz.scheme.platform.CreateWebviewProvider;
import com.xhs.codewiz.scheme.platform.DeleteWebviewProvider;
import com.xhs.codewiz.scheme.platform.ExecuteHideWebView;
import com.xhs.codewiz.scheme.platform.ExecuteShowWebView;
import com.xhs.codewiz.scheme.platform.ReadWebview;
import com.xhs.codewiz.scheme.platform.UpdateWebview;
import com.xhs.codewiz.type.platform.Webview;
import com.xhs.codewiz.type.platform.Webview.WebviewBadge;
import com.xhs.codewiz.type.platform.Webview.WebviewOptions;
import com.xhs.codewiz.type.platform.WebviewProvider;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.JBCefBrowserUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.RegisterUtil;

/**
 * <AUTHOR>
 * @date 2025/7/21 20:23
 */
public class WebviewService {

    /**
     * # platform.create.webviewProvider
     * ## 介绍
     * 该方法用于创建一个 WebView 提供者。
     * <p>
     * ## 子协议
     * ### platform.read.webview
     * 本地打开 webview 时，请求远端读取 WebView 的内容。
     * <p>
     * ### platform.update.webview
     * 远端更新 WebView 的内容时，同步到本地。
     * <p>
     * ### platform.delete.webview
     * 远端销毁、重建 webview时，同步到本地；本地关闭 webview 时，也会发送该协议。
     * <p>
     * ### platform.create.webviewMessage
     * 接收远端的 WebView 消息到本地 webview；接受本地 webview 消息到远端。
     * <p>
     * ### platform.delete.webviewProvider
     * 删除当前 WebView 提供者。
     * <p>
     * ### platform.execute.showWebview
     * 显示 WebView。
     * <p>
     * ### platform.execute.hideWebview
     * 隐藏 WebView。
     */

    private static final Logger log = Logger.getInstance(WebviewService.class);
    private static final BrowserWindowFactory factory = BrowserWindowFactory.getInstance();
    private static final AtomicBoolean HAS_INIT = new AtomicBoolean(false);

    // ========================================================
    // listen from rcs
    // ========================================================

    public static void createWebviewProvider(String param, String channel) {
        long startTime = System.currentTimeMillis();
        try {
            LoggerUtil.INSTANCE.logInfo(log, "[Stage1-Entry] createWebviewProvider started - channel: " + channel + ", paramLength: " +
                    (param != null ? param.length() : 0));
            LoggerUtil.INSTANCE.logInfo(log, "createWebviewProvider param: " + param + " channel: " + channel);

            CreateWebviewProvider request = GsonUtil.fromJson(param, CreateWebviewProvider.class);
            if (null == request || null == request.getParams()) {
                LoggerUtil.INSTANCE.logWarn(log,
                        "[Stage1-Entry] createWebviewProvider validation failed - params is null or empty, channel: " + channel);
                return;
            }
            // RcsWebSocketManager.INSTANCE.addProvider(channel, request);
            ApplicationManager.getApplication().invokeLater(() -> {
                // 路由分发webview provider
                routerWebviewProvider(channel, request.getParams());
            });
            long endTime = System.currentTimeMillis();
            LoggerUtil.INSTANCE.logInfo(log, "[Performance] createWebviewProvider completed - duration: " + (endTime - startTime) + "ms");
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "[Stage-Error] Operation failed in createWebviewProvider", e);
        }
    }

    public static void updateWebview(String param, String channel) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "updateWebview param: " + param + " channel: " + channel);
            UpdateWebview updateWebview = GsonUtil.fromJson(param, UpdateWebview.class);

            if (!Objects.isNull(updateWebview.getParams()) && CollectionUtils.isNotEmpty(updateWebview.getParams().getChanges())) {
                // 根据 channel 获取对应的 ToolBrowserWindow
                ToolBrowserWindow toolWindow = getToolBrowserWindowByChannel(channel);
                if (toolWindow == null) {
                    LoggerUtil.INSTANCE.logWarn(log, "updateWebview toolWindow is null for channel: " + channel);
                    return;
                }
                Webview currentWebview = toolWindow.getWebview();

                // 遍历所有变更
                for (UpdateWebview.UpdateWebviewParamsChanges change : updateWebview.getParams().getChanges()) {
                    String key = change.getKey();
                    Object value = change.getValue();

                    // 根据key匹配对应的字段枚举
                    WebviewField field = WebviewField.fromKey(key);
                    if (field != null) {
                        LoggerUtil.INSTANCE.logInfo(log, "Updating webview field: " + field.getKey() + " = " + value);

                        switch (field) {
                            case BADGE:
                                WebviewBadge badge = GsonUtil.fromJson(GsonUtil.toJson(value), WebviewBadge.class);
                                currentWebview.setBadge(badge);
                                break;
                            case OPTIONS:
                                WebviewOptions options = GsonUtil.fromJson(GsonUtil.toJson(value), WebviewOptions.class);
                                currentWebview.setOptions(options);
                                break;
                            case DESCRIPTION:
                                currentWebview.setDescription(String.valueOf(value));
                                break;
                            case HTML:
                                currentWebview.setHtml(String.valueOf(value));
                                break;
                            case TITLE:
                                currentWebview.setTitle(String.valueOf(value));
                                break;
                        }
                    } else {
                        LoggerUtil.INSTANCE.logWarn(log, "Unknown webview field key: " + key);
                    }
                }

                // 更新webview缓存
                toolWindow.setWebview(currentWebview);
                LoggerUtil.INSTANCE.logInfo(log, "Webview cache updated successfully");
            }
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process updateWebview", e);
        }
    }

    public static void notifyCreateWebviewMessage(String param, String channel) {
        try {
            CreateWebviewMessage createWebviewMessage = GsonUtil.fromJson(param, CreateWebviewMessage.class);
            if (createWebviewMessage == null ||
                    createWebviewMessage.getParams() == null) {
                LoggerUtil.INSTANCE.logWarn(log, "notifyCreateWebviewMessage params is null or toolWindow is null");
                return;
            }
            String message = createWebviewMessage.getParams().getMessage();

            // 根据 channel 信息获取对应的 ToolBrowserWindow
            JBCefBrowser jcefBrowserByChannel = JBCefBrowserUtil.getJcefBrowserByChannel(channel);
            if (jcefBrowserByChannel == null) {
                LoggerUtil.INSTANCE.logWarn(log,
                        "notifyCreateWebviewMessage jcefBrowserByChannel is null for channel: " + channel);
                return;
            }
            IdeToWebviewMsg.executeJavaScript(message, jcefBrowserByChannel);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process notifyCreateWebviewMessage", e);
        }
    }

    public static void deleteWebviewProvider(String param, String channel) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "deleteWebviewProvider param: " + param + " channel: " + channel);
            DeleteWebviewProvider deleteWebviewProvider = GsonUtil.fromJson(param, DeleteWebviewProvider.class);
            RcsWebSocketManager.INSTANCE.deleteProvider(channel, deleteWebviewProvider);
            List<Object> webviewProvider = RcsWebSocketManager.INSTANCE.getProvider(getRemoteChannel(channel),
                    ProviderTypeEnum.WEBVIEW);

            String providerId = getProviderId(channel);
            webviewProvider.removeIf((o -> {
                if (o instanceof CreateWebviewProvider createWebviewProvider) {
                    return createWebviewProvider.getParams().getId().equals(providerId);
                }
                return false;
            }));

            Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
            String remoteChannel = getRemoteChannel(channel);

            // 根据 providerId 找到对应的 ActivityBar 和 ToolWindow
            ActivityBar activityBar = RegisterUtil.getInstance().getActivityBarByViewId(providerId);
            if (activityBar != null) {
                ToolBrowserWindow window = factory.getToolBrowserWindow(project, activityBar.getId());
                if (window != null) {
                    // 清理 ToolBrowserWindow 实例
                    factory.removeToolBrowserWindow(project, activityBar.getId());
                }
            }
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process deleteWebviewProvider", e);
        }
    }

    public static void deleteWebview(String param, String channel) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "deleteWebview param: " + param + " channel: " + channel);
            ToolBrowserWindow toolBrowserWindow = getToolBrowserWindow();
            if (toolBrowserWindow == null) {
                LoggerUtil.INSTANCE.logWarn(log, "deleteWebview toolWindow is null");
                return;
            }
            toolBrowserWindow.setWebview(new Webview());
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process deleteWebview", e);
        }
    }

    private static String getRemoteChannel(String channel) {
        String[] channelSplit = channel.split("/");
        return channelSplit[1];
    }

    private static String getProviderId(String channel) {
        String[] channelSplit = channel.split("/");
        return channelSplit[2];
    }

    // ========================================================
    // send to rcs
    // ========================================================

    public static Webview readWebview(Project project, WebviewBuilder webviewBuilder) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "[Stage5-Webview] readWebview started - project: " + project.getName() + ", providerId: " +
                    webviewBuilder.getWebviewProvider().getId());
            LoggerUtil.INSTANCE.logInfo(log, "readWebview project: " + project.getName());
            ReadWebview readWebview = new ReadWebview();
            ReadWebview.ReadWebviewParams readWebviewParams = new ReadWebview.ReadWebviewParams();
            readWebviewParams.setProvider(webviewBuilder.getWebviewProvider().getId());
            readWebview.setParams(readWebviewParams);
            Webview webview = RcsWebSocketManager.INSTANCE.sendRequestWithChannelProvider(
                    webviewBuilder.getRemoteChannel(),
                    webviewBuilder.getWebviewProvider().getId(),
                    readWebview,
                    new TypeToken<Webview>() {
                    },
                    project,
                    5000);

            if (webview == null) {
                LoggerUtil.INSTANCE.logWarn(log, "[Stage5-Webview] readWebview failed - remote returned null, providerId: " +
                        webviewBuilder.getWebviewProvider().getId());
                return null;
            } else {
                LoggerUtil.INSTANCE.logInfo(log,
                        "[Stage5-Webview] readWebview success - providerId: " + webviewBuilder.getWebviewProvider().getId() +
                                ", hasHtml: " + (webview.getHtml() != null && !webview.getHtml().isEmpty()) + ", title: " +
                                webview.getTitle());
            }
            LoggerUtil.INSTANCE.logInfo(log, "readWebview webviews: " + webview);
            return webview;
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process readWebview", e);
            return null;
        }
    }

    public static void executeHideWebview(Project project, ToolBrowserWindow window) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "executeHideWebview project: " + project.getName());
            ExecuteHideWebView hideWebView = new ExecuteHideWebView();

            hideWebView.setParams(window.getWebviewProvider().getId());
            RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(window.getRemoteChannel(),
                    window.getWebviewProvider().getId(), hideWebView, project);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process executeHideWebview", e);
        }
    }

    public static void executeShowWebview(Project project, ToolBrowserWindow window) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "executeShowWebview project: " + project.getName()
                    + "providerId: " + window.getWebviewProvider().getId());
            ExecuteShowWebView showWebView = new ExecuteShowWebView();
            showWebView.setParams(window.getWebviewProvider().getId());
            RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(window.getRemoteChannel(),
                    window.getWebviewProvider().getId(), showWebView, project);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process executeShowWebview", e);
        }
    }

    public static void sendCreateWebviewMessage(Project project,
                                                String message,
                                                WebviewProvider webviewProvider,
                                                String channel) {
        try {
            CreateWebviewMessage createWebviewMessage = new CreateWebviewMessage();
            CreateWebviewMessage.CreateWebviewMessageParams params = new CreateWebviewMessage.CreateWebviewMessageParams();

            if (StringUtils.isBlank(channel)
                    || Objects.isNull(webviewProvider)
                    || StringUtils.isBlank(webviewProvider.getId())) {
                LoggerUtil.INSTANCE.logWarn(log, "sendCreateWebviewMessage: channel or webview provider is null");
                return;
            }

            LoggerUtil.INSTANCE.logDebug(log, "createWebviewMessage providerId: " + webviewProvider.getId());

            params.setWebview(webviewProvider.getId());
            params.setMessage(message);
            createWebviewMessage.setParams(params);

            RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(
                    channel, webviewProvider.getId(), createWebviewMessage, project);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process createWebviewMessage", e);
        }
    }

    /**
     * 获取用于发送消息的 ToolBrowserWindow
     * 优先选择当前活跃的窗口，如果找不到则选择第一个可用的
     */
    private static ToolBrowserWindow getToolBrowserWindowForMessage(Project project) {
        if (project == null) {
            return null;
        }

        // 尝试获取当前聚焦的 ToolWindow
        // 注意：IntelliJ IDEA API 中没有直接获取活跃 ToolWindow 的方法
        // 这里我们简化逻辑，直接使用第一个可用的窗口

        // 如果找不到活跃的，获取第一个有 WebviewProvider 的窗口
        return factory.getFirstAvailableWindow(project);
    }

    public static void deleteWebview(Project project) {
        try {
            // LoggerUtil.INSTANCE.logInfo(log, "deleteWebview project: " + project.getName());
            // CreateWebviewProvider cachedProvider = WebviewCacheService.getInstance().getCachedProvider();
            // if (cachedProvider.getParams().getOptions().getRetainContextWhenHidden()) {
            //     LoggerUtil.INSTANCE.logInfo(log, "deleteWebview retainContextWhenHidden: true");
            //     return;
            // }

            // DeleteWebview deleteWebview = new DeleteWebview();
            // String providerId = RcsWebSocketManager.INSTANCE.getProviderId(
            //         RcsWebSocketManager.INSTANCE.getWebviewProviderChannel(),
            //         ProviderTypeEnum.WEBVIEW);
            // LoggerUtil.INSTANCE.logInfo(log, "deleteWebview providerId: " + providerId);
            //
            // deleteWebview.setParams(providerId);
            // RcsWebSocketManager.INSTANCE.sendNotificationWithProvider(
            //         ProviderTypeEnum.WEBVIEW, deleteWebview, project);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process deleteWebview", e);
        }
    }

    /**
     * @deprecated 建议使用具体的 getToolBrowserWindow(project, toolWindowId) 方法
     * 这个方法尝试获取当前活跃的 ToolBrowserWindow，但在多个 ToolWindow 的情况下可能不准确
     */
    @Deprecated
    private static ToolBrowserWindow getToolBrowserWindow() {
        Project project = ApplicationUtil.findCurrentProject();
        if (project == null) {
            return null;
        }

        // 尝试获取当前聚焦的 ToolWindow
        // 注意：IntelliJ IDEA API 中没有直接获取活跃 ToolWindow 的方法
        // 这里我们简化逻辑，直接使用第一个可用的窗口

        // 如果找不到活跃的，返回第一个可用的
        return BrowserWindowFactory.getToolBrowserWindow(project);
    }

    /**
     * 根据 channel 信息获取对应的 ToolBrowserWindow
     */
    private static ToolBrowserWindow getToolBrowserWindowByChannel(String channel) {
        Project project = ApplicationUtil.findCurrentProject();
        if (project == null) {
            return null;
        }

        String remoteChannel = getRemoteChannel(channel);
        String providerId = getProviderId(channel);

        return factory.getToolBrowserWindowByProvider(project, remoteChannel, providerId);
    }

    private static void routerWebviewProvider(String channel, WebviewProvider webviewProvider) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "[Stage2-Router] routerWebviewProvider started - channel: " + channel + ", providerId: " +
                    (webviewProvider != null ? webviewProvider.getId() : "null"));
            Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
            if (webviewProvider == null || project == null) {
                LoggerUtil.INSTANCE.logWarn(log, "routerWebviewProvider webviewProvider or project is null");
                return;
            }

            // 从注册信息中获取 activity bar
            ActivityBar activityBar = RegisterUtil.getInstance().getActivityBarByViewId(webviewProvider.getId());
            LoggerUtil.INSTANCE.logInfo(log,
                    "[Stage2-Router] ActivityBar lookup result - providerId: " + webviewProvider.getId() + ", activityBarExists: " +
                            (activityBar != null) + ", will use: " + (activityBar != null ? "ToolWindow" : "TopicPanel"));

            String remoteChannel = getRemoteChannel(channel);
            if (activityBar != null) {
                dynamicRegisterToolWindow(project, remoteChannel, webviewProvider, activityBar);
            } else {
                TopicPanelUtil.openTopicEditor(project, remoteChannel, webviewProvider);
            }
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process routerWebviewProvider", e);
        }
    }

    private static void dynamicRegisterToolWindow(Project project,
                                                  String channel,
                                                  WebviewProvider webviewProvider,
                                                  ActivityBar activityBar) {
        LoggerUtil.INSTANCE.logInfo(log,
                "[Stage3-ToolWindow] dynamicRegisterToolWindow started - project: " + project.getName() + ", activityBarId: " +
                        activityBar.getId() + ", providerId: " + webviewProvider.getId());
        LoggerUtil.INSTANCE.logInfo(log, "dynamicRegisterToolWindow project: " + project.getName()
                + " channel: " + channel + " activityBar: " + activityBar.getId()
                + " webviewProvider: " + webviewProvider.getId());
        // 注册 toolwindow
        buildToolWindow(project, activityBar);

        // 配置 ToolBrowserWindow
        ToolBrowserWindow toolBrowserWindow = factory.getToolBrowserWindow(project, activityBar.getId());
        if (toolBrowserWindow != null) {
            toolBrowserWindow.setRemoteChannel(channel);
            toolBrowserWindow.setWebviewProvider(webviewProvider);
            // 读取页面，仅在首次创建读取
            toolBrowserWindow.setWebview(WebviewService.readWebview(project, toolBrowserWindow.getWebviewBuilder()));
            if (toolBrowserWindow.getActiveWindow().isVisible()) {
                // 需要重新打开
                LoggerUtil.INSTANCE.logInfo(log,
                        "dynamicRegisterToolWindow toolWindow is visible, executeHideWebview and executeShowWebview");
                executeHideWebview(project, toolBrowserWindow);
                executeShowWebview(project, toolBrowserWindow);
            }
            // 设置title按钮
            toolBrowserWindow.changeWindowAction(project);
        }
    }

    private static void buildToolWindow(Project project, ActivityBar activityBar) {
        ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
        // 已经注册了就不重复注册
        if (toolWindowManager.getToolWindow(activityBar.getId()) != null) {
            LoggerUtil.INSTANCE.logInfo(log,
                    "[Stage3-ToolWindow] ToolWindow already registered, skipping - activityBarId: " + activityBar.getId());
            return;
        }
        LoggerUtil.INSTANCE.logInfo(log, "[Stage3-ToolWindow] Registering new ToolWindow - activityBarId: " + activityBar.getId());

        // 注册 toolwindow
        ToolWindow toolWindow = toolWindowManager.registerToolWindow(
                activityBar.getId(),
                twm -> {
                    return null; // 暂时返回null，内容通过factory创建
                }
        );
        toolWindow.setAnchor(ToolWindowAnchor.fromText(activityBar.getDefaultPosition()), null);

        // 设置工具窗口属性
        toolWindow.setType(ToolWindowType.DOCKED, null);
        toolWindow.setAutoHide(false);
        toolWindow.setIcon(activityBar.getIconObject());
        toolWindow.setStripeTitle(activityBar.getTitle());

        // 创建toolwindow content
        factory.createToolWindowContent(project, toolWindow);

        // 获取所有Content并设置为不可关闭
        ContentManager contentManager = toolWindow.getContentManager();
        for (Content content : contentManager.getContents()) {
            content.setCloseable(false);
        }

        handleProjectThemeChange(project);
    }


    /**
     * 处理项目相关主题变更
     */
    private static void handleProjectThemeChange(Project project) {
        //仅允许触发一次
        if (HAS_INIT.getAndSet(true)) {
            return;
        }
        MessageBusConnection connect = ApplicationManager.getApplication().getMessageBus().connect();

        connect.subscribe(LafManagerListener.TOPIC, (LafManagerListener) lafManager -> {
            // 项目级别的主题变更处理

            List<ToolBrowserWindow> projectWindows =
                    BrowserWindowFactory.getInstance().getProjectWindows(project);
            for (ToolBrowserWindow projectWindow : projectWindows) {
                ActivityBar activityBar =
                        RegisterUtil.getInstance().getActivityBarByViewId(projectWindow.getWebviewProvider().getId());
                projectWindow.getActiveWindow().setIcon(activityBar.getIconObject());
            }
        });
    }

}

package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.global.ZeroBasedIndex;
/**
 * 删除 TabGroup
 */
public class DeleteTabGroup {
    private String schemaProtocol = "workspace.delete.tabgroup";
    /** TabGroup 的索引 */
    private Integer params;

    public Integer getParams() {
        return params;
    }
    public void setParams(Integer params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

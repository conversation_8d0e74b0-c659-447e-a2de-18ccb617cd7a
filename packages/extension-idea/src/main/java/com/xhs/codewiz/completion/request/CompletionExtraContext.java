package com.xhs.codewiz.completion.request;
/**
 * 通义补全用补充信息
 */
public class CompletionExtraContext {
  private Boolean isComment;
  private String triggerEvent;
  private Measurements measurements;
  private String lineSuffixCode;
  private int tabWidth;
  private String sessionId;

  public Boolean getIsComment() {
    return this.isComment;
  }

  public String getTriggerEvent() {
    return this.triggerEvent;
  }

  public Measurements getMeasurements() {
    return this.measurements;
  }

  public void setIsComment(Boolean isComment) {
    this.isComment = isComment;
  }

  public void setTriggerEvent(String triggerEvent) {
    this.triggerEvent = triggerEvent;
  }

  public void setMeasurements(Measurements measurements) {
    this.measurements = measurements;
  }

  public CompletionExtraContext() {
  }

  public Boolean getComment() {
    return isComment;
  }

  public void setComment(Boolean comment) {
    isComment = comment;
  }

  public String getLineSuffixCode() {
    return lineSuffixCode;
  }

  public void setLineSuffixCode(String lineSuffixCode) {
    this.lineSuffixCode = lineSuffixCode;
  }

  public int getTabWidth() {
    return tabWidth;
  }

  public void setTabWidth(int tabWidth) {
    this.tabWidth = tabWidth;
  }

  public String getSessionId() {
    return sessionId;
  }

  public void setSessionId(String sessionId) {
    this.sessionId = sessionId;
  }

  public CompletionExtraContext(String sessionId, Boolean isComment, String triggerEvent, Measurements measurements, String lineSuffixCode, int tabWidth) {
    this.sessionId = sessionId;
    this.isComment = isComment;
    this.triggerEvent = triggerEvent;
    this.measurements = measurements;
    this.lineSuffixCode = lineSuffixCode;
    this.tabWidth = tabWidth;
  }
}
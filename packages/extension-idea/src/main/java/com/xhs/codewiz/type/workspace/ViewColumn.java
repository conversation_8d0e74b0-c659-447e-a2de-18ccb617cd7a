package com.xhs.codewiz.type.workspace;

import java.util.*;

/**
 * 标签组所在的视图列。当index存在时，忽略其他值
 */
public class ViewColumn {
    /** 在激活标签右侧，不存在时创建 */
    private Boolean isBeside;

    /** 标签组的索引 */
    private Integer index;

    /** 是否为当前激活的标签 */
    private Boolean isActive;

    public Boolean getIsBeside() {
        return isBeside;
    }

    public void setIsBeside(Boolean isBeside) {
        this.isBeside = isBeside;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

}

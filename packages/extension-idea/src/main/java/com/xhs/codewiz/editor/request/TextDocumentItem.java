package com.xhs.codewiz.editor.request;

import com.google.gson.annotations.SerializedName;
import org.jetbrains.annotations.NotNull;

public final class TextDocumentItem {
    @SerializedName("uri")
    private final @NotNull VirtualFileUri uri;
    @SerializedName("languageId")
    private final @NotNull String languageId;
    @SerializedName("version")
    private final int version;
    @SerializedName("text")
    private final @NotNull String text;

    public TextDocumentItem(@NotNull VirtualFileUri uri, @NotNull String languageId, int version, @NotNull String text) {
        this.uri = uri;
        this.languageId = languageId;
        this.version = version;
        this.text = text;
    }

    public @NotNull VirtualFileUri getUri() {
        return this.uri;
    }

    public @NotNull String getLanguageId() {
        return this.languageId;
    }

    public int getVersion() {
        return this.version;
    }

    public @NotNull String getText() {
        return this.text;
    }
}

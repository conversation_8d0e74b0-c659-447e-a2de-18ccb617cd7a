package com.xhs.codewiz.editor.request;

import com.xhs.codewiz.completion.request.CodewizInlayList;
import it.unimi.dsi.fastutil.objects.ObjectLinkedOpenHashSet;
import it.unimi.dsi.fastutil.objects.ObjectSortedSet;
import java.util.List;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class EditorRequestResultList {
    private final EditorRequest request;
    private final Object inlayLock = new Object();
    private final ObjectLinkedOpenHashSet<CodewizInlayList> inlayLists = new ObjectLinkedOpenHashSet();
    private int index = 0;
    private int maxShownIndex = -1;
    private boolean hasOnDemandCompletions;

    public EditorRequestResultList(@NotNull EditorRequest request) {
        this.request = request;
    }

    public EditorRequest getRequest() {
        return this.request;
    }

    public void resetInlays() {
        synchronized(this.inlayLock) {
            this.inlayLists.clear();
        }
    }

    public void addInlays(@NotNull CodewizInlayList inlays) {
        synchronized(this.inlayLock) {
            this.inlayLists.add(inlays);
            this.maxShownIndex = Math.max(0, this.maxShownIndex);
        }
    }

    public void addInlaysToFirst(@NotNull CodewizInlayList inlays) {
        synchronized(this.inlayLock) {
            this.inlayLists.addAndMoveToFirst(inlays);
            this.maxShownIndex = Math.max(0, this.maxShownIndex);
        }
    }

    public @Nullable CodewizInlayList getCurrentCompletion() {
        synchronized(this.inlayLock) {
            return getAtIndexLocked(this.inlayLists, this.index);
        }
    }

    public @Nullable List<CodewizInlayList> getAllShownCompletion() {
        synchronized(this.inlayLock) {
            return (List)this.inlayLists.stream().limit((long)(this.maxShownIndex + 1)).collect(Collectors.toList());
        }
    }

    public boolean hasCurrent() {
        synchronized(this.inlayLock) {
            return this.index >= 0 && !this.inlayLists.isEmpty();
        }
    }

    public boolean hasPrev() {
        synchronized(this.inlayLock) {
            return this.inlayLists.size() > 1;
        }
    }

    public boolean hasNext() {
        synchronized(this.inlayLock) {
            return this.inlayLists.size() > 1;
        }
    }

    public @Nullable CodewizInlayList getPrevCompletion() {
        synchronized(this.inlayLock) {
            int size = this.inlayLists.size();
            if (size <= 1) {
                this.index = 0;
                return null;
            } else {
                --this.index;
                if (this.index < 0) {
                    this.index = size - 1;
                }

                return getAtIndexLocked(this.inlayLists, this.index);
            }
        }
    }

    public @Nullable CodewizInlayList getNextCompletion() {
        synchronized(this.inlayLock) {
            int size = this.inlayLists.size();
            if (size <= 1) {
                this.index = 0;
                return null;
            } else {
                ++this.index;
                if (this.index >= size) {
                    this.index = 0;
                }

                this.maxShownIndex = Math.max(this.maxShownIndex, this.index);
                return getAtIndexLocked(this.inlayLists, this.index);
            }
        }
    }

    public boolean hasOnDemandCompletions() {
        synchronized(this.inlayLock) {
            return this.hasOnDemandCompletions || this.inlayLists.size() > 1;
        }
    }

    public void setHasOnDemandCompletions() {
        synchronized(this.inlayLock) {
            this.hasOnDemandCompletions = true;
        }
    }

    private static @Nullable CodewizInlayList getAtIndexLocked(@NotNull ObjectSortedSet<CodewizInlayList> inlays, int index) {
        return (CodewizInlayList)inlays.stream().skip((long)index).findFirst().orElse(null);
    }
    
    public Object getInlayLock() {
        return this.inlayLock;
    }

    public ObjectLinkedOpenHashSet<CodewizInlayList> getInlayLists() {
        return this.inlayLists;
    }

    public int getIndex() {
        return this.index;
    }
    
    public int getMaxShownIndex() {
        return this.maxShownIndex;
    }

    public boolean isHasOnDemandCompletions() {
        return this.hasOnDemandCompletions;
    }
    
    public void setIndex(int index) {
        this.index = index;
    }

    public void setMaxShownIndex(int maxShownIndex) {
        this.maxShownIndex = maxShownIndex;
    }
}


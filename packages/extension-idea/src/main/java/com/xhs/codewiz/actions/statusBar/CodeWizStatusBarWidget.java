package com.xhs.codewiz.actions.statusBar;

import com.intellij.openapi.actionSystem.ActionGroup;
import com.intellij.openapi.actionSystem.ActionManager;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.actionSystem.DefaultActionGroup;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.openapi.ui.popup.JBPopupFactory.ActionSelectionAid;
import com.intellij.openapi.ui.popup.ListPopup;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.util.Pair;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.wm.StatusBar;
import com.intellij.openapi.wm.StatusBarWidget;
import com.intellij.openapi.wm.WindowManager;
import com.intellij.openapi.wm.impl.status.EditorBasedStatusBarPopup;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.ui.awt.RelativePoint;
import com.xhs.codewiz.actions.status.CodeWizStatus;
import com.xhs.codewiz.actions.status.CodeWizStatusService;
import com.xhs.codewiz.setting.CodeWizApplicationSettings;
import com.xhs.codewiz.utils.BundleUtil;
import com.xhs.codewiz.utils.IconsUtil;
import java.awt.Dimension;
import java.awt.Point;
import java.awt.Rectangle;
import javax.swing.JFrame;
import javax.swing.JRootPane;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CodeWizStatusBarWidget extends EditorBasedStatusBarPopup {
    public static final String WIDGET_ID = "com.xhs.codewizWidget";

    public static void update(@NotNull Project project) {
        CodeWizStatusBarWidget widget = findWidget(project);
        if (widget != null) {
            widget.update(() -> {
                widget.myStatusBar.updateWidget(WIDGET_ID);
            });
        }

    }

    public static void showStatusPopupAtCenter(@NotNull Project project) {
        CodeWizStatusBarWidget widget = findWidget(project);
        if (widget != null) {
            ListPopup popup = widget.createPopup(widget.getContext(), true);
            if (popup != null) {
                Disposer.register(widget, popup);
                Dimension dimension = popup.getContent().getPreferredSize();
                JFrame frame = WindowManager.getInstance().getFrame(project);
                if (frame != null) {
                    JRootPane component = frame.getRootPane();
                    Rectangle visibleRect = component.getVisibleRect();
                    int x = visibleRect.x + visibleRect.width / 2 - dimension.width / 2;
                    int y = visibleRect.y + visibleRect.height / 2 - dimension.height / 2;
                    popup.show(new RelativePoint(component, new Point(x, y)));
                }
            }
        }

    }

    public CodeWizStatusBarWidget(@NotNull Project project) {
        super(project, false);
    }

    public @NonNls @NotNull String ID() {
        return WIDGET_ID;
    }

    @NotNull
    protected EditorBasedStatusBarPopup.WidgetState getWidgetState(@Nullable VirtualFile file) {
        Pair<CodeWizStatus, String> statusAndMessage = CodeWizStatusService.getCurrentStatus();
        CodeWizStatus status = statusAndMessage.first;
        String toolTip;
        WidgetState state;
        if (status.isIconAlwaysShown()) {
            String message = statusAndMessage.second;
            toolTip = message == null ? BundleUtil.get("statusBar.tooltipForError", new Object[]{status.getPresentableText()}) : BundleUtil.get("statusBar.tooltipForErrorCustomMessage", new Object[]{status.getPresentableText(), message});
            state = new WidgetState(toolTip, "", true);
            state.setIcon(status.getIcon());
            return state;
        } else if (file == null) {
            return WidgetState.HIDDEN;
        } else {
            Boolean enabled = this.isCodeWizEnabled(file);
            if (enabled == null) {
                return WidgetState.HIDDEN;
            } else {
                toolTip = BundleUtil.get(enabled ? "statusBar.tooltipForEnabled" : "statusBar.tooltipForDisabled");
                state = new WidgetState(toolTip, "", true);
                state.setIcon(enabled ? status.getIcon() : IconsUtil.StatusBarIconDisabled);
                return state;
            }
        }
    }

    protected @Nullable ListPopup createPopup(DataContext context) {
        return this.createPopup(context, false);
    }

    private @Nullable ListPopup createPopup(DataContext context, boolean withStatusItem) {
        CodeWizStatus currentStatus = (CodeWizStatus)CodeWizStatusService.getCurrentStatus().first;
        if (currentStatus == CodeWizStatus.Unsupported) {
            return null;
        } else {
            AnAction configuredGroup = ActionManager.getInstance().getAction(this.findPopupMenuId(currentStatus));
            if (!(configuredGroup instanceof ActionGroup)) {
                return null;
            } else {
                Object group;
                if (withStatusItem) {
                    DefaultActionGroup statusGroup = new DefaultActionGroup();
                    statusGroup.add(new CodeWizStatusItemAction());
                    statusGroup.addSeparator();
                    statusGroup.addAll(new AnAction[]{configuredGroup});
                    group = statusGroup;
                } else {
                    group = (ActionGroup)configuredGroup;
                }

                return JBPopupFactory.getInstance().createActionGroupPopup(BundleUtil.get("statusBar.popup.title"), (ActionGroup)group, context, ActionSelectionAid.SPEEDSEARCH, withStatusItem);
            }
        }
    }

    private @NotNull String findPopupMenuId(@NotNull CodeWizStatus currentStatus) {
        if (currentStatus == CodeWizStatus.Ready) {
            return "codewiz.rcs.statusBarPopup";
        } else if (currentStatus == CodeWizStatus.NotSignedIn) {
            return "codewiz.rcs.statusBarNeedLogin";
        } else {
                return currentStatus.isDisablingClientRequests() ? "codewiz.rcs.statusBarRestartPopup" : "codewiz.rcs.statusBarErrorPopup";
        }
    }

    protected @NotNull StatusBarWidget createInstance(@NotNull Project project) {
        return new CodeWizStatusBarWidget(project);
    }

    private @Nullable Boolean isCodeWizEnabled(@NotNull VirtualFile file) {
        PsiFile psiFile = PsiManager.getInstance(this.myProject).findFile(file);
        return psiFile == null ? null : CodeWizApplicationSettings.isCodeWizEnabled(psiFile.getLanguage());
    }

    private static @Nullable CodeWizStatusBarWidget findWidget(@NotNull Project project) {
        StatusBar bar = WindowManager.getInstance().getStatusBar(project);
        if (bar != null) {
            StatusBarWidget widget = bar.getWidget(WIDGET_ID);
            if (widget instanceof CodeWizStatusBarWidget) {
                return (CodeWizStatusBarWidget)widget;
            }
        }

        return null;
    }
}

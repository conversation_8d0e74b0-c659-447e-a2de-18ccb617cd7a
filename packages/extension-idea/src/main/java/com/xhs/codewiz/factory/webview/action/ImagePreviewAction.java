package com.xhs.codewiz.factory.webview.action;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;

/**
 * 图片预览 Action
 * Demo: 硬编码图片路径进行预览
 */
public class ImagePreviewAction extends AnAction {

    public ImagePreviewAction() {
        super("图片预览", "打开图片预览对话框", null);
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            return;
        }

        // Demo: 硬编码图片路径 - 使用测试图片
        String imagePath = "/Users/<USER>/Downloads/<EMAIL>";

        ImagePreviewDialog dialog = new ImagePreviewDialog(project, imagePath);
        dialog.show();
    }
}

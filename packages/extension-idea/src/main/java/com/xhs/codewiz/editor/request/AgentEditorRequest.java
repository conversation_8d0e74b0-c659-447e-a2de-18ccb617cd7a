package com.xhs.codewiz.editor.request;


import com.intellij.openapi.Disposable;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiFile;
import com.xhs.codewiz.completion.enums.CompletionType;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import com.xhs.codewiz.editor.language.LanguageInfoManager;
import com.xhs.codewiz.enums.CompletionStatusEnum;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.utils.LoggerUtil;
import java.util.concurrent.CompletableFuture;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.annotations.TestOnly;

public class AgentEditorRequest implements EditorRequest, Disposable {
    private static final Logger LOG = Logger.getInstance(AgentEditorRequest.class);
    private final Project project;
    private final CompletionType completionType;
    private final boolean useTabIndents;
    private final int tabWidth;
    private final int requestId;
    private final LanguageInfo fileLanguage;
    private final @NotNull VirtualFileUri uri;
    private final String documentContent;
    private final int documentVersion;
    private final int offset;
    private final @NotNull LineInfo lineInfo;
    private final long requestTimestamp = 0;
    private final long documentModificationSequence;
    private volatile boolean isCancelled;
    private CompletionStatusEnum completionStatus = CompletionStatusEnum.GENERATING;
    private EditEventTriggerKind editEventTriggerKind = EditEventTriggerKind.Automatic;
    private CompletableFuture<Void> allDone;

    @TestOnly
    public static EditorRequest createEmptyGhostText(@NotNull Editor editor) {
        return create(editor, 0, CompletionType.GhostText, null);
    }

    public static @Nullable EditorRequest create(@NotNull Editor editor, int offset, @NotNull CompletionType completionType, EditEventTriggerKind triggerKind) {
        Project project = editor.getProject();
        if (project == null) {
            return null;
        } else {
            Document document = editor.getDocument();
            PsiFile file = PsiDocumentManager.getInstance(project).getPsiFile(document);
            if (file == null) {
                return null;
            } else {
                boolean useTabs = editor.getSettings().isUseTabCharacter(project);
                int tabWidth = editor.getSettings().getTabSize(project);
                LineInfo lineInfo = LineInfo.create(document, offset);
                VirtualFileUri uri = VirtualFileUri.from(file.getVirtualFile());
                AgentEditorRequest request = new AgentEditorRequest(project, completionType, useTabs, tabWidth,
                    RequestId.incrementAndGet(), LanguageInfoManager.findLanguageMapping(file),
                    uri, document.getText(), (int) CodeWizEditorUtil.getDocumentModificationStamp(document),
                    offset, lineInfo, CodeWizEditorUtil.getDocumentModificationStamp(document));
                if (triggerKind != null) {
                    request.setEditEventTriggerKind(triggerKind);
                }
                return request;
            }
        }
    }

    public void cancel() {
        if (!this.isCancelled) {
            this.isCancelled = true;
            if (null != this.allDone) {
                allDone.cancel(true);
            }
            Disposer.dispose(this);
        }
    }

    public void dispose() {
        LoggerUtil.INSTANCE.logDebug(LOG, "EditorRequest.dispose");
        this.isCancelled = true;
    }

    public boolean equalsRequest(@NotNull EditorRequest o) {
        return this.requestId == o.getRequestId();
    }

    public Disposable getDisposable() {
        return this;
    }

    public Project getProject() {
        return this.project;
    }

    public CompletionType getCompletionType() {
        return this.completionType;
    }
    
    public boolean isUseTabIndents() {
        return this.useTabIndents;
    }

    public int getTabWidth() {
        return this.tabWidth;
    }

    public int getRequestId() {
        return this.requestId;
    }

    public LanguageInfo getFileLanguage() {
        return this.fileLanguage;
    }

    public @NotNull VirtualFileUri getUri() {
        return this.uri;
    }
    
    public String getDocumentContent() {
        return this.documentContent;
    }
    
    public int getDocumentVersion() {
        return this.documentVersion;
    }

    public int getOffset() {
        return this.offset;
    }

    public @NotNull LineInfo getLineInfo() {
        return this.lineInfo;
    }
    
    public long getRequestTimestamp() {
        return this.requestTimestamp;
    }

    public long getDocumentModificationSequence() {
        return this.documentModificationSequence;
    }

    public boolean isCancelled() {
        return this.isCancelled;
    }

    public void setCancelled(boolean isCancelled) {
        this.isCancelled = isCancelled;
    }

    public CompletionStatusEnum getCompletionStatus() {
        return this.completionStatus;
    }
    public void setCompletionStatus(CompletionStatusEnum completionStatus) {
        this.completionStatus = completionStatus;
    }

    public EditEventTriggerKind getEditEventTriggerKind() {
        return editEventTriggerKind;
    }

    public void setEditEventTriggerKind(
        EditEventTriggerKind editEventTriggerKind) {
        this.editEventTriggerKind = editEventTriggerKind;
    }
    public void setCompletionFeature(CompletableFuture<Void> allDone) {
        this.allDone = allDone;
    }

    public AgentEditorRequest(Project project, CompletionType completionType, boolean useTabIndents, int tabWidth, int requestId, LanguageInfo fileLanguage, @NotNull VirtualFileUri uri, String documentContent, int documentVersion, int offset, @NotNull LineInfo lineInfo, long documentModificationSequence) {
        this.project = project;
        this.completionType = completionType;
        this.useTabIndents = useTabIndents;
        this.tabWidth = tabWidth;
        this.requestId = requestId;
        this.fileLanguage = fileLanguage;
        this.uri = uri;
        this.documentContent = documentContent;
        this.documentVersion = documentVersion;
        this.offset = offset;
        this.lineInfo = lineInfo;
        this.documentModificationSequence = documentModificationSequence;
    }
}

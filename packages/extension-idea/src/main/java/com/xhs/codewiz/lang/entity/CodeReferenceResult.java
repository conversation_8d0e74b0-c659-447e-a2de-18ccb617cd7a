package com.xhs.codewiz.lang.entity;

import java.util.List;

public class CodeReferenceResult {
  List<CodeReferenceItem> items;
  boolean success;

  public CodeReferenceResult() {
  }

  public CodeReferenceResult(List<CodeReferenceItem> items, boolean success) {
    this.items = items;
    this.success = success;
  }

  public List<CodeReferenceItem> getItems() {
    return this.items;
  }


  public boolean isSuccess() {
    return this.success;
  }


  public void setItems(List<CodeReferenceItem> items) {
    this.items = items;
  }


  public void setSuccess(boolean success) {
    this.success = success;
  }


  public static class CodeReferenceItem {
    String name;
    String filePath;
    String content;
    String signature;

  
    public String getName() {
      return this.name;
    }

  
    public String getFilePath() {
      return this.filePath;
    }

  
    public String getContent() {
      return this.content;
    }

  
    public String getSignature() {
      return this.signature;
    }

  
    public void setName(String name) {
      this.name = name;
    }

  
    public void setFilePath(String filePath) {
      this.filePath = filePath;
    }

  
    public void setContent(String content) {
      this.content = content;
    }

  
    public void setSignature(String signature) {
      this.signature = signature;
    }
  
    public CodeReferenceItem() {
    }

  
    public CodeReferenceItem(String name, String filePath, String content, String signature) {
      this.name = name;
      this.filePath = filePath;
      this.content = content;
      this.signature = signature;
    }
  }
}
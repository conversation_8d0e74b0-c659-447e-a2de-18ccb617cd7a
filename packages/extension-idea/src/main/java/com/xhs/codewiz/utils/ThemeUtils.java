package com.xhs.codewiz.utils;

import com.intellij.openapi.editor.colors.EditorColorsManager;
import com.intellij.openapi.editor.colors.EditorColorsScheme;

/**
 * <AUTHOR>
 * @date 2025/8/18 17:32
 */
public class ThemeUtils {

    /**
     * 方法1：通过编辑器颜色方案检测
     */
    public static boolean isDark() {
        try {
            EditorColorsScheme scheme = EditorColorsManager.getInstance().getGlobalScheme();
            return scheme.getName().toLowerCase().contains("dark") ||
                    scheme.getName().toLowerCase().contains("darcula");
        } catch (Exception e) {
            return false;
        }
    }
}

package com.xhs.codewiz.factory.editor;

/**
 * <AUTHOR>
 * @date 2025/8/4 11:29
 */

import java.awt.BorderLayout;
import java.beans.PropertyChangeListener;

import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.SwingConstants;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorState;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.util.UserDataHolderBase;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.ui.jcef.JBCefBrowser;
import com.intellij.util.ui.JBUI;
import com.xhs.codewiz.factory.webview.WebviewBuilder;
import com.xhs.codewiz.type.platform.Webview;
import com.xhs.codewiz.utils.JBCefBrowserUtil;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * 自定义FileEditor，用于显示聊天编辑器内容
 */
public class TopicFileEditor extends UserDataHolderBase implements FileEditor {
    private Project project;
    private TopicEditorVirtualFile virtualFile;
    private JPanel mainPanel;
    private final Disposable disposable = Disposer.newDisposable(TopicFileEditor.class.getName());

    private static final Logger LOG = Logger.getInstance(TopicFileEditor.class);

    public TopicFileEditor(Project project, TopicEditorVirtualFile virtualFile) {
        this.project = project;
        this.virtualFile = virtualFile;
        buildPanel();
    }

    public void buildPanel() {
        mainPanel = new JPanel(new BorderLayout());
        // 设置边界6，解决左侧无法拖拽
        mainPanel.setBorder(JBUI.Borders.emptyLeft(6));

        try {
            // 获取JCEF HTML面板，使用聊天页面
            WebviewBuilder webviewBuilder = JBCefBrowserUtil
                    .getWebviewBuilderByChannel(virtualFile.getRemoteChannel(), virtualFile.getWebviewId());
            if (webviewBuilder == null
                    || webviewBuilder.getJbCefBrowser() == null
                    || webviewBuilder.getWebview() == null) {
                LoggerUtil.INSTANCE.logWarn(LOG, "buildPanel: jcefBrowser is null");
                return;
            }
            // Disposer.register(disposable, mainPanel);

            // 加载webview页面
            Webview webview = webviewBuilder.getWebview();
            JBCefBrowser jbCefBrowser = webviewBuilder.getJbCefBrowser();
            if (StringUtils.isNotBlank(webview.getHtml()) && webview.getHtml().startsWith("http")) {
                jbCefBrowser.loadURL(webview.getHtml());
            } else {
                jbCefBrowser.loadHTML(webview.getHtml());
            }

            mainPanel.add(jbCefBrowser.getComponent(), BorderLayout.CENTER);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logError(LOG, "Failed to create chat editor panel", e);
            JLabel errorLabel = new JLabel("无法创建聊天编辑器: " + e.getMessage());
            errorLabel.setHorizontalAlignment(SwingConstants.CENTER);
            mainPanel.add(errorLabel, BorderLayout.CENTER);
        }
    }

    @Override
    public @NotNull JComponent getComponent() {
        return mainPanel;
    }

    @Override
    public @Nullable JComponent getPreferredFocusedComponent() {
        return mainPanel;
    }

    @Override
    public @NotNull String getName() {
        return this.virtualFile.getName();
    }

    @Override
    public void setState(@NotNull FileEditorState state) {
        // 不需要状态管理
    }

    @Override
    public boolean isModified() {
        return false;
    }

    @Override
    public boolean isValid() {
        return true;
    }

    @Override
    public void addPropertyChangeListener(@NotNull PropertyChangeListener listener) {
        // 不需要属性监听
    }

    @Override
    public void removePropertyChangeListener(@NotNull PropertyChangeListener listener) {
        // 不需要属性监听
    }

    @Override
    public @NotNull VirtualFile getFile() {
        return virtualFile;
    }

    public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }

    public TopicEditorVirtualFile getVirtualFile() {
        return virtualFile;
    }

    public void setVirtualFile(TopicEditorVirtualFile virtualFile) {
        this.virtualFile = virtualFile;
    }

    /**
     * 刷新编辑器内容
     */
    public void refreshContent() {
        if (virtualFile != null) {
            buildPanel();
        }
    }

    /**
     * 更新编辑器内容和名称
     */
    // public void updateContent(String newName, WebviewBuilder newWebviewBuilder) {
    //     if (virtualFile != null) {
    //         virtualFile.updateContent(newName, newWebviewBuilder);
    //         refreshContent();
    //     }
    // }
    public void updateName(String newName) {
        if (virtualFile != null) {
            virtualFile.updateName(newName);
        }
    }

    @Override
    public void dispose() {
        // 清理 WebviewBuilder 中对已处置 browser 的引用，避免后续误复用
        // if (virtualFile != null && virtualFile.getWebviewBuilder() != null && htmlPanel != null) {
        //     if (virtualFile.getWebviewBuilder().getJbCefBrowser() == htmlPanel) {
        //         virtualFile.getWebviewBuilder().setJbCefBrowser(null);
        //     }
        // }
        Disposer.dispose(disposable);
    }
}

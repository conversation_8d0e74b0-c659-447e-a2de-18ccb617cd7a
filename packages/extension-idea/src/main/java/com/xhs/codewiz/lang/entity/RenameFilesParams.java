package com.xhs.codewiz.lang.entity;

import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import java.util.ArrayList;
import java.util.List;
import org.eclipse.lsp4j.FileRename;
import org.eclipse.lsp4j.jsonrpc.validation.NonNull;
import org.eclipse.lsp4j.util.Preconditions;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-03-19 Description:
 */
public class RenameFilesParams implements JsonRpcNotification {
  private List<FileRename> files = new ArrayList<FileRename>();

  public RenameFilesParams() {
  }

  public RenameFilesParams(@NonNull final List<FileRename> files) {
    this.files = Preconditions.<List<FileRename>>checkNotNull(files, "files");
  }

  public List<FileRename> getFiles() {
    return files;
  }

  public void setFiles(List<FileRename> files) {
    this.files = files;
  }

  @Override
  public @NotNull String getCommandName() {
    return "workspace/didRenameFiles";
  }
}

package com.xhs.codewiz.editor.request;

import com.google.gson.annotations.SerializedName;
import org.jetbrains.annotations.NotNull;

public class TextDocumentIdentifier {
    @SerializedName("uri")
    private final @NotNull VirtualFileUri uri;

    public TextDocumentIdentifier(@NotNull VirtualFileUri uri) {
        this.uri = uri;
    }

    public @NotNull VirtualFileUri getUri() {
        return this.uri;
    }
}

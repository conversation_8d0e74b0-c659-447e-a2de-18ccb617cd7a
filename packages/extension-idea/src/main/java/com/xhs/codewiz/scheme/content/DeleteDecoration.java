package com.xhs.codewiz.scheme.content;

import java.util.*;
/**
 * 删除装饰器
 */
public class DeleteDecoration {
    private String schemaProtocol = "content.delete.decoration";
    private DeleteDecorationParams params;

    public DeleteDecorationParams getParams() {
        return params;
    }
    public void setParams(DeleteDecorationParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class DeleteDecorationParams {
        /** 装饰器 ID */
        private String id;
    
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

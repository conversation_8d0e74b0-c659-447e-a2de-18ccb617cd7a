package com.xhs.codewiz.completion.request;


import com.intellij.openapi.diagnostic.Logger;
import com.intellij.util.containers.ContainerUtil;
import com.intellij.util.containers.hash.EqualityPolicy;
import com.intellij.util.containers.hash.LinkedHashMap;
import com.intellij.util.io.DigestUtil;
import com.xhs.codewiz.completion.CodewizCompletion;
import com.xhs.codewiz.utils.LoggerUtil;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class SimpleCompletionCache implements CompletionCache {
    private static final Logger LOG = Logger.getInstance(SimpleCompletionCache.class);
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private final LinkedHashMap<CacheKey, List<CodewizCompletion>> cache;
    private @Nullable String lastPrefix;
    private @Nullable String lastPromptHash;
    private boolean lastIsMultiline;

    public SimpleCompletionCache(final int cacheSize) {
        this.cache = new LinkedHashMap<CacheKey, List<CodewizCompletion>>(cacheSize, 0.6F, (EqualityPolicy<? super CacheKey>) EqualityPolicy.CANONICAL, true) {
            protected boolean removeEldestEntry(Map.Entry<CacheKey, List<CodewizCompletion>> eldest, CacheKey key, List<CodewizCompletion> value) {
                return this.size() > cacheSize;
            }
        };
    }

    public boolean isLatestPrefix(@NotNull String prefix) {
        Lock readLock = this.lock.readLock();
        readLock.lock();

        boolean var3;
        try {
            var3 = this.lastPrefix != null && this.lastPrefix.equals(prefix);
        } finally {
            readLock.unlock();
        }

        return var3;
    }

    public @Nullable List<CodewizCompletion> get(@NotNull String prompt, boolean isMultiline) {
        LoggerUtil.INSTANCE.logDebug(LOG, "Retrieving cached api items for prompt", null);
        Lock readLock = this.lock.readLock();
        readLock.lock();

        List var4;
        try {
            var4 = (List)this.cache.get(new CacheKey(promptHash(prompt), isMultiline));
        } finally {
            readLock.unlock();
        }

        return var4;
    }

    public @Nullable List<CodewizCompletion> getLatest(@NotNull String prefix) {
        Lock readLock = this.lock.readLock();
        readLock.lock();

        List var3;
        try {
            var3 = this.getLatestLocked(prefix);
        } finally {
            readLock.unlock();
        }

        return var3;
    }

    public void add(@NotNull String prefix, @NotNull String prompt, boolean isMultiline, @NotNull CodewizCompletion item) {
        if (LOG.isTraceEnabled()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "Caching new APIChoice for prompt: " + item, null);
        }

        Lock writeLock = this.lock.writeLock();
        writeLock.lock();

        try {
            this.lastPrefix = prefix;
            this.lastPromptHash = promptHash(prompt);
            this.lastIsMultiline = isMultiline;
            CacheKey key = new CacheKey(this.lastPromptHash, this.lastIsMultiline);
            List<CodewizCompletion> apiChoices = (List)this.cache.computeIfAbsent(key, (s) -> {
                return ContainerUtil.createLockFreeCopyOnWriteList();
            });
            apiChoices.add(item.asCached());
        } finally {
            writeLock.unlock();
        }

    }

    public void clear() {
        Lock writeLock = this.lock.writeLock();
        writeLock.lock();

        try {
            this.lastPromptHash = null;
            this.lastPrefix = null;
            this.lastIsMultiline = false;
            this.cache.clear();
        } finally {
            writeLock.unlock();
        }

    }

    private @Nullable List<CodewizCompletion> getLatestLocked(@NotNull String prefix) {
        if (this.lastPrefix != null && this.lastPromptHash != null && prefix.startsWith(this.lastPrefix)) {
            List<CodewizCompletion> result = (List)this.cache.get(new CacheKey(this.lastPromptHash, this.lastIsMultiline));
            if (result == null) {
                return null;
            } else {
                String remainingPrefix = prefix.substring(this.lastPrefix.length());
                if (remainingPrefix.isEmpty()) {
                    return Collections.unmodifiableList(result);
                } else {
                    List<CodewizCompletion> adjustedChoices = (List)result.stream().map((choice) -> {
                        return choice.withoutPrefix(remainingPrefix);
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    return adjustedChoices.isEmpty() ? null : adjustedChoices;
                }
            }
        } else {
            return null;
        }
    }

    private static String promptHash(@NotNull String prompt) {
        return DigestUtil.sha256Hex(prompt.getBytes(StandardCharsets.UTF_8));
    }

    private static final class CacheKey {
        private final String promptHash;
        private final boolean isMultiline;

        public CacheKey(String promptHash, boolean isMultiline) {
            this.promptHash = promptHash;
            this.isMultiline = isMultiline;
        }

        public String getPromptHash() {
            return this.promptHash;
        }

        public boolean isMultiline() {
            return this.isMultiline;
        }
    }
}

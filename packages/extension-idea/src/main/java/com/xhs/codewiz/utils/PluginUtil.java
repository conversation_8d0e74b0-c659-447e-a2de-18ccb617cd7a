package com.xhs.codewiz.utils;

import com.intellij.ide.lightEdit.LightEdit;
import com.intellij.ide.plugins.IdeaPluginDescriptor;
import com.intellij.ide.plugins.PluginManagerCore;
import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.extensions.PluginDescriptor;
import com.intellij.openapi.extensions.PluginId;
import com.intellij.openapi.project.Project;
import java.nio.file.Path;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class PluginUtil {
    public static final PluginId CODEWIZ_ID = PluginId.getId(BundleUtil.get("codewiz.plugin.id"));

    private PluginUtil() {
    }

    public static @NotNull String getPluginName() {
        return BundleUtil.get("codewiz.plugin.id");
    }

    public static boolean isCopilotPlugin(@NotNull PluginDescriptor pluginDescriptor) {
        return pluginDescriptor.getPluginId().equals(CODEWIZ_ID);
    }

    public static @NotNull String getVersion() {
        IdeaPluginDescriptor plugin = PluginManagerCore.getPlugin(CODEWIZ_ID);
        return plugin == null ? "unknown" : plugin.getVersion();
    }

    public static boolean isNightlyVersion() {
        return getVersion().toLowerCase().contains("nightly");
    }

    public static @NotNull Path getPluginBasePath() {
        IdeaPluginDescriptor plugin = PluginManagerCore.getPlugin(CODEWIZ_ID);

        assert plugin != null;

        return plugin.getPluginPath();
    }

    public static @NotNull String getEditorName() {
        try {
            return "JetBrains-" + ApplicationInfo.getInstance().getBuild().getProductCode();
        } catch (Exception var1) {
            return "JetBrains-??";
        }
    }

    public static @NotNull String getReadableEditorName() {
        try {
            return ApplicationInfo.getInstance().getFullApplicationName();
        } catch (Exception var1) {
            return "JetBrains IDE";
        }
    }

    public static @NotNull String getEditorVersion() {
        try {
            return ApplicationInfo.getInstance().getBuild().asStringWithoutProductCode();
        } catch (Exception var1) {
            return "ERROR";
        }
    }

    public static @NotNull String editorVersionString() {
        String var10000 = getEditorName();
        return var10000 + "/" + getEditorVersion();
    }

    public static @NotNull String pluginVersionString() {
        try {
            String var10000 = getPluginName();
            return var10000 + "/" + getVersion();
        } catch (Exception var1) {
            return "copilot-intellij/ERROR";
        }
    }

    public static boolean isSupportedIDE(@Nullable Project project) {
        if (isRemoteIDE()) {
            return true;
        } else {
            return !LightEdit.owns(project);
        }
    }

    public static boolean isRemoteIDE() {
        return "true".equals(System.getProperty("org.jetbrains.projector.server.enable"));
    }

    /**
     * 禁用ide捆绑插件全行补全
     * 通过反射获取类，设置属性：2024-版本没有对应的类实现
     */
    public static void disabledFullLineCompletion() {
        boolean enabled = PluginManagerCore.isPluginInstalled(PluginId.getId("org.jetbrains.completion.full.line")) &&
            !PluginManagerCore.isDisabled(PluginId.getId("org.jetbrains.completion.full.line"));
        if (enabled) {
            //禁用ide内部的补全
            //MLCompletionSettings.Companion.getInstance().getState().setMultiLineAllowed(false);
            //MLCompletionSettings.Companion.getInstance().getState().setTriggerOnTypings(false);
            try {
                // 1. 获取 MLCompletionSettings 类
                Class<?> mlSettingsClass = Class.forName(
                    "com.intellij.ml.inline.completion.impl.configuration.MLCompletionSettings");
                // 2. 获取 Companion 对象
                Object companion = mlSettingsClass.getDeclaredField("Companion").get(null);
                // 3. 调用 Companion.getInstance() 得到单例
                Object mlSettingsInstance = companion.getClass().getMethod("getInstance").invoke(companion);
                // 4. getState()
                Object state = mlSettingsInstance.getClass().getMethod("getState").invoke(mlSettingsInstance);
                // 5. 获取 State 类型（内部类）
                Class<?> stateClass = Class.forName(
                    "com.intellij.ml.inline.completion.impl.configuration.MLCompletionSettings$State");
                // 6. 设置 triggerOnTypings=false
                java.lang.reflect.Field triggerOnTypingsField = stateClass.getDeclaredField("triggerOnTypings");
                triggerOnTypingsField.setAccessible(true);
                triggerOnTypingsField.setBoolean(state, false);
                // 7. 设置 multiLineAllowed=false
                java.lang.reflect.Field multiLineAllowedField = stateClass.getDeclaredField("multiLineAllowed");
                multiLineAllowedField.setAccessible(true);
                multiLineAllowedField.setBoolean(state, false);

            } catch (Exception t) {
                // 其它非法反射/安全异常，推荐log
            }

        }
    }
    public static boolean isDevEnv() {
        return System.getProperty("idea.is.internal") != null;
    }
}


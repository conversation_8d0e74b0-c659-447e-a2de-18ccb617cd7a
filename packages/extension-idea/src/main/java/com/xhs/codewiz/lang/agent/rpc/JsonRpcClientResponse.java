package com.xhs.codewiz.lang.agent.rpc;

public class JsonRpcClientResponse {
    final String methodName;

    public JsonRpcClientResponse(String methodName, int requestId, Object result) {
        if (methodName == null) throw new NullPointerException("methodName is marked non-null but is null");
        if (result == null) throw new NullPointerException("result is marked non-null but is null");
        this.methodName = methodName;
        this.requestId = requestId;
        this.result = result;
    }

    final int requestId;

    final Object result;

    public String getMethodName() {
        return this.methodName;
    }

    public int getRequestId() {
        return this.requestId;
    }

    public Object getResult() {
        return this.result;
    }
}



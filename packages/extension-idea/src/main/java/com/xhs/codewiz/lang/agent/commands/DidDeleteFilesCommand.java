package com.xhs.codewiz.lang.agent.commands;

import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import java.util.ArrayList;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public class DidDeleteFilesCommand implements JsonRpcNotification {
    private List<FileDelete> files;

    public DidDeleteFilesCommand(String uri){
        files = new ArrayList<FileDelete>();
        files.add(new FileDelete(uri));
    }

    @NotNull
    @Override
    public String getCommandName() {
        return "workspace/didDeleteFiles";
    }

    public class FileDelete {
        private String uri;

        public FileDelete(String uri) {
            this.uri = uri;
        }

        public String getUri() {
            return uri;
        }
    }

}

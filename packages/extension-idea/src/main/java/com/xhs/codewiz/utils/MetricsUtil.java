package com.xhs.codewiz.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.constant.PluginCommonConstant;
import com.xhs.codewiz.enums.MetricsKeyEnum;
import com.xhs.codewiz.enums.MetricsSceneEnum;
import com.xhs.codewiz.lang.LspServiceForTylm;
import com.xhs.codewiz.lang.agent.commands.CommonMetricsCommand;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes.XhsUserInfo;
import com.xhs.codewiz.listener.LSPManager;
import com.xhs.codewiz.node.RcsRunnerMetricsEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 埋点上报工具类
 * 负责处理Agent启动相关的埋点上报逻辑
 */
public class MetricsUtil {
    private static final Logger LOG = Logger.getInstance(MetricsUtil.class);

    /**
     * 此时ls还没有启动，所以需要http上报
     */
    public static void reportBeforeStart() {
        ThreadUtil.execute(() -> {
            boolean isFirstLaunch = isFirstLaunch();
            JsonObject value = getBaseJsonInfo(isFirstLaunch);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(value);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", MetricsKeyEnum.PLUGIN_LAUNCH_START.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);

            //上报日志
            value.addProperty("key", MetricsKeyEnum.PLUGIN_LAUNCH_START.getName());
            JsonObject logBody = getHttpLogBody(value, null);
            HttpUtils.postBody(logBody, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_LOG);

            LoggerUtil.INSTANCE.logInfo(LOG, "codewiz-ls agent start");
        });
    }

    /**
     * 启动成功埋点，通过ls上报
     */
    public static void reportAfterStart() {
        ThreadUtil.execute(() -> {
            boolean isFirstLaunch = isFirstLaunch();
            int time = 60;
            while(time-- > 0) {
                if (LSPManager.getInstance().isAgentRunning()) {
                    CommonMetricsCommand command = new CommonMetricsCommand();
                    command.setMetrics_scene(MetricsSceneEnum.PLUGIN.getName());
                    command.setMetrics_key(MetricsKeyEnum.PLUGIN_LAUNCH_SUCCEED.getName());
                    JsonObject value = getBaseJsonInfo(isFirstLaunch);
                    command.setMetrics_value(value);
                    //埋点
                    LSPManager.getInstance().notify(command);
                    //上报日志
                    value.addProperty("key", MetricsKeyEnum.PLUGIN_LAUNCH_SUCCEED.getName());
                    LoggerUtil.INSTANCE.logWarn(LOG, GsonUtil.toJson(value));
                    return;
                }
                ThreadUtil.sleep(1000L);
            }
        });
    }

    /**
     * 启动LSP埋点（带requestId版本）
     * @param isFirstInit 是否是首次初始化（区分初始启动和运行中重启）
     * @param requestId 启动请求ID，用于关联start和failed事件
     */
    public static void reportStartLSP(boolean isFirstInit, String requestId) {
        ThreadUtil.execute(() -> {
            boolean isFirstLaunch = isFirstLaunch();
            JsonObject value = getBaseJsonInfo(isFirstLaunch);
            value.addProperty("is_first_init", isFirstInit);
            value.addProperty("request_id", requestId);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(value);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", MetricsKeyEnum.START_LSP.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);
        });
    }

    /**
     * LSP启动失败埋点（带requestId版本），通过http上报
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @param requestId 启动请求ID，用于关联start和failed事件
     */
    public static void reportStartLSPFailed(String errorCode, String errorMessage, String requestId) {
        ThreadUtil.execute(() -> {
            boolean isFirstLaunch = isFirstLaunch();
            JsonObject value = getBaseJsonInfo(isFirstLaunch);
            value.addProperty("error_code", errorCode);
            value.addProperty("error_message", errorMessage);
            value.addProperty("request_id", requestId);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(value);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", MetricsKeyEnum.START_LSP_FAILED.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);

            //上报日志
            value.addProperty("key", MetricsKeyEnum.START_LSP_FAILED.getName());
            JsonObject logBody = getHttpLogBody(value, null);
            HttpUtils.postBody(logBody, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_LOG);
        });
    }

    /**
     * 插件启动失败埋点（重试10次后整体失败），通过http上报
     * @param lastErrorCode 最近一次错误码
     * @param lastErrorMessage 最近一次错误信息
     */
    public static void reportPluginLaunchFailed(String lastErrorCode, String lastErrorMessage) {
        ThreadUtil.execute(() -> {
            boolean isFirstLaunch = isFirstLaunch();
            JsonObject value = getBaseJsonInfo(isFirstLaunch);
            value.addProperty("error_code", lastErrorCode);
            value.addProperty("error_message", lastErrorMessage);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(value);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", MetricsKeyEnum.PLUGIN_LAUNCH_FAILED.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);

            //上报日志
            value.addProperty("key", MetricsKeyEnum.PLUGIN_LAUNCH_FAILED.getName());
            JsonObject logBody = getHttpLogBody(value, null);
            HttpUtils.postBody(logBody, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_LOG);
        });
    }

    /**
     * 判断是否为插件安装后首次启动（用于识别新用户）
     * @return true表示插件安装后首次启动
     */
    public static boolean isFirstLaunch() {
        if (LocalStorageUtil.isTrueValue("jetbrains_has_launch")) {
            return false;
        }
        LocalStorageUtil.setProperty("jetbrains_has_launch", true);
        return true;
    }

    private static JsonObject getBaseJsonInfo(Boolean isFirstLaunch) {
        JsonObject value = new JsonObject();
        value.addProperty("timestamp", System.currentTimeMillis());
        if (null != isFirstLaunch) {
            value.addProperty("is_first_launch", isFirstLaunch);
        }
        value.addProperty("plugin_version", PluginUtil.getVersion());
        value.addProperty("ide_type", "jetbrains");
        value.addProperty("lifecycle_id", LSPManager.getInstance().getLifecycleId());
        return value;
    }

    private static JsonObject getHttpLogBody(JsonElement message, String module) {
        JsonObject log = new JsonObject();
        log.addProperty("level", "info");
        log.addProperty("msg", GsonUtil.toJson(message));
        log.addProperty("clientTimestamp", System.currentTimeMillis());
        log.addProperty("traceId", LSPManager.getInstance().getLifecycleId());
        log.addProperty("ide", "jetbrains");
        String moduleName = StringUtils.isEmpty(module) ? MetricsSceneEnum.PLUGIN.getName() : module;
        log.addProperty("module", moduleName);
        XhsUserInfo info = LspServiceForTylm.getUserLoginInfo();
        if (null != info) {
            JsonObject user = new JsonObject();
            user.addProperty("userId", info.getEmail());
            user.addProperty("pluginVersion", PluginUtil.getVersion());
            log.add("env", user);
        }
        log.addProperty("pluginVersion", PluginUtil.getVersion());

        JsonArray logs = new JsonArray();
        logs.add(log);
        JsonObject body = new JsonObject();
        body.add("logs", logs);
        return body;
    }

    private static JsonObject getHttpMetricsBody(JsonObject metricsValue) {
        JsonObject metrics = new JsonObject();
        metrics.addProperty("traceId", LSPManager.getInstance().getLifecycleId());
        metrics.addProperty("timestamp", System.currentTimeMillis());
        metrics.addProperty("metricsValue", GsonUtil.toJson(metricsValue));
        metrics.addProperty("localTest", PluginUtil.isDevEnv());

        JsonObject editor = new JsonObject();
        editor.addProperty("ideSeries", "jetbrains");
        editor.addProperty("ideVersion", ApplicationInfo.getInstance().getFullVersion());
        editor.addProperty("pluginVersion", PluginUtil.getVersion());
        editor.addProperty("pluginName", PluginUtil.getPluginName());
        editor.addProperty("idePlatform", (ApplicationInfo.getInstance().getVersionName()));
        editor.addProperty("lifecycle_id", LSPManager.getInstance().getLifecycleId());

        JsonObject userInfo = new JsonObject();
        XhsUserInfo info = LspServiceForTylm.getUserLoginInfo();
        if (null != info) {
            userInfo.addProperty("email", info.getEmail());
            userInfo.addProperty("accountNo", info.getAccountNo());
            userInfo.addProperty("userId", info.getUserId());
        }
        metrics.addProperty("userInfo", GsonUtil.toJson(userInfo));

        metrics.addProperty("editorInfo", GsonUtil.toJson(editor));

        return metrics;
    }
    public static void reportRcsEnvStart() {
        ThreadUtil.execute(() -> {
            JsonObject metricsBaseValue = getBaseJsonInfo(null);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(metricsBaseValue);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_ENV_START.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);

            //上报日志
            JsonArray logValue = new JsonArray();
            logValue.add(MacAddressUtil.getMacAddress());
            JsonObject logBody = getHttpLogBody(logValue, RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_ENV_START.getName());
            HttpUtils.postBody(logBody, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_LOG);
        });
    }

    public static void reportRcsEnvError(String errorCode, String errorMsg) {
        ThreadUtil.execute(() -> {
            JsonObject metricsBaseValue = getBaseJsonInfo(null);
            metricsBaseValue.addProperty("error_code", errorCode);
            metricsBaseValue.addProperty("error_message", errorMsg);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(metricsBaseValue);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_ENV_ERROR.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);

            //上报日志
            JsonArray logValue = new JsonArray();
            logValue.add(MacAddressUtil.getMacAddress());
            logValue.add(errorMsg);
            JsonObject logBody = getHttpLogBody(logValue, RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_ENV_ERROR.getName());
            HttpUtils.postBody(logBody, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_LOG);
        });
    }
    public static void reportRcsEnvSuccess() {
        ThreadUtil.execute(() -> {
            JsonObject metricsBaseValue = getBaseJsonInfo(null);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(metricsBaseValue);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_ENV_SUCCEED.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);

            //上报日志
            JsonArray logValue = new JsonArray();
            logValue.add(MacAddressUtil.getMacAddress());
            JsonObject logBody = getHttpLogBody(logValue, RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_ENV_SUCCEED.getName());
            HttpUtils.postBody(logBody, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_LOG);
        });
    }

    public static void reportRcsStart() {
        ThreadUtil.execute(() -> {
            JsonObject metricsBaseValue = getBaseJsonInfo(null);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(metricsBaseValue);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_START.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);

            //上报日志
            JsonArray logValue = new JsonArray();
            logValue.add(MacAddressUtil.getMacAddress());
            JsonObject logBody = getHttpLogBody(logValue, RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_START.getName());
            HttpUtils.postBody(logBody, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_LOG);
        });
    }

    public static void reportRcsError(String errorCode, String errorMsg) {
        ThreadUtil.execute(() -> {
            JsonObject metricsBaseValue = getBaseJsonInfo(null);
            metricsBaseValue.addProperty("error_code", errorCode);
            metricsBaseValue.addProperty("error_message", errorMsg);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(metricsBaseValue);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_ERROR.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);

            //上报日志
            JsonArray logValue = new JsonArray();
            logValue.add(MacAddressUtil.getMacAddress());
            logValue.add(errorMsg);
            JsonObject logBody = getHttpLogBody(logValue, RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_ERROR.getName());
            HttpUtils.postBody(logBody, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_LOG);
        });
    }

    public static void reportRcsSuccess() {
        ThreadUtil.execute(() -> {
            JsonObject metricsBaseValue = getBaseJsonInfo(null);
            //上报埋点
            JsonObject metricsValue = getHttpMetricsBody(metricsBaseValue);
            metricsValue.addProperty("metricsScene", MetricsSceneEnum.PLUGIN.getName());
            metricsValue.addProperty("metricsKey", RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_SUCCEED.getName());
            HttpUtils.postBody(metricsValue, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_METRICS);

            //上报日志
            JsonArray logValue = new JsonArray();
            logValue.add(MacAddressUtil.getMacAddress());
            JsonObject logBody = getHttpLogBody(logValue, RcsRunnerMetricsEnum.RCS_SERVER_CODER_CREATE_SUCCEED.getName());
            HttpUtils.postBody(logBody, PluginCommonConstant.REPORT_URL + PluginCommonConstant.REPORT_LOG);
        });
    }
} 
package com.xhs.codewiz.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/2/2
 */
public enum ExtensionEnum {
    JAVA("java", "java"),
    RS("rs", "rust"),
    PYTHON("py", "python"),
    C("c", "c"),
    OBJ_C("h", "c"),
    CPP("cpp", "cpp"),
    CSHARP("cs", "csharp"),
    TS("ts", "typescript"),
    CSS("css", "css"),
    JS("js", "javascript"),
    HTML("html", "html"),
    ;
    private  String extension;
    private  String language;
    ExtensionEnum(String extension, String language) {
        this.language = language;
        this.extension = extension;
    }
    public static String getLanguageByExtension(String extension) {
        for (ExtensionEnum extensionEnum : ExtensionEnum.values()) {
            if (StringUtils.equals(extensionEnum.extension, extension)) {
                return extensionEnum.language;
            }
        }
        return extension;
    }
    public static String getExtensionByLanguage(String language) {
        for (ExtensionEnum extensionEnum : ExtensionEnum.values()) {
            if (StringUtils.equals(extensionEnum.language, language)) {
                return extensionEnum.extension;
            }
        }
        return language;
    }
    public static String getLanguageByPath(String uri) {
        String extension = uri.substring(uri.lastIndexOf(".") + 1);
        if (StringUtils.isBlank(extension)) {
            return extension;
        }
      return getLanguageByExtension(extension);
    }
}

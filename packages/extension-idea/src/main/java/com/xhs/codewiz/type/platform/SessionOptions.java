package com.xhs.codewiz.type.platform;

import java.util.*;

/**
 * Session 选项
 */
public class SessionOptions {
    /** 是否静默处理 Session */
    private Boolean silent;

    /** 是否清除 Session 首选项 */
    private Boolean clearSessionPreference;

    /** 是否强制创建新的 Session */
    private SessionPresentationOptions forceNewSession;

    /** 如果不存在则创建 Session */
    private Boolean createIfNone;

    /** Session 账户信息 */
    private SessionAccountInformation account;

    public Boolean getSilent() {
        return silent;
    }

    public void setSilent(Boolean silent) {
        this.silent = silent;
    }

    public Boolean getClearSessionPreference() {
        return clearSessionPreference;
    }

    public void setClearSessionPreference(Boolean clearSessionPreference) {
        this.clearSessionPreference = clearSessionPreference;
    }

    public SessionPresentationOptions getForceNewSession() {
        return forceNewSession;
    }

    public void setForceNewSession(SessionPresentationOptions forceNewSession) {
        this.forceNewSession = forceNewSession;
    }

    public Boolean getCreateIfNone() {
        return createIfNone;
    }

    public void setCreateIfNone(Boolean createIfNone) {
        this.createIfNone = createIfNone;
    }

    public SessionAccountInformation getAccount() {
        return account;
    }

    public void setAccount(SessionAccountInformation account) {
        this.account = account;
    }

}


package com.xhs.codewiz.client.model;

import com.google.gson.JsonObject;
import java.util.List;

/**
 * Author: liukunpeng Date: 2025-07-15 Description:
 */
public class ServiceRegisterResponse {
  //成功-1 失败-0
  private Integer success;
  //异常信息，仅失败时有
  private String error;
  // 远端插件实例信息
  private RegisterData data;

  public Integer getSuccess() {
    return success;
  }
  public void setSuccess(Integer success) {
    this.success = success;
  }
  public String getError() {
    return error;
  }
  public void setError(String error) {
    this.error = error;
  }
  public RegisterData getData() {
    return data;
  }
  public void setData(RegisterData data) {
    this.data = data;
  }

  public static class RegisterData {
    private List<RemoteServiceMeta> dependences;
    private JsonObject requirment;

    public List<RemoteServiceMeta> getDependences() {
      return dependences;
    }

    public void setDependences(List<RemoteServiceMeta> dependences) {
      this.dependences = dependences;
    }

    public JsonObject getRequirment() {
      return requirment;
    }

    public void setRequirment(JsonObject requirment) {
      this.requirment = requirment;
    }
  }
}

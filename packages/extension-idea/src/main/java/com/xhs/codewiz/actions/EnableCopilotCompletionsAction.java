package com.xhs.codewiz.actions;

import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.StatusBar;
import com.intellij.openapi.wm.WindowManager;
import com.intellij.psi.PsiFile;
import com.xhs.codewiz.actions.statusBar.CodeWizStatusBarWidget;
import com.xhs.codewiz.setting.CodeWizApplicationSettings;
import com.xhs.codewiz.setting.CodeWizApplicationState;
import com.xhs.codewiz.utils.BundleUtil;
import com.xhs.codewiz.utils.PluginUtil;
import org.jetbrains.annotations.NotNull;

public class EnableCopilotCompletionsAction extends AnAction {
    public EnableCopilotCompletionsAction() {
    }

    public void update(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) {
            e.getPresentation().setEnabledAndVisible(false);
        } else {
            PsiFile file = e.getData(CommonDataKeys.PSI_FILE);
            boolean currentlyDisabled = !CodeWizApplicationSettings.settings().enableCompletions
                || file != null && !CodeWizApplicationSettings.isCodeWizEnabled(file);;
            e.getPresentation().setEnabledAndVisible(currentlyDisabled);
        }
    }

    public void actionPerformed(@NotNull AnActionEvent e) {
        Project project = e.getProject();
        if (project != null && !project.isDisposed()) {
            CodeWizApplicationState settings = CodeWizApplicationSettings.settings();
            settings.enableCompletions = true;
            PsiFile file = e.getData(CommonDataKeys.PSI_FILE);
            if (file != null) {
            //settings.enableLanguage(file.getLanguage());
            }

            StatusBar bar = WindowManager.getInstance().getStatusBar(project);
            if (bar != null) {
                bar.setInfo(BundleUtil.get("action.codewiz.enableCodewiz.statusEnabled"));
            }

            CodeWizStatusBarWidget.update(project);

            PluginUtil.disabledFullLineCompletion();
        }
    }
    @Override
    public @NotNull ActionUpdateThread getActionUpdateThread() {
        return ActionUpdateThread.BGT;
    }
    public boolean isDumbAware() {
        return true;
    }
}


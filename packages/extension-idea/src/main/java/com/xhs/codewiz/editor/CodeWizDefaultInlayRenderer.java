package com.xhs.codewiz.editor;

import com.intellij.openapi.editor.DefaultLanguageHighlighterColors;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.Inlay;
import com.intellij.openapi.editor.colors.EditorColorsScheme;
import com.intellij.openapi.editor.markup.TextAttributes;
import com.intellij.openapi.util.text.StringUtil;
import com.xhs.codewiz.completion.enums.CodewizCompletionType;
import com.xhs.codewiz.editor.request.EditorRequest;
import java.awt.Graphics;
import java.awt.Rectangle;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

class CodeWizDefaultInlayRenderer implements CodeWizInlayRenderer {
    private final @NotNull List<String> lines;
    private final @NotNull String content;
    private final @NotNull CodewizCompletionType type;
    private final @NotNull TextAttributes textAttributes;
    private @Nullable Inlay<CodeWizInlayRenderer> inlay;
    private int cachedWidth = -1;
    private int cachedHeight = -1;

    CodeWizDefaultInlayRenderer(@NotNull Editor editor, @NotNull EditorRequest request, @NotNull CodewizCompletionType type, @NotNull List<String> lines) {
        this.lines = replaceLeadingTabs(lines, request);
        this.type = type;
        this.content = StringUtils.join(lines, "\n");
        this.textAttributes = getTextAttributes(editor);
    }

    public @Nullable Inlay<CodeWizInlayRenderer> getInlay() {
        return this.inlay;
    }

    public void setInlay(@NotNull Inlay<CodeWizInlayRenderer> inlay) {
        this.inlay = inlay;
    }

    public @NotNull CodewizCompletionType getType() {
        return this.type;
    }

    public @NotNull List<String> getContentLines() {
        return this.lines;
    }

    public @Nullable @NonNls String getContextMenuGroupId(@NotNull Inlay inlay) {
        return "codewiz.inlayContextMenu";
    }

    public int calcHeightInPixels(@NotNull Inlay inlay) {
        return this.cachedHeight < 0 ? (this.cachedHeight = inlay.getEditor().getLineHeight() * this.lines.size()) : this.cachedHeight;
    }

    public int calcWidthInPixels(@NotNull Inlay inlay) {
        if (this.cachedWidth < 0) {
            int width = InlayRendering.calculateWidth(inlay.getEditor(), this.content, this.lines);
            return this.cachedWidth = Math.max(1, width);
        } else {
            return this.cachedWidth;
        }
    }

    public void paint(@NotNull Inlay inlay, @NotNull Graphics g, @NotNull Rectangle region, @NotNull TextAttributes surroundingTextAttributes) {
        Editor editor = inlay.getEditor();
        if (!editor.isDisposed()) {
            InlayRendering.renderCodeBlock(editor, this.content, this.lines, g, region, this.textAttributes);
        }
    }

    static List<String> replaceLeadingTabs(@NotNull List<String> lines, @NotNull EditorRequest request) {
        return lines.stream().map((line) -> {
            int tabCount = StringUtil.countChars(line, '\t', 0, true);
            if (tabCount > 0) {
                String tabSpaces = StringUtil.repeatSymbol(' ', tabCount * request.getTabWidth());
                return tabSpaces + line.substring(tabCount);
            } else {
                return line;
            }
        }).collect(Collectors.toList());
    }

    private static @NotNull TextAttributes getTextAttributes(@NotNull Editor editor) {
        //Color userColor = CodewizApplicationSettings.settings().inlayTextColor;
        EditorColorsScheme scheme = editor.getColorsScheme();
        TextAttributes themeAttributes = scheme.getAttributes(DefaultLanguageHighlighterColors.INLAY_TEXT_WITHOUT_BACKGROUND);
        return themeAttributes;
        /*if (userColor == null && themeAttributes != null && themeAttributes.getForegroundColor() != null) {
            return themeAttributes;
        } else {
            TextAttributes customAttributes = themeAttributes != null ? themeAttributes.clone() : new TextAttributes();
            if (userColor != null) {
                customAttributes.setForegroundColor(userColor);
            }

            if (customAttributes.getForegroundColor() == null) {
                customAttributes.setForegroundColor(JBColor.GRAY);
            }

            return customAttributes;
        }*/
    }
    public @NotNull List<String> getLines() {
        return this.lines;
    }

    public @NotNull String getContent() {
        return this.content;
    }

    public void setCachedWidth(int cachedWidth) {
        this.cachedWidth = cachedWidth;
    }

    public void setCachedHeight(int cachedHeight) {
        this.cachedHeight = cachedHeight;
    }
}

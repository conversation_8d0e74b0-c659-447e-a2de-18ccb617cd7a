package com.xhs.codewiz.scheme.global;

import java.util.*;
/**
 * 创建、更新、删除全局敏感信息
 */
public class UpdateSecretStorage {
    private String schemaProtocol = "global.update.secretstorage";
    private UpdateSecretStorageParams params;

    public UpdateSecretStorageParams getParams() {
        return params;
    }
    public void setParams(UpdateSecretStorageParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateSecretStorageParams {
        /** 存储的值, 如果不提供则删除该键 */
        private String value; // optional
        /** 存储的键 */
        private String key;
    
        public String getValue() {
            return value;
        }
        public void setValue(String value) {
            this.value = value;
        }
        public String getKey() {
            return key;
        }
        public void setKey(String key) {
            this.key = key;
        }
    }
}

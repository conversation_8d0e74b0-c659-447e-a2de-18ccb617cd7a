package com.xhs.codewiz.completion;

import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.Inlay;
import com.intellij.openapi.keymap.KeymapUtil;
import com.intellij.openapi.ui.popup.Balloon;
import com.intellij.openapi.ui.popup.Balloon.Position;
import com.intellij.ui.GotItTooltip;
import com.xhs.codewiz.editor.CodeWizInlayRenderer;
import com.xhs.codewiz.editor.InlayDisposeContext;
import com.xhs.codewiz.editor.request.EditorRequest;
import com.xhs.codewiz.listener.topic.CodewizInlayListener;
import com.xhs.codewiz.utils.BundleUtil;
import com.xhs.codewiz.utils.IconsUtil;
import java.awt.Component;
import java.awt.Point;
import java.awt.Rectangle;
import java.lang.reflect.Method;
import java.util.List;
import javax.swing.JComponent;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;
import org.jetbrains.annotations.NotNull;

public class CodeWizInlayGotItListener implements CodewizInlayListener {
    public CodeWizInlayGotItListener() {
    }

    public void inlaysUpdated(@NotNull EditorRequest request, @NotNull InlayDisposeContext context, @NotNull Editor editor, @NotNull List<Inlay<CodeWizInlayRenderer>> insertedInlays) {
        if (!insertedInlays.isEmpty() && !request.isCancelled()) {
            String applyShortcut = KeymapUtil.getShortcutText("codewiz.rcs.applyInlays");
            String applyInlaysNextWord = KeymapUtil.getShortcutText("codewiz.rcs.applyInlaysNextWord");
            String applyInlaysNextLine = KeymapUtil.getShortcutText("codewiz.rcs.applyInlaysNextLine");
            String header = BundleUtil.get("editor.inlayGotIt.header");
            String message = BundleUtil.get("editor.inlayGotIt.message", new Object[]{applyShortcut, applyInlaysNextWord, applyInlaysNextLine});
            GotItTooltip tooltip = (new GotItTooltip("copilot.inlayGotIt", message, request.getDisposable())).withHeader(header).withPosition(Position.atLeft).withIcon(
                IconsUtil.CODEWIZ).andShowCloseShortcut();
            Rectangle inlayBounds = (insertedInlays.get(0)).getBounds();
            if (inlayBounds != null && tooltip.canShow()) {
                try {
                    this.showTooltip(editor.getContentComponent(), tooltip, inlayBounds.getLocation());
                } catch (Exception var13) {
                    Logger.getInstance(this.getClass()).error("Error showing inlay GotIt tooltip", var13);
                }
            }

        }
    }

    private void showTooltip(@NotNull JComponent component, @NotNull GotItTooltip tooltip, final Point location) throws Exception {
        Class<? extends GotItTooltip> tooltipClass = tooltip.getClass();
        Method method;
        if (ApplicationInfo.getInstance().getBuild().getBaselineVersion() <= 211) {
            method = tooltipClass.getMethod("show", JComponent.class, Function1.class);
            Function1<Component, Point> pointProvider = (a) -> location;
            method.invoke(tooltip, component, pointProvider);
        } else {
            method = tooltipClass.getMethod("show", JComponent.class, Function2.class);
            Function2<Component, Balloon, Point> pointProvider = (a, b) -> location;
            method.invoke(tooltip, component, pointProvider);
        }

    }
}


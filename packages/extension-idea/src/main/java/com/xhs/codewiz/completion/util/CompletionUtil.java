package com.xhs.codewiz.completion.util;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.util.TextRange;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.completion.CodewizCompletion;
import com.xhs.codewiz.completion.enums.CodewizCompletionType;
import com.xhs.codewiz.completion.request.CodewizInlayList;
import com.xhs.codewiz.completion.request.DefaultInlayList;
import com.xhs.codewiz.editor.request.CodewizEditorInlay;
import com.xhs.codewiz.editor.request.DefaultCodewizEditorInlay;
import com.xhs.codewiz.editor.request.EditorRequest;
import com.xhs.codewiz.editor.request.LineInfo;
import com.xhs.codewiz.utils.CodewizStringUtil;
import com.xhs.codewiz.editor.language.LanguageUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CompletionUtil {
    private static final Logger LOG = Logger.getInstance(CompletionUtil.class);
    private static final Pattern EOL_PATTERN = Pattern.compile("^\\s*[)}\\]\"'`]*\\s*[:{;,]?\\s*$");

    public CompletionUtil() {
    }

    static List<CodewizInlayList> createEditorCompletions(@NotNull EditorRequest request, @NotNull List<CodewizCompletion> items) {
        return items.stream().map((item) -> {
            return createEditorCompletion(request, item, true);
        }).collect(Collectors.toList());
    }

    public static @Nullable CodewizInlayList createEditorCompletion(@NotNull EditorRequest request, @NotNull CodewizCompletion CodewizCompletion, boolean dropLinePrefix) {
        ArrayList<String> lines = new ArrayList<>(CodewizCompletion.getCompletion());
        if (!lines.isEmpty() && (lines.size() != 1 || !(lines.get(0)).isEmpty() && !(lines.get(0)).equals("\n"))) {
            dropOverlappingTrailingLines(lines, request.getDocumentContent(), request.getOffset());
            if (lines.isEmpty()) {
                return null;
            } else {
                String replacementText = createReplacementText(request.getLineInfo(), lines);
                boolean replaceLinePrefix = dropLinePrefix && adjustWhitespace(lines, request.getLineInfo());
                return lines.isEmpty() ? null : new DefaultInlayList(CodewizCompletion, createReplacementRange(request, replaceLinePrefix), replacementText, createEditorInlays(request, lines));
            }
        } else {
            LoggerUtil.INSTANCE.logDebug(LOG, "ignoring empty completion: " + request, null);
            return null;
        }
    }

    private static @NotNull String createReplacementText(@NotNull LineInfo lineInfo, List<String> lines) {
        String text = StringUtil.join(lines, "\n");
        if (!lineInfo.isBlankLine()) {
            String ws = lineInfo.getWhitespaceBeforeCursor();
            if (text.startsWith(ws)) {
                return text.substring(ws.length());
            }
        }

        return text;
    }

    private static @NotNull TextRange createReplacementRange(@NotNull EditorRequest request, boolean replaceLinePrefix) {
        LineInfo lineInfo = request.getLineInfo();
        int startOffset = replaceLinePrefix ? lineInfo.getLineStartOffset() : request.getOffset();
        int endOffset = isReplaceLineSuffix(request) ? lineInfo.getLineEndOffset() - CodewizStringUtil.trailingWhitespaceLength(lineInfo.getLineSuffix()) : request.getOffset();
        return TextRange.create(startOffset, endOffset);
    }

    private static boolean isReplaceLineSuffix(@NotNull EditorRequest request) {
        String lineSuffix = request.getLineInfo().getLineSuffix();
        return CodewizStringUtil.isSpacesOrTabs(lineSuffix, false) || isValidMiddleOfTheLinePosition(lineSuffix);
    }

    private static @NotNull List<CodewizEditorInlay> createEditorInlays(@NotNull EditorRequest request, @NotNull List<String> lines) {
        ArrayList<CodewizEditorInlay> inlays = new ArrayList();
        int offset = request.getOffset();
        if (lines.size() > 1 && request.getLineInfo().isBlankLine() && (lines.get(0)).isEmpty()) {
            inlays.add(new DefaultCodewizEditorInlay(
                CodewizCompletionType.Block, offset, lines.subList(1, lines.size())));
        } else {
            String completionLine = lines.get(0);
            inlays.add(new DefaultCodewizEditorInlay(CodewizCompletionType.Inline, offset, List.of(completionLine)));

            /*String editorLineSuffix = request.getLineInfo().getLineSuffix();
            List<Pair<Integer, String>> diffs = CodewizStringUtil.createDiffInlays(editorLineSuffix, completionLine);
            if (diffs != null && !diffs.isEmpty()) {
                Iterator var7 = diffs.iterator();

                while(var7.hasNext()) {
                    Pair<Integer, String> diff = (Pair)var7.next();
                    Integer delta = (Integer)diff.getFirst();
                    inlays.add(new DefaultCodewizEditorInlay(CodewizCompletionType.Inline, offset + delta, List.of((String)diff.second)));
                }
            }
*/
            if (lines.size() > 1) {
                inlays.add(new DefaultCodewizEditorInlay(CodewizCompletionType.Block, offset, lines.subList(1, lines.size())));
            }
        }

        return inlays;
    }

    private static boolean dropOverlappingTrailingLines(@NotNull List<String> lines, @NotNull String editorContent, int offset) {
        if (offset < editorContent.length() && editorContent.charAt(offset) == '\n') {
            ++offset;
        }

        if (offset >= editorContent.length()) {
            return false;
        } else {
            List<String> editorLines = CodewizStringUtil.getNextLines(editorContent, offset, lines.size());
            int overlap = CodewizStringUtil.findOverlappingLines(lines, editorLines);

            for(int i = 0; i < overlap; ++i) {
                lines.remove(lines.size() - 1);
            }

            return overlap >= 1;
        }
    }

    public static String dropOverlappingTrailingLines(@NotNull String linesString, @NotNull String editorContent, int offset) {
        ArrayList<String> lines = new ArrayList(Arrays.asList(CodewizStringUtil.splitLines(linesString)));
        return dropOverlappingTrailingLines(lines, editorContent, offset) ? StringUtil.join(lines, "\n") : linesString;
    }

    private static boolean adjustWhitespace(@NotNull List<String> completionLines, @NotNull LineInfo lineInfo) {
        String editorWhitespacePrefix = lineInfo.getWhitespaceBeforeCursor();
        if (!completionLines.isEmpty() && !editorWhitespacePrefix.isEmpty()) {
            boolean isEditorEmptyLine = lineInfo.isBlankLine();
            boolean replacePrefixInEditor = false;
            String firstLine = (String)completionLines.get(0);
            String firstLineFixed = firstLine;
            if (firstLine.startsWith(editorWhitespacePrefix)) {
                firstLineFixed = firstLine.substring(editorWhitespacePrefix.length());
                replacePrefixInEditor = isEditorEmptyLine;
            } else if (isEditorEmptyLine) {
                String lineLeadingWhitespace = CodewizStringUtil.leadingWhitespace(firstLine);
                firstLineFixed = firstLine.substring(lineLeadingWhitespace.length());
                replacePrefixInEditor = !firstLine.isEmpty() && !lineLeadingWhitespace.startsWith(editorWhitespacePrefix);
            }

            completionLines.set(0, firstLineFixed);
            return replacePrefixInEditor;
        } else {
            return false;
        }
    }

    public static @Nullable CodewizCompletion apiChoiceWithoutPrefix(@NotNull CodewizCompletion apiChoice, @NotNull String prefix) {
        if (prefix.isEmpty()) {
            return apiChoice;
        } else {
            boolean ignoreFirstWhiteSpace = CodewizStringUtil.leadingWhitespace(prefix).isEmpty();
            List<String> completion = apiChoice.getCompletion();
            String remainingPrefix = prefix;
            int i = 0;

            for(int completionSize = completion.size(); i < completionSize; ++i) {
                String line = completion.get(i);
                int prefixLineEnd = remainingPrefix.indexOf(10);
                String prefixLine = remainingPrefix.substring(0, prefixLineEnd == -1 ? remainingPrefix.length() : prefixLineEnd);
                if (ignoreFirstWhiteSpace && i == 0) {
                    String trimmedLine = CodewizStringUtil.stripLeading(line);
                    boolean ok = prefixLineEnd == -1 ? trimmedLine.startsWith(prefixLine) : trimmedLine.equals(prefixLine);
                    if (!ok) {
                        return null;
                    }
                } else {
                    boolean ok = prefixLineEnd == -1 ? line.startsWith(prefixLine) : line.equals(prefixLine);
                    if (!ok) {
                        return null;
                    }
                }

                if (prefixLineEnd == -1) {
                    ArrayList<String> newCompletions = new ArrayList(completionSize - i);
                    int droppedWhitespace = ignoreFirstWhiteSpace && i == 0 ? CodewizStringUtil.leadingWhitespace(line).length() : 0;
                    newCompletions.add(line.substring(droppedWhitespace + prefixLine.length()));
                    if (i + 1 < completionSize) {
                        newCompletions.addAll(completion.subList(i + 1, completionSize));
                    }

                    return apiChoice.withCompletion(newCompletions);
                }

                remainingPrefix = remainingPrefix.substring(prefixLineEnd + 1);
            }

            return null;
        }
    }
    public static boolean isFrontFile(@NotNull Document document) {
        VirtualFile virtualFile = FileDocumentManager.getInstance().getFile(document);
        if (virtualFile == null) {
            return false;
        }
        String filePath = virtualFile.getPresentableUrl();
        String language = LanguageUtil.getLanguageByFilePath(filePath);
        return "JavaScript".equalsIgnoreCase(language) || "TypeScript".equals(language) || "Vue".equalsIgnoreCase(language);
    }
    public static boolean isValidMiddleOfTheLinePosition(@NotNull String lineSuffix) {
        return EOL_PATTERN.matcher(lineSuffix.trim()).matches();
    }
}


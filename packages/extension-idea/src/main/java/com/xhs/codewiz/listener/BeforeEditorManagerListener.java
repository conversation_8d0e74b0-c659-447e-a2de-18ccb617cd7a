package com.xhs.codewiz.listener;

import java.util.Arrays;
import java.util.List;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.fileEditor.ex.FileEditorManagerEx;
import com.intellij.openapi.fileEditor.impl.EditorWindow;
import com.intellij.openapi.fileEditor.impl.FileEditorManagerImpl;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.editor.util.EditorTabGroupUtil;

/**
 * Author: liukunpeng Date: 2025-07-24 Description:
 */
public class BeforeEditorManagerListener implements FileEditorManagerListener.Before {
    @Override
    public void beforeFileClosed(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
        List<EditorWindow> activeWindowList = null;
        if (source instanceof FileEditorManagerEx ex) {
            activeWindowList = Arrays.asList(ex.getWindows());
        } else if (source instanceof FileEditorManagerImpl impl) {
            activeWindowList = Arrays.asList(impl.getWindows());
        }
        EditorTabGroupUtil.closeTab(activeWindowList, source.getProject(), file);
    }
}

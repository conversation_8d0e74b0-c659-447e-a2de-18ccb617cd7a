package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.FileType;
import com.xhs.codewiz.type.file.FileSystemError;
public class ReadDirectoryResponse {
    private List<ReadDirectoryResponseResult> result; // optional
    private String error; // optional

    public List<ReadDirectoryResponseResult> getResult() {
        return result;
    }
    public void setResult(List<ReadDirectoryResponseResult> result) {
        this.result = result;
    }
    public String getError() {
        return error;
    }
    public void setError(String error) {
        this.error = error;
    }

        public static class ReadDirectoryResponseResult {
        /** 文件或目录的名称 */
        private String name;
        private FileType type;
    
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public FileType getType() {
            return type;
        }
        public void setType(FileType type) {
            this.type = type;
        }
    }
}

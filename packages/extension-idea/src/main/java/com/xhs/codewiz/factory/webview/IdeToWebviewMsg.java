package com.xhs.codewiz.factory.webview;

import com.google.gson.JsonObject;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.ui.jcef.JBCefBrowser;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * <AUTHOR>
 * @date 2025/7/22 15:58
 */
public class IdeToWebviewMsg {

    private static final Logger log = Logger.getInstance(IdeToWebviewMsg.class);

    public static void executeJavaScript(String message, JBCefBrowser jbCefBrowser) {
        String jsCode = "window.postMessage(" + message + ",'*');";
        jbCefBrowser.getCefBrowser()
                .executeJavaScript(jsCode, jbCefBrowser.getCefBrowser().getURL(), 0);


        try {
            JsonObject jsonObject = GsonUtil.fromJson(message);
            if (jsonObject.get("invoke") != null && "setChatBoxMessage".equals(jsonObject.get("invoke").getAsString())) {
                jbCefBrowser.getCefBrowser().setFocus(true);
            }
        } catch (Exception e) {
            log.error("parse executeJavaScript to json error", e);
        }
    }
}

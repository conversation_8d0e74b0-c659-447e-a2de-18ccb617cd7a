package com.xhs.codewiz.scheme.global;

import java.util.*;
import com.xhs.codewiz.type.global.Scope;
/**
 * 读取状态
 */
public class ReadState {
    private String schemaProtocol = "global.read.state";
    private ReadStateParams params;

    public ReadStateParams getParams() {
        return params;
    }
    public void setParams(ReadStateParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadStateParams {
        private Scope scope;
        /** 需要读取的状态键 */
        private String key;
    
        public Scope getScope() {
            return scope;
        }
        public void setScope(Scope scope) {
            this.scope = scope;
        }
        public String getKey() {
            return key;
        }
        public void setKey(String key) {
            this.key = key;
        }
    }
}

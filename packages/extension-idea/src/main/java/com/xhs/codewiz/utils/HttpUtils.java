package com.xhs.codewiz.utils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.intellij.openapi.diagnostic.Logger;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Map;

/**
 * Author: liukunpeng Date: 2025-07-04 Description:
 */
public class HttpUtils {
  static Logger logger = Logger.getInstance(HttpUtils.class);
  public static String postBody(JsonObject body, String url) {
    try {
      HttpClient client = HttpClient.newHttpClient();
      HttpRequest request = HttpRequest.newBuilder()
          .uri(new URI(url))
          .header("Content-Type", "application/json")
          .POST(HttpRequest.BodyPublishers.ofString(new Gson().toJson(body)))
          .build();
      HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
      if (response.statusCode() == 200) { // 如果响应码为 200，则请求成功
        return response.body();
      } else {
        LoggerUtil.INSTANCE.logWarn(logger, "Failed : HTTP error code : " + response.statusCode(), null);
        return null;
      }
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(logger, "postBody error : ", e);
      return null;
    }
  }
  public static String postBody(Map<String, String> body, String url) throws Exception {
    HttpClient client = HttpClient.newHttpClient();
    HttpRequest request = HttpRequest.newBuilder()
        .uri(new URI(url))
        .header("Content-Type", "application/json")
        .POST(HttpRequest.BodyPublishers.ofString(new Gson().toJson(body)))
        .build();
    HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
    if (response.statusCode() == 200) { // 如果响应码为 200，则请求成功
      return response.body();
    } else {
      LoggerUtil.INSTANCE.logWarn(logger, "Failed : HTTP error code : " + response.statusCode(), null);
      return null;
    }
  }
  public static <T, R> R postBody(T requestBody, TypeToken<R> typeToken, String url) {
    HttpClient client = HttpClient.newHttpClient();
    String json;
    if (requestBody instanceof String) {
      json = (String) requestBody;
    } else {
      json = GsonUtil.toJson(requestBody, false);
    }
    try {
      HttpRequest request = HttpRequest.newBuilder()
          .uri(new URI(url))
          .header("Content-Type", "application/json")
          .POST(HttpRequest.BodyPublishers.ofString(json))
          .build();
      HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
      if (response.statusCode() == 200) { // 如果响应码为 200，则请求成功
        String responseBodyStr = response.body();
        if (typeToken.getType().equals(String.class)) {
          return (R) responseBodyStr;
        }
        return GsonUtil.fromJson(responseBodyStr, typeToken);
      } else {
        LoggerUtil.INSTANCE.logWarn(logger, "Failed : HTTP error code : " + response.statusCode(), null);
        return null;
      }
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(logger, "postBody error : ", e);
      return null;
    }
  }
}

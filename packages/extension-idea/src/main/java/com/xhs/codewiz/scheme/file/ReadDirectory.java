package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 读取目录
 */
public class ReadDirectory {
    private String schemaProtocol = "file.read.directory";
    private ReadDirectoryParams params;

    public ReadDirectoryParams getParams() {
        return params;
    }
    public void setParams(ReadDirectoryParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadDirectoryParams {
        private String uri;
    
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

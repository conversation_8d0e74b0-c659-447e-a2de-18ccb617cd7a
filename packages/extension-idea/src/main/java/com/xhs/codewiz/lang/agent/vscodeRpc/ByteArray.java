package com.xhs.codewiz.lang.agent.vscodeRpc;


import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import org.jetbrains.annotations.NotNull;

public class ByteArray {
    static final byte[] EMPTY_ARRAY = new byte[0];
    private int size = 0;
    private byte[] data;

    public ByteArray() {
        this.data = EMPTY_ARRAY;
    }

    public void add(byte[] newData) {
        int oldSize = this.size;
        byte[] oldData = this.data;
        this.size += newData.length;
        this.data = new byte[this.size];
        System.arraycopy(oldData, 0, this.data, 0, oldSize);
        System.arraycopy(newData, 0, this.data, oldSize, newData.length);
    }

    public byte[] getBytes(int startOffset, int endOffset) {
        assert startOffset >= 0;

        assert startOffset <= endOffset;

        assert endOffset <= this.size;

        int length = endOffset - startOffset;
        if (length == 0) {
            return EMPTY_ARRAY;
        } else {
            byte[] copy = new byte[length];
            System.arraycopy(this.data, startOffset, copy, 0, length);
            return copy;
        }
    }

    public void deleteFirst(int length) {
        if (length == this.size) {
            this.data = EMPTY_ARRAY;
            this.size = 0;
        } else {
            byte[] newData = this.getBytes(length, this.size);
            this.data = newData;
            this.size = newData.length;
        }
    }

    public int size() {
        return this.size;
    }

    public @NotNull String toString(int startOffset, int endOffset, @NotNull Charset charset) {
        assert startOffset >= 0;

        assert startOffset <= endOffset;

        assert endOffset <= this.size;

        return new String(this.data, startOffset, endOffset - startOffset, charset);
    }

    public @NotNull String toString(@NotNull Charset charset) {
        return this.toString(0, this.size, charset);
    }

    public @NotNull String toString() {
        return this.toString(StandardCharsets.UTF_8);
    }

    public int indexOf(byte[] query) {
        if (query.length != 0 && query.length <= this.size) {
            label27:
            for(int i = 0; i < this.size - query.length + 1; ++i) {
                for(int j = 0; j < query.length; ++j) {
                    if (this.data[i + j] != query[j]) {
                        continue label27;
                    }
                }

                return i;
            }

            return -1;
        } else {
            return -1;
        }
    }
}


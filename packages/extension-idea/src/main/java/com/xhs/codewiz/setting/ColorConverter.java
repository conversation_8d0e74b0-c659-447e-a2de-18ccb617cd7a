package com.xhs.codewiz.setting;


import com.intellij.ui.ColorUtil;
import com.intellij.util.xmlb.Converter;
import java.awt.Color;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class ColorConverter extends Converter<Color> {
    public ColorConverter() {
    }

    public @Nullable Color fromString(@NotNull String value) {
        try {
            return ColorUtil.fromHex(value);
        } catch (Exception var3) {
            return null;
        }
    }

    public @Nullable String toString(@NotNull Color value) {
        return ColorUtil.toHtmlColor(value);
    }
}


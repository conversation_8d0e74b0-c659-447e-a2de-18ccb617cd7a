package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 执行终端命令
 */
public class ExecuteTerminal {
    private String schemaProtocol = "platform.execute.terminal";
    private ExecuteTerminalParams params;

    public ExecuteTerminalParams getParams() {
        return params;
    }
    public void setParams(ExecuteTerminalParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteTerminalParams {
        /** 本次执行对应的ID。\n后续会通过这个 ID 读取执行结果等相关信息。 */
        private String executeId;
        private ExecuteTerminalParamsOptions options; // optional
        /** 终端 ID */
        private String terminal;
        /** 需要执行、插入的终端消息内容 */
        private String message;
    
        public String getExecuteId() {
            return executeId;
        }
        public void setExecuteId(String executeId) {
            this.executeId = executeId;
        }
        public ExecuteTerminalParamsOptions getOptions() {
            return options;
        }
        public void setOptions(ExecuteTerminalParamsOptions options) {
            this.options = options;
        }
        public String getTerminal() {
            return terminal;
        }
        public void setTerminal(String terminal) {
            this.terminal = terminal;
        }
        public String getMessage() {
            return message;
        }
        public void setMessage(String message) {
            this.message = message;
        }
    }

        public static class ExecuteTerminalParamsOptions {
        /** 不读取执行结果 */
        private Boolean neverRead; // optional
        /** 仅插入，不执行命令 */
        private Boolean doNotExecute; // optional
    
        public Boolean getNeverRead() {
            return neverRead;
        }
        public void setNeverRead(Boolean neverRead) {
            this.neverRead = neverRead;
        }
        public Boolean getDoNotExecute() {
            return doNotExecute;
        }
        public void setDoNotExecute(Boolean doNotExecute) {
            this.doNotExecute = doNotExecute;
        }
    }
}

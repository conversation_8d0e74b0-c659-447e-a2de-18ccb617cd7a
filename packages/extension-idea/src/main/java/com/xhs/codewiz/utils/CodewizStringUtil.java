package com.xhs.codewiz.utils;


import com.intellij.openapi.util.Pair;
import com.intellij.openapi.util.ProperTextRange;
import com.intellij.openapi.util.TextRange;
import com.intellij.util.diff.Diff;
import com.intellij.util.diff.FilesTooBigForDiffException;
import com.intellij.util.text.TextRanges;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class CodewizStringUtil {
    private static final Pattern NEWLINE_PATTERN = Pattern.compile("\r\n|\n");

    private CodewizStringUtil() {
    }

    public static String[] splitLines(@NotNull CharSequence text) {
        return NEWLINE_PATTERN.split(text);
    }

    public static @NotNull String trailingWhitespace(@NotNull String text) {
        if (text.isEmpty()) {
            return "";
        } else {
            int endOffset;
            for(endOffset = text.length(); endOffset > 0; --endOffset) {
                char ch = text.charAt(endOffset - 1);
                if (ch == '\n' || !Character.isWhitespace(ch)) {
                    break;
                }
            }

            return text.substring(endOffset);
        }
    }

    public static int trailingWhitespaceLength(@NotNull String text) {
        if (text.isEmpty()) {
            return 0;
        } else {
            int length = text.length();

            int endOffset;
            for(endOffset = length; endOffset > 0; --endOffset) {
                char ch = text.charAt(endOffset - 1);
                if (ch != ' ' && ch != '\t') {
                    break;
                }
            }

            return length - endOffset;
        }
    }

    public static @NotNull String leadingWhitespace(@NotNull String text) {
        return text.isEmpty() ? "" : text.substring(0, leadingWhitespaceLength(text));
    }

    public static int leadingWhitespaceLength(@NotNull String text) {
        int length = text.length();

        int offset;
        for(offset = 0; offset < length; ++offset) {
            char ch = text.charAt(offset);
            if (ch == '\n' || !Character.isWhitespace(ch)) {
                break;
            }
        }

        return offset;
    }

    public static @NotNull String stripLeading(@NotNull String text) {
        if (text.isEmpty()) {
            return "";
        } else {
            int length = leadingWhitespaceLength(text);
            return length == 0 ? text : text.substring(length);
        }
    }

    public static int findOverlapLength(@NotNull String withTrailing, @NotNull String withLeading) {
        if (!withTrailing.isEmpty() && !withLeading.isEmpty()) {
            int trailingLength = withTrailing.length();

            for(int i = 0; i <= trailingLength; ++i) {
                if (withLeading.startsWith(withTrailing.substring(i))) {
                    return trailingLength - i;
                }
            }

            return 0;
        } else {
            return 0;
        }
    }

    public static int findOverlappingLines(@NotNull List<String> withTrailing, @NotNull List<String> withLeading) {
        if (!withTrailing.isEmpty() && !withLeading.isEmpty()) {
            int trailingSize = withTrailing.size();
            int leadingSize = withLeading.size();
            int maxLines = Math.min(trailingSize, leadingSize);
            int overlapping = 0;

            for(int i = 1; i <= maxLines; ++i) {
                List<String> lines = withTrailing.subList(trailingSize - i, trailingSize);
                if (linesMatch(withLeading.subList(0, i), lines, true)) {
                    overlapping = i;
                } else if (overlapping > 0) {
                    break;
                }
            }

            return overlapping;
        } else {
            return 0;
        }
    }

    public static @Nullable List<Pair<Integer, String>> createDiffInlays(@NotNull String editor, @NotNull String completion) {
        String commonPrefix = findCommonPrefix(completion, editor);
        String editorAdjusted = editor.substring(commonPrefix.length());
        String completionAdjusted = completion.substring(commonPrefix.length());
        int[] editorChars = editorAdjusted.chars().toArray();
        int[] completionChars = completionAdjusted.chars().toArray();
        patchCharPairs(completionChars);
        int patchDelta = commonPrefix.length();

        try {
            Diff.Change changelist = Diff.buildChanges(editorChars, completionChars);
            if (changelist == null) {
                return null;
            } else {
                LinkedList<Pair<Integer, String>> result = new LinkedList();
                ArrayList<Diff.Change> changes = changelist.toList();
                Iterator var11 = changes.iterator();

                while(var11.hasNext()) {
                    Diff.Change change = (Diff.Change)var11.next();
                    if (change.inserted > 0) {
                        result.add(Pair.create(change.line0 + patchDelta, unpatchCharPairs(completionChars, change.line1, change.inserted)));
                    }
                }

                return result;
            }
        } catch (FilesTooBigForDiffException var13) {
            return null;
        }
    }

    private static String findCommonPrefix(@NotNull String data, @NotNull String reference) {
        int maxSize = Math.min(data.length(), reference.length());
        int first = 0;

        for(int i = 0; i < maxSize && data.charAt(i) == reference.charAt(i); ++i) {
            ++first;
        }

        return data.substring(0, first);
    }

    public static List<String> getNextLines(@NotNull String text, int offset, int maxLines) {
        LinkedList<String> lines = new LinkedList();
        int done = 0;

        for(int last = offset; done < maxLines; ++done) {
            int next = text.indexOf(10, last);
            if (next == -1) {
                if (text.length() > last) {
                    lines.add(text.substring(last));
                }
                break;
            }

            lines.add(text.substring(last, next));
            last = next + 1;
        }

        return lines;
    }

    public static boolean isSpaceOrTab(char c, boolean withNewline) {
        return c == ' ' || c == '\t' || withNewline && c == '\n';
    }

    public static boolean isSpacesOrTabs(CharSequence text, boolean withNewlines) {
        for(int i = 0; i < text.length(); ++i) {
            char c = text.charAt(i);
            if (!isSpaceOrTab(c, withNewlines)) {
                return false;
            }
        }

        return true;
    }

    public static boolean linesMatch(@NotNull Iterable<String> a, @NotNull Iterable<String> b, boolean trimEnd) {
        Iterator<String> itA = a.iterator();
        Iterator<String> itB = b.iterator();

        while(itA.hasNext() && itB.hasNext()) {
            String itemA = (String)itA.next();
            String itemB = (String)itB.next();
            boolean match = trimEnd ? itemA.stripTrailing().equals(itemB.stripTrailing()) : itemA.equals(itemB);
            if (!match) {
                return false;
            }
        }

        return !itA.hasNext() && !itB.hasNext();
    }

    static int[] patchCharPairs(int[] chars) {
        int parenChar = 65536;
        TextRanges stringRanges = findStringRanges(chars);

        for(int i = 0; i < chars.length; ++i) {
            int c = chars[i];
            if ((c == 40 || c == 41) && isInRange(stringRanges, i)) {
                chars[i] = 65536 + (c == 41 ? 1 : 0);
            } else if (c == 40) {
                int closeIndex = firstMatchingPair(chars, i + 1, ')', '(', stringRanges);
                if (closeIndex != -1) {
                    chars[i] = 65536;
                    chars[closeIndex] = 65537;
                }
            }
        }

        return chars;
    }

    private static int firstMatchingPair(int[] chars, int startIndex, char pairClose, char pairOpen, TextRanges excludedRanges) {
        int openCount = 0;

        for(int i = startIndex; i < chars.length; ++i) {
            if (!isInRange(excludedRanges, i)) {
                int c = chars[i];
                if (c == pairOpen) {
                    ++openCount;
                } else if (c == pairClose) {
                    if (openCount == 0) {
                        return i;
                    }

                    --openCount;
                }
            }
        }

        return -1;
    }

    private static boolean isInRange(TextRanges ranges, int i) {
        Iterator var2 = ranges.iterator();

        while(var2.hasNext()) {
            TextRange range = (TextRange)var2.next();
            if (range.contains(i)) {
                return true;
            }

            if (i > range.getEndOffset()) {
                break;
            }
        }

        return false;
    }

    private static TextRanges findStringRanges(int[] chars) {
        TextRanges ranges = new TextRanges();
        int singleQuotedStart = -1;
        int doubleQuotedStart = -1;

        for(int i = 0; i < chars.length; ++i) {
            int c = chars[i];
            if (c == 34 && singleQuotedStart == -1) {
                if (doubleQuotedStart == -1) {
                    doubleQuotedStart = i;
                } else {
                    ranges.union(new ProperTextRange(doubleQuotedStart, i));
                    doubleQuotedStart = -1;
                }
            } else if (c == 39 && doubleQuotedStart == -1) {
                if (singleQuotedStart == -1) {
                    singleQuotedStart = i;
                } else {
                    ranges.union(new ProperTextRange(singleQuotedStart, i));
                    singleQuotedStart = -1;
                }
            }
        }

        return ranges;
    }

    static String unpatchCharPairs(int[] patchedData, int offset, int count) {
        int parenChar = 65536;
        int braceChar = 65538;
        int bracketChar = 65540;
        int[] result = new int[count];

        for(int i = 0; i < count; ++i) {
            int c = patchedData[offset + i];
            switch (c) {
                case 65536:
                    result[i] = 40;
                    break;
                case 65537:
                    result[i] = 41;
                    break;
                case 65538:
                    result[i] = 123;
                    break;
                case 65539:
                    result[i] = 125;
                    break;
                case 65540:
                    result[i] = 91;
                    break;
                case 65541:
                    result[i] = 93;
                    break;
                default:
                    result[i] = c;
            }
        }

        return new String(result, 0, count);
    }

    public static boolean isJavaLineEnding(String lineContent) {
        return StringUtils.isBlank(lineContent) || lineContent.endsWith(";") || lineContent.endsWith("{") || lineContent.endsWith("*/") || lineContent.startsWith("//");
    }
}


package com.xhs.codewiz.type.buildin;

import java.util.*;

public class ExtensionContributes {
    /** 扩展的配置选项 */
    private List<ExtensionContributesConfigurationItem> configuration;

    private ExtensionContributesViewsContainers viewsContainers;

    private Map<String, List<Object>> menus;

    private Map<String, List<Object>> views;

    private List<ExtensionContributesCommandsItem> commands;

    private List<ExtensionContributesSubmenusItem> submenus;

    public List<ExtensionContributesConfigurationItem> getConfiguration() {
        return configuration;
    }

    public void setConfiguration(List<ExtensionContributesConfigurationItem> configuration) {
        this.configuration = configuration;
    }

    public ExtensionContributesViewsContainers getViewsContainers() {
        return viewsContainers;
    }

    public void setViewsContainers(ExtensionContributesViewsContainers viewsContainers) {
        this.viewsContainers = viewsContainers;
    }

    public Map<String, List<Object>> getMenus() {
        return menus;
    }

    public void setMenus(Map<String, List<Object>> menus) {
        this.menus = menus;
    }

    public Map<String, List<Object>> getViews() {
        return views;
    }

    public void setViews(Map<String, List<Object>> views) {
        this.views = views;
    }

    public List<ExtensionContributesCommandsItem> getCommands() {
        return commands;
    }

    public void setCommands(List<ExtensionContributesCommandsItem> commands) {
        this.commands = commands;
    }

    public List<ExtensionContributesSubmenusItem> getSubmenus() {
        return submenus;
    }

    public void setSubmenus(List<ExtensionContributesSubmenusItem> submenus) {
        this.submenus = submenus;
    }

public class ExtensionContributesConfigurationItem {
    private String title;

    private Map<String, Object> properties;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

}
public static class ExtensionContributesViewsContainers {
    private List<ExtensionContributesViewsContainersActivitybarItem> activitybar;

    private List<ExtensionContributesViewsContainersPanelItem> panel;

    public List<ExtensionContributesViewsContainersActivitybarItem> getActivitybar() {
        return activitybar;
    }

    public void setActivitybar(List<ExtensionContributesViewsContainersActivitybarItem> activitybar) {
        this.activitybar = activitybar;
    }

    public List<ExtensionContributesViewsContainersPanelItem> getPanel() {
        return panel;
    }

    public void setPanel(List<ExtensionContributesViewsContainersPanelItem> panel) {
        this.panel = panel;
    }

public static class ExtensionContributesViewsContainersActivitybarItem {
    private String icon;

    private String id;

    private String title;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

}
public static class ExtensionContributesViewsContainersPanelItem {
    private String icon;

    private String id;

    private String title;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

}
}
public class ExtensionContributesCommandsItem {
    private String icon;

    private String title;

    private String command;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

}
public class ExtensionContributesSubmenusItem {
    private String id;

    private String label;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
}

package com.xhs.codewiz.lang.entity;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import org.eclipse.lsp4j.InitializeParams;

public class InitializeParamsWithConfig
    extends InitializeParams implements JsonRpcCommand<Boolean> {
  @SerializedName("ideSeries")
  private String ideSeries;
  @SerializedName("idePlatform")
  private String idePlatform;
  @SerializedName("ideVersion")
  private String ideVersion;
  @SerializedName("pluginVersion")
  private String pluginVersion;
  @SerializedName("allowStatistics")
  private Boolean allowStatistics;
  @SerializedName("inferenceMode")
  private String inferenceMode;
  @SerializedName("maxCandidateNum")
  private int maxCandidateNum;
  @SerializedName("pluginPublisher")
  private String pluginPublisher;
  @SerializedName("pluginName")
  private String pluginName;
  @SerializedName("port")
  private Integer port;
  @SerializedName("gitRepo")
  private String gitRepo;
  @SerializedName("gitBranch")
  private String gitBranch;
  @SerializedName("gitCommit")
  private String gitCommit;

  public String getIdeSeries() {
    return this.ideSeries;
  }

  public void setIdeSeries(String ideSeries) {
    this.ideSeries = ideSeries;
  }

  public String getIdePlatform() {
    return this.idePlatform;
  }

  public void setIdePlatform(String idePlatform) {
    this.idePlatform = idePlatform;
  }

  public String getIdeVersion() {
    return this.ideVersion;
  }

  public void setIdeVersion(String ideVersion) {
    this.ideVersion = ideVersion;
  }

  public String getPluginVersion() {
    return this.pluginVersion;
  }

  public void setPluginVersion(String pluginVersion) {
    this.pluginVersion = pluginVersion;
  }

  public Boolean getAllowStatistics() {
    return this.allowStatistics;
  }

  public void setAllowStatistics(Boolean allowStatistics) {
    this.allowStatistics = allowStatistics;
  }

  public String getInferenceMode() {
    return this.inferenceMode;
  }

  public void setInferenceMode(String inferenceMode) {
    this.inferenceMode = inferenceMode;
  }

  public int getMaxCandidateNum() {
    return this.maxCandidateNum;
  }

  public void setMaxCandidateNum(int maxCandidateNum) {
    this.maxCandidateNum = maxCandidateNum;
  }

  public String getPluginPublisher() {
    return this.pluginPublisher;
  }

  public void setPluginPublisher(String pluginPublisher) {
    this.pluginPublisher = pluginPublisher;
  }

  public String getPluginName() {
    return this.pluginName;
  }

  public void setPluginName(String pluginName) {
    this.pluginName = pluginName;
  }

  public Integer getPort() {
    return port;
  }

  public void setPort(Integer port) {
    this.port = port;
  }

  public String getGitRepo() {
    return gitRepo;
  }

  public void setGitRepo(String gitRepo) {
    this.gitRepo = gitRepo;
  }

  public String getGitBranch() {
    return gitBranch;
  }

  public void setGitBranch(String gitBranch) {
    this.gitBranch = gitBranch;
  }

  public String getGitCommit() {
    return gitCommit;
  }

  public void setGitCommit(String gitCommit) {
    this.gitCommit = gitCommit;
  }

  @Override
  public String getCommandName() {
    return "tylm/sendLingmaMessage";
  }

  @Override
  public Class<Boolean> getResponseType() {
    return Boolean.class;
  }
}

package com.xhs.codewiz.scheme.content;

import java.util.*;
/**
 * 删除 Action Provider
 */
public class DeleteActionProvider {
    private String schemaProtocol = "content.delete.actionprovider";
    private DeleteActionProviderParams params;

    public DeleteActionProviderParams getParams() {
        return params;
    }
    public void setParams(DeleteActionProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class DeleteActionProviderParams {
        /** 要删除的 Action Provider ID */
        private String id;
    
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

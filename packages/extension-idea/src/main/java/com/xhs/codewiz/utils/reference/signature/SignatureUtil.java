package com.xhs.codewiz.utils.reference.signature;

import com.intellij.psi.PsiElement;
import com.xhs.codewiz.utils.reference.LazyLoader;
import java.util.Map;

public class SignatureUtil {
  private static final Map<String, LazyLoader<SignatureGenerator>> SIGNATURE_GENERATORS = Map.of("default", new LazyLoader<SignatureGenerator>(() -> new DefaultSignatureGenerator()), "java", new LazyLoader<SignatureGenerator>(() -> new JavaSignatureGenerator()));

  public static String generateSignature(PsiElement element) {
    String languageId = element.getLanguage().getID().toLowerCase();
    SignatureGenerator signatureGenerator = SIGNATURE_GENERATORS.getOrDefault(languageId, SIGNATURE_GENERATORS.get("default")).get();
    return signatureGenerator.generateSignature(element);
  }
}
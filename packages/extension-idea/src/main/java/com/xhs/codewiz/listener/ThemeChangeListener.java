package com.xhs.codewiz.listener;

import javax.swing.UIManager;

import org.jetbrains.annotations.NotNull;

import com.intellij.ide.ui.LafManager;
import com.intellij.ide.ui.LafManagerListener;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.util.ui.UIUtil;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * 监听 IntelliJ IDEA 主题变化事件
 * 当用户切换主题（如从 Light 切换到 Darcula）时，此监听器会被触发
 * 
 * <AUTHOR> Team
 */
public class ThemeChangeListener implements LafManagerListener {
    
    private static final Logger LOG = Logger.getInstance(ThemeChangeListener.class);
    
    /**
     * 当主题即将发生变化时调用
     * 
     * @param source 事件源，通常是 LafManager 实例
     */
    @Override
    public void lookAndFeelChanged(@NotNull LafManager source) {
        try {
            LoggerUtil.INSTANCE.logInfo(LOG, "处理主题变化");
            // 获取当前主题信息
            UIManager.LookAndFeelInfo currentLaf = source.getCurrentLookAndFeel();

            String themeId = currentLaf.getName();

            LoggerUtil.INSTANCE.logInfo(LOG, "主题已切换到: " + themeId + " (是否为暗色主题: " + UIUtil.isUnderDarcula() + ")");
            
            // 通知所有打开的项目主题发生了变化
            notifyProjectsOfThemeChange(themeId, UIUtil.isUnderDarcula());
            
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logError(LOG, "处理主题变化事件时发生错误", e);
        }
    }
    
    /**
     * 通知所有打开的项目主题发生了变化
     * 
     * @param themeId 主题Id
     * @param isDarkTheme 是否为暗色主题
     */
    private void notifyProjectsOfThemeChange(String themeId, boolean isDarkTheme) {
        Project[] openProjects = ProjectManager.getInstance().getOpenProjects();
        for (Project project : openProjects) {
            if (!project.isDisposed()) {
                // 可以在这里向项目级别的组件发送主题变化通知
                // 例如：通过消息总线发送主题变化事件
                notifyProjectOfThemeChange(project, themeId, isDarkTheme);
            }
        }
    }
    
    /**
     * 通知单个项目主题发生了变化
     * 
     * @param project 项目实例
     * @param themeName 主题名称
     */
    private void notifyProjectOfThemeChange(Project project, String themeName, boolean isDark) {
        LoggerUtil.INSTANCE.logInfo(LOG, "通知项目 " + project.getName() + " 主题已变化 start, theme" + themeName );
        LSPManager.getInstance().notifyProjectOfThemeChange(project, themeName, isDark);
        
        LoggerUtil.INSTANCE.logInfo(LOG, "通知项目 " + project.getName() + " 主题已变化 end, theme" + themeName);
    }
}

package com.xhs.codewiz.listener;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManagerEvent;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.factory.editor.TopicEditorVirtualFile;
import com.xhs.codewiz.factory.editor.TopicFileEditor;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * Topic Editor 焦点监听器
 * 专门监听 TopicFileEditor 的焦点移入和移出事件
 *
 * <AUTHOR>
 * @date 2025/1/27 14:33
 */
public class TopicEditorFocusListener implements FileEditorManagerListener {

    private static final Logger LOG = Logger.getInstance(TopicEditorFocusListener.class);
    private final Project project;

    public TopicEditorFocusListener(@NotNull Project project) {
        this.project = project;
    }

    private static TopicEditorFocusCallback focusCallback;

    /**
     * 设置焦点事件回调
     *
     * @param callback 回调处理器
     */
    public static void setFocusCallback(TopicEditorFocusCallback callback) {
        focusCallback = callback;
    }

    @Override
    public void selectionChanged(@NotNull FileEditorManagerEvent event) {
        handleFocusChange(event);
    }

    /**
     * 处理焦点变化事件
     */
    private void handleFocusChange(@NotNull FileEditorManagerEvent event) {
        // 处理失去焦点的编辑器
        handleEditorBlur(event.getOldEditor(), event.getOldFile());

        // 处理获得焦点的编辑器
        handleEditorFocus(event.getNewEditor(), event.getNewFile());
    }

    /**
     * 处理编辑器获得焦点
     */
    private void handleEditorFocus(FileEditor newEditor, VirtualFile newFile) {
        if (newEditor instanceof TopicFileEditor && newFile instanceof TopicEditorVirtualFile) {
            TopicFileEditor topicEditor = (TopicFileEditor) newEditor;
            TopicEditorVirtualFile virtualFile = (TopicEditorVirtualFile) newFile;

            LoggerUtil.INSTANCE.logInfo(LOG,
                    "Topic Editor 获得焦点: " + virtualFile.getName() +
                            " [Project: " + project.getName() +
                            ", Channel: " + virtualFile.getRemoteChannel() +
                            ", Provider: " + virtualFile.getWebviewId() + "]");

            // 调用回调
            if (focusCallback != null) {
                try {
                    focusCallback.onTopicEditorFocused(project, topicEditor, virtualFile);
                } catch (Exception e) {
                    LoggerUtil.INSTANCE.logError(LOG, "Topic Editor 焦点回调处理出错", e);
                }
            }
        }
    }

    /**
     * 处理编辑器失去焦点
     */
    private void handleEditorBlur(FileEditor oldEditor, VirtualFile oldFile) {
        if (oldEditor instanceof TopicFileEditor && oldFile instanceof TopicEditorVirtualFile) {
            TopicFileEditor topicEditor = (TopicFileEditor) oldEditor;
            TopicEditorVirtualFile virtualFile = (TopicEditorVirtualFile) oldFile;

            LoggerUtil.INSTANCE.logInfo(LOG,
                    "Topic Editor 失去焦点: " + virtualFile.getName() +
                            " [Project: " + project.getName() +
                            ", Channel: " + virtualFile.getRemoteChannel() +
                            ", Provider: " + virtualFile.getWebviewId() + "]");

            // 调用回调
            if (focusCallback != null) {
                try {
                    focusCallback.onTopicEditorBlurred(project, topicEditor, virtualFile);
                } catch (Exception e) {
                    LoggerUtil.INSTANCE.logError(LOG, "Topic Editor 失焦回调处理出错", e);
                }
            }
        }
    }

    /**
     * 检查当前是否有 Topic Editor 处于焦点状态
     *
     * @return 如果有 Topic Editor 获得焦点则返回 true
     */
    public boolean hasTopicEditorFocused() {
        // 可以通过 FileEditorManager 获取当前选中的编辑器来判断
        try {
            FileEditor selectedEditor = com.intellij.openapi.fileEditor.FileEditorManager
                    .getInstance(project).getSelectedEditor();
            return selectedEditor instanceof TopicFileEditor;
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "检查 Topic Editor 焦点状态时出错", e);
            return false;
        }
    }

    /**
     * 获取当前焦点的 Topic Editor（如果有的话）
     *
     * @return 当前焦点的 TopicFileEditor 实例，如果没有则返回 null
     */
    public TopicFileEditor getCurrentFocusedTopicEditor() {
        try {
            FileEditor selectedEditor = com.intellij.openapi.fileEditor.FileEditorManager
                    .getInstance(project).getSelectedEditor();
            if (selectedEditor instanceof TopicFileEditor) {
                return (TopicFileEditor) selectedEditor;
            }
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "获取当前焦点 Topic Editor 时出错", e);
        }
        return null;
    }
}

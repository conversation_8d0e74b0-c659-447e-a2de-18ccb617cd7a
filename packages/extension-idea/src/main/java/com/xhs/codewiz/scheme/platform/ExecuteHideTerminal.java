package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 隐藏终端
 */
public class ExecuteHideTerminal {
    private String schemaProtocol = "platform.execute.hideterminal";
    /** 终端 ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import org.jetbrains.annotations.NotNull;

public final class NotifyAcceptedCommand implements JsonRpcCommand<String> {
    @SerializedName("sessionId")
    private  @NotNull String sessionId;
    @SerializedName("uuid")
    private  @NotNull String uuid;
    @SerializedName("acceptedLength")
    private int acceptedLength;
    private String applyStrategy;
    private int start;
    private int end;
    private long timestamp;
    private String beforeContent;
    private String afterContent;
    private String acceptContent;
    private int startLine;
    private int lineCount;

    public @NotNull String getCommandName() {
        return "notifyAccepted";
    }

    public @NotNull Class<String> getResponseType() {
        return String.class;
    }

    public @NotNull String getUuid() {
        return this.uuid;
    }

    public @NotNull String getSessionId() {
        return this.sessionId;
    }
    public @NotNull String getApplyStrategy() {
        return this.applyStrategy;
    }
    public int getStart() {
        return this.start;
    }
    public int getEnd() {
        return this.end;
    }
    public long getTimestamp() {
        return this.timestamp;
    }

    public void setSessionId(@NotNull String sessionId) {
        this.sessionId = sessionId;
    }

    public void setUuid(@NotNull String uuid) {
        this.uuid = uuid;
    }

    public int getAcceptedLength() {
        return acceptedLength;
    }

    public void setAcceptedLength(int acceptedLength) {
        this.acceptedLength = acceptedLength;
    }

    public void setApplyStrategy(String applyStrategy) {
        this.applyStrategy = applyStrategy;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public void setEnd(int end) {
        this.end = end;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getBeforeContent() {
        return beforeContent;
    }

    public void setBeforeContent(String beforeContent) {
        this.beforeContent = beforeContent;
    }

    public String getAfterContent() {
        return afterContent;
    }

    public void setAfterContent(String afterContent) {
        this.afterContent = afterContent;
    }

    public String getAcceptContent() {
        return acceptContent;
    }

    public void setAcceptContent(String acceptContent) {
        this.acceptContent = acceptContent;
    }

    public int getStartLine() {
        return startLine;
    }

    public void setStartLine(int startLine) {
        this.startLine = startLine;
    }

    public int getLineCount() {
        return lineCount;
    }

    public void setLineCount(int lineCount) {
        this.lineCount = lineCount;
    }

    public NotifyAcceptedCommand(@NotNull String sessionId, @NotNull String uuid, int acceptedLength, @NotNull String applyStrategy, int start) {
        this.sessionId = sessionId;
        this.uuid = uuid;
        this.acceptedLength = acceptedLength;
        this.applyStrategy = applyStrategy;
        this.start = start;
        this.end = start + acceptedLength;
        this.timestamp = System.currentTimeMillis();
    }
}

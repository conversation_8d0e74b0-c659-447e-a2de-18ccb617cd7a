package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.CommentCollection;
/**
 * 添加一个 Comments 合集
 */
public class CreateCommentCollection {
    private String schemaProtocol = "content.create.commentcollection";
    private CreateCommentCollectionParams params;

    public CreateCommentCollectionParams getParams() {
        return params;
    }
    public void setParams(CreateCommentCollectionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateCommentCollectionParams {
        /** Comment Provider ID，表示该集合属于哪个 Comment Provider */
        private String provider;
        private CommentCollection collection;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public CommentCollection getCollection() {
            return collection;
        }
        public void setCollection(CommentCollection collection) {
            this.collection = collection;
        }
    }
}

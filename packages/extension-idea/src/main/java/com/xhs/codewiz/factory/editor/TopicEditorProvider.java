package com.xhs.codewiz.factory.editor;

import java.util.HashMap;
import java.util.Map;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorPolicy;
import com.intellij.openapi.fileEditor.FileEditorProvider;
import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * <AUTHOR>
 * @date 2025/8/4 11:56
 */
public class TopicEditorProvider implements FileEditorProvider, DumbAware {

    private static final Logger log = Logger.getInstance(TopicEditorProvider.class);

    private static final Map<String, TopicFileEditor> editorMap = new HashMap<>();

    @Override
    public boolean accept(@NotNull Project project, @NotNull VirtualFile file) {
        return file instanceof TopicEditorVirtualFile;
    }

    @Override
    public @NotNull FileEditor createEditor(@NotNull Project project, @NotNull VirtualFile file) {
        LoggerUtil.INSTANCE.logInfo(log, "createEditor project: " + project.getName() + " file: " + file.getName());
        // if (editorMap.containsKey(project.getName())) {
        //     LoggerUtil.INSTANCE.logInfo(log, "editorMap contains project: " + project.getName());
        //     TopicFileEditor editor = editorMap.get(project.getName());
        //     editor.setProject(project);
        //     editor.setVirtualFile((TopicEditorVirtualFile) file);
        //     editor.loadUrl(((TopicEditorVirtualFile) file).getWebviewBuilder().getWebview());
        //     return editor;
        // } else {
        //     LoggerUtil.INSTANCE.logInfo(log, "editorMap not contains project: " + project.getName());
        //     TopicFileEditor editor = new TopicFileEditor(project, (TopicEditorVirtualFile) file);
        //     editorMap.put(project.getName(), editor);
        //     return editor;
        // }
        return new TopicFileEditor(project, (TopicEditorVirtualFile) file);
    }

    @Override
    public @NotNull String getEditorTypeId() {
        return "topic-editor";
    }

    @Override
    public @NotNull FileEditorPolicy getPolicy() {
        return FileEditorPolicy.HIDE_DEFAULT_EDITOR;
    }
}

package com.xhs.codewiz.utils.reference;

import java.util.function.Supplier;

public class LazyLoader<T> {
  private final Supplier<T> supplier;
  private volatile T instance;

  public LazyLoader(Supplier<T> supplier) {
    this.supplier = supplier;
  }

  /*
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  public T get() {
    if (this.instance == null) {
      LazyLoader lazyLoader = this;
      synchronized (lazyLoader) {
        if (this.instance == null) {
          this.instance = this.supplier.get();
        }
      }
    }
    return this.instance;
  }
}
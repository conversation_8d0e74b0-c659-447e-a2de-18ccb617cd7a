package com.xhs.codewiz.client;

import com.xhs.codewiz.client.model.ChannelCommonNotification;
import com.xhs.codewiz.client.model.ChannelCommonRequest;
import java.util.concurrent.CompletableFuture;
import org.eclipse.lsp4j.jsonrpc.services.JsonNotification;
import org.eclipse.lsp4j.jsonrpc.services.JsonRequest;

public interface RcsLanguageServer {

  /**
   * 通用channel内request请求
   */
  @JsonRequest("rcs/channel/request")
  CompletableFuture<Object> channelRequest(ChannelCommonRequest request);


  /**
   * 通用channel内notification请求
   */
  @JsonNotification("rcs/channel/notification")
  void channelNotification(ChannelCommonNotification request);
}
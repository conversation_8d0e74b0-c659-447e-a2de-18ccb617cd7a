package com.xhs.codewiz.actions.problem;

import com.intellij.codeInsight.daemon.impl.DaemonCodeAnalyzerImpl;
import com.intellij.codeInsight.daemon.impl.HighlightInfo;
import com.intellij.codeInsight.intention.IntentionManager;
import com.intellij.codeInsight.intention.impl.BaseIntentionAction;
import com.intellij.codeInspection.util.IntentionFamilyName;
import com.intellij.lang.annotation.HighlightSeverity;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Iconable;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.util.IncorrectOperationException;
import com.xhs.codewiz.scheme.content.CreateActionProvider;
import com.xhs.codewiz.type.content.ActionItem;
import com.xhs.codewiz.utils.IconsUtil;
import java.util.List;
import java.util.Map;
import javax.swing.Icon;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
public class CodeProblemsIntentionAction
    extends BaseIntentionAction
    implements Iconable {
  private static Logger log = Logger.getInstance(CodeProblemsIntentionAction.class);

  @NotNull
  public String getText() {
    return "Rcs问题分析";
  }

  @NotNull
  @IntentionFamilyName
  public String getFamilyName() {
    return "CodeProblemsIntentionAction";
  }

  public boolean isAvailable(@NotNull Project project, Editor editor, PsiFile psiFile) {
    int offset = editor.getCaretModel().getOffset();
    PsiElement elementAtCaret = psiFile.findElementAt(offset);
    boolean isAvailable = this.hasErrorAt(project, elementAtCaret, editor);
    if (isAvailable) {
      //需要展示小灯泡，此时拉取远端数据
      ReadActionUtil.queryActionItem(project, editor, psiFile);
      if (ReadActionUtil.actionItems.isEmpty()) {
        return false;
      }
      //开始展示，创建ActionItem
      for (Map.Entry<CreateActionProvider, List<ActionItem>> entry : ReadActionUtil.actionItems.entrySet()) {
        for (ActionItem actionItem : entry.getValue()) {
          DynamicCodeProblemsIntentionAction action = new DynamicCodeProblemsIntentionAction(actionItem, entry.getKey());
          IntentionManager.getInstance().addAction(action);
          ReadActionUtil.showActions.add(action);
        }
      }
    }
    //当前的作为父类，默认隐藏
    return false;
  }

  public void invoke(@NotNull Project project, Editor editor, PsiFile psiFile) throws IncorrectOperationException {
    //现在父类这里不做处理
  }
  public void destroy() {
    if (CollectionUtils.isNotEmpty(ReadActionUtil.showActions)) {
      for (DynamicCodeProblemsIntentionAction action : ReadActionUtil.showActions) {
        IntentionManager.getInstance().unregisterIntention(action);
      }
      ReadActionUtil.showActions.clear();
    }
    ReadActionUtil.actionItems.clear();
  }

  public boolean hasErrorAt(Project project, PsiElement element, Editor editor) {
    TextRange range = ReadActionUtil.getCodeRange(editor);
    List<HighlightInfo> highlightInfos = DaemonCodeAnalyzerImpl
        .getHighlights(editor.getDocument(), HighlightSeverity.WEAK_WARNING, project);
    if (CollectionUtils.isEmpty(highlightInfos)) {
      return false;
    }
    for (HighlightInfo info : highlightInfos) {
      int infoStartOffset = info.getStartOffset();
      int infoEndOffset = info.getEndOffset();
      if (!range.intersectsStrict(infoStartOffset, infoEndOffset )) {
        continue;
      }
      return true;
    }
    return false;
  }



  public Icon getIcon(int i) {
    return IconsUtil.CODEWIZ;
  }


}
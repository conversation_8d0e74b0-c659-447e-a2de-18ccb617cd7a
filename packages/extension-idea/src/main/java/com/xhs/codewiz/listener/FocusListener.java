package com.xhs.codewiz.listener;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.application.ApplicationActivationListener;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.wm.IdeFrame;

/**
 * <AUTHOR>
 * @date 2025/8/6 21:20
 */
public class FocusListener implements ApplicationActivationListener {

    public FocusListener() {
        // 订阅应用程序激活事件
        ApplicationManager.getApplication().getMessageBus()
                .connect()
                .subscribe(ApplicationActivationListener.TOPIC, this);
    }

    @Override
    public void applicationActivated(@NotNull IdeFrame ideFrame) {
        System.out.println("IDEA获得焦点: " +
                (ideFrame.getProject() != null ? ideFrame.getProject().getName() : "Unknown"));
        // 处理获得焦点的逻辑
        handleIdeaFocused(ideFrame);
    }

    @Override
    public void applicationDeactivated(@NotNull IdeFrame ideFrame) {
        System.out.println("IDEA失去焦点: " +
                (ideFrame.getProject() != null ? ideFrame.getProject().getName() : "Unknown"));
        // 处理失去焦点的逻辑
        handleIdeaLostFocus(ideFrame);
    }

    private void handleIdeaFocused(IdeFrame ideFrame) {
    }

    private void handleIdeaLostFocus(IdeFrame ideFrame) {
    }
}

package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.global.ZeroBasedIndex;
import com.xhs.codewiz.type.file.Uri;
/**
 * 创建工作区文件夹
 */
public class CreateWorkspaceFolder {
    private String schemaProtocol = "workspace.create.workspacefolder";
    private CreateWorkspaceFolderParams params;

    public CreateWorkspaceFolderParams getParams() {
        return params;
    }
    public void setParams(CreateWorkspaceFolderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateWorkspaceFolderParams {
        /** 工作区文件夹名称 */
        private String name;
        /** 工作区文件夹的索引 */
        private Integer index;
        private String uri;
        /** 工作区文件夹是否被信任 */
        private Boolean isTrusted;
    
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public Integer getIndex() {
            return index;
        }
        public void setIndex(Integer index) {
            this.index = index;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
        public Boolean getIsTrusted() {
            return isTrusted;
        }
        public void setIsTrusted(Boolean isTrusted) {
            this.isTrusted = isTrusted;
        }
    }
}

package com.xhs.codewiz.editor.request;


import com.intellij.openapi.editor.Document;
import com.intellij.openapi.util.TextRange;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import com.xhs.codewiz.utils.CodewizStringUtil;
import org.jetbrains.annotations.NotNull;

public final class LineInfo {
    private final int lineCount;
    private final int lineNumber;
    private final int lineStartOffset;
    private final int columnOffset;
    private final @NotNull String line;
    private final int nextLineIndent;

    public static @NotNull LineInfo create(@NotNull Document document, int offset) {
        int line = document.getLineNumber(offset);
        TextRange lineRange = TextRange.create(document.getLineStartOffset(line), document.getLineEndOffset(line));
        return new LineInfo(document.getLineCount(), line, lineRange.getStartOffset(), offset - lineRange.getStartOffset(), document.getText(lineRange), calculateNextLineIndent(document, offset));
    }

    public @NotNull String getLinePrefix() {
        return this.line.substring(0, this.columnOffset);
    }

    public @NotNull String getLineSuffix() {
        return this.line.substring(this.columnOffset);
    }

    public boolean isBlankLine() {
        return this.line.isBlank();
    }

    public @NotNull String getWhitespaceBeforeCursor() {
        return CodewizStringUtil.trailingWhitespace(this.getLinePrefix());
    }

    public int getLineEndOffset() {
        return this.getLineStartOffset() + this.line.length();
    }

    private static int calculateNextLineIndent(@NotNull Document document, int offset) {
        int maxLines = document.getLineCount();

        for(int line = document.getLineNumber(offset) + 1; line < maxLines; ++line) {
            int start = document.getLineStartOffset(line);
            int end = document.getLineEndOffset(line);
            if (start != end) {
                String lineContent = document.getText(TextRange.create(start, end));
                if (!lineContent.isBlank()) {
                    return CodeWizEditorUtil.whitespacePrefixLength(lineContent);
                }
            }
        }

        return -1;
    }
    
    public LineInfo(int lineCount, int lineNumber, int lineStartOffset, int columnOffset, @NotNull String line, int nextLineIndent) {
        this.lineCount = lineCount;
        this.lineNumber = lineNumber;
        this.lineStartOffset = lineStartOffset;
        this.columnOffset = columnOffset;
        this.line = line;
        this.nextLineIndent = nextLineIndent;
    }

    public int getLineCount() {
        return this.lineCount;
    }

    public int getLineNumber() {
        return this.lineNumber;
    }

    public int getLineStartOffset() {
        return this.lineStartOffset;
    }
    
    public int getColumnOffset() {
        return this.columnOffset;
    }
    
    public @NotNull String getLine() {
        return this.line;
    }

    public int getNextLineIndent() {
        return this.nextLineIndent;
    }
}


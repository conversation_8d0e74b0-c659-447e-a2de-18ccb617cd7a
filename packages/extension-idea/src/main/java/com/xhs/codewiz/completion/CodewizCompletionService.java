package com.xhs.codewiz.completion;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.editor.Editor;
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread;
import com.intellij.util.concurrency.annotations.RequiresEdt;
import com.xhs.codewiz.completion.request.CodewizInlayList;
import com.xhs.codewiz.completion.enums.CompletionType;
import com.xhs.codewiz.completion.request.CompletionExtraContext;
import com.xhs.codewiz.editor.request.EditorRequest;
import com.xhs.codewiz.lang.agent.commands.NotifyAcceptedCommand;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import java.util.List;
import java.util.concurrent.Flow;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface CodewizCompletionService {
    static CodewizCompletionService getInstance() {
        return ApplicationManager.getApplication().getService(CodewizCompletionService.class);
    }

    boolean isAvailable(@NotNull Editor var1);

    @Nullable EditorRequest createRequest(@NotNull Editor var1, int var2, @NotNull CompletionType var3, EditEventTriggerKind triggerKind);

    @RequiresBackgroundThread
    boolean fetchCompletions(Editor editor, @NotNull EditorRequest var1, @Nullable Integer var2, boolean var3, @NotNull Flow.Subscriber<List<CodewizInlayList>> var4);

    public boolean fetchCompletionsFromLs(CompletionExtraContext extraContext, @NotNull EditorRequest request, @Nullable Integer maxCompletions, boolean cycling, @NotNull Flow.Subscriber<List<CodewizInlayList>> subscriber);

    @RequiresEdt
    @Nullable List<CodewizInlayList> fetchCachedCompletions(@NotNull EditorRequest var1);
    void reset();

    @RequiresEdt
    boolean isSupportingOnDemandCycling(@NotNull Editor var1);

    void sendShownTelemetry(@NotNull CodewizCompletion var1, String sessionId, int start, int end, int shownLineCount, String shownContent, String filePath);

    void sendAcceptedTelemetry(NotifyAcceptedCommand command);

    void sendRejectedTelemetry(@NotNull List<CodewizCompletion> var1, String sessionId, String eventName);

}


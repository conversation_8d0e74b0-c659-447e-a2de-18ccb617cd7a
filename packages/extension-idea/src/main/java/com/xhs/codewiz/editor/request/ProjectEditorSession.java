package com.xhs.codewiz.editor.request;

import java.util.UUID;

/**
 * Author: liukunpeng Date: 2025-04-23 Description:
 */
public class ProjectEditorSession {

  private String sessionId;

  private long lastActiveTime; // 毫秒时间戳

  // session 过期时限，30分钟
  private final static long EXPIRE_INTERVAL = 30 * 60 * 1000;

  public ProjectEditorSession() {
    renewSession();
  }

  public synchronized String getSessionId() {
    long now = System.currentTimeMillis();
    if (now - lastActiveTime > EXPIRE_INTERVAL) {
      // session 过期，重新生成
      renewSession();
    } else {
      // session 未过期，更新 lastActiveTime
      lastActiveTime = now;
    }
    return sessionId;
  }

  private synchronized void renewSession() {
    this.sessionId = UUID.randomUUID().toString();
    this.lastActiveTime = System.currentTimeMillis();
  }
}

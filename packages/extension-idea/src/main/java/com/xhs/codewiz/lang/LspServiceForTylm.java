package com.xhs.codewiz.lang;

import com.intellij.ide.plugins.IdeaPluginDescriptor;
import com.intellij.ide.plugins.PluginManagerCore;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Pair;
import com.intellij.openapi.util.SystemInfo;
import com.xhs.codewiz.constant.PluginCommonConstant;
import com.xhs.codewiz.lang.agent.CodeWizAgentProcessService;
import com.xhs.codewiz.lang.entity.CloseProjectConnectParams;
import com.xhs.codewiz.lang.entity.FileSaveParams;
import com.xhs.codewiz.lang.entity.InitializeParamsWithConfig;
import com.xhs.codewiz.lang.entity.ProjectConnectParams;
import com.xhs.codewiz.lang.entity.RenameFilesParams;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes.XhsUserInfo;
import com.xhs.codewiz.utils.FileUtil;
import com.xhs.codewiz.utils.GitHandlerUtil;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LocalStorageUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.PluginUtil;
import com.xhs.codewiz.utils.ProcessUtil;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.lsp4j.WorkspaceFolder;
import org.jetbrains.concurrency.CancellablePromise;
import org.jetbrains.concurrency.Promise.State;

/**
 * Author: liukunpeng Date: 2025-03-17 Description:
 * 这个是用于通义灵码chat/服务connect功能的交互类
 */
public class LspServiceForTylm implements Disposable {
  private static final Logger LOG = Logger.getInstance(LspServiceForTylm.class);
  public static Map<String, Project> chatSessionProMap = new ConcurrentHashMap<>();
  private static final AtomicBoolean needLogin = new AtomicBoolean(true);
  public static final String COSY_INFO_FILE_NAME = ".info";
  /**
   * 打开项目的时候，需要在codewiz-lsp启动一个项目级别的Lingma客户端
   * 客户端建好后，需要同步项目路径到Lingma
   * @param project
   * @return
   */
  public static boolean initializeParams(Project project)
      throws InterruptedException {
    int time = 10;
    while(time-- > 0) {
      if (CodeWizAgentProcessService.getInstance().isRunning()) {
        break;
      }
      time++;
      Thread.sleep(3000);
    }
    if (!CodeWizAgentProcessService.getInstance().isRunning()) {
      return false;
    }
    //初始化
    InitializeParamsWithConfig params = initProjectParams(project);
    CancellablePromise<Boolean> promise = CodeWizAgentProcessService.getInstance().executeCommand(params);
    if (promise.getState() == State.REJECTED) {
      LoggerUtil.INSTANCE.logWarn(LOG, "LspServiceForTylm initializeParams REJECTED");
      return false;
    } try {
      boolean res = promise.get(5, TimeUnit.SECONDS);
      /*if (res) {
        doLogin(project);
      }*/
      return res;
    } catch (Exception e) {
      //这个后边会有重启机制，不影响使用
      LoggerUtil.INSTANCE.logWarn(LOG, "LspServiceForTylm initializeParams  getRes err, msg = ", e);
      return false;
    }
  }
  public static boolean checkProjectConnectStatus(Project project, boolean autoStart) {
    ProjectConnectParams status = new ProjectConnectParams();
    status.setProjectUri(FileUtil.getProjectBaseDir(project));
    status.setAutoStart(autoStart);
    CancellablePromise<Boolean> promise = CodeWizAgentProcessService.getInstance().executeCommand(status);
    if (promise.getState() == State.REJECTED) {
      LoggerUtil.INSTANCE.logWarn(LOG, "LspServiceForTylm checkProjectConnectStatus REJECTED");
      return false;
    } try {
      return promise.get(1, TimeUnit.SECONDS);
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(LOG, "LspServiceForTylm checkProjectConnectStatus  getRes err, msg = ", e);
      return false;
    }
  }
  public static boolean closeProjectConnect(CloseProjectConnectParams status) {
    CancellablePromise<Boolean> promise = CodeWizAgentProcessService.getInstance().executeCommand(status);
    if (promise.getState() == State.REJECTED) {
      LoggerUtil.INSTANCE.logWarn(LOG, "LspServiceForTylm closeProjectConnect REJECTED");
      return true;
    } try {
      return promise.get(1, TimeUnit.SECONDS);
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(LOG, "LspServiceForTylm closeProjectConnect  getRes err, msg = ", e);
      return true;
    }
  }

  /**
   * 这个单独列在这里，是因为之前的codewiz-ls没有支持这个命令
   * @param params
   */
  public static void didRenameFiles(RenameFilesParams params) {
    CodeWizAgentProcessService.getInstance().executeNotification(params);
  }
  /**
   * 这个单独列在这里，是因为之前的codewiz-ls没有支持这个命令
   * @param params
   */
  public static void didSave(FileSaveParams params) {
    CodeWizAgentProcessService.getInstance().executeNotification(params);
  }

  private static InitializeParamsWithConfig initProjectParams(Project project) {
    ArrayList<WorkspaceFolder> workspaceFolders = new ArrayList<WorkspaceFolder>();
    WorkspaceFolder folder = new WorkspaceFolder();
    folder.setName(project.getName());
    folder.setUri(FileUtil.getProjectBaseDir(project));
    workspaceFolders.add(folder);
    InitializeParamsWithConfig params = new InitializeParamsWithConfig();
    params.setWorkspaceFolders(workspaceFolders);
    addConfigToInitializeParams(params, project);
    return params;
  }
  private static void addConfigToInitializeParams(InitializeParamsWithConfig params, Project project) {
    params.setIdeSeries("JetBrains");
    params.setIdePlatform(ApplicationInfo.getInstance().getVersionName());
    IdeaPluginDescriptor desc = PluginManagerCore.getPlugin(PluginUtil.CODEWIZ_ID);
    params.setPluginVersion(null == desc ? null : desc.getVersion());
    params.setIdeVersion(ApplicationInfo.getInstance().getFullVersion());
    params.setPluginName(PluginUtil.getPluginName());
    params.setAllowStatistics(false);
    params.setInferenceMode("auto");
    params.setMaxCandidateNum(3);

    params.setGitRepo(GitHandlerUtil.getGitUrl(project));
    params.setGitBranch(GitHandlerUtil.getGitBranch(project));
    params.setGitCommit(GitHandlerUtil.getLatestCommitId(project));
  }

  public static XhsUserInfo getUserLoginInfo() {
    String str = LocalStorageUtil.getProperty(PluginCommonConstant.LOGIN_TOKEN_TEMP_KEY);
    if (StringUtils.isEmpty(str)) {
      return null;
    }
    XhsUserInfoRes res = GsonUtil.fromJson(str, XhsUserInfoRes.class);
    if (null == res) {
      return null;
    }
    return res.getData();
  }
  public static Pair<Integer, Long> readCosyInfoFile(int maxRetryTimes) {
    File homeDir = getCosyHomeDir();
    if (homeDir == null) {
      return null;
    }
    File infoFile = new File(homeDir, COSY_INFO_FILE_NAME);
    Integer port = null;
    Long pid = null;
    boolean delay = false;
    int i = 0;
    while (i < maxRetryTimes) {
      Pair<Integer, Long> infoPair = checkInfoFile(infoFile);
      if (infoPair != null) {
        port = infoPair.first;
        pid = infoPair.second;
        break;
      }
      try {
        Thread.sleep(1000L);
      }
      catch (InterruptedException e) {
        LoggerUtil.INSTANCE.logWarn(LOG, "Thread sleep is interrupted when waiting for .info file");
      }
      LoggerUtil.INSTANCE.logInfo(LOG, String.format("Retry for fetching .info for %d times, %d times left", ++i, maxRetryTimes - i));
      if (delay || !SystemInfo.isWindows || i != maxRetryTimes) continue;
      delay = true;
      List<Long> cosyPidList = ProcessUtil.findLingmaPidList();
      if (!CollectionUtils.isNotEmpty(cosyPidList)) continue;
      String pidListStr = org.apache.commons.lang3.StringUtils.join(cosyPidList, (String)",");
      LoggerUtil.INSTANCE.logInfo(LOG, String.format("Found lingma pid list: %s, delay max retry times", pidListStr == null ? "null" : pidListStr));
      maxRetryTimes *= 3;
    }
    if (port != null && pid != null) {
      return new Pair(port, pid);
    }
    return findProcessAndPortByName();
  }
  public static File getCosyHomeDir() {
    File homeDir = Paths.get(getUserHome(), ".lingma-xhs").toFile();
    LoggerUtil.INSTANCE.logInfo(LOG, "cosy home dir " + homeDir);
    if (!homeDir.exists() && !homeDir.mkdirs()) {
      LoggerUtil.INSTANCE.logWarn(LOG, "fail to create directory " + homeDir);
      return null;
    }
    return homeDir;
  }
  private static String getUserHome() {
    String userHome = null;
    String osName = System.getProperty("os.name");
    if (StringUtils.isNotBlank(osName)) {
      osName = osName.toLowerCase();
      if (ProcessUtil.isWindowsPlatform()) {
        userHome = System.getenv("USERPROFILE");
      } else if (osName.contains("mac")) {
        userHome = System.getenv("HOME");
      } else if (osName.contains("nix") || osName.contains("nux")) {
        userHome = System.getenv("HOME");
      }
    }
    if (StringUtils.isBlank(userHome)) {
      return System.getProperty("user.home");
    }
    return userHome;
  }
  private static Pair<Integer, Long> checkInfoFile(File infoFile) {
    if (infoFile == null) {
      return null;
    }
    if (!infoFile.exists()) {
      LoggerUtil.INSTANCE.logInfo(LOG, ".info file not exist, wait 100ms and retry");
    } else {
      try {
        String rawText = FileUtils.readFileToString(infoFile, StandardCharsets.UTF_8);
        if (rawText == null || rawText.isEmpty()) {
          LoggerUtil.INSTANCE.logWarn(LOG,".info file is empty, check failed");
          return null;
        }
        String[] lines = rawText.split("\r\n|\n");
        if (lines.length != 2) {
          LoggerUtil.INSTANCE.logInfo(LOG,".info file is empty or has more or less than 2 lines:" + rawText);
          return null;
        }
        Long port = Long.parseLong(lines[0]);
        Long pid = Long.parseLong(lines[1]);
        LoggerUtil.INSTANCE.logInfo(LOG,"Read.info file get port:" + port + ", pid:" + pid);
        if (port == null || pid == null) {
          LoggerUtil.INSTANCE.logWarn(LOG,"Cannot get port and pid from.info file, check failed");
          return null;
        }
        List<Long> cosyPidList = ProcessUtil.findLingmaPidList();
        //校验pid是否还有效
        if (CollectionUtils.isEmpty(cosyPidList) || !cosyPidList.contains(pid)) {
          LoggerUtil.INSTANCE.logWarn(LOG,"Pid in .info file is not exist in process list, check failed");
          deleteInfoFile();
          return null;
        }
        return new Pair(port.intValue(), pid);
      }
      catch (IOException e) {
        LoggerUtil.INSTANCE.logWarn(LOG,"Parsing .info file encountered Exception", e);
      }
      catch (Throwable throwable) {
        LoggerUtil.INSTANCE.logWarn(LOG,"Check info file encountered Throwable", throwable);
      }
    }
    return null;
  }
  public static void deleteInfoFile() {
    File homeDir = getCosyHomeDir();
    if (homeDir == null) {
      return;
    }
    File infoFile = new File(homeDir, COSY_INFO_FILE_NAME);
    if (infoFile.exists()) {
      try {
        infoFile.delete();
        LoggerUtil.INSTANCE.logInfo(LOG, "Delete .info file success.");
      }
      catch (Exception deleteException) {
        LoggerUtil.INSTANCE.logWarn(LOG, "Delete .info file encountered exception", (Throwable)deleteException);
      }
    }
  }
  private static Pair<Integer, Long> findProcessAndPortByName() {
    LoggerUtil.INSTANCE.logInfo(LOG, "try find process and port by name");
    List<Long> cosyPidList = ProcessUtil.findLingmaPidList();
    String pidListStr = StringUtils.join(cosyPidList, ",");
    LoggerUtil.INSTANCE.logInfo(LOG, String.format("Found cosy pid list: %s", pidListStr == null ? "null" : pidListStr));
    if (CollectionUtils.isNotEmpty(cosyPidList)) {
      return new Pair(36510, cosyPidList.get(0));
    }
    if (ProcessUtil.isWindowsPlatform()) {
      return new Pair(36510, 0L);
    }
    return null;
  }
  @Override
  public void dispose() {

  }
}

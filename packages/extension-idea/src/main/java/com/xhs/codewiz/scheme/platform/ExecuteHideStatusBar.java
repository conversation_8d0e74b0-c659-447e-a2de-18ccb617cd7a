package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 隐藏状态栏按钮
 */
public class ExecuteHideStatusBar {
    private String schemaProtocol = "platform.execute.hidestatusbar";
    /** statusBar ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

package com.xhs.codewiz.scheme.global;

import java.util.*;
import com.xhs.codewiz.type.global.Scope;
/**
 * 创建、更新、删除全局状态
 */
public class UpdateState {
    private String schemaProtocol = "global.update.state";
    private UpdateStateParams params;

    public UpdateStateParams getParams() {
        return params;
    }
    public void setParams(UpdateStateParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateStateParams {
        /** 作用域，描述 records 中内容的可读范围。不存在时默认为全局 */
        private Scope scope; // optional
        /** 状态的值, 如果不提供则删除该键 */
        private Object value; // optional
        /** 状态的键 */
        private String key;
    
        public Scope getScope() {
            return scope;
        }
        public void setScope(Scope scope) {
            this.scope = scope;
        }
        public Object getValue() {
            return value;
        }
        public void setValue(Object value) {
            this.value = value;
        }
        public String getKey() {
            return key;
        }
        public void setKey(String key) {
            this.key = key;
        }
    }
}

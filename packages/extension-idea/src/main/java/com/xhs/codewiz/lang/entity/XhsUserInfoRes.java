package com.xhs.codewiz.lang.entity;

/**
 * Author: liukunpeng Date: 2025-02-25 Description:
 */
public class XhsUserInfoRes {
  private Integer code;
  private Boolean success;
  private String msg;
  private XhsUserInfo data;

  public Integer getCode() {
    return code;
  }

  public void setCode(Integer code) {
    this.code = code;
  }

  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public XhsUserInfo getData() {
    return data;
  }

  public void setData(XhsUserInfo data) {
    this.data = data;
  }

  public static class XhsUserInfo {
    private String name; //用户名
    private String userNameAlias; //用户薯名
    private String avatar; //头像地址
    private String thumbAvatar; //头像缩略图地址
    private String mobile; //手机号
    private String email; //邮箱
    private String emailAlias; //薯名邮箱
    private String ssoType; //类型
    private String accessToken; //token
    private String userId; //用户ID
    private String accountNo; //用户账号
    private String accountType; //账号类型
    private String subsystem; //账号类型

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public String getUserNameAlias() {
      return userNameAlias;
    }

    public void setUserNameAlias(String userNameAlias) {
      this.userNameAlias = userNameAlias;
    }

    public String getAvatar() {
      return avatar;
    }

    public void setAvatar(String avatar) {
      this.avatar = avatar;
    }

    public String getThumbAvatar() {
      return thumbAvatar;
    }

    public void setThumbAvatar(String thumbAvatar) {
      this.thumbAvatar = thumbAvatar;
    }

    public String getMobile() {
      return mobile;
    }

    public void setMobile(String mobile) {
      this.mobile = mobile;
    }

    public String getEmail() {
      return email;
    }

    public void setEmail(String email) {
      this.email = email;
    }

    public String getEmailAlias() {
      return emailAlias;
    }

    public void setEmailAlias(String emailAlias) {
      this.emailAlias = emailAlias;
    }

    public String getSsoType() {
      return ssoType;
    }

    public void setSsoType(String ssoType) {
      this.ssoType = ssoType;
    }

    public String getAccessToken() {
      return accessToken;
    }

    public void setAccessToken(String accessToken) {
      this.accessToken = accessToken;
    }

    public String getUserId() {
      return userId;
    }

    public void setUserId(String userId) {
      this.userId = userId;
    }

    public String getAccountNo() {
      return accountNo;
    }

    public void setAccountNo(String accountNo) {
      this.accountNo = accountNo;
    }

    public String getAccountType() {
      return accountType;
    }

    public void setAccountType(String accountType) {
      this.accountType = accountType;
    }

    public String getSubsystem() {
      return subsystem;
    }

    public void setSubsystem(String subsystem) {
      this.subsystem = subsystem;
    }
  }
}

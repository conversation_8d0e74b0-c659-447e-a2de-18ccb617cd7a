package com.xhs.codewiz.constant;

/**
 * Author: liukunpeng Date: 2025-07-15 Description:
 */
public class PluginCommonConstant {
  public static final Integer YES = 1;
  public static final Integer NO = 0;

  public static final String CHANNEL_PATH_SUFFIX = "default";

  public static final String REMOTE_GLOBAL_STATE_KEY = "globalState";

  /**
   * 以下为初始化注册的默认指令，目前仅接入了menus
   */
  public static final String DEFAULT_COMMAND_GROUP_MENU = "menus";
  public static final String DEFAULT_COMMAND_GROUP_VIEWS = "views";


  public static final String DIFF_PANEL_COMPARE_SCHEMA = "cline-diff:";
  public static final String DIFF_PANEL_COMMENT_SCHEMA = "git:";
  public static final String DIFF_PANEL_EMPTY_SCHEMA = "untitled:";

  public static final String REPORT_URL = "http://codewiz.devops.xiaohongshu.com";
  public static final String REPORT_LOG = "/complete/api/v1/logs/batch";
  public static final String REPORT_METRICS = "/complete/metrics/v1";

  public static final String LOGIN_TOKEN_TEMP_KEY = "LOGIN_TOKEN_TEMP"; //临时存储到idea开发工具本地的登陆信息key
}

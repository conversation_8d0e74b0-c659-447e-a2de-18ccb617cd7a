package com.xhs.codewiz.utils;

import com.intellij.openapi.util.Key;
import com.intellij.openapi.vfs.VirtualFile;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.annotations.TestOnly;

public final class FileSizeUtil {
    @TestOnly
    public static final Key<Boolean> KEY_TOO_LARGE = Key.create("copilot.fileTooLarge");
    public static final long MAX_FILE_SIZE = 1048576L;

    private FileSizeUtil() {
    }

    public static boolean isSupported(@Nullable VirtualFile file) {
        return file != null && file.getLength() <= 1048576L && !KEY_TOO_LARGE.isIn(file);
    }

    public static boolean isTooLarge(@NotNull VirtualFile file) {
        return file.getLength() > 1048576L || KEY_TOO_LARGE.isIn(file);
    }
}

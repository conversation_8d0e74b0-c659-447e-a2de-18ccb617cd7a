package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.CompletionItem;
/**
 * 获取联想项的详细信息
 */
public class ReadCompletionItemDetail {
    private String schemaProtocol = "workspace.read.completionitemdetail";
    private ReadCompletionItemDetailParams params;

    public ReadCompletionItemDetailParams getParams() {
        return params;
    }
    public void setParams(ReadCompletionItemDetailParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadCompletionItemDetailParams {
        private CompletionItem item;
        /** 联想提供者的 ID */
        private String provider;
    
        public CompletionItem getItem() {
            return item;
        }
        public void setItem(CompletionItem item) {
            this.item = item;
        }
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
    }
}

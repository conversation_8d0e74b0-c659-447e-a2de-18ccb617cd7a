package com.xhs.codewiz.type.workspace;

import java.util.*;

/**
 * Tab 标签类型
 */
public abstract class TabType {
    /** 类型标识 */
    public abstract String getType();

    /** Chat 窗口 */
    public static class TabTypeChat extends TabType {
        public static final String TYPE = "chat";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

    }
    /** Merge 窗口 */
    public static class TabTypeTextMerge extends TabType {
        public static final String TYPE = "textMerge";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        /** 结果文件 */
        private String result;

        /** 输入文件2 */
        private String input2;

        /** 输入文件1 */
        private String input1;

        /** 基准文件 */
        private String base;

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }

        public String getInput2() {
            return input2;
        }

        public void setInput2(String input2) {
            this.input2 = input2;
        }

        public String getInput1() {
            return input1;
        }

        public void setInput1(String input1) {
            this.input1 = input1;
        }

        public String getBase() {
            return base;
        }

        public void setBase(String base) {
            this.base = base;
        }

    }
    /** 基本doc窗口 */
    public static class TabTypeText extends TabType {
        public static final String TYPE = "text";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        private String uri;

        public String getUri() {
            return uri;
        }

        public void setUri(String uri) {
            this.uri = uri;
        }

    }
    /** Diff 窗口 */
    public static class TabTypeTextDiff extends TabType {
        public static final String TYPE = "textDiff";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        /** 原始文件 */
        private String original;

        /** 修改后的文件 */
        private String modified;

        public String getOriginal() {
            return original;
        }

        public void setOriginal(String original) {
            this.original = original;
        }

        public String getModified() {
            return modified;
        }

        public void setModified(String modified) {
            this.modified = modified;
        }

    }
    /** 自定义 tab */
    public static class TabTypeCustom extends TabType {
        public static final String TYPE = "custom";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        /** 自定义视图类型 */
        private String viewType;

        /** 自定义内容的 URI */
        private String uri;

        public String getViewType() {
            return viewType;
        }

        public void setViewType(String viewType) {
            this.viewType = viewType;
        }

        public String getUri() {
            return uri;
        }

        public void setUri(String uri) {
            this.uri = uri;
        }

    }
    /** Webview 窗口 */
    public static class TabTypeWebview extends TabType {
        public static final String TYPE = "webview";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        /** Webview 视图类型 */
        private String viewType;

        public String getViewType() {
            return viewType;
        }

        public void setViewType(String viewType) {
            this.viewType = viewType;
        }

    }
    /** Notebook 窗口 */
    public static class TabTypeNotebook extends TabType {
        public static final String TYPE = "notebook";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        /** Notebook 视图类型 */
        private String notebookType;

        /** Notebook 文件的 URI */
        private String uri;

        public String getNotebookType() {
            return notebookType;
        }

        public void setNotebookType(String notebookType) {
            this.notebookType = notebookType;
        }

        public String getUri() {
            return uri;
        }

        public void setUri(String uri) {
            this.uri = uri;
        }

    }
    /** notebook Diff 窗口 */
    public static class TabTypeNotebookDiff extends TabType {
        public static final String TYPE = "notebookDiff";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

        /** 原始 Notebook 文件 */
        private String original;

        /** Notebook 视图类型 */
        private String notebookType;

        /** 修改后的 Notebook 文件 */
        private String modified;

        public String getOriginal() {
            return original;
        }

        public void setOriginal(String original) {
            this.original = original;
        }

        public String getNotebookType() {
            return notebookType;
        }

        public void setNotebookType(String notebookType) {
            this.notebookType = notebookType;
        }

        public String getModified() {
            return modified;
        }

        public void setModified(String modified) {
            this.modified = modified;
        }

    }
    /** Terminal 窗口 */
    public static class TabTypeTerminal extends TabType {
        public static final String TYPE = "terminal";
        private String type = TYPE;
        @Override
        public String getType() { return TYPE; }

    }
}

package com.xhs.codewiz.lang.agent.rpc;

import org.jetbrains.annotations.NotNull;

public class JsonRpcErrorException extends RuntimeException {
    private final int requestId;

    public int getRequestId() {
        return this.requestId;
    }

    private final int code;
    private final String extraData;

    public int getCode() {
        return this.code;
    }

    public String getExtraData() {
        return this.extraData;
    }

    public JsonRpcErrorException(int requestId, @NotNull JsonRpcError error) {
        this(requestId, error.code, error.error, (error.getData() == null) ? null : error.getData().toString());
    }

    public JsonRpcErrorException(int requestId, int code, @NotNull String errorMessage, String extraData) {
        this(requestId, code, errorMessage, extraData, null);
    }

    public JsonRpcErrorException(@NotNull String message, @NotNull JsonRpcErrorException cause) {
        this(cause.requestId, cause.code, message, cause.extraData, cause);
    }


    private JsonRpcErrorException(int requestId, int code, @NotNull String errorMessage,  String extraData,  Throwable cause) {
        super(createMessage(requestId, code, errorMessage, extraData), cause);

        this.requestId = requestId;
        this.code = code;
        this.extraData = extraData;
    }

    private static String createMessage(int requestId, int code, @NotNull String errorMessage,  String extraData) {
        StringBuilder result = new StringBuilder();
        result.append(errorMessage);
        result.append(", request id: ").append(requestId);
        result.append(", error code: ").append(code);
        if (extraData != null) {
            result.append(", extra data: ").append(extraData);
        }
        return result.toString();
    }
}



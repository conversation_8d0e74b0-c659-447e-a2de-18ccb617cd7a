package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.SessionOptions;
/**
 * 读取 session。\n一种特殊情况：\n1. 沙箱向本地请求读取来自沙箱自身的 Session，支持 createIfNone；\n2. 但是本地无法生成沙箱内的 Session，需要向沙箱申请；\n3. 此时需要向沙箱申请读取 Sesion，并携带 CreateIfNone
 */
public class ReadSession {
    private String schemaProtocol = "platform.read.session";
    private ReadSessionParams params;

    public ReadSessionParams getParams() {
        return params;
    }
    public void setParams(ReadSessionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadSessionParams {
        /** Session Provider 的 ID */
        private String provider;
        private SessionOptions options; // optional
        private List<String> scopes; // optional
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public SessionOptions getOptions() {
            return options;
        }
        public void setOptions(SessionOptions options) {
            this.options = options;
        }
        public List<String> getScopes() {
            return scopes;
        }
        public void setScopes(List<String> scopes) {
            this.scopes = scopes;
        }
    }
}

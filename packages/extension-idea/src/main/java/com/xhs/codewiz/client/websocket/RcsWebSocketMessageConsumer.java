package com.xhs.codewiz.client.websocket;

import static org.eclipse.lsp4j.jsonrpc.json.MessageConstants.CONTENT_LENGTH_HEADER;
import static org.eclipse.lsp4j.jsonrpc.json.MessageConstants.CONTENT_TYPE_HEADER;
import static org.eclipse.lsp4j.jsonrpc.json.MessageConstants.CRLF;
import static org.eclipse.lsp4j.jsonrpc.json.MessageConstants.JSON_MIME_TYPE;

import com.intellij.openapi.diagnostic.Logger;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import javax.websocket.Session;
import org.eclipse.lsp4j.jsonrpc.MessageConsumer;
import org.eclipse.lsp4j.jsonrpc.json.MessageJsonHandler;
import org.eclipse.lsp4j.jsonrpc.messages.Message;

public class RcsWebSocketMessageConsumer implements MessageConsumer {
  private static final Logger logger = Logger.getInstance(RcsWebSocketMessageConsumer.class);
  private final Session session;
  private final MessageJsonHandler jsonHandler;
  private String encoding;

  public RcsWebSocketMessageConsumer(Session session, MessageJsonHandler jsonHandler) {
    this.session = session;
    this.jsonHandler = jsonHandler;
    this.encoding = StandardCharsets.UTF_8.name();
  }

  public Session getSession() {
    return this.session;
  }


  public void consume(Message message) {
    String content = this.jsonHandler.serialize(message);
    try {
     /* byte[] contentBytes = content.getBytes(this.encoding);
      int contentLength = contentBytes.length;
      String header = getHeader(contentLength);
      sendMessage(header + content);*/
      sendMessage(content);
    } catch (IllegalStateException exception) {
      logger.warn("Failed to send message: " + exception.getMessage() + ", close session, caused by " + exception);
      try {
        this.session.close();
      } catch (IOException iOException) {}
    } catch (IOException exception) {
      logger.warn("Failed to send message: " + exception.getMessage() + ", caused by " + exception);
    }
  }

  protected void sendMessage(String message) throws IOException {
    synchronized (session) {

        if (session.isOpen()) {
        int length = message.length();
        if (length <= session.getMaxTextMessageBufferSize()) {
          session.getBasicRemote().sendText(message);
        } else {
          int currentOffset = 0;
          while (currentOffset < length) {
            int currentEnd = Math.min(currentOffset + session.getMaxTextMessageBufferSize(), length);
            session.getBasicRemote().sendText(message.substring(currentOffset, currentEnd), currentEnd == length);
            currentOffset = currentEnd;
          }
        }
      } else {
        logger.warn("Ignoring message due to closed session: " + message);
      }
    }
  }

  protected String getHeader(int contentLength) {
    StringBuilder headerBuilder = new StringBuilder();
    appendHeader(headerBuilder, CONTENT_LENGTH_HEADER, contentLength).append(CRLF);
    if (!StandardCharsets.UTF_8.name().equals(encoding)) {
      appendHeader(headerBuilder, CONTENT_TYPE_HEADER, JSON_MIME_TYPE);
      headerBuilder.append("; charset=").append(encoding).append(CRLF);
    }
    headerBuilder.append(CRLF);
    return headerBuilder.toString();
  }




  protected StringBuilder appendHeader(StringBuilder builder, String name, Object value) {
    return builder.append(name).append(": ").append(value);
  }
}
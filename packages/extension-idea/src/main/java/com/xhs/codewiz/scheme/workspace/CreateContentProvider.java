package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.Schema;
/**
 * 注册 Content Provider
 */
public class CreateContentProvider {
    private String schemaProtocol = "workspace.create.contentprovider";
    private CreateContentProviderParams params;

    public CreateContentProviderParams getParams() {
        return params;
    }
    public void setParams(CreateContentProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateContentProviderParams {
        /** 需要处理的 Schema 协议 */
        private String schema;
        /** Content Provider 的 ID */
        private String id;
    
        public String getSchema() {
            return schema;
        }
        public void setSchema(String schema) {
            this.schema = schema;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

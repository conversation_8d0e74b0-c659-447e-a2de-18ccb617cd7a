package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 关闭文件
 */
public class ExecuteClose {
    private String schemaProtocol = "file.execute.close";
    private ExecuteCloseParams params;

    public ExecuteCloseParams getParams() {
        return params;
    }
    public void setParams(ExecuteCloseParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteCloseParams {
        /** 要关闭的文件 */
        private String uri;
    
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

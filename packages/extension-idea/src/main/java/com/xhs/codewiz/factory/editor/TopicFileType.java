package com.xhs.codewiz.factory.editor;

import javax.swing.Icon;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.util.IconLoader;
import com.intellij.openapi.vfs.VirtualFile;

/**
 * 自定义FileType，用于TopicEditor的图标显示
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public class TopicFileType implements FileType {

    public static final TopicFileType INSTANCE = new TopicFileType();

    // 使用项目中已有的图标，或者创建新的图标文件
    private static final Icon TOPIC_ICON = IconLoader.getIcon("icons/copilot_toolwindow.svg", TopicFileType.class);

    @Override
    public @NotNull String getName() {
        return "Topic Editor";
    }

    @Override
    public @NotNull String getDescription() {
        return "CodeWiz Topic Editor";
    }

    @Override
    public @NotNull String getDefaultExtension() {
        return "topic";
    }

    @Override
    public @Nullable Icon getIcon() {
        return TOPIC_ICON;
    }

    @Override
    public boolean isBinary() {
        return false;
    }

    @Override
    public boolean isReadOnly() {
        return false;
    }

    @Override
    public @Nullable String getCharset(@NotNull VirtualFile file, byte [] content) {
        return "UTF-8";
    }
}

package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.DocumentFilter;
/**
 * 注册内联补全提供者
 */
public class CreateInlineCompletionProvider {
    private String schemaProtocol = "workspace.create.inlinecompletionprovider";
    private CreateInlineCompletionProviderParams params;

    public CreateInlineCompletionProviderParams getParams() {
        return params;
    }
    public void setParams(CreateInlineCompletionProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateInlineCompletionProviderParams {
        private CreateInlineCompletionProviderParamsProvider provider;
        private List<DocumentFilter> selector;
    
        public CreateInlineCompletionProviderParamsProvider getProvider() {
            return provider;
        }
        public void setProvider(CreateInlineCompletionProviderParamsProvider provider) {
            this.provider = provider;
        }
        public List<DocumentFilter> getSelector() {
            return selector;
        }
        public void setSelector(List<DocumentFilter> selector) {
            this.selector = selector;
        }
    }

        public static class CreateInlineCompletionProviderParamsProvider {
        /** 触发补全的字符 */
        private List<String> triggerCharacters; // optional
        /** 内联补全提供者的 ID */
        private String id;
    
        public List<String> getTriggerCharacters() {
            return triggerCharacters;
        }
        public void setTriggerCharacters(List<String> triggerCharacters) {
            this.triggerCharacters = triggerCharacters;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

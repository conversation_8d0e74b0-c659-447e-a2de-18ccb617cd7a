package com.xhs.codewiz.platform;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.factory.editor.TopicPanelUtil;
import com.xhs.codewiz.factory.webview.BrowserWindowFactory;
import com.xhs.codewiz.factory.webview.ToolBrowserWindow;
import com.xhs.codewiz.scheme.platform.ExecuteHidePanel;
import com.xhs.codewiz.scheme.platform.ExecuteShowPanel;
import com.xhs.codewiz.scheme.platform.ExecuteShowPanel.ExecuteShowPanelParams;
import com.xhs.codewiz.scheme.platform.UpdatePanel;
import com.xhs.codewiz.type.platform.BuildInPanel;
import com.xhs.codewiz.type.platform.BuildInPanel.BuildInPanelDiff;
import com.xhs.codewiz.type.platform.BuildInPanel.BuildInPanelViews;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.RegisterUtil;

/**
 * Author: liukunpeng Date: 2025-07-28 Description:
 */
public class CommonPlatformService {
    private static Integer sameContentRetryTime = 5;
    private static final Logger log = Logger.getInstance(CommonPlatformService.class);

    private static final Map<String, ExecuteShowPanelParams> BUILD_IN_PANEL_VIEWS_MAP = new HashMap<>();


    public static void showPanel(String str, String channel) {
        ExecuteShowPanel showPanel = GsonUtil.fromJson(str, ExecuteShowPanel.class);
        if (null == showPanel || null == showPanel.getParams()) {
            return;
        }
        ExecuteShowPanelParams params = showPanel.getParams();
        BuildInPanel buildInPanel = params.getPanel();
        if (buildInPanel instanceof BuildInPanelDiff diff) {
            int retry = sameContentRetryTime;
            DiffPanelUtil.showDiffPanel(params.getId(), diff, params.getOptions(), channel, null, null, retry);
        } else if (buildInPanel instanceof BuildInPanelViews views) {
            String viewId = views.getViewId();
            String toolwindowId = RegisterUtil.getInstance().getViewsContainerByViewId(viewId);
            Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
            ToolBrowserWindow window = BrowserWindowFactory.getInstance().getToolBrowserWindow(project, toolwindowId);
            ApplicationManager.getApplication().invokeLater(() -> {
                if (!window.getActiveWindow().isVisible()) {
                    window.getActiveWindow().show();
                }
            });
        } else if (buildInPanel instanceof BuildInPanel.BuildInPanelWebview views) {
            String viewId = views.getWebviewId();
            Project currentProject = ApplicationUtil.findCurrentProject();
            if (currentProject == null) {
                LoggerUtil.INSTANCE.logWarn(log, "Failed to get current project");
                return;
            }
            String key = generateViewKey(currentProject, viewId);
            BUILD_IN_PANEL_VIEWS_MAP.put(key, params);
        }
    }

    public static void closePanel(String str, String channel) {
        ExecuteHidePanel hidePanel = GsonUtil.fromJson(str, ExecuteHidePanel.class);
        if (null == hidePanel
                || null == hidePanel.getParams()
                || StringUtils.isEmpty(hidePanel.getParams().getId())) {
            return;
        }
        String panelId = hidePanel.getParams().getId();
        // 目前仅有diff面板需要关闭
        DiffPanelUtil.closeDiffPanel(panelId, channel);
    }

    public static void updatePanel(String str, String channel) {
        ApplicationManager.getApplication().invokeLater(() -> {
            LoggerUtil.INSTANCE.logInfo(log, "updatePanel: str: " + str + " channel: " + channel);
            UpdatePanel updatePanel = GsonUtil.fromJson(str, UpdatePanel.class);
            if (null == updatePanel || null == updatePanel.getParams()) {
                LoggerUtil.INSTANCE.logWarn(log, "updatePanel: updatePanel or params is null");
                return;
            }
            UpdatePanel.UpdatePanelParams params = updatePanel.getParams();
            if (MapUtils.isEmpty(BUILD_IN_PANEL_VIEWS_MAP) || CollectionUtils.isEmpty(params.getUpdates())) {
                return;
            }
            ExecuteShowPanelParams executeShowPanelParams = BUILD_IN_PANEL_VIEWS_MAP.values().stream()
                    .filter(panelParam -> Objects.equals(panelParam.getId(), params.getId()))
                    .findFirst()
                    .orElse(null);

            if (executeShowPanelParams != null) {
                for (UpdatePanel.UpdatePanelParamsUpdates update : params.getUpdates()) {
                    if ("title".equals(update.getKey())) {
                        LoggerUtil.INSTANCE.logInfo(log, "updatePanel: update title: " + update.getValue().toString());
                        TopicPanelUtil.updateEditorTitle(update.getValue().toString());
                    }
                }
            }

            // 移动焦点到编辑器
            // FocusTransferUtil.transferFocusToEditorDelay(ApplicationUtil.findCurrentProject(), 10);
        });
    }

    public static ExecuteShowPanelParams getViewPanelParams(Project project, String webviewId) {
        String key = generateViewKey(project, webviewId);
        return BUILD_IN_PANEL_VIEWS_MAP.get(key);
    }

    private static String generateViewKey(Project project, String webviewId) {
        return project.getName() + "#" + webviewId;
    }

    public static void executeHidePanel(Project project, String chanel, String panelId, boolean dispose) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "executeHidePanel project: "
                    + project.getName() + " panelId: " + panelId + " chanel: " + chanel);

            ExecuteHidePanel hidePanel = new ExecuteHidePanel();
            ExecuteHidePanel.ExecuteHidePanelParams params = new ExecuteHidePanel.ExecuteHidePanelParams();
            params.setId(panelId);
            params.setDispose(dispose);
            hidePanel.setParams(params);
            RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(
                    chanel,
                    panelId,
                    hidePanel,
                    project);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process executeHidePanel", e);
        }
    }

    public static void executeShowPanel(Project project, String chanel, String panelId, ExecuteShowPanel.ExecuteShowPanelParams params) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "executeShowPanel project: "
                    + project.getName() + " panelId: " + panelId + " chanel: " + chanel);

            ExecuteShowPanel showPanel = new ExecuteShowPanel();
            showPanel.setParams(params);

            RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(
                    chanel,
                    panelId,
                    showPanel,
                    project);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process executeHidePanel", e);
        }
    }

}

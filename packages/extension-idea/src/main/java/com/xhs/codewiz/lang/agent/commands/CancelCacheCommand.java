package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.editor.request.VirtualFileUri;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import org.jetbrains.annotations.NotNull;

public class CancelCacheCommand implements JsonRpcNotification {

    @SerializedName("uri")
    private final @NotNull VirtualFileUri uri;

    public CancelCacheCommand(@NotNull VirtualFileUri uri) {
        this.uri = uri;
    }
    @Override
    public @NotNull String getCommandName() {
        return "textDocument/cancelCache";
    }

    public @NotNull VirtualFileUri getUri() {
        return this.uri;
    }
}

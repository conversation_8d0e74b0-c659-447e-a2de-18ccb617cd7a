package com.xhs.codewiz.client.util;

import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.actions.command.ChatWindowCommandUtil;
import com.xhs.codewiz.actions.command.DefaultCommandUtil;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.client.service.RcsGlobalService;
import com.xhs.codewiz.listener.LSPManager;
import com.xhs.codewiz.service.SessionService;
import com.xhs.codewiz.terminal.TerminalService;
import com.xhs.codewiz.editor.util.EditorTabGroupUtil;
import com.xhs.codewiz.scheme.global.UpdateState;
import com.xhs.codewiz.scheme.platform.CreateConfigurations;
import com.xhs.codewiz.scheme.platform.CreateConfigurations.CreateConfigurationsParams;
import com.xhs.codewiz.scheme.workspace.CreateWorkspaceFolder;
import com.xhs.codewiz.scheme.workspace.CreateWorkspaceFolder.CreateWorkspaceFolderParams;
import com.xhs.codewiz.type.platform.AppName;
import com.xhs.codewiz.type.platform.PlatformName;
import com.xhs.codewiz.type.platform.SeriesName;
import com.xhs.codewiz.utils.MacAddressUtil;
import com.xhs.codewiz.utils.PluginUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-07-24 Description:
 */
public class RcsConnectUtil {
  private static List<Project> activeProjects = Collections.synchronizedList(new ArrayList<>());
  /**
   * 初始化连接时需要做的前置操作
   * @param project
   */
  public static void init(Project project) {
    // 创建平台配置
    createPlatformConfig(project);
    // 创建工作区配置
    //createWorkspaceConfig(project);
    // 创建工作区
    createWorkspaceFolder(project);
    // 初始化TabGroup及文件
    EditorTabGroupUtil.initTabGroup(project);
    // 上报state
    RcsGlobalService.getProjectState(project);
    // 通知主题变更
    LSPManager.getInstance().notifyProjectOfThemeChange();
  }

  public static void destroy(Project project) {
    EditorTabGroupUtil.destroyTabGroup(project);
    DefaultCommandUtil.unRegisterAction(project);
    ChatWindowCommandUtil.clearProjectActions(project);
    TerminalService.clearWeakMap(project);
    SessionService.clearProjectSessionProvider(project);
    RcsGlobalService.clearProjectCommand(project);
    activeProjects.remove(project);
  }

  private static void createPlatformConfig(Project project) {
    CreateConfigurations platformConfig = new CreateConfigurations();
    CreateConfigurationsParams params = new CreateConfigurationsParams();
    params.setSeries(SeriesName.Jetbrains);
    params.setPlatform(guessPlatformName());
    params.setVersion(ApplicationInfo.getInstance().getFullVersion());
    params.setAdapter(PluginUtil.getVersion());
    params.setLanguage("zh-CN");
    params.setMachineId(MacAddressUtil.getMacAddress());
    platformConfig.setParams(params);
    RcsWebSocketManager.INSTANCE.sendNotification(platformConfig, project);
  }
  private static void createWorkspaceConfig(Project project) {
    com.xhs.codewiz.scheme.workspace.CreateConfigurations workspace = new com.xhs.codewiz.scheme.workspace.CreateConfigurations();
    Map<String, Object> config = new HashMap<>();
    Map<String, Object> codeWiz = new HashMap<>();
    /*"Code_Wiz": {
      "thirdProvider": "lingma",
          "enableAutoCompletions": true,
          "enableCodeLens": true
    },
    "Wiz-Code-dev": {
      "enableLSPRequestResponse": true,
          "enableLSPNotificationFromIDE": true,
          "enableLSPNotificationFromLS": true,
          "enableIDELogFromWebv": true,
          "enableWebvLogFromIDE": true
    },*/
    codeWiz.put("thirdProvider", "lingma");
    codeWiz.put("enableAutoCompletions", true);
    codeWiz.put("enableCodeLens", true);
    config.put("Code_Wiz", codeWiz);

    Map<String, Object> wizCodeDev = new HashMap<>();
    wizCodeDev.put("enableLSPRequestResponse", true);
    wizCodeDev.put("enableLSPNotificationFromIDE", true);
    wizCodeDev.put("enableLSPNotificationFromLS", true);
    wizCodeDev.put("enableIDELogFromWebv", true);
    wizCodeDev.put("enableWebvLogFromIDE", true);
    config.put("Wiz-Code-dev", wizCodeDev);
    workspace.setParams(config);

    RcsWebSocketManager.INSTANCE.sendNotification(workspace, project);
  }
  private static void createWorkspaceFolder(Project project) {
    CreateWorkspaceFolder createWorkspaceFolder = new CreateWorkspaceFolder();
    CreateWorkspaceFolderParams params = new CreateWorkspaceFolderParams();
    if (!activeProjects.contains(project)) {
      activeProjects.add(project);
    }
    params.setIndex(activeProjects.indexOf(project));
    params.setName(project.getName());
    params.setUri(project.getBaseDir().getUrl());
    params.setIsTrusted(true);
    createWorkspaceFolder.setParams(params);

    RcsWebSocketManager.INSTANCE.sendNotification(createWorkspaceFolder, project);
  }

  private static PlatformName guessPlatformName() {
    String regexName = ApplicationInfo.getInstance().getVersionName().replace(" ", "");
    for (PlatformName platformEnum : PlatformName.values()) {
      String platformName = platformEnum.getValue();
      if (StringUtils.equalsIgnoreCase(regexName, platformName.replace(" ", ""))) {
        return platformEnum;
      }
    }
    //默认按idea上报
    return PlatformName.IntelliJIDEA;
  }
}

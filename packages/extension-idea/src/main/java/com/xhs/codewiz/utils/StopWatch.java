package com.xhs.codewiz.utils;

import com.intellij.openapi.diagnostic.Logger;
import java.util.LinkedHashMap;
import java.util.Map;

public class StopWatch {
  private static Logger logger = Logger.getInstance(StopWatch.class);
  org.apache.commons.lang3.time.StopWatch stopWatch = new org.apache.commons.lang3.time.StopWatch();
  Map<String, Long> timeMap = new LinkedHashMap<String, Long>();
  String label;

  public void start(String label) {
    this.stop();
    this.label = label;
    this.stopWatch.reset();
    this.stopWatch.start();
  }

  public long stop() {
    if (this.stopWatch.isStarted() && this.label != null) {
      this.stopWatch.stop();
      long time = this.stopWatch.getTime();
      this.timeMap.put(this.label, time);
      this.label = null;
      return time;
    }
    return 0L;
  }

  public void print() {
    this.stop();
    StringBuilder sb = new StringBuilder();
    sb.append("===========================\n");
    for (Map.Entry<String, Long> entry : this.timeMap.entrySet()) {
      sb.append(entry.getKey()).append("\t").append(entry.getValue()).append('\n');
    }
    sb.append("---------------------------");
    logger.info(sb.toString());
  }
}
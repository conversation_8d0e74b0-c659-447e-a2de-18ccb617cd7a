package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import org.jetbrains.annotations.NotNull;

public final class DidChangeWorkspaceFolders implements JsonRpcNotification {
    @SerializedName("event")
    private final @NotNull WorkspaceFoldersChangeEvent event;

    public @NotNull String getCommandName() {
        return "workspace/didChangeWorkspaceFolders";
    }

    public @NotNull WorkspaceFoldersChangeEvent getEvent() {
        return this.event;
    }

    public DidChangeWorkspaceFolders(@NotNull WorkspaceFoldersChangeEvent event) {
        this.event = event;
    }
}


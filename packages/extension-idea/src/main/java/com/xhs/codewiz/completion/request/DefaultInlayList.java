package com.xhs.codewiz.completion.request;


import com.intellij.openapi.util.TextRange;
import com.xhs.codewiz.completion.CodewizCompletion;
import com.xhs.codewiz.editor.request.CodewizEditorInlay;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public class DefaultInlayList implements CodewizInlayList {
    private final List<CodewizEditorInlay> inlays;
    private final @NotNull CodewizCompletion codewizCompletion;
    private final @NotNull TextRange replacementRange;
    private final @NotNull String replacementText;

    public DefaultInlayList(@NotNull CodewizCompletion codewizCompletion, @NotNull TextRange replacementRange, @NotNull String replacementText, @NotNull Collection<CodewizEditorInlay> inlays) {
        this.codewizCompletion = codewizCompletion;
        this.replacementRange = replacementRange;
        this.inlays = List.copyOf(inlays);
        this.replacementText = replacementText;
    }

    public boolean isEmpty() {
        return this.inlays.isEmpty();
    }

    public @NotNull Iterator<CodewizEditorInlay> iterator() {
        return this.inlays.iterator();
    }

    public List<CodewizEditorInlay> getInlays() {
        return this.inlays;
    }

    public @NotNull CodewizCompletion getCodewizCompletion() {
        return this.codewizCompletion;
    }

    public @NotNull TextRange getReplacementRange() {
        return this.replacementRange;
    }

    public @NotNull String getReplacementText() {
        return this.replacementText;
    }
}


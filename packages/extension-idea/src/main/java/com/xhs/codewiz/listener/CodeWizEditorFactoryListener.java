package com.xhs.codewiz.listener;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.CommandProcessor;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorKind;
import com.intellij.openapi.editor.EditorModificationUtil;
import com.intellij.openapi.editor.LogicalPosition;
import com.intellij.openapi.editor.event.BulkAwareDocumentListener;
import com.intellij.openapi.editor.event.CaretEvent;
import com.intellij.openapi.editor.event.CaretListener;
import com.intellij.openapi.editor.event.DocumentEvent;
import com.intellij.openapi.editor.event.EditorFactoryEvent;
import com.intellij.openapi.editor.event.EditorFactoryListener;
import com.intellij.openapi.editor.event.SelectionEvent;
import com.intellij.openapi.editor.event.SelectionListener;
import com.intellij.openapi.editor.ex.util.EditorUtil;
import com.intellij.openapi.editor.markup.GutterIconRenderer;
import com.intellij.openapi.editor.markup.HighlighterLayer;
import com.intellij.openapi.editor.markup.MarkupModel;
import com.intellij.openapi.editor.markup.RangeHighlighter;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.editor.CodeWizEditorManager;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import com.xhs.codewiz.editor.InlayDisposeContext;
import com.xhs.codewiz.editor.TypeOverHandler;
import com.xhs.codewiz.scheme.content.UpdateRanges;
import com.xhs.codewiz.scheme.content.UpdateRanges.UpdateRangesParams;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.utils.IconsUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.swing.Icon;
import org.jetbrains.annotations.NotNull;

public class CodeWizEditorFactoryListener implements EditorFactoryListener {
    private final CopilotSelectionListener selectionListener = new CopilotSelectionListener();
    private final CurrentFileSelectionListener currentFileSelectionListener = new CurrentFileSelectionListener();
    private static final Map<Editor, RangeHighlighter> highLightmap = new ConcurrentHashMap<>();
    //private static RangeHighlighter rangeHighlighter =  null;

    public CodeWizEditorFactoryListener() {
    }

    public void editorCreated(@NotNull EditorFactoryEvent event) {
        Editor editor = event.getEditor();
        Project project = editor.getProject();
        if (project != null && !project.isDisposed() && CodeWizEditorManager.getInstance().isAvailable(editor)) {
            Disposable editorDisposable = Disposer.newDisposable("copilotEditorListener");
            EditorUtil.disposeWithEditor(editor, editorDisposable);
            editor.getCaretModel().addCaretListener(new CopilotCaretListener(editor), editorDisposable);
            editor.getDocument().addDocumentListener(new CopilotDocumentListener(editor), editorDisposable);
            editor.getSelectionModel().addSelectionListener(this.selectionListener, editorDisposable);
            editor.getSelectionModel().addSelectionListener(this.currentFileSelectionListener, editorDisposable);
        }
    }

    private static class CopilotSelectionListener implements SelectionListener {
        private CopilotSelectionListener() {
        }

        public void selectionChanged(@NotNull SelectionEvent e) {
            Editor editor = e.getEditor();
            Project project = editor.getProject();
            if (project != null && !project.isDisposed()) {
                if (CodeWizEditorUtil.isSelectedEditor(editor)) {
                    CodeWizEditorManager.getInstance().disposeInlays(editor, InlayDisposeContext.SelectionChange);
                    //先注释左侧快捷图标
                    /*if (editor.getSelectionModel().hasSelection()) {
                        int selectionStart = editor.getDocument().getLineNumber(e.getNewRange().getStartOffset());
                        addGutterIconForLine(editor, selectionStart);
                    } else {
                        if (highLightmap.containsKey(editor)) {
                            editor.getMarkupModel().removeHighlighter(highLightmap.get(editor));
                            highLightmap.remove(editor);
                        }
                    }*/
                }
            }
        }
    }
    private static void addGutterIconForLine(Editor editor, int line) {
        // 先移除之前的图标
        if (highLightmap.containsKey(editor)) {
            editor.getMarkupModel().removeHighlighter(highLightmap.get(editor));
        }
        MarkupModel markupModel = editor.getMarkupModel();
        RangeHighlighter highlighter = markupModel.addLineHighlighter(line, HighlighterLayer.FIRST, null);
        highLightmap.put(editor, highlighter);
        highlighter.setGutterIconRenderer(new GutterIconRenderer() {
            @Override
            public Icon getIcon() {
                // 返回你需要显示的图标
                return IconsUtil.CODEWIZ;
            }

            @Override
            public AnAction getClickAction() {
                // 实现当图标被点击时执行的操作
                return new AnAction("Execute Action") {
                    @Override
                    public void actionPerformed(AnActionEvent e) {
                        /*AnAction action = ActionManager.getInstance().getAction("codewiz.addContext");
                        action.actionPerformed(e);*/
                    }
                };
            }

            @Override
            public Alignment getAlignment() {
                return Alignment.LEFT;
            }

            @Override
            public boolean equals(Object o) {
                return false;
            }

            @Override
            public int hashCode() {
                return 0;
            }
        });
    }

    private static final class CopilotCaretListener implements CaretListener {
        private final @NotNull Editor editor;

        public CopilotCaretListener(@NotNull Editor editor) {
            this.editor = editor;
        }

        public void caretPositionChanged(@NotNull CaretEvent event) {
            Project project = this.editor.getProject();
            if (project == null || project.isDisposed()) {
                return;
            }
            //无选中内容时，才上报：仅上报纯光标移动
            if (null != event.getCaret() && !event.getCaret().hasSelection()) {
                VirtualFile vf = FileDocumentManager.getInstance().getFile(editor.getDocument());
                if (null == vf) {
                    return;
                }
                // 使用延迟执行来获取换行后的真实光标位置，确保自动缩进已完成
                ApplicationManager.getApplication().invokeLater(() -> {
                    if (project.isDisposed()) {
                        return;
                    }
                    // 获取当前真实的光标位置
                    int currentOffset = editor.getCaretModel().getOffset();
                    LogicalPosition position = editor.offsetToLogicalPosition(currentOffset);
                    UpdateRanges ranges = new UpdateRanges();
                    UpdateRangesParams params = new UpdateRangesParams();
                    params.setUri(vf.getUrl());
                    List<Range> rangeList = new ArrayList<>();
                    Range range = new Range();
                    Position start = new Position();
                    start.setLine(position.line);
                    start.setCharacter(position.column);
                    range.setStart(start);
                    range.setEnd(start);
                    rangeList.add(range);
                    params.setRanges(rangeList);
                    ranges.setParams(params);
                    RcsWebSocketManager.INSTANCE.sendNotification(ranges, project);
                });
            }

            /*boolean wasTypeOver = TypeOverHandler.getPendingTypeOverAndReset(this.editor);
            if (wasTypeOver) {
                CopilotEditorManager.getInstance().editorModified(this.editor, CompletionRequestType.Forced);
                return;
            }*/
            /*if (CommandProcessor.getInstance().getCurrentCommand() != null) {
                return;
            }
            if (CopilotApplicationSettings.settings().isShowIdeCompletions() && LookupManager.getActiveLookup(this.editor) != null) {
                if (CopilotEditorManager.getInstance().hasCompletionInlays(this.editor)) {
                    CopilotEditorManager.getInstance().editorModified(this.editor, CompletionRequestType.Automatic);
                }
            }
            CopilotEditorManager.getInstance().disposeInlays(this.editor, InlayDisposeContext.CaretChange);*/
        }
    }

    private static final class CopilotDocumentListener implements BulkAwareDocumentListener {
        private final @NotNull Editor editor;

        public CopilotDocumentListener(@NotNull Editor editor) {
            this.editor = editor;
        }

        public void documentChangedNonBulk(@NotNull DocumentEvent event) {
            Project project = this.editor.getProject();
            if (project == null || project.isDisposed()) {
                return;
            }
            if (!CodeWizEditorUtil.isSelectedEditor(this.editor)) {
                return;
            }
            CodeWizEditorManager editorManager = CodeWizEditorManager.getInstance();
            if (!editorManager.isAvailable(this.editor)) {
                return;
            }
            CommandProcessor commandProcessor = CommandProcessor.getInstance();
            if (commandProcessor.isUndoTransparentActionInProgress()) {
                return;
            }
            if (commandProcessor.getCurrentCommandName() != null) {
                return;
            }
            int changeOffset = event.getOffset() + event.getNewLength();
            if (this.editor.getCaretModel().getOffset() != changeOffset) {
                return;
            }
            if (this.isIgnoreChange(event, this.editor, changeOffset)) {
                CodeWizEditorManager.getInstance().disposeInlays(this.editor, InlayDisposeContext.Typing);
            } else {
                //这里只有非命令方式才会触发，比如【底层自动同步文档到 PSI（编译结构树）】，所以这个场景基本不会触发
                EditEventTriggerKind requestType = event.getOldLength() != event.getNewLength() ? EditEventTriggerKind.Invoke : EditEventTriggerKind.Automatic;
                editorManager.editorModified(this.editor, changeOffset, requestType);
            }
        }

        private boolean isIgnoreChange(DocumentEvent event, Editor editor, int offset) {
            Document document = event.getDocument();
            if (event.getNewLength() < 1) {
                return true;
            }
            if (!editor.getEditorKind().equals(EditorKind.MAIN_EDITOR) && !ApplicationManager.getApplication().isUnitTestMode()) {
                return true;
            }
            if (!EditorModificationUtil.checkModificationAllowed(editor) || document.getRangeGuard(offset, offset) != null) {
                document.fireReadOnlyModificationAttempt();
                return true;
            }
            return false;
        }
    }
}


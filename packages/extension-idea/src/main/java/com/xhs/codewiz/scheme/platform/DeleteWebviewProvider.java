package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 删除 Webview View Provider
 */
public class DeleteWebviewProvider {
    private String schemaProtocol = "platform.delete.webviewprovider";
    /** Webview View Provider ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

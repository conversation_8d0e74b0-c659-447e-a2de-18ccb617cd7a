package com.xhs.codewiz.listener;

import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.intellij.ide.ui.LafManager;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.command.CommandProcessor;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.event.DocumentEvent;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.ex.FileEditorManagerEx;
import com.intellij.openapi.fileEditor.impl.EditorWindow;
import com.intellij.openapi.fileEditor.impl.FileEditorManagerImpl;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.openapi.project.ProjectUtil;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.FileViewProvider;
import com.intellij.testFramework.LightVirtualFile;
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread;
import com.intellij.util.ui.UIUtil;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.editor.request.LanguageInfo;
import com.xhs.codewiz.editor.request.TextDocumentItem;
import com.xhs.codewiz.editor.request.VersionedTextDocumentIdentifier;
import com.xhs.codewiz.editor.request.VirtualFileUri;
import com.xhs.codewiz.editor.util.EditorTabGroupUtil;
import com.xhs.codewiz.factory.editor.TopicEditorVirtualFile;
import com.xhs.codewiz.factory.webview.WebviewBuilder;
import com.xhs.codewiz.platform.CommonPlatformService;
import com.xhs.codewiz.enums.MetricsKeyEnum;
import com.xhs.codewiz.enums.MetricsSceneEnum;
import com.xhs.codewiz.lang.agent.CodeWizAgentProcessService;
import com.xhs.codewiz.lang.agent.commands.CancelCacheCommand;
import com.xhs.codewiz.lang.agent.commands.CommonMetricsCommand;
import com.xhs.codewiz.lang.agent.commands.DidChangeCommand;
import com.xhs.codewiz.lang.agent.commands.DidCloseCommand;
import com.xhs.codewiz.lang.agent.commands.DidDeleteFilesCommand;
import com.xhs.codewiz.lang.agent.commands.DidFocusCommand;
import com.xhs.codewiz.lang.agent.commands.DidOpenCommand;
import com.xhs.codewiz.lang.agent.commands.TextDocumentContentChangeEvent;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import com.xhs.codewiz.scheme.content.UpdateContent;
import com.xhs.codewiz.scheme.content.UpdateContent.UpdateContentParams;
import com.xhs.codewiz.scheme.content.UpdateRanges;
import com.xhs.codewiz.scheme.content.UpdateRanges.UpdateRangesParams;
import com.xhs.codewiz.scheme.file.CreateFile;
import com.xhs.codewiz.scheme.file.CreateFile.CreateFileParams;
import com.xhs.codewiz.scheme.file.DeleteFile;
import com.xhs.codewiz.scheme.file.ExecuteClose;
import com.xhs.codewiz.scheme.file.ExecuteClose.ExecuteCloseParams;
import com.xhs.codewiz.scheme.file.ExecuteOpen;
import com.xhs.codewiz.scheme.file.ExecuteOpen.ExecuteOpenParams;
import com.xhs.codewiz.scheme.file.ExecuteSave;
import com.xhs.codewiz.scheme.file.ExecuteSave.ExecuteSaveParams;
import com.xhs.codewiz.scheme.file.UpdateFile;
import com.xhs.codewiz.scheme.file.UpdateFile.UpdateFileParams;
import com.xhs.codewiz.scheme.platform.ExecuteShowPanel;
import com.xhs.codewiz.scheme.workspace.UpdateConfigurations;
import com.xhs.codewiz.type.content.Change;
import com.xhs.codewiz.type.content.Changes;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.file.Content;
import com.xhs.codewiz.type.file.FileOpenOptions;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.FileSizeUtil;
import com.xhs.codewiz.utils.FileUtil;
import com.xhs.codewiz.utils.JBCefBrowserUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.ThreadUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import javax.swing.UIManager;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class LSPManager {
    private static final Logger LOG = Logger.getInstance(LSPManager.class);
    private static final Key<Integer> KEY_FILE_OPENED = Key.create("codewiz.lspOpen");
    private final AtomicInteger agentProcessId = new AtomicInteger(0);
    private static String lifecycle_id;

    public LSPManager() {
    }

    public static @NotNull LSPManager getInstance() {
        return ApplicationManager.getApplication().getService(LSPManager.class);
    }

    public void notifyDidOpen(@NotNull Project project, @NotNull VirtualFile file) {
        if (!FileSizeUtil.isTooLarge(file)) {
            LanguageInfo language = this.findLanguageInfo(project, file);
            if (language != null) {
                //有先后顺序，需要先增加tab，然后才能penFile
                FileEditorManager source = FileEditorManager.getInstance(project);
                this.doNotifyDidOpen(file, language, project, true);
                EditorTabGroupUtil.addTab(getEditorWindowList(source), source.getProject(), file);
                this.doNotifyDidOpenForLs(file, language);
            }
        }
    }

    public void notifyDidDelete(@NotNull VirtualFile file) {
        Project project = guessProject(file);
        if (null == project || project.isDisposed()) {
            return;
        }
        DeleteFile deleteFile = new DeleteFile();
        deleteFile.setParams(file.getUrl());
        RcsWebSocketManager.INSTANCE.sendNotification(deleteFile, project);

        this.notify(new DidDeleteFilesCommand(VirtualFileUri.from(file).getUri()));
    }

    public void notifyDidCreate(@NotNull VirtualFile file) {
        Project project = guessProject(file);
        if (null == project || project.isDisposed()) {
            return;
        }
        Content content = FileUtil.buildContent(project, file);
        if (null == content) {
            return;
        }
        CreateFile createFile = new CreateFile();
        CreateFileParams params = new CreateFileParams();
        params.setUri(file.getUrl());
        params.setContent(content);
        createFile.setParams(params);

        //String remoteServiceId = RcsWebSocketManager.INSTANCE.getRemoteServiceId(createFile.getSchemeProtocol());
        //走统一发送通知渠道，null代表走default渠道
        RcsWebSocketManager.INSTANCE.sendNotification(createFile, project);
    }

    public void notifyProjectOfThemeChange() {
        UIManager.LookAndFeelInfo lookAndFeelInfo = LafManager.getInstance().getCurrentLookAndFeel();
        Project[] openProjects = ProjectManager.getInstance().getOpenProjects();
        for (Project project : openProjects) {
            if (!project.isDisposed()) {
                // 可以在这里向项目级别的组件发送主题变化通知
                // 例如：通过消息总线发送主题变化事件
                notifyProjectOfThemeChange(project, lookAndFeelInfo.getName(),  UIUtil.isUnderDarcula());
            }
        }
    }

    /**
     * 通知RCS 主题发生了变化
     * @param theme
     */
    public void notifyProjectOfThemeChange(@NotNull Project project, @NotNull String theme, boolean isDark) {
        UpdateConfigurations updateConfigurations = new UpdateConfigurations();
        List<UpdateConfigurations.UpdateConfigurationsParams> params = new ArrayList<>();
        UpdateConfigurations.UpdateConfigurationsParams updateConfigurationsParams = new UpdateConfigurations.UpdateConfigurationsParams();
        updateConfigurationsParams.setPath("workbench.colorTheme");
        updateConfigurationsParams.setValue(getRCSThemeName(theme, isDark));
        params.add(updateConfigurationsParams);
        updateConfigurations.setParams(params);
        RcsWebSocketManager.INSTANCE.sendNotification(updateConfigurations, project);
    }

    // `dark`|`light`|`dark-high-contrast`|`light-high-contrast`
    // 2023.1 和2025.2 兼容性
    private String getRCSThemeName(@NotNull String themeId, boolean isDark) {
        return switch (themeId) {
            case "ExperimentalDark", "Darcula", "JetBrainsHighContrastTheme" -> "dark";
            case "ExperimentalLight", "ExperimentalLightWithLightHeader" -> "light";
            default -> isDark ? "dark" : "light";
        };
    }


    public void notifyDidClose(@NotNull VirtualFile file) {
        this.notifyDidClose(file, null);
    }

    public void notifyDidClose(@NotNull VirtualFile file, @Nullable String pathOverride) {
        if (!isDisabled()) {
            Integer storedId = KEY_FILE_OPENED.get(file);
            if (storedId != null && storedId == this.agentProcessId.get()) {
                Project project = guessProject(file);
                if (null == project) {
                    return;
                }

                // 检查项目是否正在关闭，如果是则跳过通知
                if (project.isDisposed()) {
                    return;
                }

                KEY_FILE_OPENED.set(file, null);
                ExecuteClose excuteClose = new ExecuteClose();
                ExecuteCloseParams params = new ExecuteCloseParams();
                String uri = StringUtils.isEmpty(pathOverride) ? file.getUrl() : pathOverride;
                params.setUri(uri);
                excuteClose.setParams(params);
                RcsWebSocketManager.INSTANCE.sendNotification(excuteClose, project);
                //同步ls
                if (pathOverride != null) {
                    this.notify(new DidCloseCommand(LSPUtil.identifier(file.getFileSystem(), pathOverride)));
                } else {
                    this.notify(new DidCloseCommand(LSPUtil.identifier(file)));
                }
            }

        }
    }

    /**
     * @param file
     * @param project
     * @param dispose 是否销毁文件 关闭行为则销毁，在不同editor window移动文件则不销毁
     */
    public void closeTopicEditorFile(Project project, VirtualFile file, boolean dispose) {
        // 关闭TopicEditorVirtualFile
        if (file instanceof TopicEditorVirtualFile topicFile) {
            LoggerUtil.INSTANCE.logWarn(LOG, "closeTopicEditorFile");
            WebviewBuilder webviewBuilder = JBCefBrowserUtil.
                    getWebviewBuilderByChannel(topicFile.getRemoteChannel(), topicFile.getWebviewId());
            if (webviewBuilder == null
                    || webviewBuilder.getWebviewProvider() == null
                    || StringUtils.isBlank(webviewBuilder.getWebviewProvider().getId())) {
                LoggerUtil.INSTANCE.logWarn(LOG, "closeTopicEditorFile webviewBuilder is null");
                return;
            }
            String viewId = webviewBuilder.getWebviewProvider().getId();
            String remoteChanel = webviewBuilder.getRemoteChannel();
            ExecuteShowPanel.ExecuteShowPanelParams params = CommonPlatformService.getViewPanelParams(project, viewId);
            if (params != null) {
                CommonPlatformService.executeHidePanel(project, remoteChanel, params.getId(), dispose);
            }
        }
    }

    private void openTopicEditorFile(Project project, VirtualFile file) {
        // 打开TopicEditorFile
        if (file instanceof TopicEditorVirtualFile topicFile) {
            LoggerUtil.INSTANCE.logWarn(LOG, "closeTopicEditorFile");
            WebviewBuilder webviewBuilder = JBCefBrowserUtil.
                    getWebviewBuilderByChannel(topicFile.getRemoteChannel(), topicFile.getWebviewId());
            if (webviewBuilder == null
                    || webviewBuilder.getWebviewProvider() == null
                    || StringUtils.isBlank(webviewBuilder.getWebviewProvider().getId())) {
                LoggerUtil.INSTANCE.logWarn(LOG, "closeTopicEditorFile webviewBuilder is null");
                return;
            }
            String viewId = webviewBuilder.getWebviewProvider().getId();
            String remoteChanel = webviewBuilder.getRemoteChannel();
            ExecuteShowPanel.ExecuteShowPanelParams params = CommonPlatformService.getViewPanelParams(project, viewId);
            if (params != null) {
                CommonPlatformService.executeShowPanel(project, remoteChanel, params.getId(), params);
            }
        }
    }

    /**
     * 上报给Rcs
     * @param project
     * @param newFile
     * @param oldFile
     */
    public void notifyDidFocus(@NotNull Project project, @NotNull VirtualFile newFile, VirtualFile oldFile) {
        if (!FileSizeUtil.isTooLarge(newFile)) {
            LanguageInfo language = this.findLanguageInfo(project, newFile);
            //当前框架下，didFocus看作是open
            if (language != null) {
                ExecuteOpen excuteOpen = new ExecuteOpen();
                ExecuteOpenParams params = new ExecuteOpenParams();
                params.setContent(FileUtil.buildContent(project, newFile));
                params.setUri(newFile.getUrl());
                FileOpenOptions options = new FileOpenOptions();
                options.setDidShow(true);
                options.setDidFocus(true);
                params.setOptions(options);
                excuteOpen.setParams(params);
                RcsWebSocketManager.INSTANCE.sendNotification(excuteOpen, project);
            }
        }
    }
    /**
     * 上报给Ls
     * @param project
     * @param newFile
     * @param oldFile
     */
    public void notifyDidFocusToLs(@NotNull Project project, @NotNull VirtualFile newFile, VirtualFile oldFile) {
        if (!FileSizeUtil.isTooLarge(newFile)) {
            LanguageInfo language = this.findLanguageInfo(project, newFile);
            if (language != null) {
                this.doNotifyDidOpenForLs(newFile, language);
                VirtualFileUri oldFileUri = Optional.ofNullable(oldFile)
                    .map(VirtualFileUri::from)
                    .orElse(new VirtualFileUri(""));
                this.notify(new DidFocusCommand(VirtualFileUri.from(newFile), oldFileUri));
            }
        }
    }

    public void notifyDidChange(@NotNull DocumentEvent event) {
        if (!isDisabled()) {
            Document doc = event.getDocument();
            VirtualFile file = FileDocumentManager.getInstance().getFile(doc);
            if (file != null && file.isValid()) {
                if (this.checkFileStatus(file)) {
                    Project project = guessProject(file);
                    if (null == project || project.isDisposed()) {
                        return;
                    }
                    if (event.isWholeTextReplaced()) {
                        this.doNotifyDidChangeComplete(doc, project);
                        this.doNotifyDidChangeCompleteForLs(doc);
                    } else {
                        this.doNotifyDidChangePartial(doc, event, project);
                        this.doNotifyDidChangePartialForLs(doc, event);
                    }

                }
            }
        }
    }

    public void notifyDidChange(@NotNull VirtualFile file) {
        if (!isDisabled() && file.isValid()) {
            if (this.checkFileStatus(file)) {
                Document doc = ReadAction.compute(() -> {
                    return FileDocumentManager.getInstance().getDocument(file);
                });
                if (doc != null) {
                    Project project = guessProject(file);
                    if (null == project || project.isDisposed()) {
                        return;
                    }
                    this.doNotifyDidChangeComplete(doc, project);
                }
            }
        }
    }

    public void notifyDidSave(@NotNull VirtualFile file) {
        Project project = guessProject(file);
        if (null == project || project.isDisposed()) {
            return;
        }
        Content content = FileUtil.buildContent(project, file);
        if (null == content) {
            return;
        }
        ExecuteSave excuteSave = new ExecuteSave();
        ExecuteSaveParams params = new ExecuteSaveParams();
       // params.setContent(content);
        params.setUri(file.getUrl());
        excuteSave.setParams(params);
        RcsWebSocketManager.INSTANCE.sendRequest(excuteSave, new TypeToken<Boolean>(){}, project, 1000);
    }
    private void doNotifyDidOpen(@NotNull VirtualFile file, @NotNull LanguageInfo language, Project project, boolean didFocus) {
        int currentId = this.agentProcessId.get();
        Integer storedId = KEY_FILE_OPENED.get(file);
        if (storedId == null || storedId != currentId) {
            KEY_FILE_OPENED.set(file, currentId);
            ExecuteOpen excuteOpen = new ExecuteOpen();
            ExecuteOpenParams params = new ExecuteOpenParams();
            params.setContent(FileUtil.buildContent(project, file));
            params.setUri(file.getUrl());
            FileOpenOptions options = new FileOpenOptions();
            options.setDidShow(true);
            options.setDidFocus(didFocus);
            params.setOptions(options);
            excuteOpen.setParams(params);
            project = null == project ? ApplicationUtil.findCurrentProject() : project;
            RcsWebSocketManager.INSTANCE.sendNotification(excuteOpen, project);

            openTopicEditorFile(project, file);
        }

    }
    private void doNotifyDidOpenForLs(@NotNull VirtualFile file, @NotNull LanguageInfo language) {
        int currentId = this.agentProcessId.get();
        Integer storedId = (Integer)KEY_FILE_OPENED.get(file);
        if (storedId == null || storedId != currentId) {
            KEY_FILE_OPENED.set(file, currentId);
            TextDocumentItem textDocument = (TextDocumentItem)ReadAction.compute(() -> {
                return LSPUtil.createTextDocument(file, language);
            });
            if (textDocument == null) {
                LoggerUtil.INSTANCE.logWarn(LOG, "failed to create text document for newly opened file: " + file);
            } else {
                this.notify(new DidOpenCommand(textDocument));
            }
        }

    }

    /**
     * 文件内容更新
     * @param doc
     */
    private void doNotifyDidChangeComplete(@NotNull Document doc, Project project) {
        VersionedTextDocumentIdentifier textDocument = LSPUtil.versionedIdentifier(doc);
        if (textDocument != null) {
            UpdateFile updateFile = new UpdateFile();
            UpdateFileParams params = new UpdateFileParams();
            params.setUri(textDocument.getUri().getUri());
            VirtualFile file = FileDocumentManager.getInstance().getFile(doc);
            Content content = FileUtil.buildContent(project, file);
            params.setContent(content);
            updateFile.setParams(params);
            RcsWebSocketManager.INSTANCE.sendNotification(updateFile, project);
            //this.notify(new DidChangeCommand(textDocument, new TextDocumentContentChangeEvent(doc.getText())));
        }
    }

    /**
     * 文件内容变更
     * @param doc
     * @param event
     */
    private void doNotifyDidChangePartial(@NotNull Document doc, @NotNull DocumentEvent event, Project project) {
        VersionedTextDocumentIdentifier textDocument = LSPUtil.versionedIdentifier(doc);
        if (textDocument != null) {
            Position startPosition = LSPUtil.position(doc, event.getOffset());
            Position endPosition = LSPUtil.shiftByText(LSPUtil.position(doc, event.getOffset()), event.getOldFragment());
            String fragment = event.getNewFragment().toString();
            Range range = new Range();
            range.setStart(startPosition);
            range.setEnd(endPosition);
            Change change = new Change();
            change.setRange(range); //本次变化的范围
            change.setText(fragment); //本次变化的新内容
            Changes changes = new Changes();
            changes.setChanges(Lists.newArrayList(change));
            changes.setVersion(textDocument.getVersion());
            UpdateContent updateContent = new UpdateContent();
            UpdateContentParams params = new UpdateContentParams();
            params.setUri(textDocument.getUri().getUri());
            params.setChanges(changes);
            updateContent.setParams(params);
            RcsWebSocketManager.INSTANCE.sendNotification(updateContent, project);

            if (event.getOldLength() > 0 && event.getNewLength() <= 0) {
                //删除场景，这个场景光标事件监听不到，这里上报一下
                UpdateRanges ranges = new UpdateRanges();
                UpdateRangesParams rangesParams = new UpdateRangesParams();
                rangesParams.setUri(textDocument.getUri().getUri());
                List<Range> rangeList = new ArrayList<>();
                Range caretRange = new Range();
                caretRange.setStart(startPosition);
                caretRange.setEnd(startPosition);
                rangeList.add(range);
                rangesParams.setRanges(rangeList);
                ranges.setParams(rangesParams);
                RcsWebSocketManager.INSTANCE.sendNotification(ranges, project);
            }
        }
    }


    private void doNotifyDidChangeCompleteForLs(@NotNull Document doc) {
        VersionedTextDocumentIdentifier textDocument = LSPUtil.versionedIdentifier(doc);
        if (textDocument != null) {
            this.notify(new DidChangeCommand(textDocument, new TextDocumentContentChangeEvent(doc.getText())));
        }
    }

    private void doNotifyDidChangePartialForLs(@NotNull Document doc, @NotNull DocumentEvent event) {
        VersionedTextDocumentIdentifier textDocument = LSPUtil.versionedIdentifier(doc);
        if (textDocument != null) {
            Position startPosition = LSPUtil.position(doc, event.getOffset());
            Position endPosition = LSPUtil.shiftByText(startPosition, event.getOldFragment());
            String fragment = event.getNewFragment().toString();
            Range range = new Range();
            range.setStart(startPosition);
            range.setEnd(endPosition);
            this.notify(new DidChangeCommand(textDocument, new TextDocumentContentChangeEvent(fragment, range)));
            /*ThreadUtil.execute(() -> {
                try {
                    realTimeChangeMetric(doc, event);
                } catch (Exception e) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "realTimeChangeMetric error", e);
                }
            });*/
        }
    }
    private void realTimeChangeMetric(@NotNull Document doc, @NotNull DocumentEvent event) {
        int changeLength;
        String changeType = "";
        Position endPosition;
        int changedLineCount;
        Position startPosition = LSPUtil.position(doc, event.getOffset());
        VirtualFile file = FileDocumentManager.getInstance().getFile(doc);
        CommandProcessor commandProcessor = CommandProcessor.getInstance();
        // 检查是否在命令执行中
        String currentCommand = commandProcessor.getCurrentCommandName();

        if (event.getOldLength() > 0) {
            //说明存在删除操作：1.纯删除 2.删除+新增(替换场景)
            changeLength = event.getOldLength();
            endPosition = LSPUtil.shiftByText(LSPUtil.position(doc, event.getOffset()), event.getOldFragment());
            changeType = "DELETE";

            changedLineCount = endPosition.getLine() - startPosition.getLine() + 1;
            JsonArray changedLines = new JsonArray();
            for (int i = startPosition.getLine(); i <= endPosition.getLine(); i++) {
                changedLines.add(i);
            }
            notifyRealChange(changedLineCount, changeLength, changeType, changedLines, currentCommand, file);
        }
        if (event.getNewLength() > 0) {
            //说明存在新增操作：1.纯新增 2.删除+新增(替换场景)
            //ADD 场景
            changeLength = event.getNewLength();
            endPosition = LSPUtil.shiftByText(LSPUtil.position(doc, event.getOffset()), event.getNewFragment());
            changeType = "ADD";

            changedLineCount = endPosition.getLine() - startPosition.getLine() + 1;
            JsonArray changedLines = new JsonArray();
            for (int i = startPosition.getLine(); i <= endPosition.getLine(); i++) {
                changedLines.add(i);
            }
            notifyRealChange(changedLineCount, changeLength, changeType, changedLines, currentCommand, file);
        }
    }
    private void notifyRealChange(int changedLineCount, int changeLength, String changeType,
        JsonArray changedLines, String currentCommand, VirtualFile file) {
        JsonObject value = new JsonObject();
        value.addProperty("changedLineCount", changedLineCount);
        value.addProperty("changeLength", changeLength);
        value.addProperty("changeType", changeType);
        value.addProperty("timestamp", System.currentTimeMillis());
        if (null != file) {
            value.addProperty("filePath", file.getPath());
        }
        value.add("changedLines", changedLines);
        value.addProperty("triggerMethod", currentCommand);

        CommonMetricsCommand command = new CommonMetricsCommand();
        command.setMetrics_scene(MetricsSceneEnum.EDITOR.getName());
        command.setMetrics_key(MetricsKeyEnum.EDIT_REAL_TIME_CHANGE.getName());
        command.setMetrics_value(value);
        this.notify(command);
    }
    private @Nullable LanguageInfo findLanguageInfo(@NotNull Project project, @NotNull VirtualFile file) {
        return !isDisabled() && file.isValid() ? (LanguageInfo)ReadAction.compute(() -> {
            return LSPUtil.findLanguage(project, file);
        }) : null;
    }

    private boolean checkFileStatus(@NotNull VirtualFile file) {
        Integer storedId = KEY_FILE_OPENED.get(file);
        Project guessedProject = guessProject(file);
        if (guessedProject == null) {
            LoggerUtil.INSTANCE.logWarn(LOG, "closeTopicEditorFile file" + file.getUrl());
            return false;
        }
        FileEditorManager source = FileEditorManager.getInstance(guessedProject);
        if (storedId == null) {
            if (source.isFileOpen(file)) {
                notifyDidOpen(guessedProject, file);
                return true;
            }
            return false;
        } else if (storedId != this.agentProcessId.get()) {
            if (guessedProject != null) {
                LanguageInfo languageInfo = ReadAction.compute(() -> {
                    return LSPUtil.findLanguage(guessedProject, file);
                });

                this.doNotifyDidOpen(file, languageInfo, null, false);
                EditorTabGroupUtil.addTab(getEditorWindowList(source), guessedProject, file);
                this.doNotifyDidOpenForLs(file, languageInfo);
            }

            return false;
        } else {
            return true;
        }
    }

    private static @Nullable Project guessProject(@NotNull VirtualFile file) {
        if (file instanceof LightVirtualFile) {
            FileViewProvider cachedPsi = FileDocumentManager.getInstance().findCachedPsiInAnyProject(file);
            if (cachedPsi != null) {
                Project project = cachedPsi.getManager().getProject();
                if (!project.isDisposed()) {
                    return project;
                }
            }
        }
        // 避免在EDT中执行慢操作，直接返回null或使用更轻量级的方法
        if (ApplicationManager.getApplication().isDispatchThread()) {
            // 在EDT线程中，不执行慢操作，直接返回null
            LoggerUtil.INSTANCE.logDebug(LOG, "Skip guessProject on EDT to avoid slow operations");
            return null;
        }
        AtomicReference<Project> projectRef = new AtomicReference<>();
        CountDownLatch latch = new CountDownLatch(1);
        ApplicationManager.getApplication().runReadAction(() -> {
            try {
                Project project = ProjectUtil.guessProjectForContentFile(file);
                if (null == project) {
                    project = ApplicationUtil.findCurrentProject();
                }
                projectRef.set(project);
            } catch (Exception e) {
                LoggerUtil.INSTANCE.logWarn(LOG, "guessProject runReadAction err, msg = {}", e);
            } finally {
                latch.countDown();
            }
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return projectRef.get();
    }

    public static boolean isDisabled() {
        return false;
    }

    public void notifyCancelCache(@NotNull Editor editor) {
        if (!isDisabled()) {
            try {
                VirtualFile file = FileDocumentManager.getInstance().getFile(editor.getDocument());
                if (file == null) {
                    return;
                }
                this.notify(new CancelCacheCommand(VirtualFileUri.from(file)));
            } catch (Exception e) {
                LoggerUtil.INSTANCE.logWarn(LOG, "notifyCancelCache err, msg = {}", e);
            }
        }
    }

    private List<EditorWindow> getEditorWindowList(FileEditorManager fileEditorManager) {
        List<EditorWindow> activeWindowList = null;
        if (fileEditorManager instanceof FileEditorManagerEx ex) {
            activeWindowList = Arrays.asList(ex.getWindows());
        } else if (fileEditorManager instanceof FileEditorManagerImpl impl) {
            activeWindowList =  Arrays.asList(impl.getWindows());
        }
        return activeWindowList;
    }


    @RequiresBackgroundThread
    void afterAgentRestart() {
        if (!isDisabled()) {
            this.agentProcessId.incrementAndGet();
            Project[] var1 = ProjectManager.getInstance().getOpenProjects();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                Project project = var1[var3];
                if (!project.isDisposed()) {
                    VirtualFile[] var5 = FileEditorManager.getInstance(project).getOpenFiles();
                    int var6 = var5.length;

                    for(int var7 = 0; var7 < var6; ++var7) {
                        VirtualFile file = var5[var7];
                        this.notifyDidOpen(project, file);
                    }
                }
            }
        }
    }
    public void notify(@NotNull JsonRpcNotification notification) {
        if (!isDisabled()) {
            CodeWizAgentProcessService.getInstance().executeNotification(notification);
        }
    }
    public boolean isAgentRunning() {
        return CodeWizAgentProcessService.getInstance().isRunning();
    }
    public synchronized String getLifecycleId() {
        if (null == lifecycle_id) {
            lifecycle_id = UUID.randomUUID().toString();
        }
        return lifecycle_id;
    }
}


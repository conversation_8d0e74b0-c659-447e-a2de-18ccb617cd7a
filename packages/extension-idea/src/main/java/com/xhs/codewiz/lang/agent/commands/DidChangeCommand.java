package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.editor.request.VersionedTextDocumentIdentifier;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public final class DidChangeCommand implements JsonRpcNotification {
    @SerializedName("textDocument")
    private final @NotNull VersionedTextDocumentIdentifier textDocument;
    @SerializedName("contentChanges")
    private final List<TextDocumentContentChangeEvent> contentChanges;

    public @NotNull String getCommandName() {
        return "textDocument/didChange";
    }

    public DidChangeCommand(@NotNull VersionedTextDocumentIdentifier textDocument, @NotNull TextDocumentContentChangeEvent event) {
        this(textDocument, List.of(event));
    }

    public @NotNull VersionedTextDocumentIdentifier getTextDocument() {
        return this.textDocument;
    }

    public List<TextDocumentContentChangeEvent> getContentChanges() {
        return this.contentChanges;
    }

    public DidChangeCommand(@NotNull VersionedTextDocumentIdentifier textDocument, List<TextDocumentContentChangeEvent> contentChanges) {
        this.textDocument = textDocument;
        this.contentChanges = contentChanges;
    }
}


package com.xhs.codewiz.utils.reference.inheritance;

import com.intellij.psi.PsiClass;
import com.intellij.psi.PsiElement;
import com.xhs.codewiz.lang.Features;
import com.xhs.codewiz.utils.CodeWizPsiUtil;
import com.xhs.codewiz.utils.reference.LazyLoader;
import java.util.Map;

public class InheritanceUtil {
  private static final Map<String, Filter> FILTERS = Map.of("default", new DefaultFilter(), "java", new JavaFilter());
  private static final Map<String, LazyLoader<InheritanceResolver>> RESOLVERS = Map.of("java", new LazyLoader<InheritanceResolver>(() -> new JavaInheritanceResolver()));

  public static PsiElement[] getSupers(PsiElement[] elements) {
    if (elements.length == 0) {
      return new PsiElement[0];
    }
    String languageId = elements[0].getLanguage().getID().toLowerCase();
    LazyLoader<InheritanceResolver> resolver = RESOLVERS.get(languageId);
    if (resolver == null) {
      return new PsiElement[0];
    }
    Filter filter = FILTERS.getOrDefault(languageId, FILTERS.get("default"));
    return resolver.get().getSupers(elements, filter);
  }

  private static class JavaFilter
      extends DefaultFilter {
    private JavaFilter() {
    }

    @Override
    public boolean filterClass(PsiClass aClass) {
      String qualifiedName = aClass.getQualifiedName();
      if (qualifiedName == null) {
        return true;
      }
      return CodeWizPsiUtil.isCommonType(qualifiedName);
    }
  }

  private static class DefaultFilter
      implements Filter {
    private DefaultFilter() {
    }

    @Override
    public boolean filterClass(PsiClass aClass) {
      return false;
    }

    @Override
    public boolean filterDepth(int depth) {
      return depth > Features.QUERY_REF_FROM_IDE_INHERIT_MAX_DEPTH.intValue();
    }

    @Override
    public boolean filterCount(int count) {
      return count > Features.QUERY_REF_FROM_IDE_INHERIT_MAX_COUNT.intValue();
    }
  }
}
package com.xhs.codewiz.type.extension;

import java.util.*;

/**
 * 第三方插件信息
 */
public class Extension {
    private Kind extensionKind;

    /** 插件 ID */
    private String id;

    /** 插件导出的 API */
    private Map<String, Object> api;

    /** 插件是否处于活动状态 */
    private Boolean isActive;

    public Kind getExtensionKind() {
        return extensionKind;
    }

    public void setExtensionKind(Kind extensionKind) {
        this.extensionKind = extensionKind;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Map<String, Object> getApi() {
        return api;
    }

    public void setApi(Map<String, Object> api) {
        this.api = api;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

}

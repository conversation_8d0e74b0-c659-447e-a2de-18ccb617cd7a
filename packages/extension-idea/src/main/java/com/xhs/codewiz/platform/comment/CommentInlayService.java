package com.xhs.codewiz.platform.comment;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.Inlay;
import com.intellij.openapi.editor.ScrollType;
import com.intellij.openapi.editor.markup.MarkupModel;
import com.intellij.openapi.editor.markup.RangeHighlighter;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.platform.enums.CommentStatus;
import com.xhs.codewiz.platform.model.ShowDiffRequest;
import com.xhs.codewiz.utils.LoggerUtil;
import java.awt.Rectangle;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 评论内联显示服务
 *
 * <AUTHOR>
 */
@Service
public final class CommentInlayService implements Disposable {

    // Diff页面评论管理
    private static List<Comment> currentDiffComments = new ArrayList<>();
    private static Editor currentDiffEditor = null;

    public static CommentInlayService getInstance() {
        return ApplicationManager.getApplication().getService(CommentInlayService.class);
    }

    private static Logger logger = Logger.getInstance(CommentInlayService.class);

    /**
     * 添加评论内联显示
     */
    public void addCommentInlay(Comment comment, Editor editor) {
        if (comment == null) {
            return;
        }

        CommentInlayPanel commentInlay = new CommentInlayPanel(editor, comment);
        if (comment.getCommentInlay() != null) {
            commentInlay.setShowInlay(comment.getCommentInlay().isShowInlay());
        }

        Document document = editor.getDocument();

        int lineCount = document.getLineCount();
        int displayLine = comment.getStartLine();
        if (displayLine < 0 || displayLine >= lineCount) {
            LoggerUtil.INSTANCE.logWarn(logger, "Invalid line number: " + displayLine, null);
            return;
        }

        commentInlay.createInlay(document.getLineStartOffset(displayLine));
        editor.getContentComponent().add(commentInlay);

        comment.setCommentInlay(commentInlay);
        comment.setEditor(editor);
    }

    public void refreshComment(String noteId, CommentStatus status) {
        if (CollectionUtils.isEmpty(currentDiffComments)) {
            return;
        }
        for (Comment current : currentDiffComments) {
            if (StringUtils.equals(noteId, current.getNoteId())) {
                // 更新状态
                if (!current.getStatus().equals(status)) {
                    current.setStatus(status);
                    CommentCacheService.getInstance().updateCommentStatus(noteId, status);
                    if (null != current.getCommentInlay()) {
                        current.getCommentInlay().refresh(current.getEditor().getProject());
                    }
                }
                break;
            }
        }
    }
    /**
     * 导航到下一个评论
     */
    public void navigateToNext(Comment comment) {
        // 检查是否是diff页面的评论
        if (isDiffComment(comment)) {
            navigateToDiffComment(comment, true);
            return;
        }
        navigateToComment(comment, CommentPersistState.getInstance().getNextComment(comment.getNoteId().toString()));
    }

    /**
     * 导航到上一个评论
     */
    public void navigateToPrevious(Comment comment) {
        // 检查是否是diff页面的评论
        if (isDiffComment(comment)) {
            navigateToDiffComment(comment, false);
            return;
        }
        navigateToComment(comment, CommentPersistState.getInstance().getPreviousComment(comment.getNoteId().toString()));
    }

    private void navigateToComment(Comment comment, Comment targetComment) {
        Editor curEditor = comment.getEditor();
        CommentInlayPanel commentInlay = comment.getCommentInlay();
        if (commentInlay == null || curEditor == null || targetComment == null) {
            return;
        }

        Editor nextEditor = targetComment.getEditor();
        CommentInlayPanel nextCommentInlay = targetComment.getCommentInlay();
        if (nextEditor == null) {
            return;
        }

        if (nextCommentInlay == null) {
            addCommentInlay(targetComment, nextEditor);
            nextCommentInlay = targetComment.getCommentInlay();
        }

        // 切换到目标文件
        Project project = curEditor.getProject();
        if (curEditor != nextEditor && project != null) {
            VirtualFile file = FileDocumentManager.getInstance().getFile(nextEditor.getDocument());
            if (file != null) {
                FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);
                FileEditor[] openEditors = fileEditorManager.getEditors(file);
                if (openEditors.length > 0) {
                    fileEditorManager.openFile(file, true, true);
                } else {
                    fileEditorManager.openFile(file, true);
                }
            }
        }

        int targetOffset = nextCommentInlay.getInlay().getOffset();

        // 显示内联面板
        if (!nextCommentInlay.isShowInlay()) {
            nextCommentInlay.toggleVisibility();
        }

        // 滚动到目标位置
        nextEditor.getScrollingModel().scrollTo(nextEditor.offsetToLogicalPosition(targetOffset), ScrollType.CENTER);
        nextCommentInlay.requestFocusInWindow();
    }

    /**
     * 移除评论内联显示
     */
    public void removeCommentInlay(Editor editor, Comment comment) {
        CommentInlayPanel commentInlay = comment.getCommentInlay();
        if (commentInlay == null) {
            return;
        }

        Inlay<?> inlay = commentInlay.getInlay();
        if (inlay != null) {
            inlay.dispose();
        }
        commentInlay.setInlay(null);

        editor.getContentComponent().remove(commentInlay);
        editor.getComponent().revalidate();
        editor.getComponent().repaint();
        editor.getContentComponent().revalidate();
        editor.getContentComponent().repaint();
    }

    /**
     * 移除所有评论内联显示
     */
    public void removeAllCommentInlays() {
        Map<String, List<Comment>> filePathMap = CommentPersistState.getInstance().getAllFilePathMap();
        for (List<Comment> comments : filePathMap.values()) {
            for (Comment comment : comments) {
                removeCommentInlay(comment.getEditor(), comment);
            }
        }
    }

    /**
     * 为diff页面添加评论悬浮显示
     *
     * @param editor       diff页面右侧的编辑器
     * @param diffComments diff评论列表
     * @param targetNoteId 目标noteId，用于确定要显示的评论（可以为null）
     */
    public void addDiffComments(Editor editor, List<ShowDiffRequest.DiffComment> diffComments, String targetNoteId)
            throws InterruptedException {
        if (editor == null || diffComments == null || diffComments.isEmpty()) {
            return;
        }

        // 清除之前的diff评论状态
        clearDiffCommentsState();

        // 设置当前diff编辑器
        currentDiffEditor = editor;

        // 批量查询diff评论状态
        List<String> diffCommentIds = new ArrayList<>();
        for (ShowDiffRequest.DiffComment diffComment : diffComments) {
            diffCommentIds.add(diffComment.getNoteId());
        }

        Map<Long, CommentStatus> statusMap = new HashMap<>();
        if (!diffCommentIds.isEmpty()) {
            /*CommentStatusBatchQueryResponse response = CodeWizAgentLspService.batchQueryCommentStatus(diffCommentIds);
            if (response != null && response.isSuccess() && response.getData() != null) {
                for (CommentStatusBatchQueryResponse.CommentStatusInfo statusInfo : response.getData()) {
                    CommentStatus status = CommentStatus.fromValue(statusInfo.getVote());
                    statusMap.put(statusInfo.getId(), status);
                }
            }*/
        }

        // 转换所有评论为Comment对象并保存
        for (ShowDiffRequest.DiffComment diffComment : diffComments) {
            Comment comment = createCommentFromDiffComment(diffComment, statusMap);
            currentDiffComments.add(comment);
        }

        // 显示所有评论，默认展开
        for (Comment comment : currentDiffComments) {
            showDiffComment(comment);
        }

        // 如果指定了targetNoteId，滚动到目标评论位置
        if (targetNoteId != null) {
            // 移除立即滚动逻辑，由调用方控制滚动时机
            // ApplicationManager.getApplication().invokeLater(() -> {
            //     for (Comment comment : currentDiffComments) {
            //         if (Objects.equals(comment.getNoteId().toString(), targetNoteId)) {
            //             scrollToTargetComment(comment);
            //             break;
            //         }
            //     }
            // });
        }
    }

    /**
     * 将DiffComment转换为Comment对象
     */
    private Comment createCommentFromDiffComment(ShowDiffRequest.DiffComment diffComment, Map<Long, CommentStatus> statusMap) {
        Comment comment = new Comment();
        comment.setNoteId(diffComment.getNoteId());
        comment.setNoteContent(diffComment.getNoteContent());
        comment.setStartLine(diffComment.getStartLine() - 1);
        comment.setEndLine(diffComment.getEndLine() - 1);
        comment.setCategory("Diff评论");
        comment.setLevel(1); // 默认级别
        comment.setFilePath("diff"); // diff页面标识
        comment.setIndex(diffComment.getIndex());
        comment.setCount(diffComment.getSize());
        comment.setChannel(diffComment.getChannel());
        comment.setCollectId(diffComment.getCollectId());

        // 从批量查询结果获取评论状态
        //CommentStatus status = statusMap.getOrDefault(diffComment.getNoteId(), CommentStatus.UNKNOWN);
        comment.setStatus(diffComment.getStatus());

        // 同步DiffComment的状态
        //diffComment.setStatus(status);

        // // 从缓存加载评论状态
        // CommentCacheService cacheService = CommentCacheService.getInstance();
        // CommentStatus cachedStatus = cacheService.getCommentStatus(diffComment.getNoteId());
        // comment.setStatus(cachedStatus);
        // // 同步DiffComment的状态
        // diffComment.setStatus(cachedStatus);
        // // 缓存评论对象
        // cacheService.cacheComment(comment);

        return comment;
    }

    /**
     * 为diff评论添加gutter图标
     */
    private void addDiffGutter(Comment comment, Editor editor) {
        if (comment == null || editor == null) {
            return;
        }

        ApplicationManager.getApplication().runReadAction(() -> {
            int displayLine = comment.getStartLine();
            CommentGutterIconRenderer commentGutterIcon = new CommentGutterIconRenderer(displayLine, () -> {
                if (comment.getCommentInlay() == null) {
                    addDiffCommentInlayPanel(comment, editor);
                }
                comment.getCommentInlay().toggleVisibility();
            });

            MarkupModel markupModel = editor.getMarkupModel();
            RangeHighlighter highlighter = markupModel.addLineHighlighter(null, displayLine, 0);
            highlighter.setGutterIconRenderer(commentGutterIcon);

            // 在EDT上设置状态
            ApplicationManager.getApplication().invokeLater(() -> {
                comment.setGutterIconRendererHighlighter(highlighter);
                comment.setEditor(editor);
            });
        });
    }

    /**
     * 为diff评论添加内联面板
     */
    private void addDiffCommentInlayPanel(Comment comment, Editor editor) {
        if (comment == null || editor == null) {
            return;
        }

        ApplicationManager.getApplication().runReadAction(() -> {
            CommentInlayPanel commentInlay = new CommentInlayPanel(editor, comment);
            if (comment.getCommentInlay() != null) {
                commentInlay.setShowInlay(comment.getCommentInlay().isShowInlay());
            }

            Document document = editor.getDocument();
            int lineCount = document.getLineCount();
            int displayLine = comment.getStartLine();

            if (displayLine < 0 || displayLine >= lineCount) {
                LoggerUtil.INSTANCE.logWarn(logger, "Invalid line number: " + displayLine, null);
                return;
            }

            commentInlay.createInlay(document.getLineStartOffset(displayLine));

            // 在EDT上执行UI操作
            ApplicationManager.getApplication().invokeLater(() -> {
                editor.getContentComponent().add(commentInlay);
                comment.setCommentInlay(commentInlay);
                comment.setEditor(editor);

                // 默认显示评论
                commentInlay.setShowInlay(true);
                commentInlay.showInlay();
            });
        });
    }

    /**
     * 清除diff评论状态
     */
    private void clearDiffCommentsState() {
        if (CollectionUtils.isNotEmpty(currentDiffComments)) {
            for (Comment comment : currentDiffComments) {
                if (null != comment.getEditor()) {
                    try {
                        if (comment.getCommentInlay() != null) {
                            comment.getEditor().getContentComponent().remove(comment.getCommentInlay());
                            comment.getCommentInlay().dispose();
                        }
                    } catch (Throwable e) {
                        LoggerUtil.INSTANCE.logWarn(logger, "Failed to remove comment inlay", e);
                    }
                    try {
                        if (null != comment.getGutterIconRendererHighlighter()) {
                            comment.getEditor().getMarkupModel().removeHighlighter(comment.getGutterIconRendererHighlighter());
                        }
                    } catch (Throwable e) {
                        LoggerUtil.INSTANCE.logWarn(logger, "Failed to remove gutter icon renderer highlighter", e);
                    }
                }
            }
            currentDiffComments.clear();
            currentDiffEditor = null;
        }
    }

    /**
     * 判断是否是diff页面的评论
     */
    private boolean isDiffComment(Comment comment) {
        return comment != null &&
                "diff".equals(comment.getFilePath()) &&
                currentDiffComments.contains(comment);
    }

    /**
     * 在diff页面评论之间导航
     */
    private void navigateToDiffComment(Comment currentComment, boolean isNext) {
        if (currentDiffComments.isEmpty() || currentDiffEditor == null) {
            return;
        }

        // 找到当前评论的索引
        int currentIndex = -1;
        for (int i = 0; i < currentDiffComments.size(); i++) {
            if (currentDiffComments.get(i).getNoteId().equals(currentComment.getNoteId())) {
                currentIndex = i;
                break;
            }
        }

        if (currentIndex == -1) {
            return;
        }

        // 计算目标索引
        int targetIndex;
        if (isNext) {
            targetIndex = currentIndex + 1;
            if (targetIndex >= currentDiffComments.size()) {
                targetIndex = 0; // 循环到第一个
            }
        } else {
            targetIndex = currentIndex - 1;
            if (targetIndex < 0) {
                targetIndex = currentDiffComments.size() - 1; // 循环到最后一个
            }
        }

        Comment targetComment = currentDiffComments.get(targetIndex);

        // 直接滚动到目标评论（不隐藏当前评论，因为所有评论都应该保持展开）
        if (targetComment.getCommentInlay() != null) {
            ApplicationManager.getApplication().runReadAction(() -> {
                // 滚动到目标评论位置
                Document document = currentDiffEditor.getDocument();
                int lineStartOffset = document.getLineStartOffset(targetComment.getStartLine());
                currentDiffEditor.getScrollingModel().scrollTo(
                        currentDiffEditor.offsetToLogicalPosition(lineStartOffset),
                        ScrollType.CENTER
                );

                // 请求焦点
                ApplicationManager.getApplication().invokeLater(() -> {
                    targetComment.getCommentInlay().requestFocusInWindow();
                });
            });
        } else {
            // 如果目标评论的UI还没创建，先显示它
            showDiffComment(targetComment);
        }
    }

    /**
     * 显示diff评论
     */
    private void showDiffComment(Comment comment) {
        if (comment == null || currentDiffEditor == null) {
            return;
        }

        // 如果评论还没有创建UI组件，先创建
        if (comment.getCommentInlay() == null) {
            addDiffCommentInlayPanel(comment, currentDiffEditor);
        }
        if (comment.getGutterIconRendererHighlighter() == null) {
            addDiffGutter(comment, currentDiffEditor);
        }

        // 显示评论
        CommentInlayPanel commentInlay = comment.getCommentInlay();
        if (commentInlay != null) {
            commentInlay.setShowInlay(true);
            commentInlay.showInlay();

            // 滚动到评论位置
            // Document document = currentDiffEditor.getDocument();
            // int lineStartOffset = document.getLineStartOffset(comment.getStartLine());
            // currentDiffEditor.getScrollingModel().scrollTo(
            //         currentDiffEditor.offsetToLogicalPosition(lineStartOffset),
            //         ScrollType.CENTER
            // );

            // 请求焦点
            commentInlay.requestFocusInWindow();
        }
    }

    /**
     * 滚动到指定的diff评论
     */
    public boolean scrollToDiffComment(String targetNoteId) {
        if (currentDiffEditor == null || currentDiffComments.isEmpty()) {
            return false;
        }

        for (Comment comment : currentDiffComments) {
            if (Objects.equals(comment.getNoteId().toString(), targetNoteId)) {
                return scrollToCommentWithCheck(comment);
            }
        }
        return false;
    }

    /**
     * 带检查的滚动到评论
     */
    private boolean scrollToCommentWithCheck(Comment comment) {
        if (comment == null || currentDiffEditor == null) {
            return false;
        }

        CommentInlayPanel inlayPanel = comment.getCommentInlay();
        if (inlayPanel == null || inlayPanel.getInlay() == null) {
            return false;
        }

        // 检查inlay是否准备好
        Rectangle bounds = inlayPanel.getInlay().getBounds();
        if (bounds == null || bounds.height == 0) {
            return false;
        }

        // 在read action中执行需要读取权限的操作
        ApplicationManager.getApplication().runReadAction(() -> {
            // 记录滚动前的位置
            int beforeScrollY = currentDiffEditor.getScrollingModel().getVerticalScrollOffset();

            Document document = currentDiffEditor.getDocument();
            int lineStartOffset = document.getLineStartOffset(comment.getStartLine());

            // 使用MAKE_VISIBLE替代CENTER，减少滚动冲突
            currentDiffEditor.getScrollingModel().scrollTo(
                    currentDiffEditor.offsetToLogicalPosition(lineStartOffset),
                    ScrollType.MAKE_VISIBLE
            );

            // 检查是否真正滚动了
            ApplicationManager.getApplication().invokeLater(() -> {
                int afterScrollY = currentDiffEditor.getScrollingModel().getVerticalScrollOffset();
                if (Math.abs(afterScrollY - beforeScrollY) > 5) {
                    // 滚动成功，请求焦点
                    inlayPanel.requestFocusInWindow();
                }
            });
        });

        return true;
    }

    /**
     * 滚动到目标评论位置
     */
    private void scrollToTargetComment(Comment comment) {
        if (comment == null || currentDiffEditor == null) {
            return;
        }

        // 滚动到目标评论位置
        if (comment.getCommentInlay() != null) {
            ApplicationManager.getApplication().runReadAction(() -> {
                Document document = currentDiffEditor.getDocument();
                int lineStartOffset = document.getLineStartOffset(comment.getStartLine());
                currentDiffEditor.getScrollingModel().scrollTo(
                        currentDiffEditor.offsetToLogicalPosition(lineStartOffset),
                        ScrollType.CENTER
                );

                // 请求焦点
                ApplicationManager.getApplication().invokeLater(() -> {
                    comment.getCommentInlay().requestFocusInWindow();
                });
            });
        }
    }


    @Override
    public void dispose() {
        removeAllCommentInlays();
        clearDiffCommentsState();
    }
} 
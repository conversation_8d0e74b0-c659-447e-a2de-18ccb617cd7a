package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.Session;
/**
 * 创建 Session
 */
public class CreateSession {
    private String schemaProtocol = "platform.create.session";
    private CreateSessionParams params;

    public CreateSessionParams getParams() {
        return params;
    }
    public void setParams(CreateSessionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateSessionParams {
        /** Session Provider ID */
        private String provider;
        private Session session;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public Session getSession() {
            return session;
        }
        public void setSession(Session session) {
            this.session = session;
        }
    }
}

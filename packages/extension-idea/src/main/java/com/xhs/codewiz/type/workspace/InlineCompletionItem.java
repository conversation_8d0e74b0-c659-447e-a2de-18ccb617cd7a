package com.xhs.codewiz.type.workspace;

import java.util.*;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.content.TextOrSnippet;
import com.xhs.codewiz.type.global.Command;

/**
 * 内联补全项
 */
public class InlineCompletionItem {
    /** 插入的文本或代码片段 */
    private TextOrSnippet insertText;

    /** 是否自动补全括号对，默认为 false */
    private Boolean completeBracketPairs;

    private Range range;

    /** 过滤文本，用于控制内联补全的过滤 */
    private String filterText;

    /** 在插入内联补全时执行的命令 */
    private Command command;

    public TextOrSnippet getInsertText() {
        return insertText;
    }

    public void setInsertText(TextOrSnippet insertText) {
        this.insertText = insertText;
    }

    public Boolean getCompleteBracketPairs() {
        return completeBracketPairs;
    }

    public void setCompleteBracketPairs(Boolean completeBracketPairs) {
        this.completeBracketPairs = completeBracketPairs;
    }

    public Range getRange() {
        return range;
    }

    public void setRange(Range range) {
        this.range = range;
    }

    public String getFilterText() {
        return filterText;
    }

    public void setFilterText(String filterText) {
        this.filterText = filterText;
    }

    public Command getCommand() {
        return command;
    }

    public void setCommand(Command command) {
        this.command = command;
    }

}

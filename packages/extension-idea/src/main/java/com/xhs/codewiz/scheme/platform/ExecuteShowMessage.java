package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.global.Severity;
/**
 * 显示消息
 */
public class ExecuteShowMessage {
    private String schemaProtocol = "platform.execute.showmessage";
    private ExecuteShowMessageParams params;

    public ExecuteShowMessageParams getParams() {
        return params;
    }
    public void setParams(ExecuteShowMessageParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteShowMessageParams {
        private ExecuteShowMessageParamsOptions options; // optional
        private Severity type;
        private ExecuteShowMessageParamsMessage message;
    
        public ExecuteShowMessageParamsOptions getOptions() {
            return options;
        }
        public void setOptions(ExecuteShowMessageParamsOptions options) {
            this.options = options;
        }
        public Severity getType() {
            return type;
        }
        public void setType(Severity type) {
            this.type = type;
        }
        public ExecuteShowMessageParamsMessage getMessage() {
            return message;
        }
        public void setMessage(ExecuteShowMessageParamsMessage message) {
            this.message = message;
        }
    }

        public static class ExecuteShowMessageParamsOptions {
        /** 详细信息，只在modal下可用 */
        private String detail; // optional
        /** 是否为模态对话框 */
        private Boolean modal; // optional
    
        public String getDetail() {
            return detail;
        }
        public void setDetail(String detail) {
            this.detail = detail;
        }
        public Boolean getModal() {
            return modal;
        }
        public void setModal(Boolean modal) {
            this.modal = modal;
        }
    }

        public static class ExecuteShowMessageParamsMessage {
        /** 操作选项 */
        private List<ExecuteShowMessageParamsMessageOptions> options; // optional
        /** 消息标题 */
        private String title;
    
        public List<ExecuteShowMessageParamsMessageOptions> getOptions() {
            return options;
        }
        public void setOptions(List<ExecuteShowMessageParamsMessageOptions> options) {
            this.options = options;
        }
        public String getTitle() {
            return title;
        }
        public void setTitle(String title) {
            this.title = title;
        }
    }

        public static class ExecuteShowMessageParamsMessageOptions {
        /** 是否为关闭操作 */
        private Boolean isCloseAffordance; // optional
        /** 操作选项标题 */
        private String title;
    
        public Boolean getIsCloseAffordance() {
            return isCloseAffordance;
        }
        public void setIsCloseAffordance(Boolean isCloseAffordance) {
            this.isCloseAffordance = isCloseAffordance;
        }
        public String getTitle() {
            return title;
        }
        public void setTitle(String title) {
            this.title = title;
        }
    }
}

package com.xhs.codewiz.client.websocket;

import com.intellij.openapi.diagnostic.Logger;
import javax.websocket.MessageHandler;
import org.eclipse.lsp4j.jsonrpc.MessageConsumer;
import org.eclipse.lsp4j.jsonrpc.MessageIssueException;
import org.eclipse.lsp4j.jsonrpc.MessageIssueHandler;
import org.eclipse.lsp4j.jsonrpc.json.MessageJsonHandler;
import org.eclipse.lsp4j.jsonrpc.messages.Message;

public class RcsWebSocketMessageHandler implements MessageHandler.Partial<String>, MessageHandler.Whole<String> {
  private static final Logger logger = Logger.getInstance(RcsWebSocketMessageHandler.class);
  private final MessageConsumer callback;
  private final MessageJsonHandler jsonHandler;
  private final MessageIssueHandler issueHandler;
  private StringBuilder partialMessage;

  public RcsWebSocketMessageHandler(MessageConsumer callback, MessageJsonHandler jsonHandler, MessageIssueHandler issueHandler) {
    this.callback = callback;
    this.jsonHandler = jsonHandler;
    this.issueHandler = issueHandler;
  }


  public void onMessage(String content, boolean lastChunk) {
    try {
      if (lastChunk) {
        String wholeMessage = content;
        if (partialMessage != null) {
          partialMessage.append(content);
          wholeMessage = partialMessage.toString();
          partialMessage = null;
        }
        Message message = this.jsonHandler.parseMessage(wholeMessage);
        this.callback.consume(message);
      } else {
        if (partialMessage == null) {
          partialMessage = new StringBuilder();
        }
        partialMessage.append(content);
      }
    } catch (MessageIssueException var3) {
      logger.warn(String.format("[WebSocket] Failed to parse message: %s, MessageIssueException msg = %s", content, var3.getMessage()));
      this.issueHandler.handle(var3.getRpcMessage(), var3.getIssues());
    } catch (Exception e) {
      logger.warn(String.format("[WebSocket] Failed to parse message: %s, errMsg = %s", content, e.getMessage()));
    }
  }


  public void onMessage(String content) {
    try {
      Message message = this.jsonHandler.parseMessage(content);
      this.callback.consume(message);
    } catch (MessageIssueException var3) {
      this.issueHandler.handle(var3.getRpcMessage(), var3.getIssues());
    }
  }
}
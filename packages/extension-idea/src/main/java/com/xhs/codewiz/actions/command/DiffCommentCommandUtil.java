package com.xhs.codewiz.actions.command;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.platform.enums.CommentStatus;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-08-27
 * Description:与其他不同的是，这里有四个预设的command，所以可以直接写死
 */
public class DiffCommentCommandUtil {
    public static String commentTitleGroupKey = "comments/comment/title";
    //key->project, value->type, command
    private static Map<String, Map<CommentStatus, String>> commentActions = new ConcurrentHashMap<>();

    public static void registerDiffCommentCommand(Project project, JsonObject menus, JsonArray commands) {
        if (!menus.has(commentTitleGroupKey)) {
            return;
        }
        JsonArray commentMenu = menus.getAsJsonArray(commentTitleGroupKey);
        if (null == commentMenu || commentMenu.isEmpty()) {
            return;
        }
        Map<CommentStatus, String> projectCommentCommand = commentActions.computeIfAbsent(project.getBasePath(), k -> new HashMap<>());
        for (JsonElement element : commentMenu) {
            if (!(element instanceof JsonObject comment)) {
                continue;
            }
            if (!comment.has("when")) {
                continue;
            }
            String when = comment.get("when").getAsString();
            if (StringUtils.contains(when,CommentStatus.LIKE.getValue())
                && StringUtils.contains(when,CommentStatus.CANCEL.getValue())) {
                //当前处于取消或者点赞状态：允许点踩
                projectCommentCommand.put(CommentStatus.UNLIKE, comment.get("command").getAsString());
                continue;
            }
            if (StringUtils.contains(when,CommentStatus.UNLIKE.getValue())
                && StringUtils.contains(when,CommentStatus.CANCEL.getValue())) {
                //当前处于取消或者点踩状态：允许点赞
                projectCommentCommand.put(CommentStatus.LIKE, comment.get("command").getAsString());
                continue;
            }
            if (StringUtils.contains(when,CommentStatus.LIKE.getValue())) {
                //当前处于点赞，可以取消点赞
                projectCommentCommand.put(CommentStatus.CANCEL_UP, comment.get("command").getAsString());
                continue;
            }
            if (StringUtils.contains(when,CommentStatus.UNLIKE.getValue())) {
                //当前处于点踩，可以取消点踩
                projectCommentCommand.put(CommentStatus.CANCEL_DOWN, comment.get("command").getAsString());
            }
        }
    }
    public static String getCommandByStatus(Project project, CommentStatus status) {
        Map<CommentStatus, String> projectCommentCommand = commentActions.getOrDefault(project.getBasePath(), null);
        if (null == projectCommentCommand) {
            return null;
        }
        return projectCommentCommand.getOrDefault(status, null);
    }
}

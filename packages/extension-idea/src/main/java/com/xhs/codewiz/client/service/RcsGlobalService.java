package com.xhs.codewiz.client.service;

import com.google.gson.reflect.TypeToken;
import com.intellij.ide.BrowserUtil;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.util.io.URLUtil;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.client.model.RemoteServiceMeta;
import com.xhs.codewiz.constant.PluginCommonConstant;
import com.xhs.codewiz.enums.ExecuteCommandEnum;
import com.xhs.codewiz.scheme.global.CreateCommand;
import com.xhs.codewiz.scheme.global.CreateCommand.CreateCommandParams;
import com.xhs.codewiz.scheme.global.ExecuteCommand;
import com.xhs.codewiz.scheme.global.ExecuteCommand.ExecuteCommandParams;
import com.xhs.codewiz.scheme.global.ExecuteOpenExternal;
import com.xhs.codewiz.scheme.global.ReadSecretStorage;
import com.xhs.codewiz.scheme.global.ReadState;
import com.xhs.codewiz.scheme.global.ReadState.ReadStateParams;
import com.xhs.codewiz.scheme.global.UpdateSecretStorage;
import com.xhs.codewiz.scheme.global.UpdateSecretStorage.UpdateSecretStorageParams;
import com.xhs.codewiz.scheme.global.UpdateState;
import com.xhs.codewiz.scheme.global.UpdateState.UpdateStateParams;
import com.xhs.codewiz.type.global.Scope;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LocalStorageUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import groovy.lang.Singleton;
import java.awt.Desktop;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * Author: liukunpeng Date: 2025-07-20 Description:
 */
@Service
@Singleton
public final class RcsGlobalService {
  private static Map<String, Object> stateMap = new ConcurrentHashMap<>();
  private static final Logger LOG = Logger.getInstance(RcsGlobalService.class);
  private static final String GLOBAL_PREFIX = ".rcs-jetbrains.global.";
  //key->commandId,value->channel,params
  private static Map<Project, Map<String, Pair<String, ExecuteCommandParams>>> commandMap = new ConcurrentHashMap<>();

  static {
    String str = LocalStorageUtil.getProperty(PluginCommonConstant.REMOTE_GLOBAL_STATE_KEY);
    if (StringUtils.isBlank(str)) {
      LocalStorageUtil.setProperty(PluginCommonConstant.REMOTE_GLOBAL_STATE_KEY, "{}");
    } else {
      try {
        Map<String, Object> map = GsonUtil.fromJson(str, new TypeToken<Map<String, Object>>() {});
        stateMap.putAll(map);
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(LOG, "init readState error", e);
      }
    }
  }

  public static void updateSecretStorage(String str, String channel) {
    UpdateSecretStorage update = GsonUtil.fromJson(str, UpdateSecretStorage.class);
    if (null == update || null == update.getParams()) {
      return;
    }
    UpdateSecretStorageParams params = update.getParams();
    if (StringUtils.isEmpty(params.getValue())) {
      //空代表删除
      LocalStorageUtil.removeProperty(params.getKey());
    } else {
      LocalStorageUtil.setProperty(params.getKey(), params.getValue());
    }
  }


  public static String readSecretStorage(String str, String channel) {
    ReadSecretStorage readSecretStorage = GsonUtil.fromJson(str, ReadSecretStorage.class);
    if (null == readSecretStorage) {
      return null;
    }
    return LocalStorageUtil.getProperty(readSecretStorage.getParams());
  }

  public static void updateState(String str, String channel) {
    UpdateState updateState = GsonUtil.fromJson(str, UpdateState.class);
    if (null == updateState || null == updateState.getParams()) {
      return;
    }
    RemoteServiceMeta remoteServiceMeta = RcsWebSocketManager.INSTANCE.getRemoteServiceMeta(channel);
    String pluginName = remoteServiceMeta.getName();
    UpdateStateParams params = updateState.getParams();
    String key = params.getKey();
    Object value = params.getValue();
    if (null != params.getScope() && params.getScope().equals(Scope.Workspace)) {
      //workspace级别
      Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
      if (null != project) {
        key = "." + project.getBasePath() + "." + key;
      } else {
        key = ".default" + "." + key;
      }
    } else {
      key = GLOBAL_PREFIX + key;
    }
    key = pluginName + key;
    if (null == value) {
      stateMap.remove(key);
    } else {
      stateMap.put(key, value);
    }
    LocalStorageUtil.setProperty(PluginCommonConstant.REMOTE_GLOBAL_STATE_KEY, GsonUtil.toJson(stateMap));
  }

  public static Object readState(String str, String channel) {
    ReadState readState = GsonUtil.fromJson(str, ReadState.class);
    if (null == readState || null == readState.getParams()) {
      return null;
    }
    RemoteServiceMeta remoteServiceMeta = RcsWebSocketManager.INSTANCE.getRemoteServiceMeta(channel);
    String pluginName = remoteServiceMeta.getName();
    ReadStateParams params = readState.getParams();
    String key = params.getKey();
    if (null != params.getScope() && params.getScope().equals(Scope.Workspace)) {
      //workspace级别
      Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
      if (null != project) {
        key = "." + project.getBasePath() + "." + key;
      } else {
        key = ".default" + "." + key;
      }
    } else {
      key = GLOBAL_PREFIX + key;
    }
    key = pluginName + key;
    return stateMap.getOrDefault(key, null);
  }


  public static Boolean openExternal(String str, String channel) {
    ExecuteOpenExternal execute = GsonUtil.fromJson(str, ExecuteOpenExternal.class);
    if (null == execute || StringUtils.isEmpty(execute.getParams())) {
      return false;
    }
    return openExtLink(execute.getParams());
  }

    /**
     * 打开外部链接
     *
     * @param link
     */
    public static Boolean openExtLink(String link) {
        try {
            // 优先系统默认浏览器打开
            openURLByPb(link);
            return true;
        } catch (Throwable t) {
            // 支持桌面应用的话(windows/macos)，直接使用桌面应用方式打开
            if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                try {
                    Desktop.getDesktop().browse(new URI(link));
                    return true;
                } catch (IOException | URISyntaxException e) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "Desktop打开外部浏览器失败", null);
                    return false;
                }
            } else {
                try {
                    // 纯操作系统(linux)，可以使用命令行打开
                    Runtime.getRuntime().exec("open " + link);
                    return true;
                } catch (IOException e) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "Runtime打开外部浏览器失败", null);
                    return false;
                }
            }
        }
    }

    /**
     * 这里使用系统命令open打开而不是 Desktop.getDesktop().browse
     * 原因在于该api打开包含中文链接会有bug
     * 而不decode会导致浏览器参数异常（例如 %3D），跳转页面会发生参数错误
     * @param url
     * @throws IOException
     */
    public static void openURLByPb(String url) throws IOException {
        url = URLUtil.decode(url);
        String os = System.getProperty("os.name").toLowerCase();
        List<String> command = new ArrayList<>();

        if (os.contains("mac")) {
            command.add("open");
            command.add(url);
        } else if (os.contains("win")) {
            command.add("rundll32");
            command.add("url.dll,FileProtocolHandler");
            command.add(url);
        } else {
            command.add("xdg-open");
            command.add(url);
        }

        ProcessBuilder pb = new ProcessBuilder(command);
        pb.start();
        LoggerUtil.INSTANCE.logInfo(LOG, "使用ProcessBuilder成功打开: " + url);
    }

  public static void getProjectState(Project project) {
    if (stateMap.isEmpty()) {
      return;
    }
    List<RemoteServiceMeta> remoteServiceMetas = RcsWebSocketManager.INSTANCE.getRemoteServiceList(project);
    for (Map.Entry<String, Object> entry : stateMap.entrySet()) {
      String key = entry.getKey();
      //筛选当前workspace + global
      Scope scope;
      String pluginName;
      String projectRegex = "." + project.getBasePath() + ".";
      if (StringUtils.contains(key, projectRegex)) {
        scope = Scope.Workspace;
        pluginName = key.split(projectRegex)[0];
        key = StringUtils.substringAfter(key, project.getBasePath() + ".");
      } else if (StringUtils.contains(key, GLOBAL_PREFIX)) {
        scope = Scope.Global;
        pluginName = key.split(GLOBAL_PREFIX)[0];
        key = StringUtils.substringAfter(key, GLOBAL_PREFIX);
      } else {
        continue;
      }
      UpdateState updateState = new UpdateState();
      UpdateStateParams params = new UpdateStateParams();
      params.setScope(scope);
      params.setKey(key);
      params.setValue(entry.getValue());
      updateState.setParams(params);
      RemoteServiceMeta orderMeta = remoteServiceMetas.stream()
          .filter(meta -> meta.getName().equals(pluginName))
          .findFirst().orElse(null);
      if (null == orderMeta) {
        continue;
      }
      RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(orderMeta.getId(), "default", updateState, project);
    }
  }
  public static void createCommand(String str, String channel) {
    CreateCommand command = GsonUtil.fromJson(str, CreateCommand.class);
    if (null == command || null == command.getParams()) {
      return;
    }
    CreateCommandParams params = command.getParams();
    ExecuteCommandParams executeCommandParams = new ExecuteCommandParams();
    executeCommandParams.setCommand(params.getCommand());
    executeCommandParams.setType(params.getType());

    Project project = RcsWebSocketManager.INSTANCE.getProjectByLocalChannel(channel);
    Map<String, Pair<String, ExecuteCommandParams>> projectMap = commandMap.computeIfAbsent(project, k -> new ConcurrentHashMap<>());
    projectMap.put(params.getCommand(), Pair.of(channel, executeCommandParams));
  }
  public static Pair<String, ExecuteCommandParams> getCommandParam(Project project, String command) {
    Map<String, Pair<String, ExecuteCommandParams>> projectMap = commandMap.computeIfAbsent(project, k -> new ConcurrentHashMap<>());
    if (projectMap.containsKey(command)) {
      return projectMap.get(command);
    }
    return null;
  }
  public static Object executeCommand(String str, String channel) {
    ExecuteCommand executeCommand = GsonUtil.fromJson(str, ExecuteCommand.class);
    if (null == executeCommand || null == executeCommand.getParams()) {
      return null;
    }
    if (CollectionUtils.isEmpty(executeCommand.getParams().getArgs())) {
      return null;
    }
    ExecuteCommandEnum executeCommandEnum = ExecuteCommandEnum.getByCommand(executeCommand.getParams().getCommand());
    if (null == executeCommandEnum) {
      return null;
    }
    List<String> args = executeCommand.getParams().getArgs();
    return executeCommandEnum.executeCommand(args.get(0), channel);
  }
  public static void clearProjectCommand(Project project) {
    commandMap.remove(project);
  }
}

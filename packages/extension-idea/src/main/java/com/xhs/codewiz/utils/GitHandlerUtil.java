package com.xhs.codewiz.utils;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.vcs.ProjectLevelVcsManager;
import com.intellij.openapi.vcs.VcsRoot;
import com.intellij.openapi.vcs.changes.Change;
import com.intellij.openapi.vcs.diff.DiffProvider;
import com.intellij.openapi.vcs.history.VcsRevisionNumber;
import com.intellij.openapi.vfs.VirtualFile;
import git4idea.GitVcs;
import git4idea.commands.Git;
import git4idea.commands.GitCommand;
import git4idea.commands.GitCommandResult;
import git4idea.commands.GitLineHandler;
import java.io.File;
import java.util.Collection;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * Author: liukunpeng Date: 2025-02-27 Description:
 */
public class GitHandlerUtil {
    public static String getGitUrl(Project project) {
        GitLineHandler handler = new GitLineHandler(project, new File(project.getBasePath()), GitCommand.REMOTE);
        handler.addParameters("get-url", "origin");
        try {
            return Git.getInstance().runCommand(handler).getOutputOrThrow();
        } catch (Exception e) {
            return "";
        }
    }

    public static String getGitBranch(Project project) {
        GitLineHandler handler = new GitLineHandler(project, new File(project.getBasePath()), GitCommand.REV_PARSE);
        handler.addParameters("--abbrev-ref", "HEAD");
        try {
            return Git.getInstance().runCommand(handler).getOutputOrThrow().trim();
        } catch (Exception e) {
            return "";
        }
    }

    public static String getLatestCommitId(Project project) {
        GitLineHandler handler = new GitLineHandler(project, new File(project.getBasePath()), GitCommand.REV_PARSE);
        handler.addParameters("HEAD");
        try {
            return Git.getInstance().runCommand(handler).getOutputOrThrow().trim();
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 根据 commitId 和 filePath 获取文件内容
     *
     * @param project  项目
     * @param commitId 提交ID
     * @param filePath 文件路径（相对于项目根目录）
     * @return 文件内容，如果获取失败返回null
     */
    public static String getFileContentByCommit(Project project, String commitId, String filePath) {
        if (project == null || commitId == null || commitId.isEmpty() || filePath == null || filePath.isEmpty()) {
            return null;
        }

        try {
            // 规范化文件路径，移除开头的斜杠
            String normalizedPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;

            // 如果路径包含项目基础路径，则移除
            String basePath = project.getBasePath();
            if (basePath != null && normalizedPath.startsWith(basePath)) {
                normalizedPath = normalizedPath.substring(basePath.length());
                if (normalizedPath.startsWith("/")) {
                    normalizedPath = normalizedPath.substring(1);
                }
            }

            GitLineHandler handler = new GitLineHandler(project, new File(project.getBasePath()), GitCommand.SHOW);
            handler.addParameters(commitId + ":" + normalizedPath);

            GitCommandResult result = Git.getInstance().runCommand(handler);

            if (result.success()) {
                return result.getOutputAsJoinedString();
            } else {
                // 如果失败，尝试其他可能的路径格式
                return tryAlternativeFilePath(project, commitId, filePath);
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取HEAD版本的文件内容
     *
     * @param project  项目
     * @param filePath 文件路径（相对于项目根目录）
     * @return 文件内容，如果获取失败返回null
     */
    public static String getFileContentByHead(Project project, String filePath, String commitId) {
        if (StringUtils.isBlank(commitId)) {
            return getFileContentByCommit(project, "HEAD", filePath);
        }
        return getFileContentByCommit(project, commitId, filePath);
    }

    /**
     * 尝试其他可能的文件路径格式
     */
    private static String tryAlternativeFilePath(Project project, String commitId, String originalPath) {
        try {
            // 尝试使用原始路径
            GitLineHandler handler = new GitLineHandler(project, new File(project.getBasePath()), GitCommand.SHOW);
            handler.addParameters(commitId + ":" + originalPath);

            GitCommandResult result = Git.getInstance().runCommand(handler);
            if (result.success()) {
                return result.getOutputAsJoinedString();
            }

            // 如果原始路径是绝对路径，尝试转换为相对路径
            if (originalPath.startsWith("/")) {
                String basePath = project.getBasePath();
                if (basePath != null && originalPath.startsWith(basePath)) {
                    String relativePath = originalPath.substring(basePath.length());
                    if (relativePath.startsWith("/")) {
                        relativePath = relativePath.substring(1);
                    }

                    GitLineHandler relativeHandler = new GitLineHandler(project, new File(project.getBasePath()), GitCommand.SHOW);
                    relativeHandler.addParameters(commitId + ":" + relativePath);

                    GitCommandResult relativeResult = Git.getInstance().runCommand(relativeHandler);
                    if (relativeResult.success()) {
                        return relativeResult.getOutputAsJoinedString();
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }

        return null;
    }

    public static DiffProvider getDiffProviderFromVcs(Project project) {
        ProjectLevelVcsManager vcsManager = ProjectLevelVcsManager.getInstance(project);
        for (VcsRoot root : vcsManager.getAllVcsRoots()) {
            if (root.getVcs() instanceof GitVcs) { // 过滤 Git 仓库
                return root.getVcs().getDiffProvider();
            }
        }
        return null;
    }
}

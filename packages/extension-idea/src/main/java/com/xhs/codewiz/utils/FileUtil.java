package com.xhs.codewiz.utils;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.intellij.diff.editor.ChainDiffVirtualFile;
import com.intellij.icons.AllIcons;
import com.intellij.ide.IconProvider;
import com.intellij.lang.Language;
import com.intellij.lang.LanguageUtil;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileChooser.FileElement;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.TextEditor;
import com.intellij.openapi.fileEditor.impl.LoadTextUtil;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.fileTypes.PlainTextLanguage;
import com.intellij.openapi.project.DumbService;
import com.intellij.openapi.project.IndexNotReadyException;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.roots.ProjectFileIndex;
import com.intellij.openapi.util.SystemInfo;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.psi.search.FilenameIndex;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.testFramework.LightVirtualFile;
import com.intellij.util.containers.CollectionFactory;
import com.xhs.codewiz.enums.ExtensionEnum;
import com.xhs.codewiz.listener.LSPUtil;
import com.xhs.codewiz.type.file.Content;
import com.xhs.codewiz.type.file.Eol;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.imageio.ImageIO;
import javax.swing.Icon;
import javax.swing.ImageIcon;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class FileUtil {
  private static final Logger log = Logger.getInstance(FileUtil.class);
  public static final long MAX_FILE_SIZE = 0x100000L;
  public static final long MAX_IMAGE_FILE_SIZE = 0x1400000L;
  public static final long MAX_INDEX_FILE_SIZE = 0x500000L;
  public static final List<String> VALID_FILE_LANGUAGES = Arrays.asList("java", "python");
  public static final Pattern JAVA_CLASS_PATTERN = Pattern.compile(".*(class|interface|enum)\\s+([\\w$_]+)\\s+.*");
  private static final Pattern CODE_BLOCK_PATTERN = Pattern.compile("<code>(.*?)</code>(</a>)?");
  public static final Pattern MD_URL_PATTERN = Pattern.compile("\\[(.*?)\\]\\((.*?)\\)");
  private static final List<String> INVALID_INDEX_DIR = Arrays.asList(".git", ".idea", ".m2", "node_modules");
  private static final List<String> INVALID_INDEX_ROOT_DIR = Arrays.asList("target", "build", "dist", "out", "bin", "lib", "libs", "lib_ext");
  public static final List<String> VALIDATE_IMAGE_EXT = Arrays.asList("apng", "png", "jfif", "jpe", "jpeg", "jpg", "tif", "tiff", "webp", "bmp");
  private static final Cache<String, Pair<String, String>> FILE_URL_CACHE = Caffeine.newBuilder().expireAfterWrite(30L, TimeUnit.MINUTES).expireAfterAccess(30L, TimeUnit.MINUTES).maximumSize(5000L).build();

  public static boolean isLargeFile(@Nullable VirtualFile file) {
    return file == null || file.getLength() > 0x100000L;
  }

  public static boolean isLargeImageFile(@Nullable VirtualFile file) {
    return file == null || file.getLength() > 0x1400000L;
  }

  public static boolean checkAllFilesExist(List<String> filePaths, String root) {
    try {
      for (String filePath : filePaths) {
        if (Paths.get(root, filePath).toFile().exists()) continue;
        log.info(String.format("File [%s] exists in zip but not in local file system", filePath));
        return Boolean.FALSE;
      }
    }
    catch (Exception e) {
      log.warn("Check file exist failed" + e.getMessage());
      return Boolean.FALSE;
    }
    return Boolean.TRUE;
  }

  public static void deleteRecursive(File path) {
    File[] files = path.listFiles();
    log.info("Cleaning out folder:" + path);
    if (files != null) {
      for (File file : files) {
        if (file.isDirectory()) {
          log.info("Deleting file:" + file);
          FileUtil.deleteRecursive(file);
          file.delete();
          continue;
        }
        file.delete();
      }
    }
    path.delete();
  }

  public static final String getPathExt(String filePath) {
    String ext = null;
    int index = filePath.lastIndexOf(46);
    if (index > 0) {
      ext = filePath.substring(index + 1);
    }
    return ext;
  }

  public static final String getFileName(String filePath) {
    return filePath == null ? null : filePath.substring(filePath.lastIndexOf(FileUtil.getFileSeparator(filePath)) + 1);
  }

  public static boolean isValidImageFormat(String filePath) {
    if (StringUtils.isBlank((CharSequence)filePath)) {
      return false;
    }
    String pathExt = FileUtil.getPathExt(filePath);
    return VALIDATE_IMAGE_EXT.stream().anyMatch(ext -> ext.equalsIgnoreCase(pathExt));
  }

  public static String getLanguageFileType(Project project) {
    FileEditorManager fileEditorManager = FileEditorManager.getInstance((Project)project);
    FileEditor selectEditor = fileEditorManager.getSelectedEditor();
    if (selectEditor == null) {
      return "";
    }
    VirtualFile file = selectEditor.getFile();
    if (file == null) {
      return "";
    }
    Language language = LanguageUtil.getFileLanguage((VirtualFile)file);
    if (language == null) {
      return "";
    }
    log.info("Current code language: " + language.getDisplayName());
    return language.getDisplayName();
  }

  public static boolean createPrivacyPolicyFile(File targetFile) {
    HashMap<String, Long> map = new HashMap<String, Long>();
    map.put("timestamp", System.currentTimeMillis());
    try {
      FileUtils.write(targetFile, GsonUtil.toJson(map), "UTF-8");
      return true;
    }
    catch (IOException e) {
      log.warn("fail to create privacy policy file:" + targetFile + " cause by " + e.getMessage(), (Throwable)e);
      return false;
    }
  }

  public static String getJavaFileNameFromContent(String content) {
    Matcher matcher = JAVA_CLASS_PATTERN.matcher(content);
    if (matcher.find()) {
      return matcher.group(2);
    }
    return null;
  }

  public static String generateFileNameWhenNewFile(String ext, String content, String fullPath, String fileName, String separator, boolean isTestcase) {
    if (StringUtils.isBlank((CharSequence)fileName)) {
      return "new_file_" + System.currentTimeMillis() + (String)(ext == null ? "" : "." + ext);
    }
    if (StringUtils.isBlank((CharSequence)ext)) {
      String newFileName = FileUtil.getJavaFileNameFromContent(content);
      if (newFileName != null) {
        fileName = newFileName;
      }
    } else if ("java".equals(ext)) {
      String newFileName = FileUtil.getJavaFileNameFromContent(content);
      String originalFileName = fullPath.substring(fullPath.lastIndexOf(separator) + 1);
      if (newFileName != null) {
        fileName = newFileName;
      } else {
        fileName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
        if (isTestcase) {
          fileName = (String)fileName + "Test";
        }
      }
    } else if ("py".equals(ext)) {
      if (isTestcase) {
        fileName = "test_" + (String)fileName;
        log.info("Generate python new test file name: " + (String)fileName);
      }
    } else if ("go".equals(ext) && isTestcase) {
      fileName = (String)fileName + "_test";
      log.info("Generate golang new test file name: " + (String)fileName);
    }
    return fileName;
  }

  public static String generatePathWhenNewFile(String ext, String path, boolean isTestcase) {
    if ("java".equals(ext) && isTestcase) {
      String separator = FileUtil.getFileSeparator(path);
      return path.replace(String.format("src%smain%sjava", separator, separator), String.format("src%stest%sjava", separator, separator));
    }
    return path;
  }

  public static VirtualFile buildVirtualFile(String fileName, FileType fileType, String content, final boolean writable) {
    return new LightVirtualFile(fileName, fileType, content, StandardCharsets.UTF_8, 0L){

      public boolean isWritable() {
        return writable;
      }
    };
  }

  public static String getFileSeparator(String pathExample) {
    if ("\\".equals(File.separator) && !pathExample.contains(File.separator) && pathExample.contains("/")) {
      return "/";
    }
    return File.separator;
  }

  /*public static SceneTypeEnum detectSceneType(String filePath) {
    String fileName = FilenameUtils.getBaseName((String)filePath);
    String ext = FilenameUtils.getExtension((String)filePath);
    if ("java".equals(ext) ? fileName.endsWith("Test") || fileName.endsWith("test") : ("js".equals(ext) || "ts".equals(ext) ? fileName.endsWith(".test") || fileName.endsWith(".spec") : ("py".equals(ext) ? fileName.startsWith("test_") || fileName.endsWith("_test") : ("c".equals(ext) || "cpp".equals(ext) ? fileName.startsWith("test_") || fileName.endsWith("_test") : "go".equals(ext) && (fileName.startsWith("test_") || fileName.endsWith("_test")))))) {
      return SceneTypeEnum.TESTCASE;
    }
    return SceneTypeEnum.COMMON;
  }*/

  public static boolean isAbsolutePath(String filePath) {
    if (filePath == null) {
      return false;
    }
    return (filePath = filePath.replace('/', File.separatorChar)).matches("^[A-Za-z]:\\\\.*") && SystemInfo.isWindows || filePath.startsWith("/");
  }

  public static String existFileAfterRepair(String rootPath, String filePath) {
    List<String> paths = FileUtil.getRepairFilePath(filePath);
    paths.sort((o1, o2) -> {
      if (o1.length() == o2.length()) {
        return 0;
      }
      return o1.length() > o2.length() ? 1 : -1;
    });
    for (String path : paths) {
      File file;
      String targetPath = path;
      int extParamIndex = targetPath.lastIndexOf("#");
      if (extParamIndex > 0) {
        targetPath = targetPath.substring(0, extParamIndex);
      }
      if (!(file = new File(rootPath, targetPath)).exists()) continue;
      return path;
    }
    return null;
  }

  public static List<String> getRepairFilePath(String filePath) {
    ArrayList<String> result = new ArrayList<String>();
    if ((filePath = filePath.replace('/', File.separatorChar)).contains(File.separator)) {
      String[] split = filePath.split("[/\\\\]+");
      LinkedList<String> list = new LinkedList<String>();
      for (int i = split.length - 1; i >= 0; --i) {
        list.offerFirst(split[i]);
        result.add(StringUtils.join(list, (String)File.separator));
      }
    } else {
      result.add(filePath);
    }
    return result;
  }
  public static String getProjectBaseDir(Project project) {
    if (project == null) {
      return null;
    }
    String path = project.getBasePath();
    if (path == null) {
      return project.getPresentableUrl();
    }
    return path.replace('/', File.separatorChar);
  }
  public static String detectFileUrl(Project project, String html) {
    String rootPath = getProjectBaseDir(project);
    StringBuilder sb = new StringBuilder();
    Matcher m = CODE_BLOCK_PATTERN.matcher(html);
    while (m.find()) {
      String fileName;
      String content = m.group(1);
      if (m.groupCount() > 2 && m.group(2).equals("</a>")) {
        m.appendReplacement(sb, "");
        sb.append(m.group());
        continue;
      }
      String cacheKey = String.format("%s_%s", rootPath, content);
      Pair cache = FILE_URL_CACHE.getIfPresent(cacheKey);
      if (cache != null) {
        m.appendReplacement(sb, "");
        sb.append(String.format("<a href=\"file://%s\">%s</a>", cache.getKey(), cache.getValue()));
        continue;
      }
      String existPath = FileUtil.existFileAfterRepair(rootPath, content);
      if (StringUtils.isNotBlank(existPath)) {
        FILE_URL_CACHE.put(cacheKey, Pair.of(existPath, existPath));
        m.appendReplacement(sb, "");
        sb.append(String.format("<a href=\"file://%s\">%s</a>", existPath, existPath));
        continue;
      }
      Matcher urlMatcher = MD_URL_PATTERN.matcher(content);
      if (urlMatcher.matches()) {
        String existPathTmp;
        fileName = urlMatcher.group(1);
        String linkPath = urlMatcher.group(2);
        if (linkPath.startsWith("file://")) {
          linkPath = linkPath.substring("file://".length());
        }
        if (StringUtils.isNotBlank((CharSequence)(existPathTmp = FileUtil.existFileAfterRepair(rootPath, linkPath)))) {
          FILE_URL_CACHE.put(cacheKey, Pair.of(existPathTmp, fileName));
          m.appendReplacement(sb, "");
          sb.append(String.format("<a href=\"file://%s\">%s</a>", existPathTmp, fileName));
          continue;
        }
      } else {
        PsiFile[] files;
        fileName = content;
        if (fileName.contains(File.separator)) {
          fileName = FilenameUtils.getName((String)fileName);
        }
        if ((files = FilenameIndex.getFilesByName((Project)project, (String)fileName, (GlobalSearchScope)GlobalSearchScope.projectScope((Project)project))).length == 1) {
          String filePath = files[0].getVirtualFile().getPresentableUrl();
          FILE_URL_CACHE.put(cacheKey, Pair.of(filePath, content));
          m.appendReplacement(sb, "");
          sb.append(String.format("<a href=\"file://%s\">%s</a>", filePath, content));
          continue;
        }
        if (files.length > 1 && content.contains(File.separator)) {
          boolean found = false;
          for (PsiFile fileTmp : files) {
            String filePath = fileTmp.getVirtualFile().getPresentableUrl();
            if (!filePath.endsWith(content)) continue;
            FILE_URL_CACHE.put(cacheKey, Pair.of(filePath, content));
            m.appendReplacement(sb, "");
            sb.append(String.format("<a href=\"file://%s\">%s</a>", filePath, content));
            found = true;
            break;
          }
          if (found) continue;
        }
      }
      m.appendReplacement(sb, "");
      sb.append(m.group());
    }
    m.appendTail(sb);
    return sb.toString();
  }

  public static boolean isIgnoreIndexFile(Project project, VirtualFile file) {
    if (file == null) {
      return true;
    }
    Language langType = LanguageUtil.getFileLanguage(file);
    if (null == langType || langType instanceof PlainTextLanguage) {
      return true;
    }
    if (file.getFileType().isBinary()) {
      return true;
    }
    if (file.getLength() > 0x500000L) {
      return true;
    }
    for (String ignoreDir : INVALID_INDEX_DIR) {
      if (!file.getPath().contains(ignoreDir)) continue;
      return true;
    }
    String projectBaseDir = getProjectBaseDir(project);
    String relatePath = file.getPresentableUrl().replace(projectBaseDir, "");
    for (String ignoreDir : INVALID_INDEX_ROOT_DIR) {
      if (!relatePath.startsWith(ignoreDir)) continue;
      return true;
    }
    return false;
  }

  public static boolean downloadFile(String fileUrl, String filePath) {
    try {
      int length;
      URL url = new URL(fileUrl);
      HttpURLConnection connection = (HttpURLConnection)url.openConnection();
      InputStream inputStream = connection.getInputStream();
      File file = new File(filePath);
      File directory = file.getParentFile();
      if (!directory.exists()) {
        directory.mkdirs();
      }
      FileOutputStream fileOutputStream = new FileOutputStream(file);
      byte[] buffer = new byte[1024];
      while ((length = inputStream.read(buffer)) != -1) {
        fileOutputStream.write(buffer, 0, length);
      }
      fileOutputStream.close();
      inputStream.close();
      return true;
    }
    catch (Exception e) {
      e.printStackTrace();
      return false;
    }
  }

  public static String getTempPath(String url, String fileName) {
    if (StringUtils.isBlank((CharSequence)url)) {
      return url;
    }
    if (url.startsWith("image://")) {
      url = url.substring("image://".length());
    }
    String tempDirPath = System.getProperty("java.io.tmpdir");
    String md5Hash = Md5Util.encode(url.getBytes(StandardCharsets.UTF_8));
    Object suffix = FileUtil.getPathExt(url);
    suffix = suffix == null ? "" : "." + (String)suffix;
    fileName = StringUtils.isBlank((CharSequence)fileName) ? md5Hash + (String)suffix : new File((String)fileName).getName();
    return Paths.get(tempDirPath, new String[]{md5Hash, fileName}).toString();
  }

  public static boolean isFileInArchive(VirtualFile file) {
    if (file == null) {
      return false;
    }
    String path = file.getPath();
    String extension = file.getExtension();
    if (FileElement.isFileHidden((VirtualFile)file)) {
      return true;
    }
    if (file.getFileType().isBinary() && !FileUtil.isValidImageFormat(file.getPath())) {
      return true;
    }
    if (!file.exists() || path == null || !file.isInLocalFileSystem()) {
      return true;
    }
    if ("jar".equalsIgnoreCase(extension) || "zip".equalsIgnoreCase(extension) || "class".equalsIgnoreCase(extension)) {
      return true;
    }
    if (path.startsWith("jrt:")) {
      return true;
    }
    if (path.contains(".jdk") && path.contains("!/")) {
      return true;
    }
    return path.contains(".jar!") || path.contains(".zip!");
  }

  public static List<VirtualFile> searchVirtualFilesByNamesIgnoringCase(Project project, String query) {
    if (query == null) {
      query = "";
    }
    Collection vscFiles = FilenameIndex.getVirtualFilesByName((Project)project, (String)query, (GlobalSearchScope)GlobalSearchScope.projectScope((Project)project));
    Set fuzzyNameSet = CollectionFactory.createSmallMemoryFootprintSet();
    String fuzzyName = query.toLowerCase();
    Set<String> keys = CollectionFactory.createSmallMemoryFootprintSet();
    FilenameIndex.processAllFileNames(value -> {
      if (StringUtils.isBlank((CharSequence)fuzzyName)) {
        keys.add(value);
        return true;
      }
      if (value != null & value.toLowerCase().contains(fuzzyName)) {
        keys.add(value);
      }
      return true;
    }, (GlobalSearchScope)GlobalSearchScope.projectScope((Project)project), null);
    log.warn("keys is " + keys);
    Set<VirtualFile> files = CollectionFactory.createSmallMemoryFootprintSet();
    for (String each : keys) {
      files.addAll(FilenameIndex.getVirtualFilesByName((Project)project, (String)each, (boolean)false, (GlobalSearchScope)GlobalSearchScope.projectScope((Project)project)));
    }
    if (CollectionUtils.isEmpty((Collection)files)) {
      return new ArrayList<VirtualFile>();
    }
    if (DumbService.isDumb((Project)project)) {
      return new ArrayList<VirtualFile>(files);
    }
    ArrayList<VirtualFile> fileList = new ArrayList<VirtualFile>();
    for (VirtualFile file : files) {
      Boolean isValid;
      if (file.isDirectory() || !(isValid = FileUtil.isValidProjectFile(project, file)).booleanValue()) continue;
      fileList.add(file);
    }
    return fileList;
  }

  public static Boolean isValidProjectFile(Project project, VirtualFile file) {
    if (isFileInArchive(file)) {
      return false;
    }
    if (DumbService.isDumb(project)) {
      return true;
    }
    return DumbService.getInstance(project).runReadActionInSmartMode(() -> {
      try {
        ProjectFileIndex projectFileIndex = ProjectFileIndex.getInstance(project);
        if (projectFileIndex.isExcluded(file) || projectFileIndex.isInLibrary(file) || projectFileIndex.isInLibrarySource(file) || projectFileIndex.isUnderIgnored(file) || projectFileIndex.isInLibraryClasses(file)) {
          return false;
        }
      } catch (IndexNotReadyException e) {
        log.warn("index not ready. ignore checking ignore file");
      } catch (Throwable e) {
        log.warn("check ignore file error", e);
      }
      return true;
    });
  }

  public static boolean isValidDirectory(VirtualFile file) {
    if (file == null || file.getPath() == null) {
      return false;
    }
    for (String ignoreDir : INVALID_INDEX_DIR) {
      if (!file.getPath().contains(ignoreDir)) continue;
      return false;
    }
    return true;
  }

  public static void saveMemeryFileContentToDisk(Project project, String filePath) {
    FileDocumentManager fileDocumentManager;
    Document document;
    VirtualFile[] openFiles = FileEditorManager.getInstance((Project)project).getOpenFiles();
    VirtualFile virtualFile = null;
    for (VirtualFile file : openFiles) {
      if (!file.getPath().equals(filePath)) continue;
      virtualFile = file;
      break;
    }
    if (null != virtualFile && FileUtil.isValidProjectFile(project, virtualFile).booleanValue() && (document = (fileDocumentManager = FileDocumentManager.getInstance()).getDocument(virtualFile)) != null) {
      ApplicationManager.getApplication().invokeLater(() -> fileDocumentManager.saveDocument(document));
    }
  }

  public static String getLineSeparator(@NotNull VirtualFile file) {
    try {
      if (null == file.getDetectedLineSeparator()) {
        file.setDetectedLineSeparator(FileUtil.getLineSeparator(file.contentsToByteArray()));
      }
      return file.getDetectedLineSeparator();
    }
    catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public static String getLineSeparator(String str) {
    return StringUtils.isEmpty((CharSequence)str) ? null : FileUtil.getLineSeparator(str.getBytes());
  }

  public static String getLineSeparator(byte[] bytes) {
    if (null == bytes || bytes.length == 0) {
      return null;
    }
    if (bytes.length == 1) {
      return bytes[0] == 13 ? "\r" : "\n";
    }
    for (int i = 0; i < bytes.length - 1; ++i) {
      if (bytes[i] == 13) {
        if (bytes[i + 1] == 10) {
          return "\r\n";
        }
        return "\r";
      }
      if (bytes[i] != 10) continue;
      return "\n";
    }
    return "\n";
  }

  /**
   * data:image/png;base64,imagebase64
   * @param file
   * @return
   */
  public static String iconBase64(Project project, VirtualFile file) {
    FutureTask<Icon> task = new FutureTask<>(() -> getFileIcon(project, file));
    ApplicationManager.getApplication().runReadAction(task);
    Icon icon = null;
    try {
      icon = task.get();
    }
    catch (InterruptedException | ExecutionException e) {
    }
    try {
      if (icon instanceof ImageIcon) {
        Image image = ((ImageIcon) icon).getImage();
        return imageToBase64String(image);
      }
      /*if (icon instanceof CachedImageIcon) {
        Image image = ((CachedImageIcon) icon).getRealImage();
        return imageToBase64String(image);
      }*/
    } catch (Exception e) {

    }
    BufferedImage bufferedImage = new BufferedImage(
        icon.getIconWidth(),
        icon.getIconHeight(),
        BufferedImage.TYPE_INT_ARGB);
    Graphics g = bufferedImage.createGraphics();
    icon.paintIcon(null, g, 0, 0);
    g.dispose();
    return imageToBase64String(bufferedImage);
  }
  public static Icon getFileIcon(Project project, VirtualFile file) {
    PsiFile psiFile = PsiManager.getInstance(project).findFile(file);
    Icon icon = null;
    if (null != psiFile) {
      icon = psiFile.getIcon(0);
      if (null == icon) {
        icon = file.getFileType().getIcon();
        if (icon == null) {
          icon = AllIcons.FileTypes.Any_type;
        }
      }
    }
    return icon;
  }
  public static Icon getFileTypeIcon(Project project, String filePath) {
    FileTypeManager fileTypeManager = FileTypeManager.getInstance();
    VirtualFile virtualFile = LocalFileSystem.getInstance().findFileByPath(filePath);
    if (virtualFile == null) {
      return fileTypeManager.getFileTypeByFileName(FileUtil.getFileName(filePath)).getIcon();
    }
    FileType fileType = fileTypeManager.getFileTypeByFile(virtualFile);

    Icon icon = null;
    if (project != null) {
      try {
        PsiFile psiFile = PsiManager.getInstance(project).findFile(virtualFile);
        for (IconProvider iconProvider : IconProvider.EXTENSION_POINT_NAME.getExtensionList()) {
          icon = iconProvider.getIcon((PsiElement)psiFile, 0);
          if (icon != null) {
            break;
          }
        }
      } catch (Exception exception) {}
    }
    if (icon == null) {
      icon = fileType.getIcon();
    }
    return icon;
  }
  public static Content buildContent(Project project, VirtualFile file) {
    Content content = new Content();
    content.setEol(FileUtil.getEol(file));
    content.setEncoding(file.getCharset().name());
    if (file instanceof ChainDiffVirtualFile) {
      content.setVersion(0);
      content.setText("");
    } else {
      Document doc = ReadAction.compute(() -> {
        return FileDocumentManager.getInstance().getDocument(file);
      });
      if (null == doc) {
        return null;
      }
      content = new Content();
      content.setText(doc.getText());
      content.setLanguageId(ExtensionEnum.getLanguageByExtension(file.getExtension()));
      AtomicReference<FileEditor[]> ref = new AtomicReference<>();
      ApplicationManager.getApplication().invokeAndWait(() -> {
        ref.set(FileEditorManager.getInstance(project).getEditors(file));
      });
      int tabWidth = 4;
      for (FileEditor fileEditor : ref.get()) {
        if (fileEditor instanceof TextEditor) {
          Editor editor = ((TextEditor) fileEditor).getEditor();
          tabWidth = ReadAction.compute(() -> {
            return editor.getSettings().getTabSize(editor.getProject());
          });
        }
      }
      content.setTabSize(tabWidth);
    }
    return content;
  }
  public static Eol getEol(VirtualFile file) {
    String lineSeparator = LoadTextUtil.detectLineSeparator(file, true);
    if (StringUtils.isEmpty(lineSeparator)) {
     return Eol.LF;
    } else {
      return StringUtils.equals(lineSeparator, "\r\n") ? Eol.CRLF : Eol.LF;
    }
  }
  public static com.xhs.codewiz.type.file.FileType getFileType(VirtualFile file) {
    if (!file.exists()) {
      return com.xhs.codewiz.type.file.FileType.Unknown;
    }
    try {
      if (Files.isSymbolicLink(Paths.get(file.getPath()))) {
        return com.xhs.codewiz.type.file.FileType.SymbolicLink;
      } else if (file.isDirectory()) {
        return com.xhs.codewiz.type.file.FileType.Directory; // Directory
      } else {
        return com.xhs.codewiz.type.file.FileType.File; // File
      }
    } catch (Exception e) {
      return com.xhs.codewiz.type.file.FileType.Unknown;
    }
  }
  private static String imageToBase64String(Image image) {
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    try {
      BufferedImage buffered = new BufferedImage(
          image.getWidth(null),
          image.getHeight(null),
          BufferedImage.TYPE_INT_ARGB);
      Graphics2D bGr = buffered.createGraphics();
      bGr.drawImage(image, 0, 0, null);
      bGr.dispose();
      ImageIO.write(buffered, "png", baos);
      byte[] imageBytes = baos.toByteArray();
      return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);
    } catch (Exception e) {
      return null;
    } finally {
      try {
        baos.close();
      } catch (IOException e) {
        log.warn("imageToBase64String err, msg = {}", e);
      }
    }
  }

}

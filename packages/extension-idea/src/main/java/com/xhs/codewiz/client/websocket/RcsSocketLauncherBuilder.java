package com.xhs.codewiz.client.websocket;

import com.google.gson.GsonBuilder;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.client.message.RcsMessageJsonHandler;
import com.xhs.codewiz.type.content.ActionKind;
import com.xhs.codewiz.type.global.Severity;
import com.xhs.codewiz.type.platform.BuildInPanel;
import com.xhs.codewiz.type.platform.BuildInPanel.BuildInPanelDiff;
import com.xhs.codewiz.type.platform.BuildInPanel.BuildInPanelProblems;
import com.xhs.codewiz.type.platform.BuildInPanel.BuildInPanelViews;
import com.xhs.codewiz.type.platform.PlatformName;
import com.xhs.codewiz.type.platform.SeriesName;
import com.xhs.codewiz.type.workspace.FileChanges;
import com.xhs.codewiz.type.workspace.FileChanges.FileChangesContent;
import com.xhs.codewiz.type.workspace.FileChanges.FileChangesFile;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.RuntimeTypeAdapterFactory;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.function.Consumer;
import javax.websocket.Session;
import org.eclipse.lsp4j.jsonrpc.Endpoint;
import org.eclipse.lsp4j.jsonrpc.Launcher;
import org.eclipse.lsp4j.jsonrpc.MessageConsumer;
import org.eclipse.lsp4j.jsonrpc.RemoteEndpoint;
import org.eclipse.lsp4j.jsonrpc.json.JsonRpcMethod;
import org.eclipse.lsp4j.jsonrpc.json.MessageJsonHandler;
import org.eclipse.lsp4j.jsonrpc.services.ServiceEndpoints;

public class RcsSocketLauncherBuilder<T> extends Launcher.Builder<T> {
  private static final Logger logger = Logger.getInstance(RcsSocketLauncherBuilder.class);
  protected Session session;
  public Collection<Object> getLocalServices() {
    return this.localServices;
  }

  public RcsSocketLauncherBuilder<T> setSession(Session session) {
    this.session = session;
    return this;
  }

  public Launcher<T> create() {
    if (this.localServices == null) {
      throw new IllegalStateException("Local service must be configured.");
    }
    if (this.remoteInterfaces == null) {
      throw new IllegalStateException("Remote interface must be configured.");
    }
    MessageJsonHandler jsonHandler = this.createJsonHandler();
    RemoteEndpoint remoteEndpoint = this.createRemoteEndpoint(jsonHandler);
    this.addMessageHandlers(jsonHandler, remoteEndpoint);
    T remoteProxy = createProxy(remoteEndpoint);
    return this.createLauncher(null, remoteProxy, remoteEndpoint, null);
  }

  protected MessageJsonHandler createJsonHandler() {
    Map<String, JsonRpcMethod> supportedMethods = this.getSupportedMethods();
    Consumer<GsonBuilder> myConfig = gsonBuilder -> {
      gsonBuilder.registerTypeAdapter(Severity.class, GsonUtil.SEVERITY_ADAPTER)
          .registerTypeAdapter(PlatformName.class, GsonUtil.PLATFORM_NAME_ADAPTER)
          .registerTypeAdapter(SeriesName.class, GsonUtil.SERIES_NAME_ADAPTER)
          .registerTypeAdapter(ActionKind.class, GsonUtil.ACTION_KIND_ADAPTER)
          .registerTypeAdapterFactory(
              //文件变更父子类序列化设置
              RuntimeTypeAdapterFactory
                  .of(FileChanges.class, "type")
                  .registerSubtype(FileChangesFile.class, "file")
                  .registerSubtype(FileChangesContent.class, "content")
          ).registerTypeAdapterFactory(
              //面板变更父子类序列化设置
              RuntimeTypeAdapterFactory
                  .of(BuildInPanel.class, "type")
                  .registerSubtype(BuildInPanelDiff.class, "diff")
                  .registerSubtype(BuildInPanelProblems.class, "problems")
                  .registerSubtype(BuildInPanelViews.class, "views")
                  .registerSubtype(BuildInPanel.BuildInPanelWebview.class, "webview"));
    };
    return new RcsMessageJsonHandler(supportedMethods, myConfig);
  }

  protected RemoteEndpoint createRemoteEndpoint(MessageJsonHandler jsonHandler) {
    MessageConsumer outgoingMessageStream = new RcsWebSocketMessageConsumer(session, jsonHandler);

    Endpoint localEndpoint = ServiceEndpoints.toEndpoint(localServices);
    RemoteEndpoint remoteEndpoint;
    if (exceptionHandler == null) {
      remoteEndpoint = new RemoteEndpoint(outgoingMessageStream, localEndpoint);
    } else {
      remoteEndpoint = new RemoteEndpoint(outgoingMessageStream, localEndpoint, exceptionHandler);
    }
    jsonHandler.setMethodProvider(remoteEndpoint);
    wrapMessageConsumer(remoteEndpoint);
    return remoteEndpoint;
  }

  protected void addMessageHandlers(MessageJsonHandler jsonHandler, RemoteEndpoint remoteEndpoint) {
    MessageConsumer messageConsumer = this.wrapMessageConsumer(remoteEndpoint);
    this.session.addMessageHandler(new RcsWebSocketMessageHandler(messageConsumer, jsonHandler, remoteEndpoint));
  }
}
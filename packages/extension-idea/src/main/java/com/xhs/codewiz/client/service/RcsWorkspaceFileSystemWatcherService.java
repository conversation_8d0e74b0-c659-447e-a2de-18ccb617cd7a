package com.xhs.codewiz.client.service;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.openapi.vfs.newvfs.BulkFileListener;
import com.intellij.openapi.vfs.newvfs.events.VFileCreateEvent;
import com.intellij.openapi.vfs.newvfs.events.VFileDeleteEvent;
import com.intellij.openapi.vfs.newvfs.events.VFileEvent;
import com.intellij.openapi.vfs.newvfs.events.VFileContentChangeEvent;
import com.intellij.util.messages.MessageBusConnection;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.scheme.file.CreateFileUri;
import com.xhs.codewiz.scheme.file.DeleteFileUri;
import com.xhs.codewiz.scheme.file.UpdateFileUri;
import com.xhs.codewiz.scheme.workspace.CreateFileSystemWatcher;
import com.xhs.codewiz.scheme.workspace.DeleteFileSystemWatcher;
import com.xhs.codewiz.type.file.GlobPattern;
import com.xhs.codewiz.utils.GsonUtil;

import com.xhs.codewiz.utils.LoggerUtil;
import java.nio.file.FileSystems;
import java.nio.file.PathMatcher;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * create on 2025/7/17 19:40
 */
public class RcsWorkspaceFileSystemWatcherService {

    private static final Logger logger = Logger.getInstance(RcsWorkspaceFileSystemWatcherService.class);
    
    // 存储FileSystemWatcher的映射，key为watcherId，value为监听器连接
    private static final Map<String, MessageBusConnection> watchers = new ConcurrentHashMap<>();

    /**
     * 调用IntelliJ IDEA的sdk，实现 创建一个新的 FileSystemWatcher。FileSystemWatcher 用于监听文件系统的变化，如文件的创建、修改或删除。
     */
    public static void fileSystemWatcherCreate(String str, String channel) {
        CreateFileSystemWatcher createFileSystemWatcher = GsonUtil.fromJson(str, CreateFileSystemWatcher.class);

        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                if (createFileSystemWatcher == null || createFileSystemWatcher.getParams() == null) {
                    return;
                }
                String watcherId = createFileSystemWatcher.getParams().getId();
                GlobPattern globPattern = createFileSystemWatcher.getParams().getGlobPattern();
                Boolean ignoreCreateEvents = createFileSystemWatcher.getParams().getIgnoreCreateEvents();
                Boolean ignoreChangeEvents = createFileSystemWatcher.getParams().getIgnoreChangeEvents();
                Boolean ignoreDeleteEvents = createFileSystemWatcher.getParams().getIgnoreDeleteEvents();

                if (watcherId == null || globPattern == null) {
                    LoggerUtil.INSTANCE.logWarn(logger,"Invalid file system watcher data");
                    return;
                }
                if (watchers.containsKey(watcherId)) {
                    return;
                }

                Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
                if (project == null) {
                    LoggerUtil.INSTANCE.logWarn(logger,"No project found for file system watcher creation");
                    return;
                }

                // 创建PathMatcher用于匹配文件路径
                String pattern = globPattern.getGlob();
                String base = globPattern.getBase();
                PathMatcher pathMatcher = FileSystems.getDefault().getPathMatcher("glob:" + pattern);

                // 创建消息总线连接
                MessageBusConnection connection = project.getMessageBus().connect();

                // 注册文件系统监听器
                connection.subscribe(VirtualFileManager.VFS_CHANGES, new BulkFileListener() {
                    @Override
                    public void after(List<? extends VFileEvent> events) {
                        for (VFileEvent event : events) {
                            VirtualFile file = event.getFile();
                            if (file == null) continue;

                            String filePath = file.getUrl();

                            // 应用base路径过滤
                            if (base != null && !filePath.contains(base)) {
                                continue;
                            }

                            // 应用glob模式匹配
                            String relativePath = base != null ? filePath.substring(base.length()) : filePath;
                            if (relativePath.startsWith("/")) {
                                relativePath = relativePath.substring(1);
                            }

                            if (!pathMatcher.matches(java.nio.file.Paths.get(relativePath))) {
                                continue;
                            }

                            // 处理不同类型的文件事件
                            if (event instanceof VFileCreateEvent && !Boolean.TRUE.equals(ignoreCreateEvents)) {
                                CreateFileUri createFileUri = new CreateFileUri();
                                createFileUri.setParams(filePath);

                                RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(channel, watcherId, createFileUri, project);
                                LoggerUtil.INSTANCE.logDebug(logger,"File created: " + file.getPath());

                            } else if (event instanceof VFileContentChangeEvent && !Boolean.TRUE.equals(ignoreChangeEvents)) {
                                UpdateFileUri updateFileUri = new UpdateFileUri();
                                updateFileUri.setParams(filePath);
                                RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(channel, watcherId, updateFileUri, project);
                                LoggerUtil.INSTANCE.logDebug(logger,"File changed: " + file.getPath());

                            } else if (event instanceof VFileDeleteEvent && !Boolean.TRUE.equals(ignoreDeleteEvents)) {
                                DeleteFileUri deleteFileUri = new DeleteFileUri();
                                deleteFileUri.setParams(filePath);
                                RcsWebSocketManager.INSTANCE.sendNotificationWithChannelProvider(channel, watcherId, deleteFileUri, project);
                                LoggerUtil.INSTANCE.logDebug(logger,"File deleted: " + file.getPath());
                            }
                        }
                    }
                });

                // 存储连接以便后续删除
                watchers.put(watcherId, connection);

                LoggerUtil.INSTANCE.logInfo(logger,"File system watcher created: " + watcherId + " with pattern: " + pattern);

            } catch (Exception e) {
                LoggerUtil.INSTANCE.logWarn(logger,"Failed to create file system watcher", e);
            }
        });
    }

    /**
     * 调用IntelliJ IDEA的sdk，实现 删除指定的 FileSystemWatcher。
     */
    public static void fileSystemWatcherDelete(String str, String channel) {
        DeleteFileSystemWatcher deleteFileSystemWatcher = GsonUtil.fromJson(str, DeleteFileSystemWatcher.class);

        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                String watcherId = deleteFileSystemWatcher.getParams();

                if (watcherId == null) {
                    logger.warn("Invalid file system watcher ID for deletion");
                    return;
                }

                // 移除并断开连接
                MessageBusConnection connection = watchers.remove(watcherId);
                if (connection != null) {
                    connection.disconnect();
                    logger.info("File system watcher deleted: " + watcherId);
                } else {
                    logger.warn("File system watcher not found: " + watcherId);
                }

            } catch (Exception e) {
              LoggerUtil.INSTANCE.logWarn(logger, "Failed to delete file system watcher", e);
            }
        });
    }
    
    /**
     * 清理所有监听器（在项目关闭时调用）
     */
    public static void cleanup() {
        for (MessageBusConnection connection : watchers.values()) {
            if (connection != null) {
                connection.disconnect();
            }
        }
        watchers.clear();
    }
}

package com.xhs.codewiz.scheme.content;

import java.util.*;
/**
 * 删除 Comments
 */
public class DeleteComments {
    private String schemaProtocol = "content.delete.comments";
    private DeleteCommentsParams params;

    public DeleteCommentsParams getParams() {
        return params;
    }
    public void setParams(DeleteCommentsParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class DeleteCommentsParams {
        /** Comment Provider ID，表示该 Comments 属于哪个 Comment Provider */
        private String provider;
        /** Comments 合集 ID */
        private String collectionId;
        private List<String> commentIds;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public String getCollectionId() {
            return collectionId;
        }
        public void setCollectionId(String collectionId) {
            this.collectionId = collectionId;
        }
        public List<String> getCommentIds() {
            return commentIds;
        }
        public void setCommentIds(List<String> commentIds) {
            this.commentIds = commentIds;
        }
    }
}

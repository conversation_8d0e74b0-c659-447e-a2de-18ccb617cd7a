package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.editor.request.TextDocumentItem;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import org.jetbrains.annotations.NotNull;

public final class DidOpenCommand implements JsonRpcNotification {
    @SerializedName("textDocument")
    private final @NotNull TextDocumentItem textDocument;

    public @NotNull String getCommandName() {
        return "textDocument/didOpen";
    }

    public DidOpenCommand(@NotNull TextDocumentItem textDocument) {
        this.textDocument = textDocument;
    }

    public @NotNull TextDocumentItem getTextDocument() {
        return this.textDocument;
    }
}


package com.xhs.codewiz.actions.problem;

import com.google.gson.reflect.TypeToken;
import com.intellij.codeInsight.daemon.impl.DaemonCodeAnalyzerImpl;
import com.intellij.codeInsight.daemon.impl.HighlightInfo;
import com.intellij.codeInsight.intention.IntentionManager;
import com.intellij.lang.annotation.HighlightSeverity;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.LogicalPosition;
import com.intellij.openapi.editor.SelectionModel;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.PsiFile;
import com.xhs.codewiz.client.ProviderTypeEnum;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.enums.ExtensionEnum;
import com.xhs.codewiz.scheme.content.CreateActionProvider;
import com.xhs.codewiz.scheme.content.ReadAction;
import com.xhs.codewiz.scheme.content.ReadAction.ReadActionParams;
import com.xhs.codewiz.type.content.ActionContext;
import com.xhs.codewiz.type.content.ActionItem;
import com.xhs.codewiz.type.content.Diagnostic;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.file.DocumentFilter;
import com.xhs.codewiz.type.global.Severity;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-07-22 Description:
 */
public class ReadActionUtil {
  //key->provider,value->provider下提供的动作
  public static Map<CreateActionProvider, List<ActionItem>> actionItems = new HashMap<>();
  public static List<DynamicCodeProblemsIntentionAction> showActions = new ArrayList<>();
  public static void queryActionItem(Project project, Editor editor, PsiFile psiFile) {
    //每次触发都要清理之前注册的Intention
    if (CollectionUtils.isNotEmpty(showActions)) {
      for (DynamicCodeProblemsIntentionAction action :showActions) {
        IntentionManager.getInstance().unregisterIntention(action);
      }
      showActions.clear();
    }
    if (!actionItems.isEmpty()) {
      actionItems.clear();
    }
    List<String> channelList = RcsWebSocketManager.INSTANCE.getRemoteChannelListByProject(project);
    ReadAction readAction = buildRequest(project, editor, psiFile);
    for (String channel : channelList) {
      List<Object> providers = RcsWebSocketManager.INSTANCE.getProvider(channel, ProviderTypeEnum.ACTION);
      if (CollectionUtils.isNotEmpty(providers)) {
        for (Object provider : providers) {
          if (!(provider instanceof CreateActionProvider createActionProvider)) {
            continue;
          }
          //是actionProvider
          if (!fileSelectorFilter(createActionProvider.getParams().getSelector(), psiFile)) {
            continue;
          }
          readAction.getParams().setProvider(createActionProvider.getParams().getProvider().getId());
          List<ActionItem> res = RcsWebSocketManager.INSTANCE.sendRequestWithChannelProvider(channel,
              createActionProvider.getParams().getProvider().getId(),
              readAction, new TypeToken<List<ActionItem>>() {}, project, 500);
          if (CollectionUtils.isNotEmpty(res)) {
            actionItems.put(createActionProvider, res);
          }
        }
      }
    }
  }

  private static ReadAction buildRequest(Project project, Editor editor, PsiFile psiFile) {
    ReadAction readAction = new ReadAction();
    ReadActionParams params = new ReadActionParams();
    params.setFile(psiFile.getVirtualFile().getUrl());

    //设置range
    TextRange textRange = getCodeRange(editor);
    Range range = new Range();
    Position start = new Position();
    LogicalPosition logicalStart = editor.offsetToLogicalPosition(textRange.getStartOffset());
    start.setLine(logicalStart.line);
    start.setCharacter(logicalStart.column);
    range.setStart(start);

    Position end = new Position();
    LogicalPosition logicalEnd = editor.offsetToLogicalPosition(textRange.getEndOffset());
    end.setLine(logicalEnd.line);
    end.setCharacter(logicalEnd.column);
    range.setEnd(end);
    params.setRange(range);

    //开始设置其他信息
    ActionContext context = new  ActionContext();
    context.setTriggerKind(EditEventTriggerKind.Invoke);
    List<Diagnostic> diagnostics = listDiagnostic(project, editor, textRange);
    context.setDiagnostics(diagnostics);
    params.setContext(context);
    readAction.setParams(params);

    return readAction;
  }
  private static boolean fileSelectorFilter(List<DocumentFilter> selector, PsiFile psiFile) {
    if (CollectionUtils.isEmpty(selector)) {
      return true;
    }
    String language = ExtensionEnum.getLanguageByPath(psiFile.getVirtualFile().getUrl());
    return selector.stream().anyMatch(filter -> StringUtils.equals(language, filter.getLanguage()));
  }

  public static TextRange getCodeRange(Editor editor) {
    int endOffset;
    SelectionModel selectionModel = editor.getSelectionModel();
    int startOffset = selectionModel.getSelectionStart();
    if (startOffset == (endOffset = selectionModel.getSelectionEnd())) {
      LogicalPosition logicalPosition = editor.getCaretModel().getLogicalPosition();
      startOffset = editor.getDocument().getLineStartOffset(logicalPosition.line);
      endOffset = editor.getDocument().getLineEndOffset(logicalPosition.line);
    }
    TextRange range = new TextRange(startOffset, endOffset);
    return range;
  }

  private static List<Diagnostic> listDiagnostic(Project project, Editor editor, TextRange textRange) {
    List<HighlightInfo> highlightInfos = DaemonCodeAnalyzerImpl.getHighlights(editor.getDocument(), HighlightSeverity.WEAK_WARNING, project);
    List<Diagnostic> errorMessageList = new ArrayList<>();
    for (HighlightInfo info : highlightInfos) {
      int infoStartOffset = info.getStartOffset();
      int infoEndOffset = info.getEndOffset();
      if (!textRange.intersectsStrict(infoStartOffset, infoEndOffset) || StringUtils.isBlank(info.getDescription())) {
        continue;
      }
      Diagnostic diagnostic = new Diagnostic();
      //设置此高亮波浪线的code内容
      TextRange infoTextRange = new TextRange(infoStartOffset, infoEndOffset);
      diagnostic.setCode(editor.getDocument().getText(infoTextRange));

      //设置此高亮线(波浪线)的行数和列数
      LogicalPosition infoStart = editor.offsetToLogicalPosition(infoStartOffset);
      LogicalPosition infoEnd = editor.offsetToLogicalPosition(infoEndOffset);
      Range infoRange = new Range();
      Position start = new Position();
      start.setLine(infoStart.line);
      start.setCharacter(infoStart.column);
      Position end = new Position();
      end.setLine(infoEnd.line);
      end.setCharacter(infoEnd.column);
      infoRange.setStart(start);
      infoRange.setEnd(end);
      diagnostic.setRange(infoRange);

      if (info.getSeverity() == HighlightSeverity.ERROR) {
        diagnostic.setSeverity(Severity.Error);
      } else if (info.getSeverity() == HighlightSeverity.WARNING) {
        diagnostic.setSeverity(Severity.Warning);
      } else {
        diagnostic.setSeverity(Severity.Hint);
      }
      //录入错误/警告信息
      diagnostic.setMessage(info.getDescription());
      errorMessageList.add(diagnostic);
    }
    return errorMessageList;
  }
}

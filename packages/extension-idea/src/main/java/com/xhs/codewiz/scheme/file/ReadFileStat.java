package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 获取文件状态
 */
public class ReadFileStat {
    private String schemaProtocol = "file.read.filestat";
    private ReadFileStatParams params;

    public ReadFileStatParams getParams() {
        return params;
    }
    public void setParams(ReadFileStatParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadFileStatParams {
        private String uri;
    
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.CompletionTriggerKind;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.file.Uri;
/**
 * 获取当前联想内容
 */
public class ReadCompletion {
    private String schemaProtocol = "workspace.read.completion";
    private ReadCompletionParams params;

    public ReadCompletionParams getParams() {
        return params;
    }
    public void setParams(ReadCompletionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadCompletionParams {
        /** 联想提供者的 ID */
        private String provider;
        private ReadCompletionParamsContext context;
        private Position position;
        private String uri;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public ReadCompletionParamsContext getContext() {
            return context;
        }
        public void setContext(ReadCompletionParamsContext context) {
            this.context = context;
        }
        public Position getPosition() {
            return position;
        }
        public void setPosition(Position position) {
            this.position = position;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }

        public static class ReadCompletionParamsContext {
        /** 触发联想的字符 */
        private String triggerCharacter; // optional
        private CompletionTriggerKind triggerKind;
    
        public String getTriggerCharacter() {
            return triggerCharacter;
        }
        public void setTriggerCharacter(String triggerCharacter) {
            this.triggerCharacter = triggerCharacter;
        }
        public CompletionTriggerKind getTriggerKind() {
            return triggerKind;
        }
        public void setTriggerKind(CompletionTriggerKind triggerKind) {
            this.triggerKind = triggerKind;
        }
    }
}

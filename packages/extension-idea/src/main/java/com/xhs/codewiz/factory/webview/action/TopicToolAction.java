package com.xhs.codewiz.factory.webview.action;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.fileEditor.ex.FileEditorManagerEx;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.factory.editor.TopicFileEditor;
import com.xhs.codewiz.factory.editor.TopicPanelUtil;
import com.xhs.codewiz.factory.webview.WebviewBuilder;
import com.xhs.codewiz.utils.JBCefBrowserUtil;

/**
 * <AUTHOR>
 * @date 2025/8/8 21:26
 */
public class TopicToolAction extends AnAction {


    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        Project project = anActionEvent.getProject();
        FileEditorManagerEx fileEditorManager = FileEditorManagerEx.getInstanceEx(project);
        TopicFileEditor existingEditor = TopicPanelUtil.getExistingTopicEditor(fileEditorManager);
        WebviewBuilder webviewBuilder = JBCefBrowserUtil.getWebviewBuilderByChannel(
                existingEditor.getVirtualFile().getRemoteChannel(), existingEditor.getVirtualFile().getWebviewId());
        webviewBuilder.getJbCefBrowser().openDevtools();
    }
}

package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.CompletionOptions;
/**
 * 获取内联补全
 */
public class ReadCompletionInline {
    private String schemeProtocol = "content.read.completionInline";
    private Uri file;
    private ReadCompletionInlineContext context;
    private Position position;
    private CompletionOptions option; // optional

    public Uri getFile() {
        return file;
    }
    public void setFile(Uri file) {
        this.file = file;
    }
    public ReadCompletionInlineContext getContext() {
        return context;
    }
    public void setContext(ReadCompletionInlineContext context) {
        this.context = context;
    }
    public Position getPosition() {
        return position;
    }
    public void setPosition(Position position) {
        this.position = position;
    }
    public CompletionOptions getOption() {
        return option;
    }
    public void setOption(CompletionOptions option) {
        this.option = option;
    }

        public static class ReadCompletionInlineContext {
        private EditEventTriggerKind triggerKind;
    
        public EditEventTriggerKind getTriggerKind() {
            return triggerKind;
        }
        public void setTriggerKind(EditEventTriggerKind triggerKind) {
            this.triggerKind = triggerKind;
        }
    }
}

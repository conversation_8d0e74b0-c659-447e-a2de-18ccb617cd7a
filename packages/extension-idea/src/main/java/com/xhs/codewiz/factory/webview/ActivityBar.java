package com.xhs.codewiz.factory.webview;

import javax.swing.Icon;

import org.apache.commons.lang3.StringUtils;

/**
 * ActivityBar信息存储类
 * 对应VSCode扩展中的activitybar配置
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
public class ActivityBar {

    /**
     * ActivityBar的唯一标识符
     */
    private String id;

    /**
     * ActivityBar的显示标题
     */
    private String title;

    /**
     * ActivityBar的图标路径
     */
    private String icon;

    /**
     * ActivityBar的图标对象
     */
    private Icon iconObject;

    /**
     * ActivityBar的默认位置
     */
    private String defaultPosition;

    public ActivityBar() {
    }

    public ActivityBar(String id, String title, String icon) {
        this.id = id;
        this.title = title;
        this.icon = icon;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Icon getIconObject() {
        return iconObject;
    }

    public void setIconObject(Icon iconObject) {
        this.iconObject = iconObject;
    }

    public String getDefaultPosition() {
        if (StringUtils.isEmpty(defaultPosition)) {
            return "right";
        }
        return defaultPosition;
    }

    public void setDefaultPosition(String defaultPosition) {
        this.defaultPosition = defaultPosition;
    }

    @Override
    public String toString() {
        return "ActivityBar{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", icon='" + icon + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        ActivityBar that = (ActivityBar) o;

        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
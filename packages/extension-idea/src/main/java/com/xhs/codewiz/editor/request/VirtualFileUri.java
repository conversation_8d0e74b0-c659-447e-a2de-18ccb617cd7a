package com.xhs.codewiz.editor.request;

import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.util.SystemInfo;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.openapi.vfs.VirtualFileSystem;
import com.intellij.openapi.vfs.ex.temp.TempFileSystem;
import java.lang.reflect.Type;
import java.net.URI;
import java.net.URISyntaxException;
import org.jetbrains.annotations.NotNull;

public final class VirtualFileUri {
    private static final Logger LOG = Logger.getInstance(VirtualFileUri.class);
    private final @NotNull String uri;

    public static @NotNull VirtualFileUri from(@NotNull VirtualFile file) {
        String uri;
        try {
            String prefix = isNeedsPathPrefix(file.getFileSystem()) ? "/" : "";
            String processedPath = processPath(file.getPath());
            uri = (new URI(file.getFileSystem().getProtocol(), "", prefix + processedPath, (String)null)).toString();
        } catch (URISyntaxException var4) {
            uri = asPrefixedUri(file.getUrl());
            LOG.warn("Unable to parse as compliant URI, using fallback: " + uri);
        }

        return new VirtualFileUri(uri);
    }

    public static @NotNull VirtualFileUri from(@NotNull VirtualFileSystem fileSystem, @NotNull String path) {
        String prefix = isNeedsPathPrefix(fileSystem) && !path.startsWith("/") ? "/" : "";
        String processedPath = processPath(path);
        return new VirtualFileUri(VirtualFileManager.constructUrl(fileSystem.getProtocol(), prefix + processedPath));
    }

    private static @NotNull String processPath(@NotNull String path) {
        return SystemInfo.isWindows && path.startsWith("//") ? path.replace('/', '\\').replace("$", "%24") : path;
    }

    private static boolean isNeedsPathPrefix(@NotNull VirtualFileSystem fileSystem) {
        return SystemInfo.isWindows && fileSystem instanceof LocalFileSystem && !(fileSystem instanceof TempFileSystem);
    }

    static @NotNull String asPrefixedUri(@NotNull String url) {
        if (SystemInfo.isWindows) {
            if (url.startsWith("file:////")) {
                String var10000 = url.substring("file://".length()).replace("/", "%5C");
                return "file:///" + var10000.replace("$", "%24");
            }

            if (url.startsWith("file://") && !url.startsWith("file:///")) {
                return "file:///" + url.substring("file://".length());
            }
        }

        return url;
    }

    public VirtualFileUri(@NotNull String uri) {
        this.uri = uri;
    }

    public @NotNull String getUri() {
        return this.uri;
    }

    public static final class TypeAdapter implements JsonSerializer<VirtualFileUri> {
        public TypeAdapter() {
        }

        public JsonElement serialize(VirtualFileUri file, Type type, JsonSerializationContext context) {
            return new JsonPrimitive(file.uri);
        }
    }
}


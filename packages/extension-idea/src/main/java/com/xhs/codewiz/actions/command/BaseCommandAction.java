package com.xhs.codewiz.actions.command;

import com.intellij.openapi.actionSystem.ActionUpdateThread;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import java.util.HashMap;
import java.util.Map;
import javax.swing.Icon;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-08-01 Description:
 */
public class BaseCommandAction extends AnAction {
  private Map<String, Object> ext = new HashMap<>();

  BaseCommandAction(String text, String desc , Icon icon) {
    super(text, desc, icon);
  }
  @Override
  public void actionPerformed(@NotNull AnActionEvent anActionEvent) {

  }
  public void setExt(String key, Object value) {
    ext.put(key, value);
  }
  public Object getExt(String key) {
    return ext.get(key);
  }
  @Override
  public ActionUpdateThread getActionUpdateThread() {
    // 可选 BGT (后台线程) 或 EDT (事件分发线程)
    return ActionUpdateThread.BGT;
  }
}

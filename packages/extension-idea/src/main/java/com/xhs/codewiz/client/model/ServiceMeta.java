package com.xhs.codewiz.client.model;

/**
 * Author: liukunpeng Date: 2025-07-15 Description:
 */
public class ServiceMeta {

  /**
   * local:本地生成的随机 ID,也是 Channel
   * remote:远端插件 ID，也是消息 Channel
   */
  private String id;
  /**
   * local:该实例的本地自定义名称
   * remote:远端需要使用的插件名称
   */
  private String name;
  /**
   * local:服务的本地自定义版本号, 格式为 "x.y.z"
   * remote:远端需要使用的插件版本号, 格式为 x.y.z
   */
  private String version;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getVersion() {
    return version;
  }

  public void setVersion(String version) {
    this.version = version;
  }
}

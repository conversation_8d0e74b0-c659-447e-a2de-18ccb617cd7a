package com.xhs.codewiz.client.model;

import java.util.List;

/**
 * Author: liukunpeng Date: 2025-07-15 Description:
 */
public class ServiceRegisterRequest {
  private ServiceMeta service;
  private List<Dependence> dependences;

  public ServiceMeta getService() {
    return service;
  }
  public void setService(ServiceMeta service) {
    this.service = service;
  }
  public List<Dependence> getDependences() {
    return dependences;
  }
  public void setDependences(List<Dependence> dependences) {
    this.dependences = dependences;
  }

}

package com.xhs.codewiz.utils;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.intellij.execution.filters.ExceptionWorker;
import com.intellij.lang.Language;
import com.intellij.lang.LanguageUtil;
import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.LogicalPosition;
import com.intellij.openapi.editor.SelectionModel;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Computable;
import com.intellij.openapi.util.TextRange;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.pom.java.LanguageLevel;
import com.intellij.psi.PsiClass;
import com.intellij.psi.PsiComment;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiField;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiLiteralValue;
import com.intellij.psi.PsiMethod;
import com.intellij.psi.impl.source.tree.CompositeElement;
import com.intellij.psi.search.FilenameIndex;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.psi.util.PsiUtil;
import com.xhs.codewiz.completion.util.CompletionUtil;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-03-06 Description:
 */
public class CodeWizPsiUtil {
  public static final String DEFAULT_CLASS_NAME = "DemoClass";
  private static Logger log = Logger.getInstance(CodeWizPsiUtil.class);
  private static final Set<String> INVALID_METHOD_NAMES = new HashSet<String>(
      Arrays.asList("equals", "hashCode", "toString"));
  private static Cache<String, Boolean> classInstanceTypeCache = Caffeine.newBuilder().maximumSize(3000L).build();
  private static final List<String> COMMON_QUALIFIED_PREFIXES = Arrays.asList("java.", "javax.", "javafx.", "sun.", "org.apache.", "org.springframework.", "lombok.", "org.projectlombok", "org.aspectj.", "org.hibernate.", "org.junit.", "org.mockito.", "org.powermock.", "org.testng.", "org.jmock.", "org.slf4j", "ch.qos.logback", "kotlin.", "com.fasterxml.", "com.alibaba.fastjson", "org.mockito", "org.mybatis.", "io.swagger.", "io.springfox.", "com.github.swaggerdoc.", "org.springdoc.", "net.logstash.logback");
  private static final List<String> commentFlags = Arrays.asList("/", "#", "\"\"\"", "'''", "/*", "*");

  /**
   * 校验是否是合法有效的java方法
   * @param element
   * @return
   */
  public static boolean isInvalidJavaMethod(@NotNull PsiElement element) {
    PsiMethod method;
    //不是方法类型时直接返回
    if (!(element instanceof PsiMethod)) {
      return false;
    }
    //常见的非业务方法需要排除
    if (INVALID_METHOD_NAMES.contains((method = (PsiMethod)element).getName())) {
      return false;
    }
    //构造方法需要排除
    if (method.isConstructor()) {
      return false;
    }
    //抽象方法需要排斥
    if (isJavaAbstractMethod(method)) {
      return false;
    }
    //父级节点不是类时，需要排除
    if (!instanceOf(method.getParent(), "com.intellij.psi.PsiClass")) {
      return true;
    }
    PsiClass clazz = (PsiClass)method.getParent();
    PsiField[] fields = clazz.getAllFields();
    //类内没有字段时，那么该方法必然是有效方法
    if (fields.length == 0) {
      return true;
    }
    String methodName = method.getName().toLowerCase(Locale.ROOT);
    //存在字段时，需要排除赋值/取值方法
    for (PsiField field : fields) {
      String fieldName = field.getName().toLowerCase(Locale.ROOT);
      String setMethodName = "set" + fieldName;
      String getMethodName = "get" + fieldName;
      String isMethodName = "is" + fieldName;
      if (!setMethodName.equals(methodName) && !getMethodName.equals(methodName) && !isMethodName.equals(methodName) && !methodName.equals(fieldName)) continue;
      return false;
    }
    return true;
  }

  public static boolean isJavaAbstractMethod(@NotNull PsiMethod method) {
    if (method.hasModifierProperty("abstract")) {
      return true;
    }
    PsiClass aClass = method.getContainingClass();
    return aClass != null && aClass.isInterface() && isJavaDefaultMethod(aClass, method);
  }
  public static boolean isJavaDefaultMethod(@NotNull PsiClass aClass, @NotNull PsiMethod method) {
    return method.hasModifierProperty("default") && PsiUtil.getLanguageLevel(aClass).isAtLeast(
        LanguageLevel.JDK_1_8);
  }

  public static boolean instanceOf(Object obj, Class... possibleClasses) {
    if (obj == null || possibleClasses == null) {
      return false;
    }
    for (Class cls : possibleClasses) {
      if (cls.isInstance(obj)) {
        return true;
      }
    }
    return false;
  }

  public static boolean instanceOf(Object obj, String ... possibleClassNames) {
    if (obj == null || possibleClassNames == null) {
      return false;
    }
    String allClassNames = String.join(",", possibleClassNames);
    String cacheKey = String.format("%s:%s", obj.getClass().getName(), allClassNames);
    Boolean result = classInstanceTypeCache.getIfPresent(cacheKey);
    if (result != null) {
      return result;
    }
    result = internalInstanceOf(obj, possibleClassNames);
    classInstanceTypeCache.put(cacheKey, result);
    return result;
  }
  private static boolean internalInstanceOf(Object obj, String ... possibleClassNames) {
    String objClassName = obj.getClass().getName();
    for (String className : possibleClassNames) {
      try {
        Class<?> clazz;
        if (className.equals(objClassName)) {
          return true;
        }
        if (className.contains("$") || (clazz = ReflectUtil.classForName(className)) == null || !clazz.isInstance(obj)) continue;
        return true;
      }
      catch (ClassNotFoundException clazz) {
      }
      catch (Exception e) {
        LoggerUtil.INSTANCE.logDebug(log, "fail to instanceOf Class:" + className);
      }
    }
    return false;
  }

  public static String getLanguageByIDE() {
    String ideName = getIdeName();
    if (ideName.toLowerCase().contains("idea")) {
      return "java";
    }
    if (ideName.toLowerCase().contains("pycharm")) {
      return "python";
    }
    if (ideName.toLowerCase().contains("clion")) {
      return "c";
    }
    if (ideName.toLowerCase().contains("webstorm")) {
      return "javascript";
    }
    if (ideName.toLowerCase().contains("goland")) {
      return "go";
    }
    return null;
  }
  private static String getIdeName() {
    return ApplicationInfo.getInstance().getVersionName();
  }


  public static String getPsiClassName(PsiFile psiFile, SelectionModel selectionModel) {
    if (psiFile == null) {
      return DEFAULT_CLASS_NAME;
    }
    try {
      Class.forName("com.intellij.psi.PsiClass");
      return getJavaPsiClassName(psiFile, selectionModel);
    } catch (ClassNotFoundException e) {
      return DEFAULT_CLASS_NAME;
    }
  }
  private static String getJavaPsiClassName(PsiFile psiFile, SelectionModel selectionModel) {
    CodeWizEditorUtil.SelectionModelInfo info = CodeWizEditorUtil.getSelectionModelInfo(selectionModel);
    int start = info.getSelectionStart();
    int end = info.getSelectionEnd();
    PsiElement startElement = psiFile.findElementAt(start);
    PsiElement endElement = psiFile.findElementAt(end);
    PsiClass startClass = PsiTreeUtil.getParentOfType(startElement, PsiClass.class, false);
    PsiClass endClass = PsiTreeUtil.getParentOfType(endElement, PsiClass.class,false);
    if (startClass != null && startClass.equals(endClass)) {
      return startClass.getName();
    }
    return "DemoClass";
  }

  public static String getPsiMethodContent(Document document, PsiFile psiFile, SelectionModel selectionModel) {
    CodeWizEditorUtil.SelectionModelInfo info = CodeWizEditorUtil.getSelectionModelInfo(selectionModel);
    String result = info.getSelectedText();
    if (psiFile == null) {
      return result;
    }
    try {
      Class.forName("com.intellij.psi.PsiMethod");
      return getJavaPsiMethodContent(document, psiFile, info);
    }
    catch (ClassNotFoundException e) {
      return result;
    }
  }
  private static String getJavaPsiMethodContent (Document document, PsiFile psiFile, CodeWizEditorUtil.SelectionModelInfo info) {
    String result = info.getSelectedText();
    int start = info.getSelectionStart();
    int end = info.getSelectionEnd();
    PsiElement startElement = psiFile.findElementAt(start);
    PsiMethod startMethod = PsiTreeUtil.getParentOfType(startElement, PsiMethod.class);
    PsiElement endElement = psiFile.findElementAt(end);
    PsiMethod endMethod = PsiTreeUtil.getParentOfType(endElement, PsiMethod.class);
    if (startMethod == null && endMethod == null) {
      return result;
    }
    if (startMethod != null && endMethod == null) {
      return startMethod.getText();
    }
    if (startMethod == null && endMethod != null) {
      return endMethod.getText();
    }
    if (document != null) {
      result = document.getText(new TextRange(startMethod.getTextRange().getStartOffset(), endMethod.getTextRange().getEndOffset()));
    }
    return result;
  }

  public static boolean isCommonType(@NotNull String qualifiedName) {
    for (String prefix : COMMON_QUALIFIED_PREFIXES) {
      if (!qualifiedName.startsWith(prefix)) continue;
      return true;
    }
    return false;
  }

  public static String findErrorLineContent(Project project, Editor editor, int line) {
    return findErrorLineContentByDefault(project, editor, line);
  }

  /*
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  public static String findErrorLineContentByDefault(Project project, Editor editor, int line) {
    while (line < editor.getDocument().getLineCount()) {
      String lineContent = editor.getDocument().getText(new TextRange(editor.getDocument().getLineStartOffset(line), editor.getDocument().getLineEndOffset(line)));
      ExceptionWorker.ParsedLine myInfo = ExceptionWorker.parseExceptionLine(lineContent);
      if (myInfo == null || myInfo.fileName == null) {
        ++line;
        continue;
      }
      String fileName = myInfo.fileName;
      int documentLine = myInfo.lineNumber;
      String classFullPath = lineContent.substring(myInfo.classFqnRange.getStartOffset(), myInfo.classFqnRange.getEndOffset());
      ArrayList<VirtualFile> vFiles = new ArrayList<>(
          FilenameIndex.getVirtualFilesByName(project, fileName, GlobalSearchScope.projectScope(project)));
      if (CollectionUtils.isEmpty(vFiles)) {
        ++line;
        continue;
      }
      VirtualFile vFile =findMostRelatedVirtualFile(vFiles, classFullPath);
      LoggerUtil.INSTANCE.logDebug(log, "Find stacktrace related vfs " + vFile.getName());
      try {
        String content = new String(vFile.contentsToByteArray(true));
        Language language = LanguageUtil.getFileLanguage(vFile);
        String languageStr = null;
        if (language != null) {
          languageStr = language.getDisplayName().toLowerCase();
        }
        StringBuilder sb = getStringBuilder(content, documentLine, languageStr);
        String string = sb.toString();
        return string;
      }
      catch (IOException e) {
        LoggerUtil.INSTANCE.logWarn(log, "vFile parse exception. ", e);
      }
      finally {
        ++line;
      }
    }
    return null;
  }
  public static VirtualFile findMostRelatedVirtualFile(List<VirtualFile> virtualFiles, String classFullPath) {
    if (CollectionUtils.isEmpty(virtualFiles) || classFullPath == null) {
      return null;
    }
    for (VirtualFile virtualFile : virtualFiles) {
      String vPath = virtualFile.getPath();
      int extPos = vPath.lastIndexOf(".");
      if (extPos > 0) {
        vPath = vPath.substring(0, extPos);
      }
      if (vPath.replace("/", ".").endsWith(classFullPath)) {
        continue;
      }
      return virtualFile;
    }
    return virtualFiles.get(0);
  }

  @NotNull
  public static StringBuilder getStringBuilder(String content, int documentLine, String languageStr) {
    String[] contentLines = content.split("\n");
    StringBuilder sb = new StringBuilder();
    sb.append("```");
    if (StringUtils.isNotBlank(languageStr)) {
      sb.append(languageStr);
    }
    sb.append("\n");
    sb.append(findCompleteCodeBlock(contentLines, documentLine, "{", "}", 10));
    sb.append("\n");
    return sb;
  }
  public static String findCompleteCodeBlock(String[] contentLines, int documentLine, String blockStartSymbol, String blockEndSymbol, int maxSearchLine) {
    int i = 0;
    boolean found = false;
    while (documentLine - i >= 0 && i < maxSearchLine) {

      if (documentLine - i >= contentLines.length) {
        break;
      }
      String line = contentLines[documentLine - i];
      if (line.endsWith(blockStartSymbol)) {
        found = true;
        break;
      }
      i++;
    }
    int j = 0;
    if (found) {
      while (documentLine + j <= contentLines.length - 1 && j < maxSearchLine) {
        String line = contentLines[documentLine + j];
        if (line.endsWith(blockEndSymbol)) {
          break;
        }
        j++;
      }
    } else {
      j = maxSearchLine;
    }
    StringBuilder sb = new StringBuilder();
    for (int k = Math.max(documentLine - i, 0); k <= Math.min(documentLine + j, contentLines.length - 1); k++) {
      sb.append(contentLines[k]);
      sb.append("\n");
    }
    if (sb.length() > 1) {
      sb.setLength(sb.length() - 1);
    }
    return sb.toString();
  }
  public static PsiElement getCaratElement(Editor editor) {
    return ApplicationManager.getApplication().runReadAction(
        (Computable<PsiElement>) () -> {
          if (editor.getProject() == null) {
            return null;
          }
          int offset = CodeWizEditorUtil.getCaretOffset(editor);
          PsiFile psiFile = PsiDocumentManager.getInstance(editor.getProject()).getPsiFile(editor.getDocument());
          PsiElement psiElement = null;
          if (psiFile != null && offset > 0) {
            psiElement = findElementAtOffset(psiFile, offset);
          }
          return psiElement;
        });
  }
  public static PsiElement findElementAtOffset(PsiFile psiFile, int caretOffset) {
    PsiElement element = findPrevAtOffset(psiFile, caretOffset,  new Class[0]);
    if (element == null) {
      element = findNextAtOffset(psiFile, caretOffset,  new Class[0]);
    }
    return element;
  }
  public static PsiElement findPrevAtOffset(PsiFile psiFile, int caretOffset, Class... toSkip) {
    PsiElement element;
    if (caretOffset < 0) {
      return null;
    }
    int lineStartOffset = 0;
    Document document = PsiDocumentManager.getInstance(psiFile.getProject()).getDocument(psiFile);
    if (document != null) {
      int lineNumber = document.getLineNumber(caretOffset);
      lineStartOffset = document.getLineStartOffset(lineNumber);
    }
    do {
      caretOffset--;
      element = psiFile.findElementAt(caretOffset);
    }
    while (caretOffset >= lineStartOffset && (element == null || instanceOf(element, toSkip)));
    return instanceOf(element, toSkip) ? null : element;
  }
  public static PsiElement findNextAtOffset(PsiFile psiFile, int caretOffset, Class... toSkip) {
    PsiElement element = psiFile.findElementAt(caretOffset);
    if (element == null) {
      return null;
    }
    Document document = PsiDocumentManager.getInstance(psiFile.getProject()).getDocument(psiFile);
    int lineEndOffset = 0;
    if (document != null) {
      int lineNumber = document.getLineNumber(caretOffset);
      lineEndOffset = document.getLineEndOffset(lineNumber);
    }
    while (caretOffset < lineEndOffset && instanceOf(element, toSkip)) {
      caretOffset++;
      element = psiFile.findElementAt(caretOffset);
    }
    return instanceOf(element, toSkip) ? null : element;
  }
  public static boolean isCommentElement(PsiElement element, @NotNull Editor editor) {
    return ApplicationManager.getApplication().runReadAction(
        (Computable<Boolean>) () -> {
          TextRange range;
          if (element instanceof PsiComment|| instanceOf(element, "com.goide.psi.impl.GoCommentImpl", "com.intellij.psi.impl.source.tree.PsiCommentImpl", "com.jetbrains.cidr.lang.editor.OCASTFactory$PsiCommentPlaceholder", "com.intellij.sql.dialects.base.SqlParserDefinitionBase$SqlCommentImpl")) {
            return true;
          }
          if (element.getParent() != null && instanceOf(element.getParent(), "com.intellij.psi.javadoc.PsiDocTag", "com.intellij.psi.PsiComment", "com.intellij.lang.javascript.psi.jsdoc.impl.JSDocCommentImpl")) {
            return true;
          }
          if (commentFlags.stream().anyMatch(e -> element.getText().trim().startsWith(e))) {
            return true;
          }
          int caretOffset = CodeWizEditorUtil.getCaretOffset(editor);
          if (element.getPrevSibling() != null && element.getPrevSibling() instanceof PsiComment && (range = element.getPrevSibling().getTextRange()) != null) {
            if (range.contains(caretOffset)) {
              return true;
            }
            if (editor.getDocument().getLineNumber(range.getEndOffset()) == editor.getDocument().getLineNumber(caretOffset)) {
              return true;
            }
          }
          return element.getPrevSibling() != null
              && commentFlags.stream().anyMatch(e -> getElementText(element.getPrevSibling()).trim().startsWith(e))
              && !instanceOf(element.getPrevSibling(), "com.intellij.psi.PsiMethod")
              && editor.getDocument().getLineNumber(element.getPrevSibling().getTextOffset()) == editor.getDocument().getLineNumber(caretOffset);
        });
  }
  public static String getElementText(PsiElement element) {
    if (element != null) {
      return ApplicationManager.getApplication().runReadAction(
          (Computable<String>) () -> element.getText());
    }
    return "";
  }

  public static boolean checkCaretAround(Editor editor) {
    int offset = CodeWizEditorUtil.getCaretOffset(editor);
    LogicalPosition logicalPosition = editor.getCaretModel().getLogicalPosition();
    int lineStartOffset = editor.getDocument().getLineStartOffset(logicalPosition.line);
    int lineEndOffset = editor.getDocument().getLineEndOffset(logicalPosition.line);
    int caretOffset = offset - lineStartOffset;
    String lineText = editor.getDocument().getText(new TextRange(lineStartOffset, lineEndOffset));
    if (caretOffset > 0 && caretOffset < lineText.length()) {
      char afterChar = lineText.charAt(caretOffset);
      char beforeChar = lineText.charAt(caretOffset - 1);
      if (isValidCodeTokenChar(afterChar) && (isValidCodeTokenChar(beforeChar) || beforeChar == '(')) {
        LoggerUtil.INSTANCE.logInfo(log, "invalid position in word middle");
        return false;
      }
      if (caretOffset > 1) {
        char moreBeforeChar = lineText.charAt(caretOffset - 2);
        if (isValidCodeTokenChar(afterChar) && (beforeChar == '=' || beforeChar == ' ' && moreBeforeChar == '=')) {
          LoggerUtil.INSTANCE.logInfo(log, "invalid position after = xxxx");
          return false;
        }
      }
    }
    return true;
  }
  public static boolean isImportElement(PsiElement element, @NotNull Editor editor) {
    if (element != null && instanceOf(element.getParent(), "com.intellij.psi.impl.source.PsiImportList", "com.goide.psi.impl.GoImportDeclarationImpl")) {
      return true;
    }
    String lineCode = getLineTextAtCaret(editor);
    return lineCode.startsWith("import ") || lineCode.startsWith("from ") || lineCode.startsWith("using ");
  }
  public static String getLineTextAtCaret(@NotNull Editor editor) {
    int caretPosition = CodeWizEditorUtil.getCaretOffset(editor);
    int lineNumber = editor.getDocument().getLineNumber(caretPosition);
    int startOffset = editor.getDocument().getLineStartOffset(lineNumber);
    int endOffset = editor.getDocument().getLineEndOffset(lineNumber);
    return editor.getDocument().getText(new TextRange(startOffset, endOffset));
  }

  public static boolean isLiteralElement(Editor editor, PsiElement element) {
    if (element == null) {
      return false;
    }
    if (instanceOf(element.getParent(), "com.intellij.lang.javascript.psi.ecma6.impl.JSXXmlLiteralExpressionImpl")) {
      return false;
    }
    if (instanceOf(element, "com.jetbrains.python.psi.PyStringElement", "com.intellij.psi.PsiLiteralValue")) {
      return true;
    }
    if (instanceOf((element.getParent()), "com.jetbrains.python.psi.PyLiteralExpression", "com.goide.psi.impl.GoStringLiteralImpl", "com.jetbrains.rider.languages.fileTypes.csharp.psi.impl.CSharpNonInterpolatedStringLiteralExpressionImpl", "com.jetbrains.cidr.lang.psi.impl.OCLiteralExpressionImpl", "com.intellij.lang.javascript.psi.impl.JSLiteralExpressionImpl")) {
      return true;
    }
    if (element.getParent() instanceof CompositeElement && ((CompositeElement)element.getParent()).getPsi() instanceof PsiLiteralValue) {
      return true;
    }
    String text = getElementText(element);
    if (StringUtils.isNotBlank(text) && text.startsWith("\"") && text.endsWith("\"")) {
      return true;
    }
    return CompletionUtil.isFrontFile(editor.getDocument()) && instanceOf(element.getParent(), "com.intellij.lang.javascript.psi.e4x.impl.JSXmlAttributeValueImpl", "com.intellij.psi.impl.source.xml.XmlAttributeValueImpl");
  }

  private static boolean isValidCodeTokenChar(char ch) {
    return Character.isJavaIdentifierPart(ch) || ch == '_' || ch == '$';
  }
}

package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.Comment;
/**
 * 更新单个 Comment
 */
public class UpdateComment {
    private String schemaProtocol = "content.update.comment";
    private UpdateCommentParams params;

    public UpdateCommentParams getParams() {
        return params;
    }
    public void setParams(UpdateCommentParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateCommentParams {
        /** Comment Provider ID，表示该 Comment 属于哪个 Comment Provider */
        private String provider;
        private Comment comment;
        /** Comment 所在的 Comments 合集 ID */
        private String collectionId;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public Comment getComment() {
            return comment;
        }
        public void setComment(Comment comment) {
            this.comment = comment;
        }
        public String getCollectionId() {
            return collectionId;
        }
        public void setCollectionId(String collectionId) {
            this.collectionId = collectionId;
        }
    }
}

package com.xhs.codewiz.listener;

import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.text.LineColumn;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileSystem;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.util.concurrency.annotations.RequiresReadLock;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import com.xhs.codewiz.editor.language.LanguageInfoManager;
import com.xhs.codewiz.editor.request.LanguageInfo;
import com.xhs.codewiz.editor.request.TextDocumentIdentifier;
import com.xhs.codewiz.editor.request.TextDocumentItem;
import com.xhs.codewiz.editor.request.VersionedTextDocumentIdentifier;
import com.xhs.codewiz.editor.request.VirtualFileUri;
import com.xhs.codewiz.type.content.Position;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class LSPUtil {
    private LSPUtil() {
    }

    static @NotNull TextDocumentIdentifier identifier(@NotNull VirtualFile file) {
        return new TextDocumentIdentifier(VirtualFileUri.from(file));
    }

    static @NotNull TextDocumentIdentifier identifier(@NotNull VirtualFileSystem fileSystem, @NotNull String path) {
        return new TextDocumentIdentifier(VirtualFileUri.from(fileSystem, path));
    }

    @RequiresReadLock
    static @Nullable TextDocumentItem createTextDocument(@NotNull VirtualFile file, @NotNull LanguageInfo language) {
        Document document = FileDocumentManager.getInstance().getDocument(file);
        return document == null ? null : new TextDocumentItem(VirtualFileUri.from(file), language.getVSCodeIdWithFallback(), version(document), document.getText());
    }

    public static int version(@NotNull Document document) {
        return (int) CodeWizEditorUtil.getDocumentModificationStamp(document);
    }

    static @Nullable VersionedTextDocumentIdentifier versionedIdentifier(@NotNull Document doc) {
        VirtualFile file = FileDocumentManager.getInstance().getFile(doc);
        return file == null ? null : new VersionedTextDocumentIdentifier(VirtualFileUri.from(file), version(doc));
    }

    static @NotNull Position position(@NotNull Document document, int offset) {
        int line = document.getLineNumber(offset);
        int lineStart = document.getLineStartOffset(line);
        int column = offset - lineStart;

        assert column >= 0;
        Position position = new Position();
        position.setLine(line);
        position.setCharacter(column);
        return position;
    }

    static @NotNull Position shiftByText(@NotNull Position position, @NotNull CharSequence text) {
        if (text.isEmpty()) {
            return position;
        } else {
            LineColumn offset = StringUtil.offsetToLineColumn(text, text.length());
            int line = offset.line;
            int column = line == 0 ? position.getCharacter() + offset.column : offset.column;
            position.setLine(position.getLine() + line);
            position.setCharacter(column);
            return position;
        }
    }

    @RequiresReadLock
    static @NotNull LanguageInfo findLanguage(@Nullable Project project, @NotNull VirtualFile file) {
        PsiFile psiFile = project != null && !project.isDisposed() ? PsiManager.getInstance(project).findFile(file) : null;
        return psiFile == null ? LanguageInfoManager.findFallback(file) : LanguageInfoManager.findLanguageMapping(psiFile);
    }
}


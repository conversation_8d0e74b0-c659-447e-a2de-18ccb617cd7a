package com.xhs.codewiz.client.websocket;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RcsWebSocketUtils {
  public static final Pattern CONTENT_LENGTH_HEADER_PATTERN = Pattern.compile("Content-Length: \\d+\r\n");
  public static final Pattern ITEMS_NULL_PATTERN = Pattern.compile(".*(\"items\":null).*");
  public static final String ITEMS_NULL_REPLACE_STR = "\"items\":[]";

  public static String removeContentLengthHeader(CharSequence input) {
    Matcher matcher = CONTENT_LENGTH_HEADER_PATTERN.matcher(input);
    if (!matcher.find()) {
      return input.toString();
    }
    input = input.subSequence(matcher.end(), input.length());
    return input.toString();
  }

  public static CharSequence replaceItemsNull(CharSequence input) {
    Matcher matcher = ITEMS_NULL_PATTERN.matcher(input);
    if (matcher.find()) {
      return input.toString().replace(matcher.group(1), ITEMS_NULL_REPLACE_STR);
    }
    return input;
  }
}

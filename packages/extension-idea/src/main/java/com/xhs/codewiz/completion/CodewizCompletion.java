package com.xhs.codewiz.completion;


import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public interface CodewizCompletion {
    @NotNull List<String> getCompletion();

    @NotNull CodewizCompletion asCached();

    boolean isCached();

    @Nullable CodewizCompletion withoutPrefix(@NotNull String var1);

    @NotNull CodewizCompletion withCompletion(@NotNull List<String> var1);
}


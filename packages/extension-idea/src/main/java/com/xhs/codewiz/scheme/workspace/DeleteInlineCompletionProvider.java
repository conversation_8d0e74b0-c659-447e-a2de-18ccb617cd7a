package com.xhs.codewiz.scheme.workspace;

import java.util.*;
/**
 * 删除内联补全提供者
 */
public class DeleteInlineCompletionProvider {
    private String schemaProtocol = "workspace.delete.inlinecompletionprovider";
    /** 删除的内联补全提供者 ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

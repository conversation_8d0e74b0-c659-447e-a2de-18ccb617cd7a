/*
package com.xhs.codewiz.type.buildin;

import java.util.*;
import com.xhs.codewiz.type.global.Version;

*/
/**
 * 握手 rcs/client/service/register 参数
 *//*

public class RegisterClientServiceRequestParams {
    */
/** 本地 service 信息 *//*

    private RegisterClientServiceRequestParamsService service;

    */
/** 依赖的 service *//*

    private List<RegisterClientServiceRequestParamsDependencesItem> dependences;

    public RegisterClientServiceRequestParamsService getService() {
        return service;
    }

    public void setService(RegisterClientServiceRequestParamsService service) {
        this.service = service;
    }

    public List<RegisterClientServiceRequestParamsDependencesItem> getDependences() {
        return dependences;
    }

    public void setDependences(List<RegisterClientServiceRequestParamsDependencesItem> dependences) {
        this.dependences = dependences;
    }

*/
/**
 * 本地 service 信息
 *//*

public static class RegisterClientServiceRequestParamsService {
    */
/** 服务名称 *//*

    private String name;

    */
/** 服务 id *//*

    private String id;

    */
/** 服务版本 *//*

    private String version;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

}
public class RegisterClientServiceRequestParamsDependencesItem {
    private RegisterClientServiceRequestParamsDependencesItemActiveOptions activeOptions;

    */
/** 是否允许热更新，默认为 true *//*

    private Boolean hotUpdate;

    */
/** 服务名称 *//*

    private String name;

    private Map<String, Object> dependenceOptions;

    private List<Integer> version;

    public RegisterClientServiceRequestParamsDependencesItemActiveOptions getActiveOptions() {
        return activeOptions;
    }

    public void setActiveOptions(RegisterClientServiceRequestParamsDependencesItemActiveOptions activeOptions) {
        this.activeOptions = activeOptions;
    }

    public Boolean getHotUpdate() {
        return hotUpdate;
    }

    public void setHotUpdate(Boolean hotUpdate) {
        this.hotUpdate = hotUpdate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, Object> getDependenceOptions() {
        return dependenceOptions;
    }

    public void setDependenceOptions(Map<String, Object> dependenceOptions) {
        this.dependenceOptions = dependenceOptions;
    }

    public List<Integer> getVersion() {
        return version;
    }

    public void setVersion(List<Integer> version) {
        this.version = version;
    }

public static class RegisterClientServiceRequestParamsDependencesItemActiveOptions {
    private Boolean isDev;

    */
/** 默认全部使用 *//*

    private RegisterClientServiceRequestParamsDependencesItemActiveOptionsModule module;

    public Boolean getIsDev() {
        return isDev;
    }

    public void setIsDev(Boolean isDev) {
        this.isDev = isDev;
    }

    public RegisterClientServiceRequestParamsDependencesItemActiveOptionsModule getModule() {
        return module;
    }

    public void setModule(RegisterClientServiceRequestParamsDependencesItemActiveOptionsModule module) {
        this.module = module;
    }

*/
/**
 * 默认全部使用
 *//*

public static  class RegisterClientServiceRequestParamsDependencesItemActiveOptionsModule {
    private RegisterClientServiceRequestParamsDependencesItemActiveOptionsModuleWebview webview;

    public RegisterClientServiceRequestParamsDependencesItemActiveOptionsModuleWebview getWebview() {
        return webview;
    }

    public void setWebview(RegisterClientServiceRequestParamsDependencesItemActiveOptionsModuleWebview webview) {
        this.webview = webview;
    }

public  static class RegisterClientServiceRequestParamsDependencesItemActiveOptionsModuleWebview {
    private Boolean available;

    public Boolean getAvailable() {
        return available;
    }

    public void setAvailable(Boolean available) {
        this.available = available;
    }

}
}
}
}
}
*/

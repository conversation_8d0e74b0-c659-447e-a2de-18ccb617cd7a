package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.editor.request.VirtualFileUri;

public final class WorkspaceFolder {
    @SerializedName("uri")
    private final VirtualFileUri uri;

    public WorkspaceFolder(VirtualFileUri uri) {
        this.uri = uri;
    }

    public VirtualFileUri getUri() {
        return this.uri;
    }
}


package com.xhs.codewiz.client.service.router;

import com.xhs.codewiz.actions.command.StatusBarItemCommandUtil;
import com.xhs.codewiz.actions.util.WhenGlobalContextUtil;
import com.xhs.codewiz.client.service.RcsContentService;
import com.xhs.codewiz.client.service.RcsFileService;
import com.xhs.codewiz.client.service.RcsGlobalService;
import com.xhs.codewiz.client.service.RcsWorkspaceContentProviderService;
import com.xhs.codewiz.client.service.RcsWorkspaceFileSystemWatcherService;
import com.xhs.codewiz.platform.CommonPlatformService;
import com.xhs.codewiz.platform.LocalCrDiffUtil;
import com.xhs.codewiz.terminal.TerminalService;
import com.xhs.codewiz.completion.CodewizAgentCompletionService;
import com.xhs.codewiz.service.SessionService;
import com.xhs.codewiz.service.ShowMessageService;
import com.xhs.codewiz.service.WebviewService;
import com.xhs.codewiz.utils.FileSelectorUtil;

/**
 * Author: liukunpeng Date: 2025-07-16 Description:
 */
public enum RcsListenerRouter {
    /**
     * global部分开始
     */
    UPDATE_SECRET_STORAGE("global.update.secretstorage", "远端更新密钥",
            "notification", RcsGlobalService::updateSecretStorage, null),

    READ_SECRET_STORAGE("global.read.secretstorage", "远端读取密钥请求",
            "request", null, RcsGlobalService::readSecretStorage),

    UPDATE_STATE("global.update.state", "远端更新字段",
            "notification", RcsGlobalService::updateState, null),

    READ_STATE("global.read.state", "远端读取字段请求",
            "request", null, RcsGlobalService::readState),

    OPEN_EXTERNAL("global.execute.openexternal", "远端打开外部链接请求",
            "request", null, RcsGlobalService::openExternal),

    CREATE_COMMAND("global.create.command", "远端创建命令请求",
        "notification", RcsGlobalService::createCommand, null),
    EXECUTE_COMMAND("global.execute.command", "远端执行命令请求",
        "request", null, RcsGlobalService::executeCommand),

    /**
     * 文件部分
     */
    FILE_CREATE("file.create.file", "远端文件创建请求",
            "notification", RcsFileService::fileCreate, null),

    FILE_READ("file.read.file", "远端读区文件请求",
            "request", null, RcsFileService::fileRead),

    DIRECTORY_READ("file.read.directory", "远端读区目录请求",
            "request", null, RcsFileService::readDirectory),

    FILE_STAT_READ("file.read.filestat", "远端读读取文件信息请求",
            "request", null, RcsFileService::readFileStat),

    FILE_DELETE("file.delete.file", "远端删除本地文件请求",
            "notification", RcsFileService::fileDelete, null),

    FILE_OPEN("file.execute.open", "远端打开本地文件请求",
            "notification", RcsFileService::fileOpen, null),

    FILE_CLOSE("file.execute.close", "远端关闭本地文件请求",
            "notification", RcsFileService::fileClose, null),

    FILE_REVERT("file.execute.revert", "远端文件撤销请求",
            "notification", RcsFileService::fileRevert, null),

    FILE_SAVE("file.execute.save", "远端保存文件请求",
            "request", null, RcsFileService::fileSave),

    FILE_UPDATE_URI("file.update.fileuri", "远端更新文件uri请求",
            "notification", RcsFileService::fileUpdateUri, null),

    /**
     * 文件content部分
     */
    CONTENT_UPDATE("content.update.content", "远端更新本地内容",
            "notification", RcsContentService::contentUpdate, null),

    RANGES_UPDATE("content.update.ranges", "远端更新本地选中区域",
            "notification", RcsContentService::rangesUpdate, null),

    CREATE_ACTION_PROVIDER("content.create.actionprovider", "创建本地action提供者",
            "notification", RcsContentService::createActionProvider, null),

    CONTENT_REVEAL_RANGE("content.execute.revealrange", "远端执行选中区域",
            "notification", RcsContentService::revealRange, null),

    CONTENT_CREATE_COMMENT_PROVIDER("content.create.commentprovider", "创建本地评论提供者",
            "notification", LocalCrDiffUtil::createCommentProvider, null),

    CONTENT_DELETE_COMMENT_PROVIDER("content.delete.commentprovider", "删除本地评论提供者",
            "notification", LocalCrDiffUtil::deleteCommentProvider, null),

    CONTENT_CREATE_COMMENT_COLLECTION("content.create.commentcollection", "创建本地评论集合",
            "notification", LocalCrDiffUtil::createCommentCollection, null),

    CONTENT_UPDATE_COMMENT_COLLECTION("content.update.commentcollection", "更新本地评论集合",
            "notification", LocalCrDiffUtil::updateCommentCollection, null),

    CONTENT_DELETE_COMMENT_COLLECTION("content.delete.commentcollection", "删除本地评论集合",
            "notification", LocalCrDiffUtil::deleteCommentCollection, null),

    CONTENT_CREATE_COMMENTS("content.create.comments", "创建本地评论",
            "notification", LocalCrDiffUtil::createComments, null),

    COMMENT_UPDATE("content.update.comment", "更新本地评论",
            "notification", LocalCrDiffUtil::updateComment, null),

    CONTENT_DELETE_COMMENT("content.delete.comments", "删除本地评论",
            "notification", LocalCrDiffUtil::delComment, null),
    /**
     * 文件content装饰器部分
     */
    /*CONTENT_DECORATION_CREATE("content.create.decoration", "远端创建装饰器",
            "notification", RcsContentService::createDecoration, null),
    CONTENT_DECORATION_UPDATE("content.update.decoration", "远端更新装饰器，以及对应文件",
            "notification", RcsContentService::updateDecoration, null),
    CONTENT_DECORATION_DELETE("content.delete.decoration", "远端删除装饰器",
            "notification", RcsContentService::deleteDecoration, null),*/

    /**
     * workspace部分
     */
    FILE_SYSTEM_WATCHER_CREATE("workspace.create.filesystemwatcher", "远端建立本地文件监听",
            "notification", RcsWorkspaceFileSystemWatcherService::fileSystemWatcherCreate, null),
    FILE_SYSTEM_WATCHER_DELETE("workspace.delete.filesystemwatcher", "远端删除本地文件监听",
            "notification", RcsWorkspaceFileSystemWatcherService::fileSystemWatcherDelete, null),
    CONTENT_PROVIDER_CREATE(
            "workspace.create.contentprovider", "远端建立内容提供者请求",
            "notification", RcsWorkspaceContentProviderService::contentProviderCreate, null),
    CONTENT_PROVIDER_UPDATE(
            "workspace.update.contentprovider", "远端更新内容提供者请求",
            "notification", RcsWorkspaceContentProviderService::contentProviderUpdate, null),
    CONTENT_PROVIDER_DELETE(
            "workspace.delete.contentprovider", "远端删除内容提供者请求",
            "notification", RcsWorkspaceContentProviderService::contentProviderDelete, null),
    CREATE_INLINE_PROVIDER("workspace.create.inlinecompletionprovider", "创建本地补全提供者",
            "notification", CodewizAgentCompletionService::initInlineCompletionProvider, null),
    DELETE_INLINE_PROVIDER("workspace.delete.inlinecompletionprovider", "删除本地补全提供者",
            "notification", CodewizAgentCompletionService::delInlineCompletionProvider, null),

    UPDATE_WORKSPACE_CONTENT("workspace.execute.changes", "远端更新工作区内容",
            "request", null, RcsWorkspaceContentProviderService::updateWorkspaceContent),

    // Platform Session Management
    PLATFORM_CREATE_SESSION_PROVIDER("platform.create.sessionprovider", "创建Session提供者",
            "notification", SessionService::createSessionProvider, null),

    PLATFORM_DELETE_SESSION_PROVIDER("platform.delete.sessionProvider", "删除Session提供者",
            "notification", SessionService::deleteSessionProvider, null),
    //
    // PLATFORM_CREATE_SESSION("platform.create.session", "创建Session",
    //         "notification", PlatformService::createSession, null),
    //
    // PLATFORM_DELETE_SESSION("platform.delete.session", "删除Session",
    //         "notification", PlatformService::deleteSession, null),
    //
    // PLATFORM_UPDATE_SESSION("platform.update.session", "更新Session",
    //         "notification", PlatformService::updateSession, null),
    //
    PLATFORM_READ_SESSION("platform.read.session", "读取Session",
            "request", null, SessionService::notifySeadSession),

    // Platform WebView Management
    PLATFORM_CREATE_WEBVIEW_PROVIDER("platform.create.webviewprovider", "创建WebView提供者",
            "notification", WebviewService::createWebviewProvider, null),

    PLATFORM_DELETE_WEBVIEW_PROVIDER("platform.delete.webviewprovider", "删除WebView提供者",
            "notification", WebviewService::deleteWebviewProvider, null),

    PLATFORM_UPDATE_WEBVIEW("platform.update.webview", "更新WebView",
            "notification", WebviewService::updateWebview, null),

    PLATFORM_DELETE_WEBVIEW("platform.delete.webview", "删除WebView",
            "notification", WebviewService::deleteWebview, null),

    PLATFORM_CREATE_WEBVIEW_MESSAGE("platform.create.webviewmessage", "WebView消息",
            "notification", WebviewService::notifyCreateWebviewMessage, null),

    /*PLATFORM_CREATE_STATUSBARITEM("platform.create.statusbaritem", "创建状态栏项",
            "notification", StatusBarItemCommandUtil::createStatusBarItem, null),

    PLATFORM_UPDATE_STATUSBARITEM("platform.update.statusbaritem", "更新状态栏项",
            "notification", StatusBarItemCommandUtil::updateStatusBarItem, null),

    PLATFORM_DELETE_STATUSBARITEM("platform.delete.statusbaritem", "删除状态栏项",
            "notification", StatusBarItemCommandUtil::deleteStatusBarItem, null),

    PLATFORM_SHOW_STATUSBAR("platform.execute.showstatusbar", "展示Action到状态栏",
            "notification", StatusBarItemCommandUtil::showStatusBarItem, null),

    PLATFORM_HIDE_STATUSBAR("platform.execute.hidestatusbar", "隐藏状态栏项",
            "notification", StatusBarItemCommandUtil::hideStatusBarItem, null),
*/
    PLATFORM_SHOW_FILE_SELECTOR("platform.execute.showfileselector", "显示文件选择器",
            "request", null,  FileSelectorUtil::showFileSelector),

    PLATFORM_SHOW_SAVE_DIALOG("platform.execute.showsavedialog", "显示保存对话框",
            "request", null,  FileSelectorUtil::showSaveDialog),

    // PLATFORM_SHOW_WEBVIEW("platform.excute.showwebview", "显示WebView",
    //         "notification", PlatformService::showWebview, null),
    //
    // PLATFORM_HIDE_WEBVIEW("platform.excute.hidewebview", "隐藏WebView",
    //         "notification", PlatformService::hideWebview, null),
    //
    // Platform StatusBar Management
    // PLATFORM_CREATE_STATUSBAR("platform.create.statusbaritem", "创建状态栏项",
    //         "notification", PlatformService::createStatusBarItem, null),
    //
    // PLATFORM_DELETE_STATUSBAR_ITEM("platform.delete.statusbaritem", "删除状态栏项",
    //         "notification", PlatformService::deleteStatusBarItem, null),
    //
    // PLATFORM_SHOW_STATUSBAR("platform.excute.showstatusbar", "显示状态栏项",
    //         "notification", PlatformService::showStatusBar, null),
    //
    // PLATFORM_HIDE_STATUSBAR("platform.excute.hidestatusbar", "隐藏状态栏项",
    //         "notification", PlatformService::hideStatusBar, null),
    //
    // PLATFORM_UPDATE_STATUSBAR("platform.update.statusbaritem", "更新状态栏项",
    //         "notification", PlatformService::updateStatusBarItem, null),
    //
    PLATFORM_SHOW_MESSAGE("platform.execute.showmessage", "显示消息",
            "notification", null, ShowMessageService::showMessage),

    PLATFORM_SHOW_PANEL("platform.execute.showpanel", "显示面板",
            "notification", CommonPlatformService::showPanel, null),

    PLATFORM_CLOSE_PANEL("platform.execute.hidepanel", "关闭面板",
            "notification", CommonPlatformService::closePanel, null),

    PLATFORM_UPDATE_PANEL("platform.update.panel", "更新面板",
            "notification", CommonPlatformService::updatePanel, null),

    PLATFORM_CREATE_TERMINAL("platform.create.terminal", "创建终端",
            "request", null, TerminalService::createTerminal),

    PLATFORM_DELETE_TERMINAL("platform.delete.terminal", "删除终端",
            "notification", TerminalService::deleteTerminal, null),

    PLATFORM_EXECUTE_TERMINAL("platform.execute.terminal", "执行终端命令",
            "request", null, TerminalService::runTerminal),


    PLATFORM_HIDE_TERMINAL("platform.execute.hideterminal", "隐藏终端",
            "notification", TerminalService::hideTerminal, null),

    PLATFORM_SHOW_TERMINAL("platform.execute.showterminal", "显示终端",
            "notification", TerminalService::showTerminal, null),

    PLATFORM_READ_TERMINAL("platform.read.terminal", "读取终端",
            "request", null, TerminalService::readTerminal),

    PLATFORM_UPDATE_GLOBAL_CONTEXT("platform.update.globalcontext", "更新平台上下文",
            "notification", WhenGlobalContextUtil::registerWhenGlobalContext, null),
    ;

    private final String schemeProtocol;
    private final String desc;
    private final String type;
    // 这个是Notification处理防范，仅在Notification的时候使用
    private final NotificationFunInterface notificationFun;
    // 这个是request处理防范，仅在request的时候使用
    private final RequestFunInterface requestFun;

    RcsListenerRouter(String schemeProtocol, String desc, String type, NotificationFunInterface notificationFun,
                      RequestFunInterface requestFun) {
        this.schemeProtocol = schemeProtocol;
        this.desc = desc;
        this.type = type;
        this.notificationFun = notificationFun;
        this.requestFun = requestFun;
    }

    public static RcsListenerRouter getBySchemeProtocol(String protocol) {
        for (RcsListenerRouter listenerEnum : values()) {
            if (listenerEnum.schemeProtocol.equals(protocol)) {
                return listenerEnum;
            }
        }
        return null;
    }

    public void executeNotification(String param, String channel) {
        notificationFun.execute(param, channel);
    }

    public Object executeRequest(String param, String channel) {
        return requestFun.execute(param, channel);
    }
}

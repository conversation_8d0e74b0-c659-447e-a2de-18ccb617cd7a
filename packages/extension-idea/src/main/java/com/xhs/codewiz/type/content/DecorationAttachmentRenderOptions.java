package com.xhs.codewiz.type.content;

import java.util.*;

/**
 * 装饰器附件渲染选项。注意，文字和图标只能显示一种，不能同时显示。
 */
public class DecorationAttachmentRenderOptions {
    /** CSS border 样式 */
    private String border;

    /** border 颜色 */
    private String borderColor;

    /** 背景颜色 */
    private String backgroundColor;

    /** CSS margin 样式 */
    private String margin;

    /** 文本颜色 */
    private String color;

    /** 附件内容文本 */
    private String contentText;

    /** CSS width 样式 */
    private String width;

    /** CSS text-decoration 样式 */
    private String textDecoration;

    /** CSS font-style 样式 */
    private String fontStyle;

    /** CSS font-weight 样式 */
    private String fontWeight;

    /** 附件内容图标的绝对路径 */
    private String contentIconPath;

    /** CSS height 样式 */
    private String height;

    public String getBorder() {
        return border;
    }

    public void setBorder(String border) {
        this.border = border;
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getMargin() {
        return margin;
    }

    public void setMargin(String margin) {
        this.margin = margin;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getContentText() {
        return contentText;
    }

    public void setContentText(String contentText) {
        this.contentText = contentText;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getTextDecoration() {
        return textDecoration;
    }

    public void setTextDecoration(String textDecoration) {
        this.textDecoration = textDecoration;
    }

    public String getFontStyle() {
        return fontStyle;
    }

    public void setFontStyle(String fontStyle) {
        this.fontStyle = fontStyle;
    }

    public String getFontWeight() {
        return fontWeight;
    }

    public void setFontWeight(String fontWeight) {
        this.fontWeight = fontWeight;
    }

    public String getContentIconPath() {
        return contentIconPath;
    }

    public void setContentIconPath(String contentIconPath) {
        this.contentIconPath = contentIconPath;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

}

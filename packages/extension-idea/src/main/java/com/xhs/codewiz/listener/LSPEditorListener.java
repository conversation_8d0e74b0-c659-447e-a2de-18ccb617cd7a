package com.xhs.codewiz.listener;

import java.util.Arrays;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.FileEditorManagerEvent;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.fileEditor.FileEditorProvider;
import com.intellij.openapi.fileEditor.TextEditor;
import com.intellij.openapi.util.Pair;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.utils.SmartFileOpenUtil;

public class LSPEditorListener implements FileEditorManagerListener {
    private static final Logger log = Logger.getInstance(LSPEditorListener.class);

    public LSPEditorListener() {
    }

    public void fileOpenedSync(@NotNull FileEditorManager source, @NotNull VirtualFile file,
                               @NotNull Pair<FileEditor[], FileEditorProvider[]> editors) {
        VirtualFile virtualFile = this.determineFile(source, file);
        LSPManager.getInstance().notifyDidOpen(source.getProject(), virtualFile);

        // 打开文件, 重新定位
        // SmartFileOpenUtil.syncFileOpen(virtualFile, source.getProject());
    }

    public void fileClosed(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
        VirtualFile determinedFile = this.determineFile(source, file);

        // 异步处理文件关闭事件，避免在EDT中执行慢操作
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                LSPManager.getInstance().notifyDidClose(determinedFile);
            } catch (Exception e) {
                log.warn("Error in async notifyDidClose", e);
            }
        });
    }

    public void selectionChanged(@NotNull FileEditorManagerEvent event) {
        VirtualFile preFile = event.getOldFile();
        VirtualFile focusedFile = event.getNewFile();
        if (focusedFile != null) {
            focusedFile = this.determineFile(event.getManager(), focusedFile);
            if (preFile != null) {
                preFile = this.determineFile(event.getManager(), preFile);
            }
            LSPManager.getInstance().notifyDidFocus(event.getManager().getProject(), focusedFile, preFile);
            LSPManager.getInstance().notifyDidFocusToLs(event.getManager().getProject(), focusedFile, preFile);
        }

    }

    private VirtualFile determineFile(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
        return (VirtualFile) Arrays.stream(source.getEditors(file)).filter((editor) -> {
            return editor.getFile().equals(file);
        }).filter((editor) -> {
            return editor instanceof TextEditor;
        }).map((editor) -> {
            return this.getVirtualFileFromDocument((TextEditor) editor, file);
        }).findFirst().orElse(file);
    }

    private VirtualFile getVirtualFileFromDocument(TextEditor editor, VirtualFile backup) {
        VirtualFile virtualFile = FileDocumentManager.getInstance().getFile(editor.getEditor().getDocument());
        if (virtualFile == null) {
            virtualFile = backup;
        }

        return virtualFile;
    }
}

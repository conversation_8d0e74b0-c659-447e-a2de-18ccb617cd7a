package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 更新文件 URI，用于通知远端更新的文件 Uri。\n仅用于 `workspace.create.fileSystemWatcher`
 */
public class UpdateFileUri {
    private String schemaProtocol = "file.update.fileuri";
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

package com.xhs.codewiz.factory.webview.util;

import java.awt.Component;

import javax.swing.JComponent;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.IdeFocusManager;
import com.intellij.openapi.wm.IdeFrame;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.openapi.wm.WindowManager;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * <AUTHOR>
 * @date 2025/8/6 21:40
 */
public class FocusTransferUtil {

    // log
    private static final Logger logger = Logger.getInstance(FocusTransferUtil.class);

    public static void transferFocusToEditorDelay(Project project, int time) {
        if (project == null || project.isDisposed()) {
            LoggerUtil.INSTANCE.logWarn(logger, "Project is disposed");
            return;
        }
        // 延迟焦点转移，确保webview和所有UI组件加载完成后再执行
        // 使用Timer确保在合适的时机转移焦点，避免与webview加载竞争
        javax.swing.Timer focusTimer = new javax.swing.Timer(time, e -> {
            FocusTransferUtil.transferFocusToEditor(project);
        });
        focusTimer.setRepeats(false);
        focusTimer.start();
    }

    public static void transferFocusToEditorDelay(Project project) {
        transferFocusToEditorDelay(project, 75);
    }

    /**
     * 将焦点从ToolWindow转移到编辑器（ToolWindow保持可见）
     */
    public static void transferFocusToEditor(@NotNull Project project) {
        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                FileEditorManager editorManager = FileEditorManager.getInstance(project);
                // Editor editor = editorManager.getSelectedTextEditor();
                FileEditor[] allEditors = editorManager.getAllEditors();
                for (FileEditor editor : allEditors) {
                    if (editor != null && editor.getComponent().isDisplayable()) {
                        // 确保编辑器组件可见和可获得焦点
                        IdeFocusManager focusManager = IdeFocusManager.getInstance(project);

                        // 强制请求焦点，使用最高优先级
                        focusManager.requestFocus(editor.getComponent(), true);

                        // 备用方案：直接设置焦点
                        ApplicationManager.getApplication().invokeLater(() -> {
                            if (!editor.getComponent().hasFocus()) {
                                editor.getComponent().requestFocusInWindow();
                                LoggerUtil.INSTANCE.logInfo(logger, "使用备用方案转移焦点到编辑器");
                            } else {
                                LoggerUtil.INSTANCE.logInfo(logger, "焦点已成功转移到编辑器");
                            }
                        });
                        break;
                    }
                    // else {
                    //     // 如果没有打开的编辑器，转移到编辑区域
                    //     transferFocusToEditorArea(project);
                    // }
                }


            } catch (Exception e) {
                System.err.println("转移焦点到编辑器失败: " + e.getMessage());
                // 最后的兜底方案
                transferFocusToMainWindow(project);
            }
        });
    }

    /**
     * 转移焦点到编辑区域（即使没有打开文件）
     */
    public static void transferFocusToEditorArea(@NotNull Project project) {
        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                // 获取主窗口
                IdeFrame frame = WindowManager.getInstance().getIdeFrame(project);
                if (frame != null) {
                    JComponent component = frame.getComponent();

                    // 查找编辑器组件
                    JComponent editorComponent = findEditorComponent(component);
                    if (editorComponent != null) {
                        IdeFocusManager.getInstance(project).requestFocus(editorComponent, true);
                        System.out.println("焦点已转移到编辑区域");
                        return;
                    }
                }

                // 后备方案：转移到主窗口
                transferFocusToMainWindow(project);
            } catch (Exception e) {
                System.err.println("转移焦点失败: " + e.getMessage());
                transferFocusToMainWindow(project);
            }
        });
    }

    /**
     * 查找编辑器组件
     */
    private static JComponent findEditorComponent(JComponent parent) {
        // 递归查找编辑器相关组件
        if (parent.getClass().getSimpleName().contains("EditorsSplitters") ||
                parent.getClass().getSimpleName().contains("EditorComposite")) {
            return parent;
        }

        for (Component child : parent.getComponents()) {
            if (child instanceof JComponent) {
                JComponent result = findEditorComponent((JComponent) child);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 转移焦点到主窗口
     */
    public static void transferFocusToMainWindow(@NotNull Project project) {
        ApplicationManager.getApplication().invokeLater(() -> {
            IdeFrame frame = WindowManager.getInstance().getIdeFrame(project);
            if (frame != null) {
                JComponent component = frame.getComponent();
                IdeFocusManager.getInstance(project).requestFocus(component, true);
                System.out.println("焦点已转移到主窗口");
            }
        });
    }

    /**
     * 让指定的ToolWindow失去焦点
     */
    public static void deactivateToolWindowFocus(@NotNull Project project, @NotNull String toolWindowId) {
        ToolWindowManager manager = ToolWindowManager.getInstance(project);
        ToolWindow toolWindow = manager.getToolWindow(toolWindowId);

        if (toolWindow != null && toolWindow.isActive()) {
            System.out.println("正在移除ToolWindow焦点: " + toolWindowId);
            transferFocusToEditor(project);
        }
    }

    /**
     * 让当前活动的ToolWindow失去焦点
     */
    public static void deactivateCurrentToolWindowFocus(@NotNull Project project) {
        ToolWindowManager manager = ToolWindowManager.getInstance(project);

        for (String id : manager.getToolWindowIds()) {
            ToolWindow toolWindow = manager.getToolWindow(id);
            if (toolWindow != null && toolWindow.isActive()) {
                System.out.println("当前活动ToolWindow失去焦点: " + id);
                transferFocusToEditor(project);
                break;
            }
        }
    }
}

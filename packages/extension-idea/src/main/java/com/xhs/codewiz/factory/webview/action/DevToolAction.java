package com.xhs.codewiz.factory.webview.action;

import org.jetbrains.annotations.NotNull;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.factory.webview.ToolBrowserWindow;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * 开发者工具Action
 */
public class DevToolAction extends AnAction {

    private ToolBrowserWindow toolBrowserWindow;
    // log
    private static final Logger log = Logger.getInstance(DevToolAction.class);

    public DevToolAction(ToolBrowserWindow toolBrowserWindow) {
        super("Rednote CodeWiz Tools", "Open Rednote CodeWiz Tools", AllIcons.Actions.StartDebugger);
        this.toolBrowserWindow = toolBrowserWindow;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        if (toolBrowserWindow == null) {
            LoggerUtil.INSTANCE.logWarn(log, "toolBrowserWindow is null");
            return;
        }
        this.toolBrowserWindow.getCodeWizBrowser().openDevtools();
    }

    // set toolBrowserWindow
    public void setToolBrowserWindow(ToolBrowserWindow toolBrowserWindow) {
        this.toolBrowserWindow = toolBrowserWindow;
    }

    // get toolBrowserWindow
    public ToolBrowserWindow getToolBrowserWindow() {
        return toolBrowserWindow;
    }
} 
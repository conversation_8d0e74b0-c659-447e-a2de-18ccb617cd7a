package com.xhs.codewiz.scheme.global;

import java.util.*;
import com.xhs.codewiz.type.global.ZeroBasedIndex;
/**
 * 创建流式内容
 */
public class CreateStreamString {
    private String schemaProtocol = "global.create.streamstring";
    private CreateStreamStringParams params;

    public CreateStreamStringParams getParams() {
        return params;
    }
    public void setParams(CreateStreamStringParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateStreamStringParams {
        /** 流ID */
        private String streamId;
        /** 流索引 */
        private Integer index;
        /** 流内容 */
        private String value;
    
        public String getStreamId() {
            return streamId;
        }
        public void setStreamId(String streamId) {
            this.streamId = streamId;
        }
        public Integer getIndex() {
            return index;
        }
        public void setIndex(Integer index) {
            this.index = index;
        }
        public String getValue() {
            return value;
        }
        public void setValue(String value) {
            this.value = value;
        }
    }
}

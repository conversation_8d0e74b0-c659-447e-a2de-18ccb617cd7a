package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 读取 Webview
 */
public class ReadWebview {
    private String schemaProtocol = "platform.read.webview";
    private ReadWebviewParams params;

    public ReadWebviewParams getParams() {
        return params;
    }
    public void setParams(ReadWebviewParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadWebviewParams {
        /** Webview 的安全配置 */
        private ReadWebviewParamsSecurity security; // optional
        /** Webview Provider 的 ID */
        private String provider;
        /** Webview 的上下文 */
        private Map<String, Object> context; // optional
    
        public ReadWebviewParamsSecurity getSecurity() {
            return security;
        }
        public void setSecurity(ReadWebviewParamsSecurity security) {
            this.security = security;
        }
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public Map<String, Object> getContext() {
            return context;
        }
        public void setContext(Map<String, Object> context) {
            this.context = context;
        }
    }

        /**
     * Webview 的安全配置
     */
    public static class ReadWebviewParamsSecurity {
        /** CSP 源 */
        private String cspSource;
    
        public String getCspSource() {
            return cspSource;
        }
        public void setCspSource(String cspSource) {
            this.cspSource = cspSource;
        }
    }
}

package com.xhs.codewiz.lang.agent;

import com.intellij.notification.Notification;
import com.intellij.notification.NotificationAction;
import com.intellij.notification.NotificationType;
import com.xhs.codewiz.lang.agent.notifications.CodeWizNotifications;
import com.xhs.codewiz.lang.agent.notifications.MessageActionItem;
import com.xhs.codewiz.lang.agent.notifications.WindowLogMessageRequest;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcRequestListener;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.BundleUtil;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.concurrency.AsyncPromise;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.Promises;

public class ShowMessageRequestHandler implements
    JsonRpcRequestListener<WindowLogMessageRequest, MessageActionItem> {
    private static final int LOG_MESSAGE_TYPE = 4;
    private static final Map<Integer, NotificationType> messageTypeToNotificationType;

    public ShowMessageRequestHandler() {
    }

    public @NotNull Promise<MessageActionItem> handleMessage(@NotNull WindowLogMessageRequest request) {
        if (request.getMessageType() == 4) {
            return Promises.resolvedPromise();
        } else {
            Notification notification = CodeWizNotifications.createFullContentNotification(
                BundleUtil.get("requestsDisabledNotification.title"), request.getMessage(), determineType(request), true);
            AsyncPromise<MessageActionItem> promise = new AsyncPromise();
            List<MessageActionItem> actions = request.getActions();
            if (actions != null && !actions.isEmpty()) {
                actions.forEach(addAction(promise, notification));
            } else {
                promise.setResult(null);
            }

            Objects.requireNonNull(promise);
            notification.whenExpired(promise::cancel);
            notification.notify(ApplicationUtil.findCurrentProject());
            return promise;
        }
    }

    private static @NotNull Consumer<MessageActionItem> addAction(AsyncPromise<MessageActionItem> promise, Notification notification) {
        return (actionItem) -> {
            notification.addAction(NotificationAction.createSimple(actionItem.getTitle(), () -> {
                promise.setResult(actionItem);
            }));
        };
    }

    private static NotificationType determineType(WindowLogMessageRequest request) {
        return (NotificationType)messageTypeToNotificationType.getOrDefault(request.getMessageType(), NotificationType.INFORMATION);
    }

    public Class<WindowLogMessageRequest> getRequestType() {
        return WindowLogMessageRequest.class;
    }

    public Class<MessageActionItem> getResponseType() {
        return MessageActionItem.class;
    }

    static {
        messageTypeToNotificationType = new Int2ObjectOpenHashMap(Map.of(1, NotificationType.ERROR, 2, NotificationType.WARNING, 3, NotificationType.INFORMATION));
    }
}

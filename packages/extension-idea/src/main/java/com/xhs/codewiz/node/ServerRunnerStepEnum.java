package com.xhs.codewiz.node;

/**
 * Author: liukunpeng Date: 2025-07-25 Description:
 */
public enum ServerRunnerStepEnum {

  UNSTART(0, "未开始", "未触发启动server流程"),

  CHECK_NVM_INSTALLED(1, "检查nvm环境中", "nvm环境检查失败，请检查本地环境是否正常"),


  NVM_INSTALL(2, "安装nvm中", "nvm安装失败，请联系CodeWiz同学或自行安装，安装后需重启IDE"),


  CHECK_NODE_VERSION(3, "检查node版本中", "node版本检查失败，请检查本地环境是否正常"),


  NODE_INSTALL(4, "node固定版本安装中(22.16.0)", "node(22.16.0)安装失败，，请联系CodeWiz同学或自行安装，安装后需重启IDE"),


  SERVER_START(5, "Server服务启动中", "服务启动异常，请联系CodeWiz同学或尝试重启"),

  SERVER_START_SUCCESS(6, "服务启动成功", "服务启动成工"),

  SERVER_START_FAIL(7, "服务启动失败", "服务启动失败，请联系CodeWiz同学或尝试重启");


  public final int stepCode;
  public final String stepDesc;
  public final String errorTitle;

  ServerRunnerStepEnum(int stepCode, String stepDesc, String errorTitle) {
    this.stepCode = stepCode;
    this.stepDesc = stepDesc;
    this.errorTitle = errorTitle;
  }
}

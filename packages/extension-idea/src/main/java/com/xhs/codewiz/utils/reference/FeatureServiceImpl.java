package com.xhs.codewiz.utils.reference;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.intellij.openapi.diagnostic.Logger;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import org.apache.commons.lang3.StringUtils;

public class FeatureServiceImpl
    implements FeatureService {
  private static final Logger LOGGER = Logger.getInstance(FeatureServiceImpl.class);
  Map<String, String> features = new ConcurrentHashMap<String, String>();
  ReadWriteLock rwLock = new ReentrantReadWriteLock();
  Lock readLock = this.rwLock.readLock();
  Lock writeLock = this.rwLock.writeLock();
  private static volatile FeatureServiceImpl instance;

  /*
   * WARNING - Removed try catching itself - possible behaviour change.
   * Enabled force condition propagation
   * Lifted jumps to return sites
   */
  public static FeatureServiceImpl getImplInstance() {
    if (instance != null) return instance;
    Class<FeatureServiceImpl> clazz = FeatureServiceImpl.class;
    synchronized (FeatureServiceImpl.class) {
      if (instance != null) return instance;
      instance = new FeatureServiceImpl();
      // ** MonitorExit[var0] (shouldn't be in output)
      return instance;
    }
  }

  @Override
  public void updateFeatures(Map<String, String> features) {
    this.writeLock.lock();
    try {
      this.features.clear();
      LOGGER.debug("update features:" + features);
      if (features != null && !features.isEmpty()) {
        this.features.putAll(features);
      }
    }
    finally {
      this.writeLock.unlock();
    }
  }

  @Override
  public void updateFeatures(Object experimental) {
    if (experimental == null) {
      return;
    }
    if (experimental instanceof JsonObject) {
      HashMap<String, String> newFeatures = new HashMap<String, String>();
      JsonObject expr = (JsonObject)experimental;
      JsonObject featObj = expr.getAsJsonObject("features");
      if (featObj != null) {
        for (Map.Entry entry : featObj.entrySet()) {
          String key = (String)entry.getKey();
          if (entry.getValue() == null || StringUtils.isBlank((CharSequence)key)) continue;
          String value = ((JsonElement)entry.getValue()).getAsString();
          newFeatures.put(key, value);
        }
        this.updateFeatures(newFeatures);
      }
    }
  }

  /*
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  @Override
  public Integer getIntegerFeature(String key) {
    this.readLock.lock();
    try {
      String value = this.features.get(key);
      Integer n = StringUtils.isBlank((CharSequence)value) ? null : Integer.valueOf(value);
      return n;
    }
    catch (Exception e) {
      LOGGER.warn("get feature error from " + key, (Throwable)e);
      Integer n = null;
      return n;
    }
    finally {
      this.readLock.unlock();
    }
  }

  @Override
  public Integer getIntegerFeature(String key, Integer defaultValue) {
    Integer value = this.getIntegerFeature(key);
    return value == null ? defaultValue : value;
  }

  /*
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  @Override
  public String getStringFeature(String key) {
    this.readLock.lock();
    try {
      String string = this.features.get(key);
      return string;
    }
    catch (Exception e) {
      LOGGER.warn("get feature error from " + key, (Throwable)e);
      String string = null;
      return string;
    }
    finally {
      this.readLock.unlock();
    }
  }

  @Override
  public String getStringFeature(String key, String defaultValue) {
    String value = this.getStringFeature(key);
    return StringUtils.isBlank((CharSequence)value) ? defaultValue : value;
  }

  /*
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  @Override
  public Boolean getBooleanFeature(String key) {
    this.readLock.lock();
    try {
      String value = this.features.get(key);
      Boolean bl = StringUtils.isBlank((CharSequence)value) ? null : Boolean.valueOf(value);
      return bl;
    }
    catch (Exception e) {
      LOGGER.warn("get feature error from " + key, (Throwable)e);
      Boolean bl = null;
      return bl;
    }
    finally {
      this.readLock.unlock();
    }
  }

  @Override
  public Boolean getBooleanFeature(String key, Boolean defaultValue) {
    Boolean value = this.getBooleanFeature(key);
    return value == null ? defaultValue : value;
  }

  /*
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  @Override
  public Long getLongFeature(String key) {
    this.readLock.lock();
    try {
      String value = this.features.get(key);
      Long l = StringUtils.isBlank((CharSequence)value) ? null : Long.valueOf(value);
      return l;
    }
    catch (Exception e) {
      LOGGER.warn("get feature error from " + key, (Throwable)e);
      Long l = null;
      return l;
    }
    finally {
      this.readLock.unlock();
    }
  }

  @Override
  public Long getLongFeature(String key, Long defaultValue) {
    Long value = this.getLongFeature(key);
    return value == null ? defaultValue : value;
  }

  /*
   * WARNING - Removed try catching itself - possible behaviour change.
   */
  @Override
  public Double getDoubleFeature(String key) {
    this.readLock.lock();
    try {
      String value = this.features.get(key);
      Double d = StringUtils.isBlank((CharSequence)value) ? null : Double.valueOf(value);
      return d;
    }
    catch (Exception e) {
      LOGGER.warn("get feature error from " + key, (Throwable)e);
      Double d = null;
      return d;
    }
    finally {
      this.readLock.unlock();
    }
  }

  @Override
  public Double getDoubleFeature(String key, Double defaultValue) {
    Double value = this.getDoubleFeature(key);
    return value == null ? defaultValue : value;
  }
}
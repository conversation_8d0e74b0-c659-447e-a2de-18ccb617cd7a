package com.xhs.codewiz.client;

import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.actions.command.DefaultCommandUtil;
import com.xhs.codewiz.client.model.ChannelCommonNotification;
import com.xhs.codewiz.client.model.ChannelCommonRequest;
import com.xhs.codewiz.client.model.ChannelCommonResponse;
import com.xhs.codewiz.client.model.Dependence;
import com.xhs.codewiz.client.model.RemoteServiceMeta;
import com.xhs.codewiz.client.model.ServiceMeta;
import com.xhs.codewiz.client.model.ServiceRegisterRequest;
import com.xhs.codewiz.client.model.ServiceRegisterResponse;
import com.xhs.codewiz.client.util.RcsConnectUtil;
import com.xhs.codewiz.constant.PluginCommonConstant;
import com.xhs.codewiz.enums.ExtensionEnum;
import com.xhs.codewiz.enums.RemoteServiceEnum;
import com.xhs.codewiz.node.NodeServerRunner;
import com.xhs.codewiz.node.ServerRunnerStepEnum;
import com.xhs.codewiz.scheme.content.CreateActionProvider;
import com.xhs.codewiz.scheme.platform.CreateSessionProvider;
import com.xhs.codewiz.scheme.platform.CreateWebviewProvider;
import com.xhs.codewiz.scheme.workspace.CreateContentProvider;
import com.xhs.codewiz.scheme.workspace.CreateInlineCompletionProvider;
import com.xhs.codewiz.scheme.workspace.ReadInlineCompletion;
import com.xhs.codewiz.type.file.DocumentFilter;
import com.xhs.codewiz.type.workspace.InlineCompletionItem;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.DocumentFilterMatchUtil;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.PluginUtil;
import com.xhs.codewiz.utils.RegisterUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.io.IOException;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * Author: liukunpeng Date: 2025-03-18 Description: -> Cosy
 */
public class RcsWebSocketManager {

  private static final Logger logger = Logger.getInstance(RcsWebSocketManager.class);
  private static final String PATH_SPILT = "/";
  public static final RcsWebSocketManager INSTANCE = new RcsWebSocketManager();
  //本地服务id，唯一标识，随机生成，同IDE内多workspace共享
  public static Map<Project, String> LOCAL_SERVICE_ID_MAP ;
  //用来进行数据交互的websocket服务
  private Map<Project, RcsWebSocketService> rcsWebSocketServiceMap;
  private Map<Project, Boolean> readyMap;
  //当前远端支持的插件及其channel映射:project->远端插件ID列表
  private Map<Project, List<RemoteServiceMeta>> projectRemoteServiceMetaMap;
  //key是二级channel，即RemoteServiceMeta.id, value是该远端插件channel下支持的各种provider
  private Map<String, List<Object>> providerManageMap;
  private Map<String, Lock> connectLockMap = new ConcurrentHashMap<String, Lock>();


  public RcsWebSocketManager() {
    LOCAL_SERVICE_ID_MAP = new ConcurrentHashMap<Project, String>();
    this.projectRemoteServiceMetaMap = new ConcurrentHashMap<Project, List<RemoteServiceMeta>>();
    this.providerManageMap = new ConcurrentHashMap<String, List<Object>>();
    this.rcsWebSocketServiceMap = new ConcurrentHashMap<Project, RcsWebSocketService>();
    this.readyMap = new ConcurrentHashMap<Project, Boolean>();
  }

  /**
   * 初始化与远端服务建立连接
   *
   * @return
   * @throws IOException
   */
  public boolean connectCosyServer(Project project) throws IOException {
    if (rcsWebSocketServiceMap.containsKey(project)
        && rcsWebSocketServiceMap.get(project).isSessionOpen()
        && projectRemoteServiceMetaMap.containsKey(project)
        && CollectionUtils.isNotEmpty(projectRemoteServiceMetaMap.get(project))) {
      readyMap.put(project, true);
      return true;
    }
    String lockKey = project.getBasePath();
    Lock connectLock = this.connectLockMap.computeIfAbsent(lockKey, e -> new ReentrantLock());
    if (!connectLock.tryLock()) {
      LoggerUtil.INSTANCE.logWarn(logger, "Cannot get connect lock for project:" + project.getBasePath());
      return false;
    }
    try {
      //需要确保服务已经起来了
      if (!NodeServerRunner.checkServerStatus()) {
        NodeServerRunner.startServer();
        NodeServerRunner.waitForServer(null);
        //二次确认
        if (!NodeServerRunner.checkServerStatus()) {
          Pair<ServerRunnerStepEnum, String> step = NodeServerRunner.getCurrentStep();
          LoggerUtil.INSTANCE.logError(logger, "Cannot start to node server, step : " + step.getLeft().stepDesc + "titleMsg: " + step.getRight(), null);
          return false;
        }
      }
      if (!LOCAL_SERVICE_ID_MAP.containsKey(project) || StringUtils.isBlank(LOCAL_SERVICE_ID_MAP.get(project))) {
        LOCAL_SERVICE_ID_MAP.put(project, UUID.randomUUID().toString());
      }
      RcsWebSocketService rcsWebSocketService = RcsWebSocketService.createService();
      try {
        rcsWebSocketService.connect(new URI(getConnectAddress()), project);
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(logger, "connect RCS Server err", e);
        return false;
      }
      if (null == rcsWebSocketService.getServer()) {
        return false;
      }
      rcsWebSocketServiceMap.put(project, rcsWebSocketService);
      //1. 首次握手
      register(project);
      //2. 开始注册本地连接
      ServiceRegisterResponse registerResponse = serviceRegister(project);
      if (null == registerResponse
          || PluginCommonConstant.NO.equals(registerResponse.getSuccess())
          || null == registerResponse.getData()
          || CollectionUtils.isEmpty(registerResponse.getData().getDependences())) {
        LoggerUtil.INSTANCE.logWarn(logger,
            "serviceRegister fail, resp = " + GsonUtil.toJson(registerResponse));
        return false;
      }
      //3. 根据支持插件返回的ID，进行插件初始化
      List<RemoteServiceMeta> projectRemotePlugin = registerResponse.getData().getDependences();
      if (CollectionUtils.isEmpty(projectRemotePlugin)) {
        return false;
      }
      projectRemoteServiceMetaMap.put(project, projectRemotePlugin);
      ThreadUtil.execute(()
          -> DefaultCommandUtil.registerChannelCommand(project, registerResponse.getData().getRequirment()));
      //异步缓存注册信息
      RegisterUtil.getInstance().cacheRegisterInfoAsync(registerResponse.getData());
      //4.注册平台信息
      RcsConnectUtil.init(project);
      //5.通知ready
      if (!localServiceReady(project)) {
        this.close(project);
        return false;
      }
      readyMap.put(project, true);
      return true;
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logError(logger, "connect RCS Server err", e);
      return false;
    } finally {
      connectLock.unlock();
    }
  }

  /**
   * 本地初始注册
   */
  private void register(Project project) {
    ChannelCommonRequest channelCommonNotification = new ChannelCommonRequest(
        PluginCommonConstant.CHANNEL_PATH_SUFFIX, null);
    channelCommonNotification.setProtocol("rcs/client/register");
    rcsWebSocketServiceMap.get(project).getServer().channelRequest(channelCommonNotification);
  }

  /**
   * 服务注册，返回支持的插件组件
   * @return
   */
  private ServiceRegisterResponse serviceRegister(Project project) {
    ServiceRegisterRequest registerRequest = new ServiceRegisterRequest();
    ServiceMeta localServiceMeta = new ServiceMeta();
    localServiceMeta.setId(LOCAL_SERVICE_ID_MAP.get(project));
    localServiceMeta.setName(PluginUtil.getPluginName());
    localServiceMeta.setVersion(PluginUtil.getVersion());
    registerRequest.setService(localServiceMeta);

    List<Dependence> dependences = Lists.newArrayList();
    //2.2 获取支持的插件
    RemoteServiceEnum[] values = RemoteServiceEnum.values();
    for (RemoteServiceEnum value : values) {
      Dependence dependence = new Dependence();
      dependence.setName(value.getName());
      dependence.setVersion(value.version());
      if (value.isDisableWebview()) {
        Map<String, Object> activeOptions = new HashMap<>();
        JsonObject available = new JsonObject();
        available.addProperty("available", false);
        JsonObject webview = new JsonObject();
        webview.add("webview", available);
        activeOptions.put("module", webview);
        dependence.setActiveOptions(activeOptions);
      }
      dependences.add(dependence);
    }
    registerRequest.setDependences(dependences);
    ChannelCommonRequest register = new ChannelCommonRequest(
        PluginCommonConstant.CHANNEL_PATH_SUFFIX, registerRequest);
    register.setProtocol("rcs/client/service/register");
    //2.3 发送注册请求
    CompletableFuture<Object> registeCompletableFuture = rcsWebSocketServiceMap.get(project).
        getServer().channelRequest(register);
    try {
      Object response = registeCompletableFuture.get(180, TimeUnit.SECONDS);
      return GsonUtil.fromJson(
          GsonUtil.toJson(response), ServiceRegisterResponse.class);
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(logger, "connect RCS serviceRegister err", e);
      return null;
    }
  }

private boolean localServiceReady(Project project) {
  //3.1 开始channel初始化
  String channel = LOCAL_SERVICE_ID_MAP.get(project) + "/" + PluginCommonConstant.CHANNEL_PATH_SUFFIX;
  ChannelCommonRequest request = new ChannelCommonRequest(channel, null);
  request.setProtocol("rcs/jrpc/ready/request");
  CompletableFuture<Object> channelReadyRes = rcsWebSocketServiceMap.get(project).getServer()
      .channelRequest(request);
  try {
    Object resObj = channelReadyRes.get(60, TimeUnit.SECONDS);
    ChannelCommonResponse response = GsonUtil.fromJson(
        GsonUtil.toJson(resObj), ChannelCommonResponse.class);
    if (PluginCommonConstant.YES.equals(response.getSuccess())) {
      return true;
    }
    LoggerUtil.INSTANCE.logWarn(logger,
        " localServiceReady fail, resp = " + GsonUtil.toJson(
            resObj));
  } catch (InterruptedException | ExecutionException | TimeoutException e) {
    //本插件解析失败，那么继续下一个远端插件
    LoggerUtil.INSTANCE.logWarn(logger, "localServiceReady err", e);
  }
  return false;
}
  /**
   * 发送请求
   *
   * @param params          请求参数
   * @param typeToken    返回类型
   * @param <T>
   * @return 返回远端不同插件的结果合集
   */
  public <T> List<T> sendRequest(Object params, TypeToken<T> typeToken, Project project, Integer timeout) {
    List<T> unionResponse = Lists.newArrayList();
    List<RemoteServiceMeta> remoteServiceMetas = projectRemoteServiceMetaMap.get(project);
    if (CollectionUtils.isEmpty(remoteServiceMetas)) {
      return unionResponse;
    }
    RcsWebSocketService service = rcsWebSocketServiceMap.get(project);
    for (RemoteServiceMeta remoteService : remoteServiceMetas) {
      String channel =
          LOCAL_SERVICE_ID_MAP.get(project) + "/" + remoteService.getId() + "/" + PluginCommonConstant.CHANNEL_PATH_SUFFIX;
      ChannelCommonRequest request = new ChannelCommonRequest(channel, params);
      CompletableFuture<Object> response = service.getServer()
          .channelRequest(request);
      try {
        Object resObj = response.get(timeout, TimeUnit.MILLISECONDS);
        unionResponse.add(GsonUtil.fromJson(GsonUtil.toJson(resObj), typeToken));
      } catch (InterruptedException | ExecutionException | TimeoutException e) {
        //本插件解析失败，那么继续下一个远端插件
        LoggerUtil.INSTANCE.logWarn(logger, "channelRequest err", e);
      }
    }
    return unionResponse;
  }
  /**
   * 发送请求（特定channel + 特定provider）
   *
   * @param params          请求参数
   * @param <T>
   * @return 返回远端不同插件的结果合集
   */
  public <T> T  sendRequestWithChannelProvider(String remoteChannel, String providerId, Object params,
      TypeToken<T> typeToken, Project project, Integer timeout) {
    RcsWebSocketService service = rcsWebSocketServiceMap.get(project);
    if (!rcsWebSocketServiceMap.containsKey(project)) {
      return null;
    }
    String channel;
    if (StringUtils.isNotEmpty(remoteChannel)) {
      remoteChannel = parseRemoteChannel(remoteChannel);
      channel =
          LOCAL_SERVICE_ID_MAP.get(project) + "/" + remoteChannel + "/" + providerId;
    } else {
      channel =
          LOCAL_SERVICE_ID_MAP.get(project) + "/" + providerId;
    }
    ChannelCommonRequest request = new ChannelCommonRequest(channel, params);
    CompletableFuture<Object> response = service.getServer()
        .channelRequest(request);
    try {
      Object resObj = response.get(timeout, TimeUnit.MILLISECONDS);
      return GsonUtil.fromJson(GsonUtil.toJson(resObj), typeToken);
    } catch (InterruptedException | ExecutionException | TimeoutException e) {
      //本插件解析失败，那么继续下一个远端插件
      LoggerUtil.INSTANCE.logWarn(logger, "channelRequest err", e);
      return null;
    }
  }
  /**
   * 发送请求（特定channel + 特定provider）
   *
   * @param params          请求参数
   * @return 返回远端不同插件的结果合集
   */
  public void sendNotificationWithChannelProvider(String remoteChannel, String providerId, Object params, Project project) {
    remoteChannel = parseRemoteChannel(remoteChannel);
    RcsWebSocketService service = rcsWebSocketServiceMap.get(project);
    if (!rcsWebSocketServiceMap.containsKey(project)) {
      return;
    }
    String channel =
        LOCAL_SERVICE_ID_MAP.get(project) + "/" + remoteChannel + "/" + providerId;
    try {
      ChannelCommonNotification notification = new ChannelCommonNotification(channel, params);
      service.getServer().channelNotification(notification);
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(logger, "send sendNotificationWithChannelProvider error", e);
    }
  }
  /**
   * 发送通知
   *
   * @param params
   */
  public void sendNotification(Object params, Project project) {
    List<RemoteServiceMeta> remoteServiceMetas = projectRemoteServiceMetaMap.get(project);
    if (CollectionUtils.isEmpty(remoteServiceMetas)) {
      return;
    }
    RcsWebSocketService service = rcsWebSocketServiceMap.get(project);
    //通知类型的话，需要通知所有远端插件
    for (RemoteServiceMeta remoteService : remoteServiceMetas) {
      String channel =
          LOCAL_SERVICE_ID_MAP.get(project) + "/" + remoteService.getId() + "/" + PluginCommonConstant.CHANNEL_PATH_SUFFIX;
      try {
        ChannelCommonNotification notification = new ChannelCommonNotification(channel,
            params);
        service.getServer().channelNotification(notification);
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(logger, "sendNotification error", e);
      }
    }
  }

  /**
   * 发送请求
   *
   * @param params          请求参数
   * @param responseType    返回类型
   * @param <T>
   * @return 返回远端不同插件的结果合集
   */
  public <T> List<T> sendRequestWithProvider(ProviderTypeEnum provider, Object params, Class<T> responseType, Project project) {
    List<T> unionResponse = Lists.newArrayList();
    List<RemoteServiceMeta> remoteServiceMetas = projectRemoteServiceMetaMap.get(project);
    if (CollectionUtils.isEmpty(remoteServiceMetas)) {
      return unionResponse;
    }
    RcsWebSocketService service = rcsWebSocketServiceMap.get(project);
    for (RemoteServiceMeta remoteService : remoteServiceMetas) {
      String channel =
          LOCAL_SERVICE_ID_MAP.get(project) + "/" + remoteService.getId() + "/" + getProviderId(remoteService.getId(), provider);
      ChannelCommonRequest request = new ChannelCommonRequest(channel, params);
      CompletableFuture<Object> response = service.getServer()
          .channelRequest(request);
      try {
        Object resObj = response.get(5, TimeUnit.SECONDS);
        unionResponse.add(GsonUtil.fromJson(GsonUtil.toJson(resObj), responseType));
      } catch (InterruptedException | ExecutionException | TimeoutException e) {
        //本插件解析失败，那么继续下一个远端插件
        LoggerUtil.INSTANCE.logWarn(logger, "channelRequest err", e);
        continue;
      }
    }
    return unionResponse;
  }

  /**
   * 发送通知
   *
   * @param params
   */
  public void sendNotificationWithProvider(ProviderTypeEnum provider, Object params, Project project) {
    //通知类型的话，需要通知所有远端插件
    List<RemoteServiceMeta> remoteServiceMetas = projectRemoteServiceMetaMap.get(project);
    if (CollectionUtils.isEmpty(remoteServiceMetas)) {
      return;
    }
    RcsWebSocketService service = rcsWebSocketServiceMap.get(project);
    for (RemoteServiceMeta remoteService : remoteServiceMetas) {
      String channel =
          LOCAL_SERVICE_ID_MAP.get(project) + "/" + remoteService.getId() + "/" + getProviderId(remoteService.getId(), provider);
      ChannelCommonNotification notification = new ChannelCommonNotification(channel,
          params);
      try {
        service.getServer().channelNotification(notification);
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(logger, "sendNotificationWithProvider error", e);
      }
    }
  }

  public boolean checkWebsocketAgent(Project project, boolean autoStart) {
    boolean status = readyMap.getOrDefault(project, false)
        && rcsWebSocketServiceMap.containsKey(project)
        && rcsWebSocketServiceMap.get(project).isSessionOpen()
        && projectRemoteServiceMetaMap.containsKey(project)
        && CollectionUtils.isNotEmpty(projectRemoteServiceMetaMap.get(project));
    if (!status && autoStart) {
      //如果状态不对，并且设置需要重连，那么尝试重新连接
      ThreadUtil.execute(() -> {
        try {
          connectCosyServer(project);
        } catch (IOException e) {
          LoggerUtil.INSTANCE.logWarn(logger, "connectCosyServer err", e);
        }
      });
    }
    return status;
  }

  public boolean close(Project project) {
    RcsConnectUtil.destroy(project);
    if (projectRemoteServiceMetaMap.containsKey(project)
        && CollectionUtils.isNotEmpty(projectRemoteServiceMetaMap.get(project))) {
      List<RemoteServiceMeta> remoteServiceMetas = projectRemoteServiceMetaMap.get(project);
      //挨个取消注册
      remoteServiceMetas.forEach(remoteChannel -> {
        String channel = remoteChannel.getId() + PluginCommonConstant.CHANNEL_PATH_SUFFIX;
        ChannelCommonNotification request = new ChannelCommonNotification(channel, null);
        //rcsWebSocketService.getServer().remotePluginReadyRemove(request);
      });
      projectRemoteServiceMetaMap.remove(project);
    }
    if (rcsWebSocketServiceMap.containsKey(project) && rcsWebSocketServiceMap.get(project).isSessionOpen()) {
      rcsWebSocketServiceMap.get(project).closeSession();
      rcsWebSocketServiceMap.remove(project);
    }
    readyMap.remove(project);
    LOCAL_SERVICE_ID_MAP.remove(project);
    return true;
  }

  /**
   * 添加provider
   * @param channel
   * @param provider
   */
  public void addProvider(String channel, Object provider) {
    channel = parseRemoteChannel(channel);
    if (!providerManageMap.containsKey(channel)) {
      providerManageMap.put(channel, Lists.newArrayList(provider));
    } else {
      providerManageMap.get(channel).add(provider);
    }
  }

  public void deleteProvider(String channel, Object provider) {
    channel = parseRemoteChannel(channel);
    if (providerManageMap.containsKey(channel)) {
      providerManageMap.get(channel).remove(provider);
    }
  }
  public List<Object> getProvider(String channel, ProviderTypeEnum providerTypeEnum)  {
    channel = parseRemoteChannel(channel);
    List<Object> providers = providerManageMap.get(channel);
    if (CollectionUtils.isEmpty(providers)) {
      return Lists.newArrayList();
    }
    switch (providerTypeEnum) {
      case ACTION -> {
        return providers.stream()
            .filter(o -> o instanceof CreateActionProvider)
            .toList();
      }
      case CONTENT -> {
        return providers.stream()
            .filter(o -> o instanceof CreateContentProvider)
            .toList();
      }
      case SESSION -> {
        return providers.stream()
            .filter(o -> o instanceof CreateSessionProvider)
            .toList();
      }
      case WEBVIEW -> {
        return providers.stream()
            .filter(o -> o instanceof CreateWebviewProvider)
            .toList();
      }
      case INLINE_COMPLETION -> {
        return providers.stream()
            .filter(o -> o instanceof CreateInlineCompletionProvider)
            .toList();
      }
      default -> {
        return Lists.newArrayList();
      }
    }
  }
  public String getProviderId(String channel, ProviderTypeEnum providerTypeEnum) {
    channel = parseRemoteChannel(channel);
    String providerId = PluginCommonConstant.CHANNEL_PATH_SUFFIX;
    List<Object> providers = providerManageMap.get(channel);
    if (CollectionUtils.isEmpty(providers)) {
      return providerId;
    }
    switch (providerTypeEnum) {
      case ACTION -> {
        CreateActionProvider provider = (CreateActionProvider) providers.stream().filter(o -> o instanceof CreateActionProvider).findFirst().orElse(null);
        if (null != provider) {
          providerId = provider.getParams().getProvider().getId();
        }
      }
      case CONTENT -> {
        CreateContentProvider provider = (CreateContentProvider) providers.stream().filter(o -> o instanceof CreateContentProvider).findFirst().orElse(null);
        if (null != provider) {
          providerId = provider.getParams().getId();
        }
      }
      case SESSION -> {
        CreateSessionProvider provider = (CreateSessionProvider) providers.stream().filter(o -> o instanceof CreateSessionProvider).findFirst().orElse(null);
        if (null != provider) {
          providerId = provider.getParams().getProvider().getId();
        }
      }
      case WEBVIEW -> {
        CreateWebviewProvider provider = (CreateWebviewProvider) providers.stream().filter(o -> o instanceof CreateWebviewProvider).findFirst().orElse(null);
        if (null != provider) {
          providerId = provider.getParams().getId();
        }
      }
      case INLINE_COMPLETION -> {
        CreateInlineCompletionProvider provider = (CreateInlineCompletionProvider) providers.stream().filter(o -> o instanceof CreateInlineCompletionProvider).findFirst().orElse(null);
        if (null != provider) {
          providerId = provider.getParams().getProvider().getId();
        }
      }
    }
    return providerId;
  }

  /**
   * 代码补全，这个特殊，所以单独写出来
   * @param params
   * @return
   */
  public List<CompletableFuture<List<InlineCompletionItem>>> doCompletion(ReadInlineCompletion params, Project project) {
    List<CompletableFuture<List<InlineCompletionItem>>> futureList = new ArrayList<>();
    List<String> remoteServiceMetas = getRemoteChannelListByProject(project);
    if (CollectionUtils.isEmpty(remoteServiceMetas)) {
      return futureList;
    }
    RcsWebSocketService service = rcsWebSocketServiceMap.get(project);
    for (String remoteChannel : remoteServiceMetas) {
      //找到该channel下的补全provider，通常来说只有一个
      List<Object> completionObj = getProvider(remoteChannel, ProviderTypeEnum.INLINE_COMPLETION);
      for (Object obj : completionObj) {
        CreateInlineCompletionProvider provider = (CreateInlineCompletionProvider) obj;
        //判断是否是当前支持语言的补全
        if (!DocumentFilterMatchUtil.matchesAny(params.getParams().getUri(),
            provider.getParams().getSelector())) {
          continue;
        }
        String channelId = LOCAL_SERVICE_ID_MAP.get(project) + "/" + remoteChannel + "/" + provider.getParams().getProvider().getId();
        //需要设置本次的providerId
        params.getParams().setProvider(provider.getParams().getProvider().getId());
        ChannelCommonRequest request = new ChannelCommonRequest(channelId, params);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        LoggerUtil.INSTANCE.logWarn(logger, "doCompletion, time = " + sdf.format(new Date()));
        CompletableFuture<List<InlineCompletionItem>> response = service
            .getServer().channelRequest(request)
            .thenApply(resObj -> {
              if (resObj != null) {
                List<InlineCompletionItem> items = GsonUtil.fromJson(GsonUtil.toJson(resObj), new TypeToken<List<InlineCompletionItem>>() {});
                return items;
              }
              return null;
            }).exceptionally(ex -> {
              LoggerUtil.INSTANCE.logWarn(logger, "channelRequest err", ex);
              return null;
            });
        futureList.add(response);
      }
    }
    return futureList;
  }

  public Project getProjectByRemoteChannel(String channel) {
    String remoteChannel = parseRemoteChannel(channel);
    Project project = null;
    for (Map.Entry<Project, List<RemoteServiceMeta>> entry : projectRemoteServiceMetaMap.entrySet()) {
      List<RemoteServiceMeta> remoteServiceMetas = entry.getValue();
      if (CollectionUtils.isEmpty(remoteServiceMetas)) {
        continue;
      }
      if (remoteServiceMetas.stream().anyMatch(meta -> StringUtils.equals(remoteChannel, meta.getId()))) {
        project = entry.getKey();
        break;
      }
    }
    if (null == project) {
      project = ApplicationUtil.findCurrentProject();;
    }
    if (project != null) {
      return project.isDisposed() ? null : project;
    }
    return null;
  }
  public Project getProjectByLocalChannel(String channel) {
    String localChannel = parseLocalChannel(channel);
    Project project = null;
    for (Map.Entry<Project, String> entry : LOCAL_SERVICE_ID_MAP.entrySet()) {
      if (StringUtils.equals(localChannel, entry.getValue())) {
        project = entry.getKey();
        break;
      }
    }
    if (null == project) {
      project = ApplicationUtil.findCurrentProject();;
    }
    if (project != null) {
      return project.isDisposed() ? null : project;
    }
    return null;
  }
  public List<String> getRemoteChannelListByProject(Project project) {
    List<RemoteServiceMeta> remoteServiceMetas = projectRemoteServiceMetaMap.get(project);
    if (CollectionUtils.isEmpty(remoteServiceMetas)) {
      return new ArrayList<>();
    }
    return remoteServiceMetas.stream().map(RemoteServiceMeta::getId).toList();
  }

  public RcsWebSocketService getWebSocketService(Project project) {
    return rcsWebSocketServiceMap.get(project);
  }
  public RcsWebSocketService getFirstWebSocketService() {
    for (RcsWebSocketService service : rcsWebSocketServiceMap.values()) {
      if (service.isSessionOpen()) {
        return service;
      }
    }
    return rcsWebSocketServiceMap.values().stream().findFirst().orElse(null);
  }
  /**
   *  通信时的channel是三级完整路径，但是本地存储时仅需要关注第二级即可
   * @param channel
   * @return
   */
  public String parseRemoteChannel(String channel) {
    if (StringUtils.contains(channel, PATH_SPILT)) {
      try {
        String[] channelSplit = channel.split(PATH_SPILT);
        channel = channelSplit[1];
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(logger, "parseRemoteChannel err", e);
      }
    }
    return channel;
  }
  public List<RemoteServiceMeta> getRemoteServiceList(Project project) {
    return projectRemoteServiceMetaMap.get(project);
  }
  public RemoteServiceMeta getRemoteServiceMeta(String channel) {
    String remoteChannel = parseRemoteChannel(channel);
    Project project = getProjectByRemoteChannel(remoteChannel);
    if (project != null) {
      List<RemoteServiceMeta> remoteServiceMetas = projectRemoteServiceMetaMap.get(project);
      if (remoteServiceMetas != null) {
        for (RemoteServiceMeta meta : remoteServiceMetas) {

          if (meta.getId().equals(remoteChannel)) {
            return meta;
          }
        }
      }
    }
    return null;
  }
  /**
   *  通信时的channel是三级完整路径，取本地实例映射的第一级
   * @param channel
   * @return
   */
  private static String parseLocalChannel(String channel) {
    if (StringUtils.contains(channel, PATH_SPILT)) {

      try {
        String[] channelSplit = channel.split(PATH_SPILT);
        channel = channelSplit[0];
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(logger, "parseLocalChannel err", e);
      }
    }
    return channel;
  }
  private String getConnectAddress() {
    String prot = NodeServerRunner.getServerPort();
    if (StringUtils.isEmpty(prot)) {
      prot = "5467";
    }
    // prot = "5467";
    return "ws://localhost:" + prot;
  }

}

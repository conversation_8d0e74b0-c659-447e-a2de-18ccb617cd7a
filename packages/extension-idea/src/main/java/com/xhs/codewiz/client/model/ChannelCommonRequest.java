package com.xhs.codewiz.client.model;

/**
 * Author: liukunpeng Date: 2025-07-15 Description:
 */
public class ChannelCommonRequest {
  private  String protocol = "rcs/jrpc/request";
  //通信渠道
  private String channel;
  //实际对应的请求参数
  private Object params;
  // 构造函数
  public ChannelCommonRequest(String channel, Object params) {
    this.channel = channel;
    this.params = params;
  }

  public String getProtocol() {
    return protocol;
  }

  public void setProtocol(String protocol) {
    this.protocol = protocol;
  }

  public String getChannel() {
    return channel;
  }

  public void setChannel(String channel) {
    this.channel = channel;
  }

  public Object getParams() {
    return params;
  }

  public void setParams(Object params) {
    this.params = params;
  }
}

package com.xhs.codewiz.setting;

import com.intellij.lang.Language;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.PersistentStateComponent;
import com.intellij.openapi.components.State;
import com.intellij.openapi.components.Storage;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiFile;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@State(
        //name = "github-copilot",
        name = "codewiz-rcs",
        storages = {@Storage("CodeWiz-rcs.xml")}
)
public class CodeWizApplicationSettings implements PersistentStateComponent<CodeWizApplicationState> {
    private CodeWizApplicationState state;

    public CodeWizApplicationSettings() {
    }

    public static @NotNull CodeWizApplicationState settings() {
        CodeWizApplicationState state = ApplicationManager.getApplication().getService(CodeWizApplicationSettings.class).getState();

        assert state != null;

        return state;
    }

    public static boolean isCodeWizEnabled(@NotNull Project project, @NotNull Editor editor) {
        PsiFile file = PsiDocumentManager.getInstance(project).getPsiFile(editor.getDocument());
        return file != null && isCodeWizEnabled(file);
    }

    public static boolean isCodeWizEnabled(@NotNull PsiFile file) {
        Language language = file.getLanguage();
        return isCodeWizEnabled(language);
    }

    public static boolean isCodeWizEnabled(@NotNull Language language) {
        CodeWizApplicationState settings = settings();
        return settings.enableCompletions && settings.isEnabled(language);
    }

    public synchronized @Nullable CodeWizApplicationState getState() {
        return this.state;
    }

    public synchronized void noStateLoaded() {
        this.state = new CodeWizApplicationState();
    }

    public synchronized void loadState(@NotNull CodeWizApplicationState state) {
        this.state = state;
    }
}

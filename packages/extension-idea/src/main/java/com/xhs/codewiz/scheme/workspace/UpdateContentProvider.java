package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * Content Provider 内容发生变更
 */
public class UpdateContentProvider {
    private String schemaProtocol = "workspace.update.contentprovider";
    private UpdateContentProviderParams params;

    public UpdateContentProviderParams getParams() {
        return params;
    }
    public void setParams(UpdateContentProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateContentProviderParams {
        /** Content Provider 的 ID */
        private String id;
        /** 发生变更的 URI */
        private String uri;
    
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

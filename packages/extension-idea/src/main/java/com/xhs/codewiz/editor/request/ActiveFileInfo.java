package com.xhs.codewiz.editor.request;

import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.SelectionModel;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import com.xhs.codewiz.enums.ExtensionEnum;
import com.xhs.codewiz.type.content.Position;

public class ActiveFileInfo {
    String fileName;
    String filePath;
    Position start;
    Position end;
    String selectedCode;
    String fileProtocol;
    Boolean isBinary;
    String fileType;
    String languageId;

    public ActiveFileInfo(String fileName, String filePath, String fileProtocol, Boolean isBinary, String fileType, String languageId) {
        this.fileName = fileName;
        this.filePath = filePath;
        this.isBinary = isBinary;
        this.fileProtocol = fileProtocol;
        this.fileType = fileType;
        this.languageId = languageId;
    }

    public ActiveFileInfo(String fileName, String filePath, String selectedCode, Position start, Position end, Boolean isBinary) {
        this.fileName = fileName;
        this.filePath = filePath;
        this.selectedCode = selectedCode;
        this.start = start;
        this.end = end;
        this.isBinary = isBinary;
    }

    public static ActiveFileInfo getFromEditor(Editor editor) {
        Document document = editor.getDocument();
        VirtualFile file = FileDocumentManager.getInstance().getFile(document);
        String filePath = "";
        String fileName = "";
        String fileProtocol = "";
        String fileType = "";
        String languageId = "";
        Boolean isBinary = null;
        if (file != null) {
            filePath = file.getPresentableUrl();
            fileName = file.getName();
            fileType = file.getFileType().getName();
            isBinary = file.getFileType().isBinary();
            fileProtocol = file.getFileSystem().getProtocol();
            languageId = ExtensionEnum.getLanguageByExtension(file.getExtension());
        }
        ActiveFileInfo activeFileInfo = new ActiveFileInfo(fileName, filePath, fileProtocol, isBinary, fileType, languageId);
        SelectionModel selectionModel = editor.getSelectionModel();
        CodeWizEditorUtil.SelectionModelInfo info = CodeWizEditorUtil.getSelectionModelInfo(selectionModel);
        String selectedText = info.getSelectedText();
        if (selectedText != null) {
            activeFileInfo.setSelectedCode(selectedText);
        }
        if (null != info.getSelectionStartPosition()) {
          Position start =new Position();
          start.setLine(info.getSelectionStartPosition().line);
          start.setCharacter(info.getSelectionStartPosition().column);
          activeFileInfo.setStart(start);
        }
        if (null != info.getSelectionEndPosition()) {
          Position end = new Position();
          end.setLine(info.getSelectionEndPosition().line);
          end.setCharacter(info.getSelectionEndPosition().column);
          activeFileInfo.setEnd(end);
        }
        return activeFileInfo;
    }

    public String getFileName() {
        return this.fileName;
    }

    public String getFilePath() {
        return this.filePath;
    }

    public Position getStart() {
        return this.start;
    }

    public Position getEnd() {
        return this.end;
    }

    public String getSelectedCode() {
        return this.selectedCode;
    }

    public String getFileProtocol() {
        return this.fileProtocol;
    }

    public Boolean getIsBinary() {
        return this.isBinary;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public void setStart(Position start) {
        this.start = start;
    }

    public void setEnd(Position end) {
        this.end = end;
    }

    public void setSelectedCode(String selectedCode) {
        this.selectedCode = selectedCode;
    }

    public void setFileProtocol(String fileProtocol) {
        this.fileProtocol = fileProtocol;
    }

    public void setIsBinary(Boolean isBinary) {
        this.isBinary = isBinary;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getLanguageId() {
        return languageId;
    }

    public void setLanguageId(String languageId) {
        this.languageId = languageId;
    }
}
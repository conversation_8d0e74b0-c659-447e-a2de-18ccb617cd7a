package com.xhs.codewiz.update;

import java.io.File;

public class UpdateConstant {
    public final static String MARKET_URL = "https://artifactory.devops.xiaohongshu.com/artifactory/generic-codewiz-plugin/market/update.xml";
    public final static String CODE_WIZ_HOME = System.getProperty("user.home") + File.separator + ".rednote";
    public final static String BALLOON_TITLE = "Rednote CodeWiz";
    public final static String BALLOON_CONTENT = "编程助手 Rednote CodeWiz 已更新【%s】, 需重启IDE后生效.";
    public final static String UPDATE_CONFIG_SIT = "https://codewiz.devops.sit.xiaohongshu.com/agent-manage/api/config/getPluginConfig";
    public final static String UPDATE_CONFIG_PROD = "https://codewiz-agent.devops.xiaohongshu.com/agent-manage/api/config/getPluginConfig";
}

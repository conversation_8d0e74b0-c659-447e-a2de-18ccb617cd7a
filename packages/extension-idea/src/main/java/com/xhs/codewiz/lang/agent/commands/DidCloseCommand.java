package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.editor.request.TextDocumentIdentifier;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import org.jetbrains.annotations.NotNull;

public final class DidCloseCommand implements JsonRpcNotification {
    @SerializedName("textDocument")
    private final @NotNull TextDocumentIdentifier textDocument;

    public @NotNull String getCommandName() {
        return "textDocument/didClose";
    }

    public DidCloseCommand(@NotNull TextDocumentIdentifier textDocument) {
        this.textDocument = textDocument;
    }

    public @NotNull TextDocumentIdentifier getTextDocument() {
        return this.textDocument;
    }
}


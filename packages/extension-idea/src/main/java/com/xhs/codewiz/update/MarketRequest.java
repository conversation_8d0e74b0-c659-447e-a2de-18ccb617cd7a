package com.xhs.codewiz.update;

import static com.xhs.codewiz.update.UpdateConstant.CODE_WIZ_HOME;
import static com.xhs.codewiz.update.UpdateConstant.MARKET_URL;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.utils.BundleUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

public class MarketRequest {
    private static final Logger LOG = Logger.getInstance(MarketRequest.class);

    private static final String pluginDirectoryName = "codewiz-idea-plugin";

    public static final AtomicBoolean isDownloading = new AtomicBoolean(false);

    public static final AtomicReference<String> sourceDirectory = new AtomicReference<>();

    public static Plugins.Plugin fetchPluginMeta() throws IOException {
        File xmlFile = new File(CODE_WIZ_HOME + File.separator + "temp.xml");
        FileUtils.copyURLToFile(new URL(MARKET_URL), xmlFile, 1000, 120000);
        XmlMapper xmlMapper = new XmlMapper();
        xmlMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        Plugins plugins = xmlMapper.readValue(xmlFile, Plugins.class);
        return plugins.getPluginList().stream().
                filter(element -> element.getId().equals(BundleUtil.get("codewiz.plugin.id"))).findFirst().orElse(null);
    }

    public static boolean downloadPlugin(String marketUrl) throws IOException {
        //开始下载
        if (isDownloading.compareAndSet(false, true)) {
            try {
                FileUtils.cleanDirectory(new File(CODE_WIZ_HOME));
                //每一次清空内容后，需要同步重置解压资源路径
                sourceDirectory.set("");
                String dest = CODE_WIZ_HOME + File.separator + "temp.zip";
                MarketRequest.downloadZipFile(marketUrl, dest);
                File unzipDirectory = UnzipAndReplace.unzip(dest, CODE_WIZ_HOME);
                //解压成功后，需要同步设置解压资源路径
                sourceDirectory.set(unzipDirectory.getAbsolutePath());
                return true;
                //UnzipAndReplace.replaceDirectory(unzipDirectory.getAbsolutePath(), PathManager.getPluginsPath() + File.separator + unzipDirectory.getName());

            } catch (Exception e) {
                LoggerUtil.INSTANCE.logWarn(LOG, "Failed to download plugin", e);
                return false;
            } finally {
                isDownloading.set(false);
            }
        }
        return false;
    }

    public static Object updateDirectoryFromRcs(String rcsUnzipPath, String channel) {
        if (StringUtils.isEmpty(rcsUnzipPath)) {
            return "rcsUnzipPath is empty";
        }
        String path = rcsUnzipPath + "/" + pluginDirectoryName;
        File file = new File(path);
        if (file.exists() && file.isDirectory()) {
            sourceDirectory.set(path);
        }
        return null;
    }
    public static Object doRcsRestartForUpdate(String rcsUnzipPath, String channel) {
        //如果解压目录为空，尝试重新设置目录
        if (StringUtils.isEmpty(sourceDirectory.get())) {
            updateDirectoryFromRcs(rcsUnzipPath, channel);
        }
        //如果还为空，则返回null
        if (StringUtils.isEmpty(sourceDirectory.get())) {
            return null;
        }
        ApplicationManager.getApplication().invokeLater(() -> {
            ApplicationManager.getApplication().exit(false, true, true);
        });
        return sourceDirectory.get();
    }
    private static void downloadZipFile(String url, String destination) throws IOException {
        FileUtils.copyURLToFile(new URL(url), new File(destination), 1000, 600000);
    }
}

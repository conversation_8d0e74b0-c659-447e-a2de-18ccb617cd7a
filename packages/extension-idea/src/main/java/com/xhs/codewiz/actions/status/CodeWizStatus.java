package com.xhs.codewiz.actions.status;

import com.intellij.util.ui.PresentableEnum;
import com.xhs.codewiz.utils.BundleUtil;
import com.xhs.codewiz.utils.IconsUtil;
import javax.swing.Icon;
import org.jetbrains.annotations.NotNull;

public enum CodeWizStatus implements PresentableEnum {
    Ready,
    NotSignedIn,
    CompletionInProgress,
    AgentWarning,
    AgentError,
    AgentBroken,
    IncompatibleClient,
    Unsupported,
    UnknownError;

    private CodeWizStatus() {
    }

    public boolean isIconAlwaysShown() {
        return this != Ready && this != CompletionInProgress;
    }

    public boolean isDisablingClientRequests() {
        return this == IncompatibleClient || this == AgentBroken;
    }

    public @NotNull Icon getIcon() {
        switch (this) {
            case Ready:
                return IconsUtil.StatusBarIcon;
            case CompletionInProgress:
                return IconsUtil.StatusBarCompletionInProgress;
            case AgentWarning:
                return IconsUtil.StatusBarIconWarning;
            default:
                return IconsUtil.StatusBarIconError;
        }
    }

    public String getPresentableText() {
        switch (this) {
            case Ready:
                return BundleUtil.get("codewizStatus.ready");
            case CompletionInProgress:
                return BundleUtil.get("codewizStatus.completionInProgress");
            case AgentWarning:
                return BundleUtil.get("codewizStatus.agentWarning");
            case NotSignedIn:
                return BundleUtil.get("codewizStatus.notSignedIn");
            case AgentError:
                return BundleUtil.get("codewizStatus.agentError");
            case AgentBroken:
                return BundleUtil.get("codewizStatus.agentBroken");
            case IncompatibleClient:
                return BundleUtil.get("codewizStatus.incompatibleClient");
            case Unsupported:
                return BundleUtil.get("codewizStatus.unsupported");
            case UnknownError:
                return BundleUtil.get("codewizStatus.unknownError");
            default:
                throw new IllegalStateException("Unexpected value: " + this);
        }
    }
}


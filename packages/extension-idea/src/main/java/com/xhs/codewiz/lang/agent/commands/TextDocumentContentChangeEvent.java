package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.type.content.Range;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class TextDocumentContentChangeEvent {
    @SerializedName("range")
    private final @Nullable Range range;
    @SerializedName("text")
    private final @NotNull String text;

    public TextDocumentContentChangeEvent(@NotNull String text) {
        this(text, null);
    }

    public TextDocumentContentChangeEvent(@NotNull String text, @Nullable Range range) {
        this.range = range;
        this.text = text;
    }

    public @Nullable Range getRange() {
        return this.range;
    }

    public @NotNull String getText() {
        return this.text;
    }
}


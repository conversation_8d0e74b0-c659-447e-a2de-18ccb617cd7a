package com.xhs.codewiz.utils;

import com.google.common.collect.Lists;
import com.intellij.openapi.diagnostic.Attachment;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.util.ExceptionUtil;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.client.RcsWebSocketService;
import com.xhs.codewiz.client.model.ChannelCommonNotification;
import com.xhs.codewiz.constant.PluginCommonConstant;
import com.xhs.codewiz.scheme.global.CreateTrace;
import com.xhs.codewiz.scheme.global.CreateTrace.CreateTraceParams;
import com.xhs.codewiz.type.global.Severity;
import java.util.function.Consumer;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class LoggerUtil {
    public static final LoggerUtil INSTANCE = new LoggerUtil();

    public static final Consumer<Throwable> LogAsDebug = (throwable) -> {
        Logger.getInstance(LoggerUtil.class).debug(throwable);
    };

    private static final int MAX_ENTRIES = 16;

    private LoggerUtil() {
    }

    public void logDebug(@NotNull Logger logger, @NotNull String message) {
      logDebug(logger, message, null);
    }

    public void logDebug(@NotNull Logger logger, @NotNull String message, Throwable throwable) {
        if (null == throwable) {
            logger.debug(message);
        } else {
            logger.debug(message + ":" + ExceptionUtil.getThrowableText(throwable));
        }
    }
    public void logInfo(@NotNull Logger logger, @NotNull String message) {
        logger.info(message);
        sneLogToRcs(message, Severity.Information, "plugin.jetbrains");
    }


    public void logWarn(@NotNull Logger logger, @NotNull String message) {
        logWarn(logger, message, null);
        sneLogToRcs(message, Severity.Warning, "plugin.jetbrains");
    }

    public void logWarn(@NotNull Logger logger, @NotNull String message, @Nullable Throwable original) {
        logger.warn(message, original);
        if (original != null) {
            message += ": " + ExceptionUtil.getThrowableText(original);
        }
        sneLogToRcs(message, Severity.Warning, "plugin.jetbrains");
    }

    public void logError(@NotNull Logger logger, @NotNull String message, @Nullable Throwable original, Attachment... attachments) {
      //本地打warn
        logger.warn(message, original);
        if (original != null) {
            message += ": " + ExceptionUtil.getThrowableText(original);
        }
        sneLogToRcs(message, Severity.Error, "plugin.jetbrains");
    }

    private void sneLogToRcs(String  message, Severity severity, String module) {
        CreateTrace createTrace = new CreateTrace();
        CreateTraceParams params = new CreateTraceParams();
        params.setType(severity);
        params.setModule(module);
        params.setVersion(PluginUtil.getVersion());
        params.setArgs(Lists.newArrayList(message));
        createTrace.setParams(params);

        ChannelCommonNotification notification = new ChannelCommonNotification(PluginCommonConstant.CHANNEL_PATH_SUFFIX,
            createTrace);
        RcsWebSocketService service = RcsWebSocketManager.INSTANCE.getFirstWebSocketService();
        if (null != service) {
            service.getServer().channelNotification(notification);
        }
    }
}


package com.xhs.codewiz.lang.agent.vscodeRpc;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Multimaps;
import com.google.gson.JsonElement;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.util.concurrency.AppExecutorUtil;
import com.intellij.util.concurrency.BoundedTaskExecutor;
import com.intellij.util.containers.ContainerUtil;
import com.xhs.codewiz.lang.agent.CodeWizAgentProcessService;
import com.xhs.codewiz.lang.agent.rpc.JsonRPC;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcClientResponse;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcErrorException;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcMessageHandler;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcRequestListener;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcResponse;
import com.xhs.codewiz.utils.LoggerUtil;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.AsyncPromise;
import org.jetbrains.concurrency.Promise;

public class DefaultJsonRpcMessageHandler implements JsonRpcMessageHandler {
    private static final Logger LOG = Logger.getInstance(DefaultJsonRpcMessageHandler.class);
    private final ExecutorService taskExecutor = AppExecutorUtil.createBoundedApplicationPoolExecutor("Code Wiz JSON-RPC handler", 1);
    private final ConcurrentMap<Integer, PendingRequest<?>> pendingRequests = new ConcurrentHashMap();
    private final List<JsonRpcNotificationListener> notificationListeners = ContainerUtil.createLockFreeCopyOnWriteList();
    private final ListMultimap<String, JsonRpcRequestListener<?, ?>> requestListeners = Multimaps.synchronizedListMultimap(ArrayListMultimap.create());
    private final AtomicBoolean isShutdown = new AtomicBoolean();

    public DefaultJsonRpcMessageHandler() {
    }

    public <T> AsyncPromise<T> addPendingRequest(int requestId, @NotNull String commandName, @NotNull Class<T> responseType, @Nullable Runnable onCancel) {
        CancellableAsyncPromise<T> promise = new CancellableAsyncPromise(onCancel);
        PendingRequest<?> presentValue = (PendingRequest)this.pendingRequests.put(requestId, new PendingRequest(promise, responseType, commandName, System.currentTimeMillis()));
        if (presentValue != null) {
            throw new RuntimeException("Duplicate request id: " + requestId);
        } else {
            return promise;
        }
    }

    public void addNotificationListeners(@NotNull Collection<JsonRpcNotificationListener> listeners) {
        this.notificationListeners.addAll(listeners);
    }

    public void removeNotificationListener(@NotNull JsonRpcNotificationListener listener) {
        this.notificationListeners.remove(listener);
    }

    public void handleJsonMessage(@NotNull String message) {
        if (this.isShutdown.get()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "handleJsonMessage called for shutdown message handler");
        } else {
            if (LOG.isTraceEnabled()) {
                LoggerUtil.INSTANCE.logDebug(LOG, String.format("JSON message received: %s", message));
            }

            long timestamp = System.currentTimeMillis();

            JsonRpcResponse jsonResponse;
            try {
                jsonResponse = JsonRPC.parseResponse(message);
            } catch (JsonRpcErrorException var6) {
                this.handleErrorResponse(var6);
                return;
            } catch (Exception var7) {
                LoggerUtil.INSTANCE.logError(LOG, "Error parsing JSON-RPC message: " + message, var7);
                return;
            }

            if (jsonResponse.isNotification()) {
                this.handleNotificationMessage(jsonResponse);
            } else if (jsonResponse.isServerRequest()) {
                this.handleRequestMessage(jsonResponse);
            } else {
                this.handleCommandResponse(jsonResponse, timestamp);
            }

        }
    }

    private void handleNotificationMessage(@NotNull JsonRpcResponse jsonResponse) {
        if (LOG.isDebugEnabled()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "Received server notification: " + jsonResponse.getNotificationName());
        }

        this.processNotification(jsonResponse);
    }

    private void handleRequestMessage(@NotNull JsonRpcResponse request) {
        String requestName = request.getNotificationName();
        if (LOG.isDebugEnabled()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "Received server request: " + requestName);
        }

        if (!this.isShutdown.get()) {
            this.taskExecutor.execute(() -> {
                assert requestName != null;

                Iterator var3 = this.requestListeners.get(requestName).iterator();

                while(var3.hasNext()) {
                    JsonRpcRequestListener<?, ?> listener = (JsonRpcRequestListener)var3.next();

                    try {
                        this.handleRequestListener(request, listener);
                    } catch (Exception var6) {
                        LoggerUtil.INSTANCE.logError(LOG, "error in JSON-RPC request handler", var6);
                    }
                }

            });
        }
    }

    private <I, O> void handleRequestListener(@NotNull JsonRpcResponse request, JsonRpcRequestListener<I, O> listener) {
        Class<I> requestType = listener.getRequestType();
        I parsedRequest = request.getResponse().isJsonNull() ? null : JsonRPC.parseResponse(request.getResponse(), requestType);
        if (parsedRequest != null) {
            Promise<O> result = listener.handleMessage(parsedRequest);
            result.onSuccess((r) -> {
                if (r == null) {
                    LoggerUtil.INSTANCE.logDebug(LOG, "Request handler returned null, not sending response for request " + request.getRequestId());
                } else {
                    JsonRpcClientResponse response = new JsonRpcClientResponse((String)Objects.requireNonNull(request.getNotificationName()), (Integer)Objects.requireNonNull(request.getRequestId()), r);
                    CodeWizAgentProcessService.getInstance().executeResponse(response);
                }
            });
            result.onError((e) -> {
                LoggerUtil.INSTANCE.logError(LOG, "Error processing request " + request.getRequestId(), e);
            });
        }

    }

    private void handleCommandResponse(@NotNull JsonRpcResponse jsonResponse, long timestamp) {
        Integer id = jsonResponse.getRequestId();

        assert id != null;

        PendingRequest<?> pending = this.pendingRequests.remove(id);
        if (pending == null) {
            LoggerUtil.INSTANCE.logError(LOG, "received unexpected response data for id: " + id + ", response: " + jsonResponse, null);
        } else {
            try {
                this.traceResponse(jsonResponse, timestamp, pending);
                Object parsedResponse = jsonResponse.getResponse().isJsonNull() ? null : JsonRPC.parseResponse(jsonResponse.getResponse(), pending.resultType);
                this.taskExecutor.submit(() -> {
                   // AsyncPromise<Object> promise = pending.promise;
                    AsyncPromise promise = pending.promise;
                    promise.setResult(parsedResponse);
                });
            } catch (Exception var7) {
                LoggerUtil.INSTANCE.logError(LOG, "Error processing response of the agent", var7);
                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    pending.promise.setError(var7);
                });
            }

        }
    }

    private void traceResponse(@NotNull JsonRpcResponse jsonResponse, long timestamp, PendingRequest<?> pending) {
        if (LOG.isTraceEnabled()) {
            LoggerUtil.INSTANCE.logDebug(LOG, String.format("[%d] Response received. Command: %s, duration: %d ms, response: %s", jsonResponse.getRequestId(), pending.commandName, timestamp - pending.startTimestamp, jsonResponse.getResponse()));
        }

    }

    private void handleErrorResponse(@NotNull JsonRpcErrorException e) {
        int id = e.getRequestId();
        String message = e.getMessage();
        LoggerUtil.INSTANCE.logDebug(LOG, "received JSON-RPC error. id: " + id + ", error: " + message);
        PendingRequest<?> pending = (PendingRequest)this.pendingRequests.remove(id);
        if (pending == null) {
            LoggerUtil.INSTANCE.logWarn(LOG, "no pending response found for request ID " + id, e);
        } else {
            Object mappedError;
            if (message.contains("NotSignedIn")) {
 //               mappedError = new NotSignedInException(id);
                System.err.println("codewiz log NotSignedInException");
            } else if (pending.commandName.equals("signInConfirm")) {
//                mappedError = new SignInException(e.getMessage());
                System.err.println("codewiz log SignInException");
            } else {
                mappedError = new JsonRpcErrorException("Error processing command " + pending.commandName, e);
            }

            ApplicationManager.getApplication().executeOnPooledThread(() -> {
                pending.promise.setError(new RuntimeException("Code Wiz handleErrorResponse"));
            });
        }
    }

    private void processNotification(@NotNull JsonRpcResponse notification) {
        if (!this.isShutdown.get()) {
            this.taskExecutor.execute(() -> {
                String name = notification.getNotificationName();
                JsonElement message = notification.getResponse();

                assert name != null;

                Iterator var4 = this.notificationListeners.iterator();

                while(var4.hasNext()) {
                    JsonRpcNotificationListener listener = (JsonRpcNotificationListener)var4.next();

                    try {
                        if (listener.handleMessage(name, message)) {
                            break;
                        }
                    } catch (Exception var7) {
                        LoggerUtil.INSTANCE.logError(LOG, "error in JSON-RPC notification handler", var7);
                    }
                }

            });
        }
    }

    public <I> void shutdown() {
        if (!this.isShutdown.compareAndSet(false, true)) {
            throw new IllegalStateException("message handler was already shutdown");
        } else {
            if (this.taskExecutor instanceof BoundedTaskExecutor) {
                try {
                    ((BoundedTaskExecutor)this.taskExecutor).clearAndCancelAll();
                } catch (Exception var2) {
                    LoggerUtil.INSTANCE.logDebug(LOG, "Error calling clearAndCancelAll", var2);
                }
            }

            this.taskExecutor.shutdown();
        }
    }

    public <I, O> void addRequestListener(String lspCommand, JsonRpcRequestListener<I, O> listener) {
        this.requestListeners.put(lspCommand, listener);
    }

    private static final class PendingRequest<T> {
        private final AsyncPromise<T> promise;
        private final Class<T> resultType;
        private final String commandName;
        private final long startTimestamp;

        public PendingRequest(AsyncPromise<T> promise, Class<T> resultType, String commandName, long startTimestamp) {
            this.promise = promise;
            this.resultType = resultType;
            this.commandName = commandName;
            this.startTimestamp = startTimestamp;
        }

        public AsyncPromise<T> getPromise() {
            return this.promise;
        }

        public Class<T> getResultType() {
            return this.resultType;
        }

        public String getCommandName() {
            return this.commandName;
        }

        public long getStartTimestamp() {
            return this.startTimestamp;
        }
    }
}

package com.xhs.codewiz.lang.agent;

import com.google.gson.JsonElement;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.lang.agent.notifications.LogMessageNotification;
import com.xhs.codewiz.lang.agent.notifications.WindowLogMessageNotification;
import com.xhs.codewiz.lang.agent.rpc.JsonRPC;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import org.jetbrains.annotations.NotNull;

class LogMessageNotificationListener implements JsonRpcNotificationListener {
    private static final Logger AGENT_LOG = Logger.getInstance("#codewiz");

    LogMessageNotificationListener() {
    }

    public boolean handleMessage(@NotNull String name, @NotNull JsonElement message) {
        switch (name) {
            case "window/logMessage":
                ((WindowLogMessageNotification) JsonRPC.parseResponse(message, WindowLogMessageNotification.class)).outputMessage(AGENT_LOG);
                return true;
            case "LogMessage":
                ((LogMessageNotification)JsonRPC.parseResponse(message, LogMessageNotification.class)).outputMessage(AGENT_LOG);
                return true;
            default:
                return false;
        }
    }
}

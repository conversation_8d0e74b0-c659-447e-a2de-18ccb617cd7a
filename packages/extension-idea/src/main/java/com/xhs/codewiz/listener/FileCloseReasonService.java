package com.xhs.codewiz.listener;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.util.Alarm;
import com.xhs.codewiz.utils.LoggerUtil;

@Service(Service.Level.PROJECT)
public final class FileCloseReasonService implements FileEditorManagerListener, FileEditorManagerListener.Before, Disposable {

    private static final Logger LOG = Logger.getInstance(FileCloseReasonService.class);

    public enum CloseReason {
        CLOSED,  // 真正关闭
        MOVED    // 关闭后很快又被重新打开（通常是拖动到另一个 editor window）
    }

    public interface Listener {
        void onFileClosed(@NotNull Project project, @NotNull VirtualFile file, @NotNull CloseReason reason);
    }

    private final Project project;
    private final Alarm alarm;
    private final long reopenGraceMs = 200L;
    private final Map<VirtualFile, Runnable> pending = new ConcurrentHashMap<>();
    private final CopyOnWriteArrayList<Listener> listeners = new CopyOnWriteArrayList<>();

    public FileCloseReasonService(Project project) {
        this.project = project;
        this.alarm = new Alarm(Alarm.ThreadToUse.SWING_THREAD, this);

        // 订阅 before/after 事件
        project.getMessageBus()
                .connect(this)
                .subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, this);
        project.getMessageBus()
                .connect(this)
                .subscribe(FileEditorManagerListener.Before.FILE_EDITOR_MANAGER, this);
    }

    public static FileCloseReasonService getInstance(Project project) {
        return project.getService(FileCloseReasonService.class);
    }

    public void addListener(@NotNull Listener listener) {
        listeners.add(Objects.requireNonNull(listener));
    }

    public void removeListener(@NotNull Listener listener) {
        listeners.remove(listener);
    }

    @Override
    public void beforeFileClosed(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
        if (pending.containsKey(file)) {
            return;
        }
        Runnable task = () -> {
            // 超时后仍未被重新打开，认定为真正关闭
            pending.remove(file);
            fire(file, CloseReason.CLOSED);
        };
        pending.put(file, task);
        alarm.addRequest(task, (int) reopenGraceMs);
        LoggerUtil.INSTANCE.logDebug(LOG, "FileCloseReasonService schedule close: " + file.getPath());
    }

    @Override
    public void fileOpened(@NotNull FileEditorManager source, @NotNull VirtualFile file) {
        Runnable task = pending.remove(file);
        if (task != null) {
            alarm.cancelRequest(task);
            fire(file, CloseReason.MOVED);
            LoggerUtil.INSTANCE.logDebug(LOG, "FileCloseReasonService detect move: " + file.getPath());
        }
    }

    @Override
    public void fileOpenedSync(@NotNull FileEditorManager source, @NotNull VirtualFile file,
                               @NotNull com.intellij.openapi.util.Pair<com.intellij.openapi.fileEditor.FileEditor[],
                                       com.intellij.openapi.fileEditor.FileEditorProvider[]> editors) {
        // 与 fileOpened 相同处理，确保在不同平台版本下都能捕获
        Runnable task = pending.remove(file);
        if (task != null) {
            alarm.cancelRequest(task);
            fire(file, CloseReason.MOVED);
            LoggerUtil.INSTANCE.logDebug(LOG, "FileCloseReasonService detect move(sync): " + file.getPath());
        }
    }

    private void fire(@NotNull VirtualFile file, @NotNull CloseReason reason) {
        for (Listener listener : listeners) {
            try {
                listener.onFileClosed(project, file, reason);
            } catch (Throwable t) {
                LoggerUtil.INSTANCE.logError(LOG, "FileCloseReasonService listener error", t);
            }
        }
    }

    @Override
    public void dispose() {
        try {
            alarm.cancelAllRequests();
            pending.clear();
        } catch (Throwable t) {
            LoggerUtil.INSTANCE.logError(LOG, "FileCloseReasonService dispose error", t);
        }
    }
}



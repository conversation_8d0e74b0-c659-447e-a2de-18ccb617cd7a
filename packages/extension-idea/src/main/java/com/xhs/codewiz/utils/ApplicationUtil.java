package com.xhs.codewiz.utils;


import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.openapi.wm.IdeFocusManager;
import com.intellij.openapi.wm.IdeFrame;
import java.io.File;
import java.util.Arrays;
import java.util.stream.Collectors;
import org.jetbrains.annotations.Nullable;

public final class ApplicationUtil {
    private ApplicationUtil() {
    }

    @Nullable
    public static Project findCurrentProject() {
        IdeFrame frame = IdeFocusManager.getGlobalInstance().getLastFocusedFrame();
        Project project = frame != null ? frame.getProject() : null;
        if (isValidProject(project)) {
            return project;
        } else {
            Project[] var2 = ProjectManager.getInstance().getOpenProjects();
            int var3 = var2.length;

            for(int var4 = 0; var4 < var3; ++var4) {
                Project p = var2[var4];
                if (isValidProject(p)) {
                    return p;
                }
            }

            return null;
        }
    }

    public static String getCurrentProjectBaseDir() {
        Project project = findCurrentProject();
        String path = project.getBasePath();
        if (path == null) {
            return project.getPresentableUrl();
        }
        return path.replace('/', File.separatorChar);
    }

    @Nullable
    public static Iterable<Project> findValidProjects() {
        return (Iterable)Arrays.stream(ProjectManager.getInstance().getOpenProjects()).filter(ApplicationUtil::isValidProject).collect(Collectors.toList());
    }

    private static boolean isValidProject(@Nullable Project project) {
        return project != null && !project.isDisposed() && !project.isDefault();
    }
    public static Project getProjectByPath(String workspacePath) {
        for (Project project : ProjectManager.getInstance().getOpenProjects()) {
            String path = getProjectBaseDir(project);
            if (path == null || !path.equals(workspacePath)) continue;
            return project;
        }
        return null;
    }
    public static String getProjectBaseDir(Project project) {
        if (project == null) {
            return null;
        }
        String path = project.getBasePath();
        if (path == null) {
            return project.getPresentableUrl();
        }
        return path.replace('/', File.separatorChar);
    }
}


package com.xhs.codewiz.type.workspace;

import java.util.*;

/**
 * 视图 Tab 标签，用于管理文档窗口
 */
public class Tab {
    /** 是否有未保存的更改 */
    private Boolean isDirty;

    /** 是否为预览标签 */
    private Boolean isPreview;

    /** 是否被固定 */
    private Boolean isPinned;

    /** 标签名称 */
    private String label;

    /** Tab 标签类型 */
    private TabType type;

    /** 是否为当前激活的标签 */
    private Boolean isActive;

    public Boolean getIsDirty() {
        return isDirty;
    }

    public void setIsDirty(Boolean isDirty) {
        this.isDirty = isDirty;
    }

    public Boolean getIsPreview() {
        return isPreview;
    }

    public void setIsPreview(Boolean isPreview) {
        this.isPreview = isPreview;
    }

    public Boolean getIsPinned() {
        return isPinned;
    }

    public void setIsPinned(Boolean isPinned) {
        this.isPinned = isPinned;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public TabType getType() {
        return type;
    }

    public void setType(TabType type) {
        this.type = type;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

}

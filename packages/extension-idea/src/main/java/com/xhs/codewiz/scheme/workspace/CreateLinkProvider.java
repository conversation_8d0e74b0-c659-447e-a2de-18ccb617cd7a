package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.DocumentFilter;
/**
 * 注册链接提供者
 */
public class CreateLinkProvider {
    private String schemaProtocol = "workspace.create.linkprovider";
    private CreateLinkProviderParams params;

    public CreateLinkProviderParams getParams() {
        return params;
    }
    public void setParams(CreateLinkProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateLinkProviderParams {
        private CreateLinkProviderParamsProvider provider;
        private List<DocumentFilter> selector;
    
        public CreateLinkProviderParamsProvider getProvider() {
            return provider;
        }
        public void setProvider(CreateLinkProviderParamsProvider provider) {
            this.provider = provider;
        }
        public List<DocumentFilter> getSelector() {
            return selector;
        }
        public void setSelector(List<DocumentFilter> selector) {
            this.selector = selector;
        }
    }

        public static class CreateLinkProviderParamsProvider {
        /** 链接提供者的 ID */
        private String id;
    
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
    }
}

// package com.xhs.codewiz.service;
//
// import java.util.List;
// import java.util.concurrent.CopyOnWriteArrayList;
// import java.util.concurrent.atomic.AtomicReference;
//
// import org.jetbrains.annotations.NotNull;
//
// import com.intellij.openapi.application.ApplicationManager;
// import com.intellij.openapi.components.Service;
// import com.intellij.openapi.diagnostic.Logger;
// import com.xhs.codewiz.factory.webview.BrowserWindowFactory;
// import com.xhs.codewiz.factory.webview.ToolBrowserWindow;
// import com.xhs.codewiz.scheme.platform.CreateWebviewProvider;
// import com.xhs.codewiz.type.platform.Webview;
// import com.xhs.codewiz.utils.LoggerUtil;
//
// /**
//  * Webview缓存服务
//  * 用于管理全局Webview缓存，支持缓存更新和监听功能
//  * 相比HTML缓存提供更好的扩展性，可以缓存Webview的所有属性
//  */
// @Service
// public final class WebviewCacheService {
//
//     private static final Logger logger = Logger.getInstance(WebviewCacheService.class);
//
//     // 默认HTML内容
//     private static final String DEFAULT_HTML = """
//
//             """;
//
//     // 创建默认Webview对象
//     private static final Webview DEFAULT_WEBVIEW = createDefaultWebview();
//
//     private final AtomicReference<Webview> cachedWebview = new AtomicReference<>(DEFAULT_WEBVIEW);
//     private final AtomicReference<CreateWebviewProvider> cachedProvider = new AtomicReference<>();
//     private final AtomicReference<ToolBrowserWindow> toolWindow = new AtomicReference<>();
//     private final List<WebviewCacheListener> listeners = new CopyOnWriteArrayList<>();
//
//     public static WebviewCacheService getInstance() {
//
//         return ApplicationManager.getApplication().getService(WebviewCacheService.class);
//     }
//
//     /**
//      * 创建默认Webview对象
//      */
//     private static Webview createDefaultWebview() {
//         Webview webview = new Webview();
//         webview.setHtml(DEFAULT_HTML);
//         webview.setTitle("CodeWiz");
//         webview.setDescription("CodeWiz正在初始化");
//         return webview;
//     }
//
//     /**
//      * 获取缓存的Webview对象
//      */
//     public Webview getCachedWebview() {
//         return cachedWebview.get();
//     }
//
//     public void removeCacheWebview() {
//         cachedWebview.set(createDefaultWebview());
//     }
//
//     public CreateWebviewProvider getCachedProvider() {
//         return cachedProvider.get();
//     }
//
//     public void updateProvider(@NotNull CreateWebviewProvider provider) {
//         cachedProvider.set(provider);
//     }
//
//     public void updateToolWindow(@NotNull ToolBrowserWindow toolWindow) {
//         this.toolWindow.set(toolWindow);
//     }
//
//     public ToolBrowserWindow getToolWindow() {
//         return toolWindow.get();
//     }
//
//     /**
//      * 更新缓存的Webview对象
//      */
//     public void updateWebview(@NotNull Webview webview) {
//         cachedWebview.set(webview);
//         notifyListeners(webview);
//     }
//
//     /**
//      * 添加缓存变更监听器
//      */
//     public void addListener(@NotNull WebviewCacheListener listener) {
//         listeners.add(listener);
//     }
//
//     /**
//      * 移除缓存变更监听器
//      */
//     public void removeListener(@NotNull WebviewCacheListener listener) {
//         listeners.remove(listener);
//     }
//
//     /**
//      * 通知所有监听器缓存已变更
//      */
//     private void notifyListeners(Webview newWebview) {
//         ApplicationManager.getApplication().invokeLater(() -> {
//             // 先通知所有注册的监听器
//             for (WebviewCacheListener listener : listeners) {
//                 try {
//                     listener.onWebviewChanged(newWebview);
//                 } catch (Exception e) {
//                     LoggerUtil.INSTANCE.logError(logger, "通知Webview缓存监听器时发生错误", e);
//                 }
//             }
//
//             // 额外确保所有打开的toolwindow都收到更新和JavaScript注入
//             refreshAllToolWindowsWithJsInjection(newWebview);
//         });
//     }
//
//     /**
//      * 刷新所有toolwindow并重新注入JavaScript
//      */
//     private void refreshAllToolWindowsWithJsInjection(Webview webview) {
//         LoggerUtil.INSTANCE.logInfo(logger, "Refreshing all toolwindows with JavaScript injection");
//
//         for (ToolBrowserWindow browserWindow : BrowserWindowFactory.projectWindow.values()) {
//             try {
//                 if (browserWindow.getCodeWizBrowser() != null) {
//                     LoggerUtil.INSTANCE.logInfo(logger, "📝 Updating toolwindow: " + browserWindow.toString());
//
//                     // 加载新HTML
//                     browserWindow.getCodeWizBrowser().loadHTML(webview.getHtml());
//                     browserWindow.setWebview(webview);
//
//                     // 等待HTML加载完成后注入JavaScript
//                     ApplicationManager.getApplication().executeOnPooledThread(() -> {
//                         try {
//                             Thread.sleep(100); // 等待HTML加载
//                             // ToolBrowserWindow.injectJsScript(browserWindow);
//                             LoggerUtil.INSTANCE.logInfo(logger, "JavaScript injected to toolwindow: " + browserWindow.toString());
//                         } catch (InterruptedException e) {
//                             Thread.currentThread().interrupt();
//                         } catch (Exception e) {
//                             LoggerUtil.INSTANCE.logError(logger, "Failed to inject JavaScript", e);
//                         }
//                     });
//                 }
//             } catch (Exception e) {
//                 LoggerUtil.INSTANCE.logError(logger, "刷新toolwindow时发生错误", e);
//             }
//         }
//     }
//
//     /**
//      * 刷新所有已打开的toolwindow
//      */
//     public void refreshAllToolWindows() {
//         Webview webview = getCachedWebview();
//         ApplicationManager.getApplication().invokeLater(() -> {
//             for (ToolBrowserWindow browserWindow : BrowserWindowFactory.projectWindow.values()) {
//                 try {
//                     if (browserWindow.getCodeWizBrowser() != null) {
//                         browserWindow.getCodeWizBrowser().loadHTML(webview.getHtml());
//                         browserWindow.setWebview(webview);
//                         LoggerUtil.INSTANCE.logInfo(logger, "已刷新toolwindow内容");
//                     }
//                 } catch (Exception e) {
//                     LoggerUtil.INSTANCE.logError(logger, "刷新toolwindow时发生错误", e);
//                 }
//             }
//         });
//     }
//
//     /**
//      * 触发重新加载HTML的操作
//      * 用于每次打开toolwindow时强制刷新
//      */
//     public void triggerReload() {
//         Webview currentWebview = getCachedWebview();
//         LoggerUtil.INSTANCE.logInfo(logger, "触发Webview重新加载");
//         notifyListeners(currentWebview);
//     }
//
//     /**
//      * Webview缓存监听器接口
//      */
//     public interface WebviewCacheListener {
//         void onWebviewChanged(Webview newWebview);
//     }
// }
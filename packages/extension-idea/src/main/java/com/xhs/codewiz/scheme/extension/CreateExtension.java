package com.xhs.codewiz.scheme.extension;

import java.util.*;
import com.xhs.codewiz.type.extension.Extension;
/**
 * 创建第三方插件信息
 */
public class CreateExtension {
    private String schemaProtocol = "extension.create.extension";
    private Extension params;

    public Extension getParams() {
        return params;
    }
    public void setParams(Extension params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

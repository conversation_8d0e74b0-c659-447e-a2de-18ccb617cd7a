package com.xhs.codewiz.type.platform;

import java.util.*;

/**
 * 终端退出原因
 */
public enum TerminalExitReason {
    /** 扩展关闭了终端 */
    Extension(4),
    /** 用户关闭了终端 */
    User(3),
    /** 未知原因 */
    Unknown(0),
    /** 终端被关闭 */
    Shutdown(1),
    /** 终端进程退出 */
    Process(2);

    private final int value;

    TerminalExitReason(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}

package com.xhs.codewiz.lang.agent;

import com.google.gson.JsonObject;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcClientResponse;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcRequestListener;
import org.jetbrains.concurrency.CancellablePromise;

public interface CodeWizAgentProcessService {
    static CodeWizAgentProcessService getInstance() {
        return ApplicationManager.getApplication().getService(CodeWizAgentProcessService.class);
    }

    boolean isRunning();

    default <T>  CancellablePromise<T> executeCommand(JsonRpcCommand<T> command) {
        return this.executeCommand(command, (JsonObject)null);
    }

    <T>  CancellablePromise<T> executeCommand(JsonRpcCommand<T> var1,  JsonObject var2);

    void executeResponse(JsonRpcClientResponse var1);

    CancellablePromise<Object> executeCommonLsp(JsonObject request, String commandName);

    void commonNotification4UI(JsonObject request, String commandName);

    default void executeNotification(JsonRpcNotification notification) {
        this.executeNotification(notification, (JsonObject)null);
    }

    void executeNotification(JsonRpcNotification var1, JsonObject var2);

    void addNotificationListener(Disposable var1, JsonRpcNotificationListener var2);

    <I, O> void addRequestListener(String var1, JsonRpcRequestListener<I, O> var2);
}

package com.xhs.codewiz.lang.agent;

import com.google.gson.JsonObject;
import com.intellij.execution.ExecutionException;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.util.EmptyRunnable;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.util.ConcurrencyUtil;
import com.xhs.codewiz.lang.agent.commands.CancelRequestNotification;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcClientResponse;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcMessageHandler;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcRequestListener;
import com.xhs.codewiz.lang.agent.vscodeRpc.AgentProcessHandler;
import com.xhs.codewiz.lang.agent.vscodeRpc.DefaultJsonRpcMessageHandler;
import com.xhs.codewiz.lang.agent.vscodeRpc.VSCodeJsonRpc;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.PluginUtil;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.AsyncPromise;
import org.jetbrains.concurrency.CancellablePromise;
import org.jetbrains.concurrency.Promises;


abstract class CodeWizAgentProcessServiceImpl implements CodeWizAgentProcessServiceEx {
    private static final Logger LOG = Logger.getInstance(CodeWizAgentProcessServiceImpl.class);
    private static final String THREAD_NAME = "Code Wiz agent";
    private final AtomicInteger requestId = new AtomicInteger();
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private final AtomicBoolean isShutdown = new AtomicBoolean(false);
    private final ExecutorService agentExecutor = ConcurrencyUtil.newSingleThreadExecutor("Code Wiz agent");
    private final DefaultJsonRpcMessageHandler messageHandler = new DefaultJsonRpcMessageHandler();
    private final @NotNull AgentProcessHandler agentProcess;

    CodeWizAgentProcessServiceImpl(int restartAttempts, boolean isInit) throws ExecutionException {
        this.agentProcess = launchAgent(this.messageHandler, restartAttempts, isInit);
        //禁用ide内部的补全
        PluginUtil.disabledFullLineCompletion();
    }


    private static void updateThreadName(@NotNull String type, @NotNull String name) {
        Thread.currentThread().setName(String.format("%s: %s '%s'", "Code Wiz agent", type, name));
    }

    private static void resetThreadName() {
        Thread.currentThread().setName("Code Wiz agent");
    }

    private static boolean isRestartException(@NotNull Exception e) {
        return e instanceof IOException && StringUtil.contains(e.getMessage(), "Stream closed");
    }

    private static @NotNull AgentProcessHandler launchAgent(@NotNull JsonRpcMessageHandler messageHandler, int restartAttempts,
                                                            boolean isInit) throws ExecutionException {
        boolean debugMode = ApplicationManager.getApplication().isUnitTestMode() || ApplicationManager.getApplication().isInternal();
        GeneralCommandLine cmdline = CodeWizAgentCommandLine.createAgentCommandLine(debugMode, restartAttempts);
        return new AgentProcessHandler(cmdline, messageHandler, isInit);
    }

    abstract void beforeCommand(@NotNull JsonRpcCommand<?> var1);

    abstract void afterCommand(@NotNull JsonRpcCommand<?> var1);

    abstract void afterCommand();

    abstract void afterNotification();

    abstract void beforeNotification(@NotNull JsonRpcNotification var1);

    abstract void beforeResponse(@NotNull JsonRpcClientResponse var1);

    abstract void afterNotification(@NotNull JsonRpcNotification var1);

    abstract void onRestartException(@NotNull Exception var1, @NotNull String var2, @NotNull String var3, @Nullable Integer var4);

    public boolean isRunning() {
        return !this.agentProcess.isProcessTerminated() && !this.agentProcess.isProcessTerminating();
    }

    public final <T> @NotNull CancellablePromise<T> executeCommand(@NotNull JsonRpcCommand<T> command,
                                                                   @Nullable JsonObject additionalProperties) {
        if (this.isShutdown()) {
            return Promises.rejectedCancellablePromise("agent was shutdown");
        } else {
            int nextRequestId = this.requestId.getAndIncrement();
            LoggerUtil.INSTANCE.logDebug(LOG,
                    "Registering result for request ID: " + nextRequestId + ", command: " + command.getCommandName());
            AsyncPromise<T> promise = this.createCommandPromise(command, nextRequestId);
            this.executeCommandWithPromise(promise, command, nextRequestId, additionalProperties);
            return promise;
        }
    }

    public void executeResponse(@NotNull JsonRpcClientResponse response) {
        if (!this.isShutdown()) {
            String responseName = response.getMethodName();
            if (LOG.isDebugEnabled()) {
                LoggerUtil.INSTANCE.logDebug(LOG, "Sending response:" + responseName);
            }

            this.agentExecutor.submit(() -> {
                try {
                    updateThreadName("response", responseName);
                    this.beforeResponse(response);
                    VSCodeJsonRpc.sendResponse(this.agentProcess.getProcessInput(), response);
                } catch (Exception var7) {
                    this.handleAgentExceptionLocked(responseName, var7);
                } finally {
                    resetThreadName();
                }

            });
        }
    }

    public final CancellablePromise<Object> executeCommonLsp(@NotNull JsonObject request, String commandName) {
        if (this.isShutdown()) {
            return Promises.rejectedCancellablePromise("agent was shutdown");
        } else {
            int nextRequestId = this.requestId.getAndIncrement();
            LoggerUtil.INSTANCE.logDebug(LOG, "Registering result for request ID: " + nextRequestId + ", command: " + commandName);
            AsyncPromise<Object> promise = this.createCommonLspPromise(request, nextRequestId, commandName);
            this.executeCommandWithPromise(promise, request, nextRequestId, commandName);
            return promise;
        }
    }

    public void commonNotification4UI(@NotNull JsonObject request, String commandName) {
        if (this.isShutdown()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "commonNotification4UI called for shutdown agent");
        } else {
            if (LOG.isDebugEnabled()) {
                LoggerUtil.INSTANCE.logDebug(LOG, "Sending notification:" + commandName);
            }

            this.agentExecutor.submit(() -> {
                try {
                    updateThreadName("notification", commandName);
                    VSCodeJsonRpc.sendCommonNotification4UI(this.agentProcess.getProcessInput(), request, commandName);
                    this.afterNotification();
                } catch (Exception var7) {
                    this.handleAgentExceptionLocked(commandName, var7);
                } finally {
                    resetThreadName();
                }

            });
        }
    }

    private void executeCommandWithPromise(@NotNull AsyncPromise<Object> promise, @NotNull JsonObject request, int nextRequestId,
                                           String commandName) {
        this.agentExecutor.submit(() -> {
            try {
                updateThreadName("command", commandName);
                //this.beforeCommand(null);
                VSCodeJsonRpc.sendCommonLsp(this.agentProcess.getProcessInput(), nextRequestId, request, commandName);
                this.afterCommand();
            } catch (Exception var9) {
                this.handleAgentExceptionLocked(commandName, var9);
                promise.setError(var9.getMessage());
            } finally {
                resetThreadName();
            }

        });
    }

    private AsyncPromise<Object> createCommonLspPromise(JsonObject request, int nextRequestId, String commandName) {

        return this.messageHandler.addPendingRequest(nextRequestId, commandName, Object.class, () -> {
            if (LOG.isTraceEnabled()) {
                LoggerUtil.INSTANCE.logDebug(LOG, "Sending cancel notification for request " + nextRequestId + ", command: " + commandName);
            }

            this.executeNotification(new CancelRequestNotification(nextRequestId));
        });
    }

    public void addNotificationListener(@NotNull Disposable parentDisposable, @NotNull JsonRpcNotificationListener listener) {
        if (this.isShutdown()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "addNotificationListener called for shutdown agent");
        } else {
            Disposer.tryRegister(parentDisposable, () -> {
                this.messageHandler.removeNotificationListener(listener);
            });
            this.messageHandler.addNotificationListeners(List.of(listener));
        }
    }

    public <I, O> void addRequestListener(@NotNull String lspCommand, @NotNull JsonRpcRequestListener<I, O> listener) {
        if (this.isShutdown()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "addRequestListener called for shutdown agent");
        } else {
            this.messageHandler.addRequestListener(lspCommand, listener);
        }
    }

    public void initialize(@NotNull Collection<JsonRpcNotificationListener> listeners) {
        if (this.isInitialized.compareAndExchange(false, true)) {
            throw new IllegalStateException("agent was already initialized");
        } else {
            this.messageHandler.addNotificationListeners(listeners);

            try {
                /*TextDocumentSyncClientCapabilities syncCapabilities =
                        new TextDocumentSyncClientCapabilities(true, null, null, null);
                ClientCapabilities clientCapabilities =
                        new ClientCapabilities(new TextDocumentClientCapabilities(syncCapabilities), new WorkspaceCapabilities(true));
                List<WorkspaceFolder> workspaceFolders = this.collectWorkspaceFolders();
                this.executeCommand(new InitializeCommand(clientCapabilities, workspaceFolders));
                SetEditorInfoCommand.NameAndVersion editor =
                        new SetEditorInfoCommand.NameAndVersion(CopilotPlugin.getEditorName(), CopilotPlugin.getEditorVersion(),
                                CopilotPlugin.getReadableEditorName());
                SetEditorInfoCommand.NameAndVersion plugin =
                        new SetEditorInfoCommand.NameAndVersion(CopilotPlugin.getPluginName(), CopilotPlugin.getVersion(),
                                CopilotPlugin.getReadableEditorName());
                EditorSettings agentSettings = EditorSettings.basedOn(CopilotApplicationSettings.settings());
                this.executeCommand(new SetEditorInfoCommand(editor, plugin, agentSettings, AgentProxySettings.fromPlatform()));*/
            } catch (Exception var8) {
                LoggerUtil.INSTANCE.logError(LOG, "error initializing agent", var8);
            }

        }
    }

    public void startNotify() {
        this.agentProcess.startNotify();
    }

    public boolean isShutdown() {
        return this.isShutdown.get();
    }

    public void shutdown() {
        if (!this.isShutdown.compareAndSet(false, true)) {
            throw new IllegalStateException("agent was already shutdown");
        } else {
            boolean isAlive = !ApplicationManager.getApplication().isDisposed();

            try {
                this.messageHandler.shutdown();
                if (isAlive) {
                    this.agentProcess.destroyProcess();
                }

                this.agentExecutor.shutdown();
                if (isAlive) {
                    this.agentExecutor.awaitTermination(1L, TimeUnit.SECONDS);
                }
            } catch (Exception var6) {
                LoggerUtil.INSTANCE.logWarn(LOG, "error terminating agent", var6);
            } finally {
                if (!isAlive || !this.agentProcess.isProcessTerminating() && !this.agentProcess.isProcessTerminated()) {
                    if (this.agentProcess.canKillProcess()) {
                        this.agentProcess.killProcess();
                    } else {
                        LoggerUtil.INSTANCE.logWarn(LOG, "Unable to forcefully terminate agent process", null);
                    }
                }

            }

        }
    }

    public void flush() {
        if (this.isShutdown()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "flush called for shutdown agent");
        } else {
            try {
                this.agentExecutor.submit(EmptyRunnable.INSTANCE).get(15L, TimeUnit.SECONDS);
            } catch (Exception var2) {
                throw new RuntimeException("Error flushing agent executor service", var2);
            }
        }
    }

   /* private List<WorkspaceFolder> collectWorkspaceFolders() {
        return AgentWorkspaceFolders.asWorkspaceFolders(ProjectManager.getInstance().getOpenProjects());
    }*/

    private void handleAgentExceptionLocked(@NotNull String commandName, @NotNull Exception e) {
        if (this.isShutdown()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "handleAgentExceptionLocked called for shutdown agent");
        } else {
            if (isRestartException(e)) {
                this.onRestartException(e, commandName, this.agentProcess.getRecentOutput(), this.agentProcess.getExitCode());
            } else {
                int id = this.requestId.get();
                LoggerUtil.INSTANCE.logWarn(LOG,
                        "Failed to execute JSON-RPC command or notification. Request: " + id + ", command: " + commandName, e);
            }

        }
    }

    private <T> AsyncPromise<T> createCommandPromise(@NotNull JsonRpcCommand<T> command, int nextRequestId) {
        return this.messageHandler.addPendingRequest(nextRequestId, command.getCommandName(), command.getResponseType(), () -> {
            if (LOG.isTraceEnabled()) {
                LoggerUtil.INSTANCE.logDebug(LOG, "Sending cancel notification for request " + nextRequestId + ", command: " + command);
            }

            this.executeNotification(new CancelRequestNotification(nextRequestId));
        });
    }

    private <T> void executeCommandWithPromise(@NotNull AsyncPromise<T> promise, @NotNull JsonRpcCommand<T> command, int nextRequestId,
                                               @Nullable JsonObject additionalProperties) {
        this.agentExecutor.submit(() -> {
            try {
                updateThreadName("command", command.getCommandName());
                this.beforeCommand(command);
                VSCodeJsonRpc.sendCommand(this.agentProcess.getProcessInput(), nextRequestId, command, additionalProperties);
                this.afterCommand(command);
            } catch (Exception var9) {
                this.handleAgentExceptionLocked(command.getCommandName(), var9);
                promise.setError(var9.getMessage());
            } finally {
                resetThreadName();
            }

        });
    }

    public void executeNotification(@NotNull JsonRpcNotification notification, @Nullable JsonObject additionalProperties) {
        if (this.isShutdown()) {
            LoggerUtil.INSTANCE.logDebug(LOG, "executeNotification called for shutdown agent");
        } else {
            if (LOG.isDebugEnabled()) {
                LoggerUtil.INSTANCE.logDebug(LOG, "Sending notification:" + notification.getCommandName());
            }

            this.agentExecutor.submit(() -> {
                try {
                    updateThreadName("notification", notification.getCommandName());
                    this.beforeNotification(notification);
                    VSCodeJsonRpc.sendNotification(this.agentProcess.getProcessInput(), notification, additionalProperties);
                    this.afterNotification(notification);
                } catch (Exception var7) {
                    this.handleAgentExceptionLocked(notification.getCommandName(), var7);
                } finally {
                    resetThreadName();
                }

            });
        }
    }
}


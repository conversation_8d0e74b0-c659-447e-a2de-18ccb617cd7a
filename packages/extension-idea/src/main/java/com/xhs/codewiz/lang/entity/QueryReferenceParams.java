package com.xhs.codewiz.lang.entity;

public class QueryReferenceParams {
  String workspace;
  String key;
  int targetRow;
  int targetColumn;


  public QueryReferenceParams() {
  }


  public String getWorkspace() {
    return this.workspace;
  }


  public String getKey() {
    return this.key;
  }


  public int getTargetRow() {
    return this.targetRow;
  }


  public int getTargetColumn() {
    return this.targetColumn;
  }


  public void setWorkspace(String workspace) {
    this.workspace = workspace;
  }


  public void setKey(String key) {
    this.key = key;
  }


  public void setTargetRow(int targetRow) {
    this.targetRow = targetRow;
  }


  public void setTargetColumn(int targetColumn) {
    this.targetColumn = targetColumn;
  }


  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (!(o instanceof QueryReferenceParams)) {
      return false;
    }
    QueryReferenceParams other = (QueryReferenceParams)o;
    if (!other.canEqual(this)) {
      return false;
    }
    String this$workspace = this.getWorkspace();
    String other$workspace = other.getWorkspace();
    if (this$workspace == null ? other$workspace != null : !this$workspace.equals(other$workspace)) {
      return false;
    }
    String this$key = this.getKey();
    String other$key = other.getKey();
    if (this$key == null ? other$key != null : !this$key.equals(other$key)) {
      return false;
    }
    if (this.getTargetRow() != other.getTargetRow()) {
      return false;
    }
    return this.getTargetColumn() == other.getTargetColumn();
  }


  protected boolean canEqual(Object other) {
    return other instanceof QueryReferenceParams;
  }


  public int hashCode() {
    int PRIME = 59;
    int result = 1;
    String $workspace = this.getWorkspace();
    result = result * 59 + ($workspace == null ? 43 : $workspace.hashCode());
    String $key = this.getKey();
    result = result * 59 + ($key == null ? 43 : $key.hashCode());
    result = result * 59 + this.getTargetRow();
    result = result * 59 + this.getTargetColumn();
    return result;
  }


  public String toString() {
    return "QueryReferenceParams(workspace=" + this.getWorkspace() + ", key=" + this.getKey() + ", targetRow=" + this.getTargetRow() + ", targetColumn=" + this.getTargetColumn() + ")";
  }
}
package com.xhs.codewiz.platform.comment;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.Service;
import com.xhs.codewiz.platform.enums.CommentStatus;
import java.util.concurrent.TimeUnit;

/**
 * Comment缓存服务
 * 根据noteId缓存comment状态，提供快速的状态检索和更新
 *
 * <AUTHOR>
 */
@Service
public final class CommentCacheService {

    // 评论状态缓存，以noteId为key
    private static final Cache<String, CommentStatus> COMMENT_STATUS_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(100)
            .build();

    // 评论内容缓存，以noteId为key
    private static final Cache<String, Comment> COMMENT_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(100)
            .build();

    public static CommentCacheService getInstance() {
        return ApplicationManager.getApplication().getService(CommentCacheService.class);
    }

    /**
     * 获取评论状态
     *
     * @param noteId 评论ID
     * @return 评论状态，未找到时返回UNKNOWN
     */
    public CommentStatus getCommentStatus(String noteId) {
        if (noteId == null) {
            return CommentStatus.UNKNOWN;
        }
        return COMMENT_STATUS_CACHE.get(noteId, key -> CommentStatus.UNKNOWN);
    }

    /**
     * 获取评论状态（Long类型noteId）
     */
    public CommentStatus getCommentStatus(Long noteId) {
        return getCommentStatus(noteId != null ? noteId.toString() : null);
    }

    /**
     * 更新评论状态
     *
     * @param noteId 评论ID
     * @param status 新状态
     */
    public void updateCommentStatus(String noteId, CommentStatus status) {
        if (noteId == null || status == null) {
            return;
        }
        COMMENT_STATUS_CACHE.put(noteId, status);
        
        // 同时更新评论缓存中的状态
        Comment cachedComment = COMMENT_CACHE.getIfPresent(noteId);
        if (cachedComment != null) {
            cachedComment.setStatus(status);
        }
    }

    /**
     * 更新评论状态（Long类型noteId）
     */
    public void updateCommentStatus(Long noteId, CommentStatus status) {
        updateCommentStatus(noteId != null ? noteId.toString() : null, status);
    }

    /**
     * 缓存评论对象
     *
     * @param comment 评论对象
     */
    public void cacheComment(Comment comment) {
        if (comment == null || comment.getNoteId() == null) {
            return;
        }
        String noteId = comment.getNoteId().toString();
        COMMENT_CACHE.put(noteId, comment);
        
        // 同时缓存状态
        COMMENT_STATUS_CACHE.put(noteId, comment.getStatus());
    }

    /**
     * 获取缓存的评论对象
     *
     * @param noteId 评论ID
     * @return 评论对象，未找到时返回null
     */
    public Comment getCachedComment(String noteId) {
        if (noteId == null) {
            return null;
        }
        return COMMENT_CACHE.getIfPresent(noteId);
    }

    /**
     * 获取缓存的评论对象（Long类型noteId）
     */
    public Comment getCachedComment(Long noteId) {
        return getCachedComment(noteId != null ? noteId.toString() : null);
    }

    /**
     * 从缓存中移除评论
     *
     * @param noteId 评论ID
     */
    public void removeComment(String noteId) {
        if (noteId == null) {
            return;
        }
        COMMENT_CACHE.invalidate(noteId);
        COMMENT_STATUS_CACHE.invalidate(noteId);
    }

    /**
     * 从缓存中移除评论（Long类型noteId）
     */
    public void removeComment(Long noteId) {
        removeComment(noteId != null ? noteId.toString() : null);
    }

    /**
     * 清空所有缓存
     */
    public void clearAllCache() {
        COMMENT_CACHE.invalidateAll();
        COMMENT_STATUS_CACHE.invalidateAll();
    }

    /**
     * 批量更新评论状态到缓存
     *
     * @param comments 评论列表
     */
    public void batchCacheComments(java.util.List<Comment> comments) {
        if (comments == null || comments.isEmpty()) {
            return;
        }
        
        for (Comment comment : comments) {
            cacheComment(comment);
        }
    }

    /**
     * 检查评论是否已被用户操作过
     *
     * @param noteId 评论ID
     * @return true if 有用户操作，false otherwise
     */
    public boolean hasUserAction(String noteId) {
        CommentStatus status = getCommentStatus(noteId);
        return status != CommentStatus.UNKNOWN;
    }

    /**
     * 检查评论是否已被用户操作过（Long类型noteId）
     */
    public boolean hasUserAction(Long noteId) {
        return hasUserAction(noteId != null ? noteId.toString() : null);
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return String.format("Comment Cache: %d items, Status Cache: %d items",
                COMMENT_CACHE.estimatedSize(),
                COMMENT_STATUS_CACHE.estimatedSize());
    }
} 
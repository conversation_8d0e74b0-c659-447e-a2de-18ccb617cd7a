package com.xhs.codewiz.enums;

/**
 * Author: liukunpeng Date: 2025-04-23 Description:
 */
public enum MetricsKeyEnum {
    COMPLETE_INIT(1001, "complete_init", "补全开始"),
    COMPLETE_GENERATE(1002, "complete_generate", "补全结果"),
    COMPLETE_SHOWN(1003, "complete_shown", "补全上屏"),
    COMPLETE_ACCEPTED(1004, "complete_accepted", "补全采纳"),
    COMPLETE_REJECTED(1005, "complete_rejected", "补全拒绝"),
    COMPLETE_CANCEL(1006, "complete_cancel", "取消补全：触发新补全会取消上一次未完成的补全"),
    COMPLETE_ERROR(1007, "complete_error", "补全流程异常上报"),
    COMPLETE_CANCEL_AFTER_SHOWN(1008, "complete_cancel_after_shown", "上屏后取消"),


    CHAT_INIT(3001, "chat_init", "问答开始"),
    CHAT_GENERATED(3002, "chat_generated", "生成问答内容完毕"),
    CHAT_CODE_SUGGESTED(3003, "chat_code_suggested", "问答代码推荐"),
    CHAT_CODE_ACCEPTED(3004, "chat_code_accepted", "采纳代码推荐"),
    CHAT_CANCEL(3005, "chat_cancel", "取消问答"),
    CHAT_ERROR(3006, "chat_error", "问答异常"),
    CHAT_THINK_FINISH(3007, "chat_think_finish", "思考过程结束"),

    EDIT_REAL_TIME_CHANGE(2001, "editor_real_time_change", "编辑器实时修改"),

    PLUGIN_LAUNCH_START(4001, "pluginStartActivate", "插件启动"),
    PLUGIN_LAUNCH_SUCCEED(4002, "pluginActivateSuccess", "插件启动成功"),
    PLUGIN_LAUNCH_FAILED(4003, "pluginActivateFail", "插件启动失败"),
    PLUGIN_REQUEST_START(4005, "plugin_request_start", "插件请求开始"),
    PLUGIN_REQUEST_SUCCEED(4006, "plugin_request_succeed", "插件请求成功"),
    PLUGIN_REQUEST_FAILED(4007, "plugin_request_failed", "插件请求失败"),

    START_LSP(4008, "start_lsp", "启动LSP"),
    START_LSP_FAILED(4009, "start_lsp_failed", "LSP启动失败");

    private final Integer type;
    private final String name;
    private final String desc;

    MetricsKeyEnum(Integer type, String name, String desc) {
        this.type = type;
        this.name = name;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public String getName() {
        return name;
    }
}

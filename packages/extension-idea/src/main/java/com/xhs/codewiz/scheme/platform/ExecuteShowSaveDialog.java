package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 显示保存对话框
 */
public class ExecuteShowSaveDialog {
    private String schemaProtocol = "platform.execute.showsavedialog";
    private ExecuteShowSaveDialogParams params;

    public ExecuteShowSaveDialogParams getParams() {
        return params;
    }
    public void setParams(ExecuteShowSaveDialogParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteShowSaveDialogParams {
        /** 保存按钮的文本 */
        private String saveLabel; // optional
        /** 默认选择的文件路径 */
        private String defaultUri; // optional
        private Map<String, List<String>> filters; // optional
        /** 保存对话框标题 */
        private String title; // optional
    
        public String getSaveLabel() {
            return saveLabel;
        }
        public void setSaveLabel(String saveLabel) {
            this.saveLabel = saveLabel;
        }
        public String getDefaultUri() {
            return defaultUri;
        }
        public void setDefaultUri(String defaultUri) {
            this.defaultUri = defaultUri;
        }
        public Map<String, List<String>> getFilters() {
            return filters;
        }
        public void setFilters(Map<String, List<String>> filters) {
            this.filters = filters;
        }
        public String getTitle() {
            return title;
        }
        public void setTitle(String title) {
            this.title = title;
        }
    }
}

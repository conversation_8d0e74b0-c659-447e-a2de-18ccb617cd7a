package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 显示 Webview
 */
public class ExecuteShowWebView {
    private String schemaProtocol = "platform.execute.showwebview";
    /** Webview provider ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

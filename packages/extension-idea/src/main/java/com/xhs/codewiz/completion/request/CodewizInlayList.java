package com.xhs.codewiz.completion.request;


import com.intellij.openapi.util.TextRange;
import com.xhs.codewiz.completion.CodewizCompletion;
import com.xhs.codewiz.editor.request.CodewizEditorInlay;
import java.util.List;
import org.jetbrains.annotations.NotNull;

public interface CodewizInlayList extends Iterable<CodewizEditorInlay> {
    boolean isEmpty();

    @NotNull CodewizCompletion getCodewizCompletion();

    @NotNull TextRange getReplacementRange();

    @NotNull String getReplacementText();

    @NotNull List<CodewizEditorInlay> getInlays();
}


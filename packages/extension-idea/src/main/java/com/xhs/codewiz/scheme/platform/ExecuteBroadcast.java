package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 全局广播
 */
public class ExecuteBroadcast {
    private String schemaProtocol = "platform.execute.broadcast";
    /** 全局通知内容 */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

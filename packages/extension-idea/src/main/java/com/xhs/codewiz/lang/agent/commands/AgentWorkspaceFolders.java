package com.xhs.codewiz.lang.agent.commands;

import com.intellij.openapi.components.ComponentManager;
import com.intellij.openapi.module.Module;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectUtil;
import com.intellij.openapi.roots.ModuleRootManager;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.editor.request.VirtualFileUri;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.jetbrains.annotations.Nullable;

public final class AgentWorkspaceFolders {
    public static DidChangeWorkspaceFolders projectAddedNotification(Project project) {
        return folderAddedNotification(asWorkspaceFolders(project));
    }

    public static DidChangeWorkspaceFolders projectRemovedNotification(Project project) {
        return folderRemovedNotification(asWorkspaceFolders(project));
    }

    public static DidChangeWorkspaceFolders moduleAddedNotification(Module module) {
        return folderAddedNotification(asWorkspaceFolders(module));
    }

    public static DidChangeWorkspaceFolders moduleRemovedNotification(Module module) {
        return folderRemovedNotification(asWorkspaceFolders(module));
    }

    public static List<WorkspaceFolder> asWorkspaceFolders(Project... projects) {
        return (List)Arrays.stream(projects).filter(Predicate.not(ComponentManager::isDisposed)).map(AgentWorkspaceFolders::asWorkspaceFolder).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static List<WorkspaceFolder> asWorkspaceFolders(Module... modules) {
        return (List)Arrays.stream(modules).filter(Predicate.not(Module::isDisposed)).flatMap(AgentWorkspaceFolders::asWorkspaceFolder).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static DidChangeWorkspaceFolders folderRemovedNotification(List<WorkspaceFolder> folders) {
        return new DidChangeWorkspaceFolders(new WorkspaceFoldersChangeEvent(Collections.emptyList(), folders));
    }

    private static DidChangeWorkspaceFolders folderAddedNotification(List<WorkspaceFolder> folders) {
        return new DidChangeWorkspaceFolders(new WorkspaceFoldersChangeEvent(folders, Collections.emptyList()));
    }

    private static @Nullable WorkspaceFolder asWorkspaceFolder(Project project) {
        VirtualFile projectDir = ProjectUtil.guessProjectDir(project);
        return projectDir != null ? asWorkspaceFolder(projectDir) : null;
    }

    private static Stream<WorkspaceFolder> asWorkspaceFolder(Module module) {
        VirtualFile[] contentRoots = ModuleRootManager.getInstance(module).getContentRoots();
        return Arrays.stream(contentRoots).map(AgentWorkspaceFolders::asWorkspaceFolder);
    }

    private static WorkspaceFolder asWorkspaceFolder(VirtualFile virtualFile) {
        return new WorkspaceFolder(VirtualFileUri.from(virtualFile));
    }

    private AgentWorkspaceFolders() {
    }
}


package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.workspace.LinkItem;
/**
 * 获取链接的详细信息
 */
public class ReadLinkDetail {
    private String schemaProtocol = "workspace.read.linkdetail";
    private ReadLinkDetailParams params;

    public ReadLinkDetailParams getParams() {
        return params;
    }
    public void setParams(ReadLinkDetailParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadLinkDetailParams {
        private LinkItem item;
        /** Link Provider 的 ID */
        private String provider;
    
        public LinkItem getItem() {
            return item;
        }
        public void setItem(LinkItem item) {
            this.item = item;
        }
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
    }
}

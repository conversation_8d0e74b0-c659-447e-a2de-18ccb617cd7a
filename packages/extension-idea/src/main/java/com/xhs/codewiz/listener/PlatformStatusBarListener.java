package com.xhs.codewiz.listener;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.wm.StatusBarListener;
import com.intellij.openapi.wm.StatusBarWidget;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * @ClassName PlatformStatusBarListener
 * @Description 监听本地StatusBar变化并通知远端（可选功能）
 * 注意：这与platform.excute.hideStatusBar/showStatusBar协议方向相反
 * 这些协议是远端发送到本地的请求，由StatusBarManagerListener处理
 * @Date 2025/7/17 16:55
 * <AUTHOR>
 */
public class PlatformStatusBarListener implements StatusBarListener {
    private static final Logger log = Logger.getInstance(PlatformStatusBarListener.class);
    
    // 是否启用本地StatusBar变化通知
    private static final boolean ENABLE_LOCAL_CHANGE_NOTIFICATION = false;

    @Override
    public void widgetAdded(@NotNull StatusBarWidget widget, @NonNls @Nullable String anchor) {
        // if (!ENABLE_LOCAL_CHANGE_NOTIFICATION) {
        //     return;
        // }
        //
        // try {
        //     LoggerUtil.INSTANCE.logDebug(log, "Local StatusBar widget added: " + widget.ID());
        //
        //     // 注意：这是本地变化通知远端，与协议方向相反
        //     // platform.excute.showStatusBar是远端请求本地显示
        //     ExcuteShowStatusBar excuteShowStatusBar = new ExcuteShowStatusBar();
        //     excuteShowStatusBar.setParams(widget.ID());
        //
        //     RcsWebSocketManager.INSTANCE.sendNotification(excuteShowStatusBar, ApplicationUtil.findCurrentProject());
        //
        //     LoggerUtil.INSTANCE.logDebug(log, "Notified remote of local widget addition: " + widget.ID());
        //
        // } catch (Exception e) {
        //     LoggerUtil.INSTANCE.logError(log, "Failed to notify remote of widget addition", e);
        // }
    }

    @Override
    public void widgetRemoved(@NonNls @NotNull String id) {
        // if (!ENABLE_LOCAL_CHANGE_NOTIFICATION) {
        //     return;
        // }
        //
        // try {
        //     LoggerUtil.INSTANCE.logDebug(log, "Local StatusBar widget removed: " + id);
        //
        //     // 注意：这是本地变化通知远端，与协议方向相反
        //     // platform.excute.hideStatusBar是远端请求本地隐藏
        //     ExcuteHideStatusBar excuteHideStatusBar = new ExcuteHideStatusBar();
        //     excuteHideStatusBar.setParams(id);
        //
        //     RcsWebSocketManager.INSTANCE.sendNotification(excuteHideStatusBar, ApplicationUtil.findCurrentProject());
        //
        //     LoggerUtil.INSTANCE.logDebug(log, "Notified remote of local widget removal: " + id);
        //
        // } catch (Exception e) {
        //     LoggerUtil.INSTANCE.logError(log, "Failed to notify remote of widget removal", e);
        // }
    }

}

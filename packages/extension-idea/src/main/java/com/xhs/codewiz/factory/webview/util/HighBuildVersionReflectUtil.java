package com.xhs.codewiz.factory.webview.util;

import com.intellij.openapi.editor.colors.EditorColorsManager;
import com.intellij.openapi.editor.colors.EditorColorsScheme;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import javax.swing.Icon;

/**
 * 高版本构建反射工具类
 */
public class HighBuildVersionReflectUtil {
    private static final String[] CLASS_NAMES = {
        "com.intellij.polySymbols.webTypes.WebTypesSvgStringIconLoader",
        "com.intellij.webSymbols.webTypes.WebTypesSvgStringIconLoader"
    };
    /**
     * 判断是否是暗色主题
     */
    public static boolean isUnderDarcula() {
        try {
            EditorColorsScheme scheme = EditorColorsManager.getInstance().getGlobalScheme();
            return scheme.getName().toLowerCase().contains("dark") || 
                   scheme.getName().toLowerCase().contains("darcula");
        } catch (Exception e) {
            return false;
        }
    }
    /**
     * 尝试兼容性调用WebTypesSvgStringIconLoader.INSTANCE.loadIcon(content)
     *
     * @param content svg字符串
     * @return loadIcon方法的返回值(比如Icon对象)
     * @throws Exception 如果反射失败
     */
    public static Icon loadWebTypesSvgIcon(String content) {
        ClassLoader cl = HighBuildVersionReflectUtil.class.getClassLoader();

        Class<?> targetClass = null;
        for (String className : CLASS_NAMES) {
            try {
                targetClass = cl.loadClass(className);
                break;
            } catch (ClassNotFoundException ignored) {
            }
        }
        if (targetClass == null) {
            return null;
        }
        // 获取INSTANCE字段
        try {
            Field instanceField = targetClass.getField("INSTANCE");
            Object instance = instanceField.get(null);
            // 获取loadIcon(String)方法
            Method loadIconMethod = targetClass.getMethod("loadIcon", String.class);

            // 调用loadIcon
            return (Icon) loadIconMethod.invoke(instance, content);
        } catch (Exception e) {
            return null;
        }
    }
} 
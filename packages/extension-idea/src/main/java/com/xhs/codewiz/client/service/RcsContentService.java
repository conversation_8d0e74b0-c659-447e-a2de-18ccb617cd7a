package com.xhs.codewiz.client.service;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.CaretModel;
import com.intellij.openapi.editor.CaretState;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorFactory;
import com.intellij.openapi.editor.LogicalPosition;
import com.intellij.openapi.editor.ScrollType;
import com.intellij.openapi.editor.markup.EffectType;
import com.intellij.openapi.editor.markup.HighlighterLayer;
import com.intellij.openapi.editor.markup.HighlighterTargetArea;
import com.intellij.openapi.editor.markup.MarkupModel;
import com.intellij.openapi.editor.markup.RangeHighlighter;
import com.intellij.openapi.editor.markup.TextAttributes;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Computable;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.ui.JBColor;
import com.xhs.codewiz.client.ProviderTypeEnum;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.factory.webview.util.HighBuildVersionReflectUtil;
import com.xhs.codewiz.scheme.content.CreateDecoration;
import com.xhs.codewiz.scheme.content.DeleteDecoration;
import com.xhs.codewiz.scheme.content.CreateActionProvider;
import com.xhs.codewiz.scheme.content.DeleteActionProvider;
import com.xhs.codewiz.scheme.content.ExecuteRevealRange;
import com.xhs.codewiz.scheme.content.ExecuteRevealRange.ExecuteRevealRangeParams;
import com.xhs.codewiz.scheme.content.UpdateContent;
import com.xhs.codewiz.scheme.content.UpdateContent.UpdateContentParams;
import com.xhs.codewiz.scheme.content.UpdateDecoration;
import com.xhs.codewiz.scheme.content.UpdateRanges;
import com.xhs.codewiz.scheme.content.UpdateRanges.UpdateRangesParams;
import com.xhs.codewiz.type.content.Change;
import com.xhs.codewiz.type.content.DecorationRenderOptions;
import com.xhs.codewiz.type.content.DecorationRenderOptionsBase;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.content.RevealType;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.DocumentUtils;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import java.awt.Color;
import java.awt.Font;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.swing.LookAndFeel;
import javax.swing.UIManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-07-16 Description:
 */
public class RcsContentService {
  private static final Logger logger = Logger.getInstance(RcsContentService.class);

  // 装饰器缓存：装饰器ID -> 装饰器配置
  private static final Map<String, DecorationRenderOptions> decorationCache = new ConcurrentHashMap<>();

  // 装饰器高亮缓存：装饰器ID -> RangeHighlighter列表
  private static final Map<String, List<RangeHighlighter>> decorationHighlighters = new ConcurrentHashMap<>();

  public static void contentUpdate(String str, String channel) {
    contentUpdateWithRes(str, channel);
  }

  public static Boolean contentUpdateWithRes(String str, String channel) {
    UpdateContent updateContent = GsonUtil.fromJson(str, UpdateContent.class);
    UpdateContentParams params = updateContent.getParams();
    if (null == params
        || null == params.getChanges()
        || CollectionUtils.isEmpty(params.getChanges().getChanges())) {
      return true;
    }
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    return WriteCommandAction.runWriteCommandAction(project, (Computable<Boolean>)() -> {
      try {
        // 写文件
        Path path = RcsFileService.getPathWithUri(params.getUri());
        VirtualFile file = VirtualFileManager.getInstance().refreshAndFindFileByNioPath(path);
        if (null == file) {
          return false;
        }
        Document document = FileDocumentManager.getInstance().getDocument(file);
        List<Change> changeList = params.getChanges().getChanges();
        for (Change change : changeList) {
          Position start = change.getRange().getStart();
          int startLine = Math.min(start.getLine(), document.getLineCount() - 1);
          startLine = Math.max(startLine, 0);
          int startOffset;
          if (startLine == document.getLineCount() - 1) {
            startOffset = document.getTextLength();
          } else {
            startOffset = DocumentUtils.getOffset(document, startLine, start.getCharacter());
          }
          Position end = change.getRange().getEnd();
          int endLine = Math.min(end.getLine(), document.getLineCount() - 1);
          endLine = Math.max(endLine, 0);
          int endOffset;
          if (endLine == document.getLineCount() - 1) {
            endOffset = document.getTextLength();
          } else {
            endOffset = DocumentUtils.getOffset(document, endLine, end.getCharacter());
          }
          document.replaceString(startOffset, endOffset, change.getText());
        }
        FileDocumentManager.getInstance().saveDocument(document);
        file.refresh(false, false);
        // 马上刷新使IDE识别
        LocalFileSystem.getInstance().refreshAndFindFileByNioFile(path);
        return true;
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(logger, "local contentUpdate err: ", e);
        return false;
      }
    });
  }

  public static void rangesUpdate(String str, String channel) {
    UpdateRanges updateRanges = GsonUtil.fromJson(str, UpdateRanges.class);
    if (null == updateRanges || null == updateRanges.getParams()) {
      return;
    }
    UpdateRangesParams params = updateRanges.getParams();
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);
    WriteCommandAction.runWriteCommandAction(project, () -> {
      Path path = RcsFileService.getPathWithUri(params.getUri());
      VirtualFile file = VirtualFileManager.getInstance().refreshAndFindFileByNioPath(path);
      if (null == file) {
        return;
      }
      fileEditorManager.openFile(file, true);
      Editor editor = FileEditorManager.getInstance(project).getSelectedTextEditor();
      if (null == editor) {
        return;
      }
      if (CollectionUtils.isEmpty(params.getRanges())) {
        //取消选中
        editor.getSelectionModel().removeSelection();
      } else {
        CaretModel caretModel = editor.getCaretModel();
        List<CaretState> caretStates = new ArrayList<>();
        for (Range range : params.getRanges()) {
          LogicalPosition pos1 = new LogicalPosition(range.getStart().getLine(), range.getStart().getCharacter());
          LogicalPosition pos2 = new LogicalPosition(range.getEnd().getLine(), range.getEnd().getCharacter());
          CaretState caretState = new CaretState(pos1, pos1, pos2);
          caretStates.add(caretState);
        }
        // true 表示去重合并相同的光标
        caretModel.setCaretsAndSelections(caretStates, true);
      }
    });
  }

  public static void createActionProvider(String str, String channel) {
    CreateActionProvider createActionProvider = GsonUtil.fromJson(str, CreateActionProvider.class);
    if (null == createActionProvider || null == createActionProvider.getParams()) {
      return;
    }
    List<Object> nowProviderList = RcsWebSocketManager.INSTANCE.getProvider(channel, ProviderTypeEnum.ACTION);
    if (CollectionUtils.isNotEmpty(nowProviderList)) {
      for (Object provider : nowProviderList) {
        CreateActionProvider now = (CreateActionProvider) provider;
        if (StringUtils.equals(createActionProvider.getParams().getProvider().getId(),
            now.getParams().getProvider().getId())) {
          RcsWebSocketManager.INSTANCE.deleteProvider(channel, provider);
        }
      }
    }
    RcsWebSocketManager.INSTANCE.addProvider(channel, createActionProvider);
  }

  public static void deleteActionProvider(String str, String channel) {
    DeleteActionProvider deleteActionProvider = GsonUtil.fromJson(str, DeleteActionProvider.class);
    if (null == deleteActionProvider || null == deleteActionProvider.getParams()) {
      return;
    }
    List<Object> nowProviderList = RcsWebSocketManager.INSTANCE.getProvider(channel, ProviderTypeEnum.ACTION);
    if (CollectionUtils.isNotEmpty(nowProviderList)) {
      for (Object provider : nowProviderList) {
        CreateActionProvider now = (CreateActionProvider) provider;
        if (StringUtils.equals(deleteActionProvider.getParams().getId(),
            now.getParams().getProvider().getId())) {
          RcsWebSocketManager.INSTANCE.deleteProvider(channel, provider);
        }
      }
    }
  }

  public static void revealRange(String str, String channel) {
    ExecuteRevealRange reveal = GsonUtil.fromJson(str, ExecuteRevealRange.class);
    if (null == reveal || null == reveal.getParams()) {
      return;
    }
    ExecuteRevealRangeParams params = reveal.getParams();
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);
    ApplicationManager.getApplication().invokeLater(() -> {
      Path path = RcsFileService.getPathWithUri(params.getUri());
      VirtualFile file = VirtualFileManager.getInstance().refreshAndFindFileByNioPath(path);
      if (null == file) {
        return;
      }
      fileEditorManager.openFile(file, true);
      Editor editor = FileEditorManager.getInstance(project).getSelectedTextEditor();;
      if (editor !=null && null != params.getRange()) {
        Document document = editor.getDocument();
        //计算起止点
        Position start = params.getRange().getStart();
        Position end = params.getRange().getEnd();

        int startOffset, endOffset;
        int startLine = Math.min(start.getLine(), document.getLineCount() - 1);
        startLine = Math.max(startLine, 0);
        if (start.getCharacter() == 0) {
          //0可能代表没有传，尝试获取
          startOffset = document.getLineStartOffset(startLine);
        } else {
          startOffset = editor.logicalPositionToOffset(new LogicalPosition(startLine, start.getCharacter()));
        }
        int endLine = Math.min(end.getLine(), document.getLineCount() - 1);
        endLine = Math.max(endLine, 0);
        if (end.getCharacter() == 0) {
          endOffset = document.getLineEndOffset(endLine);
        } else {
          endOffset = editor.logicalPositionToOffset(new LogicalPosition(endLine, end.getCharacter()));
        }
        startOffset = Math.min(startOffset, document.getTextLength());
        endOffset = Math.min(endOffset, document.getTextLength());
        //设置选中区域
        editor.getSelectionModel().setSelection(startOffset, endOffset);
        //设置光标位置
        editor.getCaretModel().moveToOffset(startOffset);
        //滚动到选中区域
        ScrollType scrollType = null;
        if (RevealType.InCenter == params.getType()) {
          scrollType = ScrollType.CENTER;
        } else if (RevealType.InCenterIfOutsideViewport == params.getType()) {
          scrollType = ScrollType.MAKE_VISIBLE;
        } else if (RevealType.AtTop == params.getType()) {
          scrollType = ScrollType.CENTER_UP;
        } else {
          scrollType = ScrollType.RELATIVE;
        }
        editor.getScrollingModel().scrollToCaret(scrollType);
      }
    });
  }

  /**
   * 创建一个装饰器，将装饰器数据缓存起来
   * 等待update的时候使用
   */
  public static void createDecoration(String str, String channel) {
    CreateDecoration createDecoration = GsonUtil.fromJson(str, CreateDecoration.class);
    CreateDecoration.CreateDecorationParams params = createDecoration.getParams();

    if (null == params || StringUtils.isEmpty(params.getId()) || null == params.getOptions()) {
      logger.info("createDecoration: Invalid parameters");
      return;
    }

    try {
      // 缓存装饰器配置
      decorationCache.put(params.getId(), params.getOptions());
      logger.info("Created decoration with ID: " + params.getId());
    } catch (Exception e) {
      logger.warn("createDecoration error: ", e);
    }
  }

  /**
   * 根据缓存的装饰器id，找到装饰器缓存，根据url找到文件，然后根据List<Range>找到文件起始与结束
   * 使用装饰器修饰这些文件内容，将这些装饰器内容缓存起来
   */
  public static void updateDecoration(String str, String channel) {
    UpdateDecoration updateDecoration = GsonUtil.fromJson(str, UpdateDecoration.class);
    UpdateDecoration.UpdateDecorationParams params = updateDecoration.getParams();

    if (null == params || StringUtils.isEmpty(params.getId()) || StringUtils.isEmpty(params.getUri())) {
      logger.warn("updateDecoration: Invalid parameters");
      return;
    }

    WriteCommandAction.runWriteCommandAction(ApplicationUtil.findCurrentProject(), () -> {
      try {
        // 获取装饰器配置
        DecorationRenderOptions options = decorationCache.get(params.getId());
        if (null == options) {
          logger.warn("updateDecoration: Decoration not found for ID: " + params.getId());
          return;
        }

        // 清除旧的高亮
        clearDecorationHighlighters(params.getId());

        // 获取文件和编辑器
        VirtualFile file = VirtualFileManager.getInstance().findFileByUrl(params.getUri());
        if (null == file) {
          logger.warn( "updateDecoration: File not found: " + params.getUri());
          return;
        }

        Document document = FileDocumentManager.getInstance().getDocument(file);
        if (null == document) {
          logger.warn( "updateDecoration: Document not found for file: " + params.getUri());
          return;
        }

        Editor[] editors = EditorFactory.getInstance().getEditors(document);
        if (editors.length == 0) {
          logger.warn("updateDecoration: No editors found for document");
          return;
        }

        // 应用装饰到所有编辑器
        for (Editor editor : editors) {
          applyDecorationToEditor(editor, params.getId(), options, params.getRanges());
        }

      } catch (Exception e) {
        logger.warn("updateDecoration error: ", e);
      }
    });
  }

  /**
   * 根据装饰器id删除装饰器以及对应的修改文件内容还原。
   */
  public static void deleteDecoration(String str, String channel) {
    DeleteDecoration deleteDecoration = GsonUtil.fromJson(str, DeleteDecoration.class);
    DeleteDecoration.DeleteDecorationParams params = deleteDecoration.getParams();

    if (null == params || StringUtils.isEmpty(params.getId())) {
      logger.warn("deleteDecoration: Invalid parameters");
      return;
    }

    try {
      // 清除高亮
      clearDecorationHighlighters(params.getId());

      // 移除装饰器配置
      decorationCache.remove(params.getId());

      logger.info("Deleted decoration with ID: " + params.getId());
    } catch (Exception e) {
      logger.warn("deleteDecoration error: ", e);
    }
  }

  /**
   * 将装饰应用到编辑器
   */
  private static void applyDecorationToEditor(Editor editor, String decorationId,
                                              DecorationRenderOptions options, List<Range> ranges) {
    if (CollectionUtils.isEmpty(ranges)) {
      return;
    }

    MarkupModel markupModel = editor.getMarkupModel();
    List<RangeHighlighter> highlighters = new ArrayList<>();

    // 创建文本属性
    TextAttributes textAttributes = createTextAttributes(options);

    for (Range range : ranges) {
      try {
        Position start = range.getStart();
        Position end = range.getEnd();

        int startOffset = DocumentUtils.getOffset(editor.getDocument(), start.getLine(), start.getCharacter());
        int endOffset = DocumentUtils.getOffset(editor.getDocument(), end.getLine(), end.getCharacter());

        // 创建高亮
        RangeHighlighter highlighter = markupModel.addRangeHighlighter(
            startOffset,
            endOffset,
            HighlighterLayer.SELECTION - 1, // 比选择层低一级
            textAttributes,
            Boolean.TRUE.equals(options.getIsWholeLine()) ? HighlighterTargetArea.LINES_IN_RANGE : HighlighterTargetArea.EXACT_RANGE
        );

        highlighters.add(highlighter);
      } catch (Exception e) {
        logger.warn("Error applying decoration to range: ", e);
      }
    }

    // 缓存高亮器
    decorationHighlighters.put(decorationId, highlighters);
  }

  /**
   * 创建文本属性
   */
  private static TextAttributes createTextAttributes(DecorationRenderOptions options) {
    TextAttributes attributes = new TextAttributes();

    // 根据当前主题选择相应的配置
    DecorationRenderOptionsBase baseOptions = selectThemeOptions(options);

    // 设置背景色
    if (StringUtils.isNotEmpty(baseOptions.getBackgroundColor())) {
      Color bgColor = parseColor(baseOptions.getBackgroundColor());
      if (bgColor != null) {
        attributes.setBackgroundColor(bgColor);
      }
    }

    // 设置前景色
    if (StringUtils.isNotEmpty(baseOptions.getColor())) {
      Color fgColor = parseColor(baseOptions.getColor());
      if (fgColor != null) {
        attributes.setForegroundColor(fgColor);
      }
    }

    // 设置字体样式
    int fontType = Font.PLAIN;
    if ("italic".equals(baseOptions.getFontStyle())) {
      fontType |= Font.ITALIC;
    }
    if ("bold".equals(baseOptions.getFontWeight()) || "700".equals(baseOptions.getFontWeight())) {
      fontType |= Font.BOLD;
    }
    if (fontType != Font.PLAIN) {
      attributes.setFontType(fontType);
    }

    // 设置下划线/删除线等效果
    if (StringUtils.isNotEmpty(baseOptions.getTextDecoration())) {
      String textDecoration = baseOptions.getTextDecoration().toLowerCase();
      if (textDecoration.contains("underline")) {
        attributes.setEffectType(EffectType.LINE_UNDERSCORE);
        if (StringUtils.isNotEmpty(baseOptions.getColor())) {
          Color effectColor = parseColor(baseOptions.getColor());
          if (effectColor != null) {
            attributes.setEffectColor(effectColor);
          }
        }
      } else if (textDecoration.contains("line-through")) {
        attributes.setEffectType(EffectType.STRIKEOUT);
      }
    }

    // 设置边框（通过outline实现）
    if (StringUtils.isNotEmpty(baseOptions.getOutline()) || StringUtils.isNotEmpty(baseOptions.getBorder())) {
      attributes.setEffectType(EffectType.BOXED);
      if (StringUtils.isNotEmpty(baseOptions.getOutlineColor())) {
        Color outlineColor = parseColor(baseOptions.getOutlineColor());
        if (outlineColor != null) {
          attributes.setEffectColor(outlineColor);
        }
      } else if (StringUtils.isNotEmpty(baseOptions.getBorderColor())) {
        Color borderColor = parseColor(baseOptions.getBorderColor());
        if (borderColor != null) {
          attributes.setEffectColor(borderColor);
        }
      }
    }

    return attributes;
  }

  /**
   * 解析颜色字符串
   */
  private static Color parseColor(String colorStr) {
    if (StringUtils.isEmpty(colorStr)) {
      return null;
    }

    try {
      // 处理 hex 颜色 (#RRGGBB 或 #RGB)
      if (colorStr.startsWith("#")) {
        if (colorStr.length() == 4) {
          // #RGB -> #RRGGBB
          colorStr = "#" + colorStr.charAt(1) + colorStr.charAt(1)
                   + colorStr.charAt(2) + colorStr.charAt(2)
                   + colorStr.charAt(3) + colorStr.charAt(3);
        }
        return Color.decode(colorStr);
      }

      // 处理 rgb(r,g,b) 格式
      if (colorStr.startsWith("rgb(") && colorStr.endsWith(")")) {
        String rgb = colorStr.substring(4, colorStr.length() - 1);
        String[] parts = rgb.split(",");
        if (parts.length == 3) {
          int r = Integer.parseInt(parts[0].trim());
          int g = Integer.parseInt(parts[1].trim());
          int b = Integer.parseInt(parts[2].trim());
          return new Color(r, g, b);
        }
      }

      // 处理 rgba(r,g,b,a) 格式
      if (colorStr.startsWith("rgba(") && colorStr.endsWith(")")) {
        String rgba = colorStr.substring(5, colorStr.length() - 1);
        String[] parts = rgba.split(",");
        if (parts.length == 4) {
          int r = Integer.parseInt(parts[0].trim());
          int g = Integer.parseInt(parts[1].trim());
          int b = Integer.parseInt(parts[2].trim());
          float a = Float.parseFloat(parts[3].trim());
          return new Color(r, g, b, (int)(a * 255));
        }
      }

      // 处理命名颜色
      switch (colorStr.toLowerCase()) {
        case "red": return Color.RED;
        case "green": return Color.GREEN;
        case "blue": return Color.BLUE;
        case "yellow": return Color.YELLOW;
        case "orange": return Color.ORANGE;
        case "pink": return Color.PINK;
        case "cyan": return Color.CYAN;
        case "magenta": return Color.MAGENTA;
        case "black": return Color.BLACK;
        case "white": return Color.WHITE;
        case "gray": case "grey": return Color.GRAY;
        default: return null;
      }
    } catch (Exception e) {
      logger.warn("Failed to parse color: " + colorStr, e);
      return null;
    }
  }

  /**
   * 根据当前主题选择合适的装饰器配置
   */
  private static DecorationRenderOptionsBase selectThemeOptions(DecorationRenderOptions options) {
    boolean isDarkTheme = isDarkTheme();

    if (isDarkTheme) {
      // 如果是暗色主题，优先使用dark配置
      if (options.getDark() != null) {
        logger.info("Using dark theme decoration options");
        return mergeWithBase(options.getDark(), options.getDark());
      }
      logger.info("Dark theme detected, but no dark-specific options provided, using base options");
    } else {
      // 如果是亮色主题，优先使用light配置
      if (options.getLight() != null) {
        logger.info("Using light theme decoration options");
        return mergeWithBase(options.getLight(), options.getLight());
      }
      logger.info("Light theme detected, but no light-specific options provided, using base options");
    }

    // 如果没有特定主题的配置，使用基础配置
    return options.getLight();
  }

  /**
   * 检测当前是否为暗色主题
   */
  private static boolean isDarkTheme() {
    try {
      // 方法1: 使用IntelliJ IDEA的UIUtil检测Darcula主题
      if (HighBuildVersionReflectUtil.isUnderDarcula()) {
        logger.info("Dark theme detected by UIUtil.isUnderDarcula()");
        return true;
      }

      // 方法2: 使用JBColor检测主题亮度
      if (!JBColor.isBright()) {
        logger.info("Dark theme detected by JBColor.isBright()");
        return true;
      }

      // 方法3: 通过Look and Feel名称检测
      LookAndFeel lookAndFeel = UIManager.getLookAndFeel();
      if (lookAndFeel != null) {
        String lafName = lookAndFeel.getName().toLowerCase();
        logger.info("Current Look and Feel: " + lafName);
        if (lafName.contains("dark") || lafName.contains("darcula")) {
          logger.info("Dark theme detected by Look and Feel name");
          return true;
        }
      }

      logger.info("Light theme detected");
      return false;
    } catch (Exception e) {
      logger.warn("Error detecting theme, defaulting to light theme", e);
      return false;
    }
  }

  /**
   * 将主题特定配置与基础配置合并
   */
  private static DecorationRenderOptionsBase mergeWithBase(DecorationRenderOptionsBase base,
                                                           DecorationRenderOptionsBase theme) {
    // 创建一个新的配置对象，基于基础配置
    DecorationRenderOptionsBase merged = new DecorationRenderOptionsBase();

    // 复制基础配置的所有属性
    copyBaseAttributes(merged, base);

    // 用主题特定的配置覆盖非空属性
    if (theme.getBackgroundColor() != null) merged.setBackgroundColor(theme.getBackgroundColor());
    if (theme.getColor() != null) merged.setColor(theme.getColor());
    if (theme.getBorder() != null) merged.setBorder(theme.getBorder());
    if (theme.getBorderColor() != null) merged.setBorderColor(theme.getBorderColor());
    if (theme.getBorderRadius() != null) merged.setBorderRadius(theme.getBorderRadius());
    if (theme.getBorderSpacing() != null) merged.setBorderSpacing(theme.getBorderSpacing());
    if (theme.getBorderStyle() != null) merged.setBorderStyle(theme.getBorderStyle());
    if (theme.getBorderWidth() != null) merged.setBorderWidth(theme.getBorderWidth());
    if (theme.getOutline() != null) merged.setOutline(theme.getOutline());
    if (theme.getOutlineColor() != null) merged.setOutlineColor(theme.getOutlineColor());
    if (theme.getOutlineStyle() != null) merged.setOutlineStyle(theme.getOutlineStyle());
    if (theme.getOutlineWidth() != null) merged.setOutlineWidth(theme.getOutlineWidth());
    if (theme.getFontStyle() != null) merged.setFontStyle(theme.getFontStyle());
    if (theme.getFontWeight() != null) merged.setFontWeight(theme.getFontWeight());
    if (theme.getTextDecoration() != null) merged.setTextDecoration(theme.getTextDecoration());
    if (theme.getCursor() != null) merged.setCursor(theme.getCursor());
    if (theme.getOpacity() != null) merged.setOpacity(theme.getOpacity());
    if (theme.getLetterSpacing() != null) merged.setLetterSpacing(theme.getLetterSpacing());
    if (theme.getGutterIconPath() != null) merged.setGutterIconPath(theme.getGutterIconPath());
    if (theme.getGutterIconSize() != null) merged.setGutterIconSize(theme.getGutterIconSize());
    if (theme.getOverviewRulerColor() != null) merged.setOverviewRulerColor(theme.getOverviewRulerColor());
    if (theme.getBefore() != null) merged.setBefore(theme.getBefore());
    if (theme.getAfter() != null) merged.setAfter(theme.getAfter());

    return merged;
  }

  /**
   * 复制基础属性到目标对象
   */
  private static void copyBaseAttributes(DecorationRenderOptionsBase target, DecorationRenderOptionsBase source) {
    target.setBackgroundColor(source.getBackgroundColor());
    target.setColor(source.getColor());
    target.setBorder(source.getBorder());
    target.setBorderColor(source.getBorderColor());
    target.setBorderRadius(source.getBorderRadius());
    target.setBorderSpacing(source.getBorderSpacing());
    target.setBorderStyle(source.getBorderStyle());
    target.setBorderWidth(source.getBorderWidth());
    target.setOutline(source.getOutline());
    target.setOutlineColor(source.getOutlineColor());
    target.setOutlineStyle(source.getOutlineStyle());
    target.setOutlineWidth(source.getOutlineWidth());
    target.setFontStyle(source.getFontStyle());
    target.setFontWeight(source.getFontWeight());
    target.setTextDecoration(source.getTextDecoration());
    target.setCursor(source.getCursor());
    target.setOpacity(source.getOpacity());
    target.setLetterSpacing(source.getLetterSpacing());
    target.setGutterIconPath(source.getGutterIconPath());
    target.setGutterIconSize(source.getGutterIconSize());
    target.setOverviewRulerColor(source.getOverviewRulerColor());
    target.setBefore(source.getBefore());
    target.setAfter(source.getAfter());
  }

  /**
   * 清除装饰器的所有高亮
   */
  private static void clearDecorationHighlighters(String decorationId) {
    List<RangeHighlighter> highlighters = decorationHighlighters.remove(decorationId);
    if (CollectionUtils.isNotEmpty(highlighters)) {
      for (RangeHighlighter highlighter : highlighters) {
        try {
          highlighter.dispose();
        } catch (Exception e) {
          logger.warn("Error disposing highlighter: ", e);
        }
      }
    }
  }
}

package com.xhs.codewiz.listener;

import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;

import org.apache.commons.lang3.StringUtils;
import org.cef.browser.CefBrowser;
import org.cef.browser.CefFrame;
import org.cef.callback.CefQueryCallback;
import org.cef.handler.CefMessageRouterHandlerAdapter;

import com.google.gson.JsonObject;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.factory.webview.WebviewBuilder;
import com.xhs.codewiz.service.WebviewService;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.LoggerUtil;


public class ListenerJsRouteHandle extends CefMessageRouterHandlerAdapter {
    static Logger log = Logger.getInstance(ListenerJsRouteHandle.class);
    private final Project project;
    private final WebviewBuilder builder;

    public ListenerJsRouteHandle(Project project, WebviewBuilder webviewBuilder) {
        this.project = project;
        this.builder = webviewBuilder;
    }

    @Override
    public boolean onQuery(CefBrowser browser, CefFrame frame, long queryId, String request, boolean persistent,
                           CefQueryCallback callback) {
        if (project.isDisposed()) {
            return false;
        }
        if (RcsWebSocketManager.INSTANCE.checkWebsocketAgent(project, false)) {
            LoggerUtil.INSTANCE.logDebug(log, "onQuery request: " + request);
            // 消息处理
            WebviewService.sendCreateWebviewMessage(
                    project,
                    request,
                    this.builder.getWebviewProvider(),
                    this.builder.getRemoteChannel());
        } else {
            try {
                RcsWebSocketManager.INSTANCE.connectCosyServer(project);
            } catch (IOException e) {
                LoggerUtil.INSTANCE.logWarn(log, "connectCosyServer err", e);
            }
        }
        return true;
    }

    @Override
    public void onQueryCanceled(CefBrowser browser, CefFrame frame, long queryId) {
        log.info("🔄 Query canceled - Query ID: " + queryId);
    }

    /**
     * 2024 及更早，CefMessageRouter 可为每个 JBCefClient/JBCefBrowser 独立挂 handler，
     * 且通信消息只在那个 handler/Router 之间流通。
     * 2025 强制 remoteClient，所有 messageRouter/handler 都是全局的，所有消息都到这个全局列表，
     * 这会导致消息不隔离，出现跨 handler 的消息。
     *
     * @return
     */
    private Project getRealRouteProject(CefBrowser browser) {
        if (null == browser || StringUtils.isBlank(browser.getURL())) {
            return project;
        }

        String url = browser.getURL();
        log.info("Browser URL: " + url);

        // 检查URL是否包含查询参数
        if (!url.contains("?")) {
            log.info("No query parameters in URL, using default project");
            return project;
        }

        String[] urlParts = url.split("\\?");
        if (urlParts.length < 2) {
            log.info("Invalid URL format, using default project");
            return project;
        }

        String paramStr = urlParts[1];
        String[] params = paramStr.split("&");
        String projectUriParam = Arrays.stream(params)
                .filter(str -> str.startsWith("projectUri")).findFirst().orElse(null);
        // 不为空，那么使用初始化时录入url的参数来定位project
        if (StringUtils.isNotBlank(projectUriParam)) {
            String projectUri = projectUriParam.substring(projectUriParam.indexOf("=") + 1);
            projectUri = new String(Base64.getDecoder().decode(projectUri));
            Project matchPro = ApplicationUtil.getProjectByPath(projectUri);
            return null == matchPro ? project : matchPro;
        }
        return project;
    }

    /**
     * 处理Ui通知插件的操作行为
     *
     * @param jsonObject
     */
    public void handleCommand(JsonObject jsonObject, Project project) {

    }
}

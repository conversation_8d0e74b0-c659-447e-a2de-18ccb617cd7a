package com.xhs.codewiz.lang.agent;

import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.execution.configurations.PathEnvironmentVariableUtil;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.util.SystemInfoRt;
import com.intellij.openapi.util.io.FileUtil;
import com.intellij.util.system.CpuArch;
import com.xhs.codewiz.lang.LspServiceForTylm;
import com.xhs.codewiz.lang.entity.StartConfig;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes.XhsUserInfo;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.PluginUtil;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CodeWizAgentCommandLine {
    public static final String isScriptLaunch = System.getenv("isScriptLaunch");
    static final int MAX_RESTART_ATTEMPTS = 10;
    private static final Logger LOG = Logger.getInstance(CodeWizAgentCommandLine.class);
    private static final int RESTARTS_UNTIL_FALLBACK = 3;
    public static String executableFile = "codewiz-ls";

    static {
        if (isScriptLaunch != null && isScriptLaunch.equals("1")) {
            executableFile = "plugin_starter.sh";
        }
    }

    //public static final String executableFile = "plugin_starter.sh";
    public CodeWizAgentCommandLine() {
    }

    static @NotNull GeneralCommandLine createAgentCommandLine(boolean enableDebugMode, int restartCount) {
        GeneralCommandLine cmdline = createAgentBinaryCommandline();
        cmdline.setCharset(StandardCharsets.UTF_8);
        if (enableDebugMode) {
            cmdline.withEnvironment("GITHUB_COPILOT_DEBUG", "true");
            cmdline.withEnvironment("COPILOT_AGENT_VERBOSE", "true");
            //DebugServer.enableDebugServer(cmdline);
        }

        if (restartCount >= 1) {
            cmdline.withEnvironment("NODE_OPTIONS", "");
        }

        if (restartCount >= 2) {
            cmdline.withEnvironment("NODE_ICU_DATA", "");
        }

        return cmdline;
    }

    private static boolean shouldUseAgentBinary() {
        String env = System.getenv("COPILOT_BINARY");
        if (env != null && !env.isEmpty()) {
            return "true".equals(env);
        } else {
            return findAgentBinary() != null;
        }
    }

    public static @NotNull GeneralCommandLine createAgentJavaScriptCommandline() {
        Path nodePath = getNodeExecutablePath();
        Path agentDistPath = CodeWizAgentUtil.getAgentDirectoryPath();
        if (nodePath != null && agentDistPath != null) {
            Path agentFilePath = agentDistPath.resolve("agent-debug.js");
            if (Files.exists(agentFilePath)) {
                LoggerUtil.INSTANCE.logDebug(LOG, "Using DEBUG version of agent.js");
            } else {
                agentFilePath = agentDistPath.resolve("agent.js");
                if (!Files.exists(agentFilePath)) {
                    throw new IllegalStateException("Unable to locate agent.js file");
                }
            }

            return new GeneralCommandLine(nodePath.toString(), agentFilePath.toString());
        } else {
            throw new IllegalStateException("Unable to locate node or agent dist dir");
        }
    }

    public static @NotNull GeneralCommandLine createAgentBinaryCommandline() {
        Path executablePath = findAgentBinary();
        if (executablePath == null) {
            throw new IllegalStateException("Unable to locate agent binary");
        } else {
            if (SystemInfoRt.isUnix && !Files.isExecutable(executablePath)) {
                try {
                    FileUtil.setExecutable(executablePath.toFile());
                } catch (IOException var2) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "Failed to make agent binary executable", var2);
                }
            }

            if (isScriptLaunch != null && isScriptLaunch.equals("1")) {

                String startShellPath = executablePath.toString();

                String[] strs = startShellPath.split(executableFile);

                GeneralCommandLine commandLine = new GeneralCommandLine();
                if (SystemInfoRt.isWindows) {
                    commandLine.setExePath("cmd.exe");
                    commandLine.addParameter("/c"); //代表执行完毕后执行close
                    commandLine.addParameter(startShellPath);
                    commandLine.addParameter(strs[0]);
                } else {
                    commandLine.setExePath("sh");
                    commandLine.addParameter(startShellPath);
                    commandLine.addParameter(strs[0]);
                }
                return commandLine;
            } else {
                GeneralCommandLine commandLine = new GeneralCommandLine(executablePath.toString());
                commandLine.setExePath("/bin/bash");
                commandLine.addParameter("-c");
                String binaryPath = executablePath.toString().replace(" ", "\\ ");


                // 创建启动配置
                StartConfig config = StartConfig.createDefault();
                String metaJson = config.toJson();

                StringBuilder paramBuilder = new StringBuilder(binaryPath);
                //只有本地调试时，开启这些辅助功能
                if (PluginUtil.isDevEnv()) {
                    paramBuilder.append(" -DIS_DEV_ENV=1");
                    paramBuilder.append(" --debug");
                    /*String jfrPath = binaryPath.split(executableFile)[0];
                   paramBuilder.append(" -Xmx256m");
                   paramBuilder
                   //用于输出gc信息
                   .append(" -XX:+PrintGC -XX:+VerboseGC")
                   //允许jmc转出jfr文件，用于分析
                   .append(" -XX:StartFlightRecording=filename=").append(jfrPath).append("codewiz.jfr")
                   //允许jvm内存溢出时转出hprof文件，同时允许主动触发生成dump文件(比如VisualVm)
                   .append(" -XX:+ExitOnOutOfMemoryError -XX:HeapDumpPath=").append(jfrPath).append("codewiz.hprof");*/
                }
                //登陆过的话，需要传递用户信息
                XhsUserInfo userInfo = LspServiceForTylm.getUserLoginInfo();
                if (null != userInfo) {
                    paramBuilder.append(" -userEmail ").append(userInfo.getEmail());
                }
                paramBuilder.append(" -meta '").append(metaJson).append("'");
                commandLine.addParameter(paramBuilder.toString());

                return commandLine;
            }

        }
    }

    public static @Nullable Path findAgentBinary() {
        if (!SystemInfoRt.isMac && !CpuArch.isIntel64()) {
            String arch = System.getProperty("os.arch");
            LoggerUtil.INSTANCE.logDebug(LOG, "Agent binary is unsupported, os.arch: " + arch);
            //TelemetryService.getInstance().track("editor.intellij.agentUnsupported", Map.of("os.arch", arch));
            return null;
        } else {
            Path binDir = CodeWizAgentUtil.getAgentBinaryDirectoryPath();
            if (binDir == null) {
                return null;
            } else {
                Path executable = null;
                if (SystemInfoRt.isLinux) {
                    executable = binDir.resolve(executableFile + "-linux");
                } else if (SystemInfoRt.isWindows) {
                    executable = binDir.resolve(executableFile + ".exe");
                } else if (SystemInfoRt.isMac) {
                    if (CpuArch.isArm64()) {
                        //executable = binDir.resolve("copilot-agent-macos-arm64");
                        executable = binDir.resolve(executableFile);
                    } else {
                        //executable = binDir.resolve("copilot-agent-macos");
                        executable = binDir.resolve(executableFile);
                    }
                }

                return executable != null && Files.exists(executable) ? executable : null;
            }
        }
    }

    public static @Nullable Path getNodeExecutablePath() {
        File path = PathEnvironmentVariableUtil.findExecutableInPathOnAnyOS("node");
        if (path == null) {
            LoggerUtil.INSTANCE.logDebug(LOG, "node executable not found in $PATH");
            return null;
        } else {
            Path nioPath = path.toPath();
            if (SystemInfoRt.isUnix && !Files.isExecutable(nioPath)) {
                LoggerUtil.INSTANCE.logWarn(LOG, "node executable has no execute permissions: " + nioPath);
                return null;
            } else {
                LoggerUtil.INSTANCE.logDebug(LOG, "Found node executable at " + nioPath);
                return nioPath;
            }
        }
    }
}


package com.xhs.codewiz.scheme.content;

import java.util.*;
/**
 * 注册 Comment Provider
 */
public class CreateCommentProvider {
    private String schemaProtocol = "content.create.commentprovider";
    private CreateCommentProviderParams params;

    public CreateCommentProviderParams getParams() {
        return params;
    }
    public void setParams(CreateCommentProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateCommentProviderParams {
        /** Comment Provider ID */
        private String id;
        /** Comment Provider 显示的标，用来描述该 provider 下对应的所有评论类型 */
        private String label;
    
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public String getLabel() {
            return label;
        }
        public void setLabel(String label) {
            this.label = label;
        }
    }
}

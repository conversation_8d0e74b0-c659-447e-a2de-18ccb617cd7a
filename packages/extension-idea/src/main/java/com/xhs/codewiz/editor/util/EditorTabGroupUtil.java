package com.xhs.codewiz.editor.util;

import com.google.common.collect.Lists;
import com.intellij.diff.editor.ChainDiffVirtualFile;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.LogicalPosition;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.ex.FileEditorManagerEx;
import com.intellij.openapi.fileEditor.impl.EditorWindow;
import com.intellij.openapi.fileEditor.impl.FileEditorManagerImpl;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.editor.request.ActiveFileInfo;
import com.xhs.codewiz.scheme.content.UpdateRanges;
import com.xhs.codewiz.scheme.content.UpdateRanges.UpdateRangesParams;
import com.xhs.codewiz.scheme.file.ExecuteOpen;
import com.xhs.codewiz.scheme.file.ExecuteOpen.ExecuteOpenParams;
import com.xhs.codewiz.scheme.workspace.CreateTabGroup;
import com.xhs.codewiz.scheme.workspace.CreateTabGroup.CreateTabGroupParams;
import com.xhs.codewiz.scheme.workspace.DeleteTabGroup;
import com.xhs.codewiz.scheme.workspace.UpdateTabGroup;
import com.xhs.codewiz.scheme.workspace.UpdateTabGroup.UpdateTabGroupParams;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.file.FileOpenOptions;
import com.xhs.codewiz.type.workspace.Tab;
import com.xhs.codewiz.type.workspace.TabGroup;
import com.xhs.codewiz.type.workspace.TabType.TabTypeText;
import com.xhs.codewiz.type.workspace.TabType.TabTypeTextDiff;
import com.xhs.codewiz.type.workspace.ViewColumn;
import com.xhs.codewiz.utils.FileUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * Author: liukunpeng Date: 2025-07-24 Description:
 */
public class EditorTabGroupUtil {
  private static final Logger log = Logger.getInstance(EditorTabGroupUtil.class);
  //这个主要是为了防止初始化与文件监听事件同时add
  private static Map<Project, List<EditorWindow>> registerTabWindow = new ConcurrentHashMap<>();
  //这个用来标记处于删除前状态的文件，在更新tabGroup时需要跳过该文件
  private static final Key<Boolean> NEED_DELETE_FILE = new Key<>("beforeDeleteFile");
  public static final Key<Pair<String, String>> CHAIN_DIFF_FILE_PATH = new Key<>("chainDiffFilePath");

  /**
   * 项目初始化建联时同步tabGroup
   * @param project
   */
  public static void initTabGroup(Project project) {
    try {
      FileEditorManager editorManager = FileEditorManager.getInstance(project);
      List<EditorWindow> activeWindowList = null;
      if (editorManager instanceof FileEditorManagerEx ex) {
        activeWindowList = Arrays.asList(ex.getWindows());
      } else if (editorManager instanceof FileEditorManagerImpl impl) {
        activeWindowList =  Arrays.asList(impl.getWindows());
      }
      if (CollectionUtils.isEmpty(activeWindowList)) {
        return;
      }
      if (registerTabWindow.containsKey(project)
          && CollectionUtils.isNotEmpty(registerTabWindow.get(project))) {
        //初始化只会在启动阶段触发一次，重启时远端是新的实例，需要清理本地数据
        registerTabWindow.remove(project);
      }
      for (int i = 0; i < activeWindowList.size(); i++) {
        try {
          EditorWindow window = activeWindowList.get(i);
          CreateTabGroup createTabGroup = new CreateTabGroup();
          CreateTabGroup.CreateTabGroupParams params = new CreateTabGroup.CreateTabGroupParams();
          TabGroup tabGroup =  buildTaGroup(window, !(i == 0));
          params.setTabGroup(tabGroup);
          params.setTabGroupIndex(i);
          createTabGroup.setParams(params);
          RcsWebSocketManager.INSTANCE.sendNotification(createTabGroup, project);
          initFileAfterInitTabGroup(editorManager, window, project);
        } catch (Exception e) {
          LoggerUtil.INSTANCE.logWarn(log, "initTabGroup foreach fail", e);
        }
      }
      registerTabWindow.put(project, new ArrayList<>(activeWindowList));
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(log, "initTabGroup fail", e);
    }
  }

  /**
   * 项目关闭销毁时移除关联关系
   * @param project
   */
  public static void destroyTabGroup(Project project) {
    registerTabWindow.remove(project);
  }

  /**
   * 运行过程中文件打开事件触发同步tab
   *  1.当前打开文件为第一个文件，需要执行新增tabGroup操作
   *  2.非第一个文件，进行update
   * @param editorWindows
   * @param project
   * @param virtualFile
   */
  public static void addTab(List<EditorWindow> editorWindows, Project project, VirtualFile virtualFile) {
    ApplicationManager.getApplication().invokeLater(() -> {
      try {
        if (CollectionUtils.isEmpty(editorWindows)) {
          return;
        }
        EditorWindow fileWindow = editorWindows.stream()
            .filter(window -> window.getFileList().contains(virtualFile))
            .findFirst().orElse(null);
        if (null == fileWindow) {
          return;
        }
        if (!registerTabWindow.containsKey(project) || !registerTabWindow.get(project).contains(fileWindow)) {
          //新增tabGroup的话，就不需要额外去上报tab了
          addTabGroup(editorWindows, project, fileWindow);
        } else {
          updateTabGroup(editorWindows, project, fileWindow);
        }
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(log, "addTab fail", e);
      }
    });
  }

  /**
   * 运行过程中文件关闭事件触发同步tab
   *  1.当前打开文件为第一个文件，需要执行删除tabGroup操作
   *  2.非第一个文件，进行update
   * @param editorWindows
   * @param project
   * @param virtualFile
   */
  public static void closeTab(List<EditorWindow> editorWindows, Project project, VirtualFile virtualFile) {
    //预关闭必须invokeAndWait，不然invokeLater会交给下一个UI事件处理，那时候文件已经关闭结束了
    ApplicationManager.getApplication().invokeAndWait(() -> {
      try {
        if (CollectionUtils.isEmpty(editorWindows)) {
          return;
        }
        EditorWindow fileWindow = editorWindows.stream()
            .filter(window -> window.getFileList().contains(virtualFile))
            .findFirst().orElse(null);
        if (null == fileWindow) {
          return;
        }
        if (CollectionUtils.isEmpty(fileWindow.getFileList())
            || (fileWindow.getFileList().size() == 1 && fileWindow.getFileList().contains(virtualFile))) {
          deleteTabGroup(editorWindows, project, fileWindow);
        } else {
          virtualFile.putUserData(NEED_DELETE_FILE, true);
          updateTabGroup(editorWindows, project, fileWindow);
        }
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(log, "deleteTab fail", e);
      }
    });
  }

  private static void deleteTabGroup(List<EditorWindow> editorWindows, Project project, EditorWindow orderWindow) {
    if (CollectionUtils.isEmpty(editorWindows)) {
      return;
    }
    DeleteTabGroup deleteTabGroup = new DeleteTabGroup();
    deleteTabGroup.setParams(editorWindows.indexOf(orderWindow));
    RcsWebSocketManager.INSTANCE.sendNotification(deleteTabGroup, project);
    if (registerTabWindow.containsKey(project)) {
      registerTabWindow.get(project).remove(orderWindow);
    }
  }

  public static void updateTabGroup(List<EditorWindow> editorWindows, Project project, EditorWindow orderWindow) {
    if (CollectionUtils.isEmpty(editorWindows)) {
      return;
    }
    UpdateTabGroup update = new UpdateTabGroup();
    UpdateTabGroupParams params = new UpdateTabGroupParams();
    params.setTabGroupIndex(editorWindows.indexOf(orderWindow));
    params.setTabGroup(buildTaGroup(orderWindow, !(0 == editorWindows.indexOf(orderWindow))));
    update.setParams(params);
    RcsWebSocketManager.INSTANCE.sendNotification(update, project);
  }

  /**
   * 运行过程中打开文件触发的新增tabGroup
   * @param
   * @param project
   */
  private static void addTabGroup(List<EditorWindow> editorWindows, Project project, EditorWindow orderWindow) {
    if (CollectionUtils.isEmpty(editorWindows)) {
      return;
    }
    if (registerTabWindow.containsKey(project)
        && registerTabWindow.get(project).contains(orderWindow)) {
      return;
    }
    int index = editorWindows.indexOf(orderWindow);
    CreateTabGroup createTabGroup = new CreateTabGroup();
    CreateTabGroupParams params = new CreateTabGroupParams();
    params.setTabGroup(buildTaGroup(orderWindow, !(index == 0)));
    params.setTabGroupIndex(index);
    createTabGroup.setParams(params);

    RcsWebSocketManager.INSTANCE.sendNotification(createTabGroup, project);

    if (!registerTabWindow.containsKey(project)) {
      registerTabWindow.put(project, Lists.newArrayList(orderWindow));
    } else {
      registerTabWindow.get(project).add(orderWindow);
    }
  }
  private static TabGroup buildTaGroup(EditorWindow window, boolean beSide) {
    // 构建TabGroup对象
    TabGroup tabGroup = new TabGroup();
    tabGroup.setIsActive(window.isShowing());

    AtomicReference<List<VirtualFile>> ref = new AtomicReference<>();
    ApplicationManager.getApplication().invokeAndWait(() -> {
      ref.set(window.getFileList());
    });
    FileDocumentManager documentManager = FileDocumentManager.getInstance();
    List<Tab> tabList = new ArrayList<>();
    for (VirtualFile file : ref.get()) {
      Boolean isTempFile = file.getUserData(NEED_DELETE_FILE);
      if (null != isTempFile && isTempFile) {
        file.putUserData(NEED_DELETE_FILE, false);
        continue;
      }
      tabList.add(buildTab(file, window, documentManager));
    }
    tabGroup.setTabs(tabList);

    // 设置ViewColumn
    ViewColumn viewColumn = new ViewColumn();
    viewColumn.setIsBeside(beSide);
    tabGroup.setViewColumn(viewColumn);

    return tabGroup;
  }
  private static Tab buildTab(VirtualFile file, EditorWindow window, FileDocumentManager documentManager) {
    Tab tab = new Tab();
    // 设置基本属性
    tab.setLabel(file.getName());
    tab.setIsActive(file.equals(window.getSelectedFile()));
    tab.setIsDirty(documentManager.isFileModified(file));
    tab.setIsPinned(false); // IntelliJ IDEA没有pin概念，默认为false
    tab.setIsPreview(false); // 简化处理，默认为false

    if (file instanceof ChainDiffVirtualFile) {
      try {
        Pair<String, String> pair = file.getUserData(CHAIN_DIFF_FILE_PATH);
        TabTypeTextDiff diff = new TabTypeTextDiff();
        diff.setOriginal(pair.getLeft());
        diff.setModified(pair.getRight());
        tab.setType(diff);
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(log, "buildTab fail: ", e);
        // 降级为常规
        TabTypeText tabType = new TabTypeText();
        tabType.setUri(file.getUrl());
        tab.setType(tabType);
      }
    } else {
      // 设置TabType
      TabTypeText tabType = new TabTypeText();
      tabType.setUri(file.getUrl());
      tab.setType(tabType);
    }

    return tab;
  }

  /**
   * 在initTabGroup之后，初始化文件
   * @param editorWindow
   * @param project
   */
  private static void initFileAfterInitTabGroup(FileEditorManager editorManager, EditorWindow editorWindow, Project project) {
    AtomicReference<List<VirtualFile>> ref = new AtomicReference<>();
    ApplicationManager.getApplication().invokeAndWait(() -> {
      ref.set(editorWindow.getFileList());
    });
    List<VirtualFile> files = ref.get();
    if (CollectionUtils.isEmpty(files)) {
      return;
    }
    //分屏场景下，也只有一个文件窗口编辑器存在焦点，也只有这一个需要设置选区
    Editor editor = editorManager.getSelectedTextEditor();
    VirtualFile currentFile = null;
    if (editor != null) {
      currentFile = editor.getVirtualFile();
    }
    for (VirtualFile file : files) {
      //LSPManager.getInstance().notifyDidOpen(editorWindow.getProject(), file);
      //防止init后多余触发updateTabGroup，不走notifyDidOpen的api
      try {
        ExecuteOpen excuteOpen = new ExecuteOpen();
        ExecuteOpenParams params = new ExecuteOpenParams();
        params.setContent(FileUtil.buildContent(project, file));
        params.setUri(file.getUrl());
        FileOpenOptions options = new FileOpenOptions();
        options.setDidShow(true);
        if (file.equals(currentFile)) {
          options.setDidFocus(true);
        }
        params.setOptions(options);
        excuteOpen.setParams(params);
        RcsWebSocketManager.INSTANCE.sendNotification(excuteOpen, project);
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(log, "initFileAfterInitTabGroup fail", e);
      }
    }
    if (null != editor) {
      updateFocusFileRange(currentFile, editor);
    }
  }
  private static void updateFocusFileRange(VirtualFile file, Editor editor) {
    if (editor != null && file != null && !file.getFileType().isBinary()) {
      UpdateRanges ranges = new UpdateRanges();
      UpdateRangesParams params = new UpdateRangesParams();
      List<Range> rangeList = new ArrayList<>();
      ActiveFileInfo activeFileInfo = ActiveFileInfo.getFromEditor(editor);
      params.setUri(file.getUrl());
      Range range = new Range();
      LogicalPosition caretPosition = editor.getCaretModel().getLogicalPosition();
      Position backupPosition = new Position();
      backupPosition.setLine(caretPosition.line);
      backupPosition.setCharacter(caretPosition.column);
      if (null != activeFileInfo.getStart()) {
        range.setStart(activeFileInfo.getStart());
      } else {
        range.setStart(backupPosition);
      }
      if (null != activeFileInfo.getEnd()) {
        range.setEnd(activeFileInfo.getEnd());
      } else {
        range.setEnd(backupPosition);
      }
      rangeList.add(range);
      params.setRanges(rangeList);
      ranges.setParams(params);
      RcsWebSocketManager.INSTANCE.sendNotification(ranges, editor.getProject());
    }
  }
}

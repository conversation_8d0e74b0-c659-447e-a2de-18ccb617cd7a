package com.xhs.codewiz.update;

import static java.util.concurrent.TimeUnit.MINUTES;

import com.intellij.ide.plugins.IdeaPluginDescriptor;
import com.intellij.ide.plugins.PluginManagerCore;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.extensions.PluginId;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.StartupActivity;
import com.intellij.util.concurrency.AppExecutorUtil;
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread;
import com.xhs.codewiz.setting.CodeWizApplicationSettings;
import com.xhs.codewiz.utils.BundleUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * 启动一个线程轮询市场Code Wiz插件版本
 * 如果版本有diff（不关注版本先后）
 * 下载解压替换
 * 已被【AutoUpdateActivityFromRemote】替代
 */
@Deprecated
public class AutoUpdateActivity implements StartupActivity, StartupActivity.DumbAware {
    private static final Logger LOG = Logger.getInstance(AutoUpdateActivity.class);
    private volatile String lastUpdateVersion = "0";
    private final AtomicBoolean HAS_INIT = new AtomicBoolean(false);

    @Override
    @RequiresBackgroundThread
    public void runActivity(@NotNull Project project) {
        doUpdateCheck();
    }
    private void doUpdateCheck() {
        //仅允许触发一次
        if (HAS_INIT.getAndSet(true)) {
            return;
        }
        try {
            AppExecutorUtil.getAppScheduledExecutorService().scheduleWithFixedDelay(() -> {
                try {
                    //未开启自动强制升级时直接返回
                    if (!CodeWizApplicationSettings.settings().autoUpdate.checkForUpdate) {
                        return;
                    }
                    //获取远端市场插件信息
                    Plugins.Plugin marketPlugin = MarketRequest.fetchPluginMeta();
                    //没有市场信息，直接返回
                    if (marketPlugin == null) {
                        return;
                    }
                    /*if (!marketVersionEnabled(marketPlugin.getVersion())) {
                        return;
                    }*/
                    //获取本地已安装插件信息
                    IdeaPluginDescriptor localPlugin = PluginManagerCore.getPlugin(PluginId.getId(
                        BundleUtil.get("codewiz.plugin.id")));
                    String thisLastUpdateVersion = getLastUpdateVersion(localPlugin.getVersion());
                    //当前市场版本小于等于最后一次更新版本时，不需要从市场同步下载
                    if (!isNeedUpdate(thisLastUpdateVersion, marketPlugin.getVersion())) {
                        //如果最后一次更新版本，与安装版本不一致，说明用户还没有重启实施覆盖安装
                        if (!StringUtils.equals(lastUpdateVersion, localPlugin.getVersion())
                            && UpdateNotificationManager.doNotAsk == 0 ) {
                            UpdateNotificationManager.notification(localPlugin.getVersion(), lastUpdateVersion);
                        }
                        return;
                    }
                    if (MarketRequest.isDownloading.get()) {
                        return;
                    }
                    //开始下载
                    boolean res = MarketRequest.downloadPlugin(marketPlugin.getUrl());
                    if (res) {
                        //ExtractLanguageServerFromJar.extractLanguageServer(unzipDirectory.getName());
                        UpdateNotificationManager.notification(localPlugin.getVersion(), marketPlugin.getVersion());
                        //本地下载成功后，最新更新版本提升至此次市场版本
                        lastUpdateVersion = marketPlugin.getVersion();
                    }
                } catch (IOException e) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "plugin Code Wiz hot-patch chain exec failed", e);
                }
            }, 0, 20L, MINUTES);
        }
        catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed update", e);
        }
    }
    /*private boolean marketVersionEnabled(String marketVersion) {
        List<PluginVersionMsg> versionMsgList = CopilotApplicationSettings.settings().versionHistoryList;
        //列表为空，代表尚未维护，无需考虑是否禁用
        if (CollectionUtils.isEmpty(versionMsgList)) {
            return true;
        }
        for (PluginVersionMsg versionMsg : versionMsgList) {
            if (StringUtils.equals(marketVersion, versionMsg.getVersion())) {
                return versionMsg.isEnabled();
            }
        }
        //非维护版本，不允许升级
        return false;
    }*/
    private boolean isNeedUpdate(String localVersion, String marketVersion) {
        return StringUtils.compare(localVersion, marketVersion)  < 0;
    }

    /**
     * 获取本地上次下载升级后的版本号
     * 未下载升级过时返回当前安装版本
     * @return
     */
    private String getLastUpdateVersion(String localVersion) {
        if (StringUtils.compare(lastUpdateVersion, localVersion)  < 0) {
            lastUpdateVersion = localVersion;
        }
        return lastUpdateVersion;
    }
}

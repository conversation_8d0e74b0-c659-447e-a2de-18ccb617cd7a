package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.CommentCollection;
/**
 * 更新 Comments 合集
 */
public class UpdateCommentCollection {
    private String schemaProtocol = "content.update.commentcollection";
    private UpdateCommentCollectionParams params;

    public UpdateCommentCollectionParams getParams() {
        return params;
    }
    public void setParams(UpdateCommentCollectionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateCommentCollectionParams {
        /** Comment Provider ID，表示该集合属于哪个 Comment Provider */
        private String provider;
        private CommentCollection collection;
        /** 要更新的 Comments 合集 ID */
        private String collectionId;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public CommentCollection getCollection() {
            return collection;
        }
        public void setCollection(CommentCollection collection) {
            this.collection = collection;
        }
        public String getCollectionId() {
            return collectionId;
        }
        public void setCollectionId(String collectionId) {
            this.collectionId = collectionId;
        }
    }
}

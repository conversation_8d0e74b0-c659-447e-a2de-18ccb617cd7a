package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 删除 Session
 */
public class DeleteSession {
    private String schemaProtocol = "platform.delete.session";
    private DeleteSessionParams params;

    public DeleteSessionParams getParams() {
        return params;
    }
    public void setParams(DeleteSessionParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class DeleteSessionParams {
        /** Session Provider ID */
        private String provider;
        /** 要删除的 Session ID */
        private String sessionId;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public String getSessionId() {
            return sessionId;
        }
        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }
    }
}

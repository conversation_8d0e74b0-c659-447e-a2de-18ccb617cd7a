package com.xhs.codewiz.enums;

import com.xhs.codewiz.client.service.router.RequestFunInterface;
import com.xhs.codewiz.update.MarketRequest;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-08-25
 * Description: rcs调用execute.command处理枚举类
 */
public enum ExecuteCommandEnum {
    UPDATE_READY("rcs.update.ready", "RCS更新插件后同步解压路径", MarketRequest::updateDirectoryFromRcs),
    UPDATE("rcs.update", "用户执行更新逻辑", MarketRequest::doRcsRestartForUpdate);

    private final String command;

    private final String desc;

    private final RequestFunInterface requestFun;

    ExecuteCommandEnum(String command, String desc, RequestFunInterface requestFun) {
        this.command = command;
        this.desc = desc;
        this.requestFun = requestFun;
    }
    public static ExecuteCommandEnum getByCommand(String command) {
        for (ExecuteCommandEnum executeCommandEnum : values()) {
            if (StringUtils.equals(executeCommandEnum.command, command)) {
                return executeCommandEnum;
            }
        }
        return null;
    }
    public Object executeCommand(String param, String channel) {
        return requestFun.execute(param, channel);
    }
}

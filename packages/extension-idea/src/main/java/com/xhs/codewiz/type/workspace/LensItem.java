package com.xhs.codewiz.type.workspace;

import java.util.*;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.global.Command;

/**
 * Lens 项
 */
public class LensItem {
    /** Lens 项的范围 */
    private Range range;

    /** 在 Lens 上点击时执行的命令 */
    private Command command;

    public Range getRange() {
        return range;
    }

    public void setRange(Range range) {
        this.range = range;
    }

    public Command getCommand() {
        return command;
    }

    public void setCommand(Command command) {
        this.command = command;
    }

}

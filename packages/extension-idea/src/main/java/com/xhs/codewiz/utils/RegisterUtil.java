package com.xhs.codewiz.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.swing.Icon;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.client.model.RemoteServiceMeta;
import com.xhs.codewiz.client.model.ServiceRegisterResponse;
import com.xhs.codewiz.client.model.ServiceRegisterResponse.RegisterData;
import com.xhs.codewiz.factory.webview.ActivityBar;

/**
 * 注册信息缓存工具类
 * 用于缓存远程通道注册信息，并提供通过路径获取指定组件信息的功能
 * <p>
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
public class RegisterUtil {

    private static final RegisterUtil INSTANCE = new RegisterUtil();
    private static final Logger logger = Logger.getInstance(RegisterUtil.class);

    /**
     * 缓存合并的注册信息
     */
    private JsonObject mergedRequirement;

    /**
     * 缓存依赖信息
     */
    private List<RemoteServiceMeta> dependences;

    private RegisterUtil() {
    }

    public static RegisterUtil getInstance() {
        return INSTANCE;
    }

    /**
     * 缓存注册信息
     */
    public void cacheRegisterInfo(RegisterData data) {
        try {
            if (data == null) {
                return;
            }

            LoggerUtil.INSTANCE.logInfo(
                    logger,
                    "cacheRegisterInfo: " + GsonUtil.toJson(data)
            );

            // 缓存合并的requirement
            this.mergedRequirement = data.getRequirment();
            // 缓存依赖信息
            this.dependences = data.getDependences();
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(
                    logger,
                    "cacheRegisterInfo error: " + e.getMessage()
            );
        }
    }

    /**
     * 异步缓存注册信息
     */
    public void cacheRegisterInfoAsync(RegisterData data) {
        ThreadUtil.execute(() -> cacheRegisterInfo(data));
    }

    /**
     * 获取完整的注册信息
     *
     * @return 注册信息JsonObject，如果不存在返回null
     */
    public JsonObject getRegisterInfo() {
        return mergedRequirement;
    }

    /**
     * 通过路径获取指定组件信息
     * 支持类似"contributes.viewsContainers.activitybar"的路径格式
     *
     * @param path 路径，用点分隔，如"contributes.commands"
     * @return 获取到的JsonElement，可能是JsonObject、JsonArray或基础类型
     */
    public JsonElement getByPath(String path) {
        JsonObject registerInfo = getRegisterInfo();
        if (registerInfo == null) {
            return null;
        }

        return getByPath(registerInfo, path);
    }

    /**
     * 从JsonElement中通过路径获取数据
     *
     * @param element 要搜索的JsonElement
     * @param path    路径，用点分隔
     * @return 获取到的JsonElement
     */
    private JsonElement getByPath(JsonElement element, String path) {
        if (element == null || path == null || path.isEmpty()) {
            return element;
        }

        String[] pathParts = path.split("\\.");
        JsonElement current = element;

        for (String part : pathParts) {
            if (current == null || !current.isJsonObject()) {
                return null;
            }

            JsonObject obj = current.getAsJsonObject();
            current = obj.get(part);
        }

        return current;
    }

    /**
     * 获取所有commands信息
     *
     * @return commands的JsonArray，如果不存在返回空数组
     */
    public JsonArray getCommands() {
        JsonElement commands = getByPath("contributes.commands");
        if (commands != null && commands.isJsonArray()) {
            return commands.getAsJsonArray();
        }
        return new JsonArray();
    }

    /**
     * 获取所有menus信息
     *
     * @return menus的JsonObject，如果不存在返回空对象
     */
    public JsonObject getMenus() {
        JsonElement menus = getByPath("contributes.menus");
        if (menus != null && menus.isJsonObject()) {
            return menus.getAsJsonObject();
        }
        return new JsonObject();
    }

    /**
     * 获取所有views信息
     *
     * @return views的JsonObject，如果不存在返回空对象
     */
    public JsonObject getViews() {
        JsonElement views = getByPath("contributes.views");
        if (views != null && views.isJsonObject()) {
            return views.getAsJsonObject();
        }
        return new JsonObject();
    }

    /**
     * 获取activity bar的viewsContainers信息
     *
     * @return activity bar的JsonArray，如果不存在返回空数组
     */
    public JsonArray getActivityBarContainers() {
        JsonElement activitybar = getByPath("contributes.viewsContainers.activitybar");
        if (activitybar != null && activitybar.isJsonArray()) {
            return activitybar.getAsJsonArray();
        }
        return new JsonArray();
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        this.mergedRequirement = null;
        this.dependences = null;
        LoggerUtil.INSTANCE.logInfo(
                logger,
                "Cleared all register cache"
        );
    }

    /**
     * 检查是否已缓存注册信息
     *
     * @return 是否已缓存
     */
    public boolean isCached() {
        return mergedRequirement != null || dependences != null;
    }

    /**
     * 获取所有activitybar信息
     *
     * @return activitybar列表
     */
    public List<JsonObject> getAllActivityBarObjects() {
        List<JsonObject> results = new ArrayList<>();
        JsonArray activityBars = getActivityBarContainers();

        for (int i = 0; i < activityBars.size(); i++) {
            JsonElement element = activityBars.get(i);
            if (element.isJsonObject()) {
                results.add(element.getAsJsonObject());
            }
        }

        return results;
    }

    /**
     * 根据view的id反向获取其所属的views容器名称
     *
     * @param viewId view的id，如"rednote-codewiz.SidebarProvider"
     * @return views容器名称，如"rednote-codewiz-ActivityBar"，如果未找到返回null
     */
    public String getViewsContainerByViewId(String viewId) {
        JsonObject views = getViews();
        if (views == null || views.size() == 0) {
            return null;
        }

        // 遍历所有views容器
        for (Map.Entry<String, JsonElement> containerEntry : views.entrySet()) {
            String containerName = containerEntry.getKey();
            JsonElement containerViews = containerEntry.getValue();

            if (containerViews.isJsonArray()) {
                JsonArray viewArray = containerViews.getAsJsonArray();

                // 检查容器中的每个view
                for (int i = 0; i < viewArray.size(); i++) {
                    JsonElement viewElement = viewArray.get(i);
                    if (viewElement.isJsonObject()) {
                        JsonObject viewObj = viewElement.getAsJsonObject();
                        if (viewObj.has("id")) {
                            String currentViewId = viewObj.get("id").getAsString();
                            if (viewId.equals(currentViewId)) {
                                return containerName;
                            }
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 根据activitybar的id获取对应的view id列表
     *
     * @param activityBarId activitybar的id，如"rednote-codewiz-ActivityBar"
     * @return 对应的view id列表，如果未找到返回空列表
     */
    public List<String> getViewIdsByActivityBarId(String activityBarId) {
        List<String> viewIds = new ArrayList<>();

        if (activityBarId == null || activityBarId.trim().isEmpty()) {
            return viewIds;
        }

        JsonObject views = getViews();
        if (views == null || views.size() == 0) {
            return viewIds;
        }

        // 在views中查找以activityBarId为key的容器
        if (views.has(activityBarId)) {
            JsonElement containerViews = views.get(activityBarId);

            if (containerViews.isJsonArray()) {
                JsonArray viewArray = containerViews.getAsJsonArray();

                // 提取每个view的id
                for (int i = 0; i < viewArray.size(); i++) {
                    JsonElement viewElement = viewArray.get(i);
                    if (viewElement.isJsonObject()) {
                        JsonObject viewObj = viewElement.getAsJsonObject();
                        if (viewObj.has("id")) {
                            String viewId = viewObj.get("id").getAsString();
                            if (viewId != null && !viewId.trim().isEmpty()) {
                                viewIds.add(viewId);
                            }
                        }
                    }
                }
            }
        }

        return viewIds;
    }


    /**
     * 将JsonObject转换为ActivityBar对象
     *
     * @param activityBarObj ActivityBar的JSON对象
     * @return ActivityBar对象，如果转换失败返回null
     */
    private ActivityBar convertJsonToActivityBar(JsonObject activityBarObj) {
        if (activityBarObj == null || !activityBarObj.has("id")) {
            return null;
        }

        ActivityBar activityBar = new ActivityBar();
        activityBar.setId(activityBarObj.get("id").getAsString());

        if (activityBarObj.has("title")) {
            activityBar.setTitle(activityBarObj.get("title").getAsString());
        }

        if (activityBarObj.has("icon")) {
            String iconPath = activityBarObj.get("icon").getAsString();
            activityBar.setIcon(iconPath);

            // 处理图标对象
            Icon icon = IconsUtil.getRcsIcon(iconPath);
            activityBar.setIconObject(icon);
        }

        if (activityBarObj.has("defaultPosition")) {
            activityBar.setDefaultPosition(activityBarObj.get("defaultPosition").getAsString());
        }

        return activityBar;
    }

    /**
     * 获取所有ActivityBar列表
     *
     * @return ActivityBar对象列表
     */
    public List<ActivityBar> getAllActivityBars() {
        List<ActivityBar> results = new ArrayList<>();
        List<JsonObject> allActivityBars = getAllActivityBarObjects();

        for (JsonObject activityBarObj : allActivityBars) {
            ActivityBar activityBar = convertJsonToActivityBar(activityBarObj);
            if (activityBar != null) {
                results.add(activityBar);
            }
        }

        return results;
    }

    /**
     * 获取所有依赖信息
     *
     * @return 依赖信息列表，如果没有依赖返回空列表
     */
    public List<RemoteServiceMeta> getDependences() {
        if (dependences == null) {
            return new ArrayList<>();
        }
        return new ArrayList<>(dependences);
    }

    /**
     * 获取所有依赖的顶层ID列表
     *
     * @return 依赖顶层ID列表，如果没有依赖返回空列表
     */
    public List<String> getDependenceIds() {
        if (dependences == null || dependences.isEmpty()) {
            return new ArrayList<>();
        }

        return dependences.stream()
                .map(RemoteServiceMeta::getId)
                .filter(id -> id != null && !id.trim().isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 获取所有依赖中的所有层级ID列表
     * 包括：顶层id、service.id、service.connection.id
     *
     * @return 所有层级的ID列表，如果没有依赖返回空列表
     */
    public List<String> getAllDependenceIds() {
        if (dependences == null || dependences.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> allIds = new ArrayList<>();

        for (RemoteServiceMeta dependence : dependences) {
            // 添加顶层ID
            if (dependence.getId() != null && !dependence.getId().trim().isEmpty()) {
                allIds.add(dependence.getId());
            }

            // 解析service对象中的ID
            if (dependence.getService() != null) {
                extractServiceIds(dependence.getService(), allIds);
            }
        }

        return allIds;
    }

    /**
     * 从service对象中提取所有ID
     *
     * @param service service对象
     * @param idList  ID列表容器
     */
    private void extractServiceIds(Object service, List<String> idList) {
        if (service == null) {
            return;
        }

        try {
            // 将service对象转换为JSON，然后解析ID
            String serviceJson = GsonUtil.toJson(service);
            JsonObject serviceObj = GsonUtil.fromJson(serviceJson, JsonObject.class);

            if (serviceObj != null) {
                // 提取service.id
                if (serviceObj.has("id")) {
                    String serviceId = serviceObj.get("id").getAsString();
                    if (serviceId != null && !serviceId.trim().isEmpty()) {
                        idList.add(serviceId);
                    }
                }

                // 提取service.connection.id
                if (serviceObj.has("connection")) {
                    JsonElement connectionElement = serviceObj.get("connection");
                    if (connectionElement.isJsonObject()) {
                        JsonObject connectionObj = connectionElement.getAsJsonObject();
                        if (connectionObj.has("id")) {
                            String connectionId = connectionObj.get("id").getAsString();
                            if (connectionId != null && !connectionId.trim().isEmpty()) {
                                idList.add(connectionId);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(
                    logger,
                    "extractServiceIds error: " + e.getMessage()
            );
        }
    }

    /**
     * 通过view ID获取对应的ActivityBar
     *
     * @param viewId view的id，如"rednote-codewiz.SidebarProvider"
     * @return 对应的ActivityBar对象，如果未找到返回null
     */
    public ActivityBar getActivityBarByViewId(String viewId) {
        // 首先获取views容器名称
        String containerName = getViewsContainerByViewId(viewId);
        if (containerName == null) {
            return null;
        }

        // 获取所有activitybar配置
        JsonArray activityBars = getActivityBarContainers();
        if (activityBars == null || activityBars.size() == 0) {
            return null;
        }

        // 查找匹配的ActivityBar
        for (int i = 0; i < activityBars.size(); i++) {
            JsonElement element = activityBars.get(i);
            if (element.isJsonObject()) {
                JsonObject activityBarObj = element.getAsJsonObject();
                if (activityBarObj.has("id")) {
                    String activityBarId = activityBarObj.get("id").getAsString();
                    // 检查activityBar的id是否与容器名称匹配
                    if (containerName.equals(activityBarId)) {
                        return convertJsonToActivityBar(activityBarObj);
                    }
                }
            }
        }

        return null;
    }


    public static void main(String[] args) {
        String register = """
                {
                        "success": 1,
                        "data": {
                            "dependences": [
                                {
                                    "id": "i-1753946173424-22894024490469955",
                                    "name": "CodeWiz.rednote-codewiz",
                                    "version": "0.10.2",
                                    "service": {
                                        "id": "9231a68d-1aaa-4c6c-b43b-fa85200f5476",
                                        "name": "CodeWiz",
                                        "version": "0.0.1",
                                        "connection": {
                                            "id": "i-1753946164523-17410762297030336",
                                            "version": "0.0.1"
                                        }
                                    }
                                },
                                {
                                    "id": "i-1753946173426-8104816452999308",
                                    "name": "Rednote.rednote-codewiz",
                                    "version": "0.9.NaN.1",
                                    "service": {
                                        "id": "9231a68d-1aaa-4c6c-b43b-fa85200f5476",
                                        "name": "CodeWiz",
                                        "version": "0.0.1",
                                        "connection": {
                                            "id": "i-1753946164523-17410762297030336",
                                            "version": "0.0.1"
                                        }
                                    }
                                }
                            ],
                            "requirment": {
                                "contributes": {
                                    "viewsContainers": {
                                        "activitybar": [
                                            {
                                                "id": "rednote-codewiz-ActivityBar",
                                                "title": "CodeWiz",
                                                "icon": "/Users/<USER>/code/rcs/red-code-service/.rcs-dependences/rednote-codewiz/0.10.2/assets/icons/icon.svg"
                                            },
                                            {
                                                "id": "codewiz-issue",
                                                "title": "codewiz Issue",
                                                "icon": "/Users/<USER>/code/rcs/red-code-service/.rcs-dependences/codewiz-workspace/9.9.9/resources/issue.svg"
                                            },
                                            {
                                                "id": "codewiz-collaboration",
                                                "title": "codewiz 协作",
                                                "icon": "/Users/<USER>/code/rcs/red-code-service/.rcs-dependences/codewiz-workspace/9.9.9/resources/collab.svg"
                                            }
                                        ]
                                    },
                                    "views": {
                                        "rednote-codewiz-ActivityBar": [
                                            {
                                                "type": "webview",
                                                "id": "rednote-codewiz.SidebarProvider",
                                                "name": "CodeWiz"
                                            }
                                        ],
                                        "codewiz-issue": [
                                            {
                                                "type": "webview",
                                                "id": "CodeWiz.Issue",
                                                "name": "codewiz Issue"
                                            }
                                        ],
                                        "codewiz-collaboration": [
                                            {
                                                "type": "webview",
                                                "id": "CodeWiz.Collaboration",
                                                "name": "Collaboration"
                                            }
                                        ]
                                    },
                                    "commands": [
                                        {
                                            "command": "rednote-codewiz.diffComments.toNextComment",
                                            "title": "下一个"
                                        },
                                        {
                                            "command": "rednote-codewiz.diffComments.toPreviousComment",
                                            "title": "上一个"
                                        },
                                        {
                                            "command": "rednote-codewiz.plusButtonClicked",
                                            "title": "新任务"
                                        },
                                        {
                                            "command": "rednote-codewiz.promptsButtonClicked",
                                            "title": "模式"
                                        },
                                        {
                                            "command": "rednote-codewiz.mcpButtonClicked",
                                            "title": "MCP 服务器"
                                        },
                                        {
                                            "command": "rednote-codewiz.historyButtonClicked",
                                            "title": "历史记录"
                                        },
                                        {
                                            "command": "rednote-codewiz.marketplaceButtonClicked",
                                            "title": "应用市场"
                                        },
                                        {
                                            "command": "rednote-codewiz.popoutButtonClicked",
                                            "title": "在编辑器中打开"
                                        },
                                        {
                                            "command": "rednote-codewiz.accountButtonClicked",
                                            "title": "Account"
                                        },
                                        {
                                            "command": "rednote-codewiz.settingsButtonClicked",
                                            "title": "设置"
                                        },
                                        {
                                            "command": "rednote-codewiz.openInNewTab",
                                            "title": "在新标签页中打开",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.explainCode",
                                            "title": "解释代码",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.explainCodePlus",
                                            "title": "🤔深度解释代码",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.fixCode",
                                            "title": "修复代码",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.improveCode",
                                            "title": "改进代码",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.addToContext",
                                            "title": "添加到上下文",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.newTask",
                                            "title": "新任务",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.terminalAddToContext",
                                            "title": "将终端内容添加到上下文",
                                            "category": "Terminal"
                                        },
                                        {
                                            "command": "rednote-codewiz.terminalFixCommand",
                                            "title": "修复此命令",
                                            "category": "Terminal"
                                        },
                                        {
                                            "command": "rednote-codewiz.terminalExplainCommand",
                                            "title": "解释此命令",
                                            "category": "Terminal"
                                        },
                                        {
                                            "command": "rednote-codewiz.setCustomStoragePath",
                                            "title": "设置自定义存储路径",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.importSettings",
                                            "title": "导入设置",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.focusInput",
                                            "title": "聚焦输入字段",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "rednote-codewiz.acceptInput",
                                            "title": "接受输入/建议",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "wizcode:restart-language-server",
                                            "title": "启动/重启语言服务器",
                                            "category": "CodeWiz"
                                        },
                                        {
                                            "command": "wizcode:login",
                                            "title": "登录",
                                            "category": "login"
                                        },
                                        {
                                            "command": "wizcode:logout",
                                            "title": "登出",
                                            "category": "CodeWiz",
                                            "group": "WizMate@5"
                                        },
                                        {
                                            "command": "codewiz.showTopic",
                                            "title": "显示话题",
                                            "category": "CodeWiz"
                                        }
                                    ],
                                    "menus": {
                                        "editor/context": [
                                            {
                                                "submenu": "rednote-codewiz.contextMenu",
                                                "group": "1"
                                            }
                                        ],
                                        "rednote-codewiz.contextMenu": [
                                            {
                                                "command": "rednote-codewiz.addToContext",
                                                "group": "1_actions@1"
                                            },
                                            {
                                                "command": "rednote-codewiz.explainCode",
                                                "group": "1_actions@2"
                                            },
                                            {
                                                "command": "rednote-codewiz.explainCodePlus",
                                                "group": "1_actions@3"
                                            },
                                            {
                                                "command": "rednote-codewiz.improveCode",
                                                "group": "1_actions@4"
                                            }
                                        ],
                                        "terminal/context": [
                                            {
                                                "submenu": "rednote-codewiz.terminalMenu",
                                                "group": "2"
                                            }
                                        ],
                                        "rednote-codewiz.terminalMenu": [
                                            {
                                                "command": "rednote-codewiz.terminalAddToContext",
                                                "group": "1_actions@1"
                                            },
                                            {
                                                "command": "rednote-codewiz.terminalFixCommand",
                                                "group": "1_actions@2"
                                            },
                                            {
                                                "command": "rednote-codewiz.terminalExplainCommand",
                                                "group": "1_actions@3"
                                            }
                                        ],
                                        "view/title": [
                                            {
                                                "command": "rednote-codewiz.plusButtonClicked",
                                                "group": "navigation@1",
                                                "when": "view == rednote-codewiz.SidebarProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.settingsButtonClicked",
                                                "group": "navigation@2",
                                                "when": "view == rednote-codewiz.SidebarProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.accountButtonClicked",
                                                "group": "navigation@3",
                                                "when": "view == rednote-codewiz.SidebarProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.historyButtonClicked",
                                                "group": "overflow@1",
                                                "when": "view == rednote-codewiz.SidebarProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.marketplaceButtonClicked",
                                                "group": "overflow@2",
                                                "when": "view == rednote-codewiz.SidebarProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.promptsButtonClicked",
                                                "group": "overflow@3",
                                                "when": "view == rednote-codewiz.SidebarProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.mcpButtonClicked",
                                                "group": "overflow@4",
                                                "when": "view == rednote-codewiz.SidebarProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.popoutButtonClicked",
                                                "group": "overflow@5",
                                                "when": "view == rednote-codewiz.SidebarProvider"
                                            }
                                        ],
                                        "editor/title": [
                                            {
                                                "command": "rednote-codewiz.plusButtonClicked",
                                                "group": "navigation@1",
                                                "when": "activeWebviewPanelId == rednote-codewiz.TabPanelProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.settingsButtonClicked",
                                                "group": "navigation@2",
                                                "when": "activeWebviewPanelId == rednote-codewiz.TabPanelProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.accountButtonClicked",
                                                "group": "navigation@3",
                                                "when": "activeWebviewPanelId == rednote-codewiz.TabPanelProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.historyButtonClicked",
                                                "group": "overflow@1",
                                                "when": "activeWebviewPanelId == rednote-codewiz.TabPanelProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.marketplaceButtonClicked",
                                                "group": "overflow@2",
                                                "when": "activeWebviewPanelId == rednote-codewiz.TabPanelProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.promptsButtonClicked",
                                                "group": "overflow@3",
                                                "when": "activeWebviewPanelId == rednote-codewiz.TabPanelProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.mcpButtonClicked",
                                                "group": "overflow@4",
                                                "when": "activeWebviewPanelId == rednote-codewiz.TabPanelProvider"
                                            },
                                            {
                                                "command": "rednote-codewiz.popoutButtonClicked",
                                                "group": "overflow@5",
                                                "when": "activeWebviewPanelId == rednote-codewiz.TabPanelProvider"
                                            }
                                        ],
                                        "comments/commentThread/title": [
                                            {
                                                "command": "rednote-codewiz.diffComments.toNextComment",
                                                "group": "navigation",
                                                "when": "commentController == DiffComments && !commentThreadIsEmpty"
                                            },
                                            {
                                                "command": "rednote-codewiz.diffComments.toPreviousComment",
                                                "group": "navigation",
                                                "when": "commentController == DiffComments && !commentThreadIsEmpty"
                                            }
                                        ]
                                    },
                                    "submenus": [
                                        {
                                            "id": "rednote-codewiz.contextMenu",
                                            "label": "CodeWiz"
                                        },
                                        {
                                            "id": "rednote-codewiz.terminalMenu",
                                            "label": "CodeWiz"
                                        }
                                    ],
                                    "configuration": [
                                        {
                                            "title": "CodeWiz",
                                            "properties": {
                                                "rednote-codewiz.allowedCommands": {
                                                    "type": "array",
                                                    "items": {
                                                        "type": "string"
                                                    },
                                                    "default": [
                                                        "npm test",
                                                        "npm install",
                                                        "tsc",
                                                        "git log",
                                                        "git diff",
                                                        "git show"
                                                    ],
                                                    "description": "启用'始终批准执行操作'时可以自动执行的命令"
                                                },
                                                "rednote-codewiz.deniedCommands": {
                                                    "type": "array",
                                                    "items": {
                                                        "type": "string"
                                                    },
                                                    "default": [],
                                                    "description": "将自动拒绝而不询问批准的命令前缀。在与允许命令冲突的情况下，最长前缀匹配优先。添加 * 以拒绝所有命令。"
                                                },
                                                "rednote-codewiz.commandExecutionTimeout": {
                                                    "type": "number",
                                                    "default": 0,
                                                    "minimum": 0,
                                                    "maximum": 600,
                                                    "description": "等待命令执行完成超时前的最大时间（秒）（0 = 无超时，1-600秒，默认：0秒）"
                                                },
                                                "rednote-codewiz.vsCodeLmModelSelector": {
                                                    "type": "object",
                                                    "properties": {
                                                        "vendor": {
                                                            "type": "string",
                                                            "description": "语言模型的供应商（例如 copilot）"
                                                        },
                                                        "family": {
                                                            "type": "string",
                                                            "description": "语言模型的系列（例如 gpt-4）"
                                                        }
                                                    },
                                                    "description": "VSCode 语言模型 API 设置"
                                                },
                                                "rednote-codewiz.customStoragePath": {
                                                    "type": "string",
                                                    "default": "",
                                                    "description": "自定义存储路径。留空以使用默认位置。支持绝对路径（例如：'D:\\\\CodeWizStorage'）"
                                                },
                                                "rednote-codewiz.enableCodeActions": {
                                                    "type": "boolean",
                                                    "default": true,
                                                    "description": "启用 CodeWiz 快速修复"
                                                },
                                                "rednote-codewiz.autoImportSettingsPath": {
                                                    "type": "string",
                                                    "default": "",
                                                    "description": "CodeWiz 配置文件的路径，用于在扩展启动时自动导入。支持绝对路径和相对于主目录的路径（例如 '~/Documents/codewiz-settings.json'）。留空以禁用自动导入。"
                                                }
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                """;

        ServiceRegisterResponse serviceRegisterResponse = GsonUtil.fromJson(register, ServiceRegisterResponse.class);
        RegisterUtil instance = getInstance();
        instance.cacheRegisterInfo(serviceRegisterResponse.getData());

        // 测试获取所有ActivityBar(JSON)
        List<JsonObject> allActivityBars = instance.getAllActivityBarObjects();
        System.out.println("All ActivityBars (JSON): " + allActivityBars.size());

        // 测试获取所有ActivityBar(对象列表)
        List<ActivityBar> allActivityBarList = instance.getAllActivityBars();
        System.out.println("All ActivityBar objects count: " + allActivityBarList.size());
        for (ActivityBar bar : allActivityBarList) {
            System.out.println("ActivityBar - ID: " + bar.getId() + ", Title: " + bar.getTitle());
        }

        // 测试获取所有依赖信息
        List<RemoteServiceMeta> dependences = instance.getDependences();
        System.out.println("Dependences count: " + dependences.size());
        for (RemoteServiceMeta dep : dependences) {
            System.out.println("Dependence - ID: " + dep.getId() + ", Name: " + dep.getName() + ", Version: " + dep.getVersion());
        }

        // 测试获取依赖顶层ID列表
        List<String> dependenceIds = instance.getDependenceIds();
        System.out.println("Dependence top-level IDs count: " + dependenceIds.size());
        for (String id : dependenceIds) {
            System.out.println("Dependence top-level ID: " + id);
        }

        // 测试获取所有层级的依赖ID列表
        List<String> allDependenceIds = instance.getAllDependenceIds();
        System.out.println("All dependence IDs count: " + allDependenceIds.size());
        for (String id : allDependenceIds) {
            System.out.println("All dependence ID: " + id);
        }

        // 测试通过view ID获取views容器名称
        String viewsContainerByViewId = instance.getViewsContainerByViewId("rednote-codewiz.SidebarProvider");
        System.out.println("Views Container: " + viewsContainerByViewId);

        // 测试通过activitybar ID获取view ID列表 (反向查询)
        List<String> viewIdsByActivityBar = instance.getViewIdsByActivityBarId("rednote-codewiz-ActivityBar");
        System.out.println("View IDs by ActivityBar ID count: " + viewIdsByActivityBar.size());
        for (String viewId : viewIdsByActivityBar) {
            System.out.println("View ID: " + viewId);
        }

        // 测试通过view ID获取ActivityBar
        ActivityBar activityBar = instance.getActivityBarByViewId("rednote-codewiz.SidebarProvider");
        System.out.println("ActivityBar: " + activityBar);

        // 测试其他功能
        JsonArray commands = instance.getCommands();
        System.out.println("Commands count: " + commands.size());

        JsonObject menus = instance.getMenus();
        System.out.println("Menus count: " + menus.size());

        JsonObject views = instance.getViews();
        System.out.println("Views count: " + views.size());
    }
}
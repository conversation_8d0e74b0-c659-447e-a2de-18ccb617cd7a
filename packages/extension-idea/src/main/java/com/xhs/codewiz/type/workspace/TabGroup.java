package com.xhs.codewiz.type.workspace;

import java.util.*;

/**
 * Tab 标签组，用于管理多个 Tab 标签
 */
public class TabGroup {
    /** 标签组所在的视图列 */
    private ViewColumn viewColumn;

    /** 标签组中的 Tab 列表 */
    private List<Tab> tabs;

    /** 是否为当前激活的标签组 */
    private Boolean isActive;

    /** 当前激活的 Tab 索引 */
    private Integer activeTab;

    public ViewColumn getViewColumn() {
        return viewColumn;
    }

    public void setViewColumn(ViewColumn viewColumn) {
        this.viewColumn = viewColumn;
    }

    public List<Tab> getTabs() {
        return tabs;
    }

    public void setTabs(List<Tab> tabs) {
        this.tabs = tabs;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Integer getActiveTab() {
        return activeTab;
    }

    public void setActiveTab(Integer activeTab) {
        this.activeTab = activeTab;
    }

}

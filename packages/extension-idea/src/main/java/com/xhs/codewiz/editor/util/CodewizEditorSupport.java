package com.xhs.codewiz.editor.util;


import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.extensions.ExtensionPointName;
import org.jetbrains.annotations.NotNull;

public interface CodewizEditorSupport {
    ExtensionPointName<CodewizEditorSupport> EP = ExtensionPointName.create("com.xhs.codewiz.editorSupport");

    static boolean isEditorCompletionsSupported(@NotNull Editor editor) {
        if (!EP.hasAnyExtensions()) {
            return true;
        } else {
            return EP.findFirstSafe((editorSupport) -> {
                return !editorSupport.isCompletionsEnabled(editor);
            }) == null;
        }
    }

    boolean isCompletionsEnabled(@NotNull Editor var1);
}

package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.SessionProvider;
/**
 * 创建 Session Provider
 */
public class CreateSessionProvider {
    private String schemaProtocol = "platform.create.sessionprovider";
    private CreateSessionProviderParams params;

    public CreateSessionProviderParams getParams() {
        return params;
    }
    public void setParams(CreateSessionProviderParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateSessionProviderParams {
        private SessionProvider provider;
    
        public SessionProvider getProvider() {
            return provider;
        }
        public void setProvider(SessionProvider provider) {
            this.provider = provider;
        }
    }
}

package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 隐藏 Webview
 */
public class ExecuteHideWebView {
    private String schemaProtocol = "platform.execute.hidewebview";
    /** Webview provider ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

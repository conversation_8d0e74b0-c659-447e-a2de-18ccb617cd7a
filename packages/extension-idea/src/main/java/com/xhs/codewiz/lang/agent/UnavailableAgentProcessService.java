package com.xhs.codewiz.lang.agent;

import com.google.gson.JsonObject;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcClientResponse;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcRequestListener;
import com.xhs.codewiz.utils.LoggerUtil;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.CancellablePromise;
import org.jetbrains.concurrency.Promises;

final class UnavailableAgentProcessService implements CodeWizAgentProcessServiceEx {
    private static final Logger LOG = Logger.getInstance(UnavailableAgentProcessService.class);
    private final String cause;

    public UnavailableAgentProcessService(@NotNull String cause) {
        this.cause = cause;
    }

    public boolean isRunning() {
        return false;
    }

    public void startNotify() {
    }

    public <T> @NotNull CancellablePromise<T> executeCommand(@NotNull JsonRpcCommand<T> command, @Nullable JsonObject additionalProperties) {
        LoggerUtil.INSTANCE.logDebug(LOG, "agent service is unavailable: " + this.cause + ", command: " + command);
        return Promises.rejectedCancellablePromise("service is unavailable");
    }

    public void executeNotification(@NotNull JsonRpcNotification notification, @Nullable JsonObject additionalProperties) {
        LoggerUtil.INSTANCE.logDebug(LOG, "agent service is unavailable: " + this.cause + ", notification: " + notification);
    }

    public void addNotificationListener(@NotNull Disposable parentDisposable, @NotNull JsonRpcNotificationListener listener) {
        LoggerUtil.INSTANCE.logDebug(LOG, "agent service is unavailable and unable to add a listener: " + this.cause);
    }

    public <I, O> void addRequestListener(@NotNull String lspCommand, @NotNull JsonRpcRequestListener<I, O> listener) {
        LoggerUtil.INSTANCE.logDebug(LOG, "agent service is unavailable and unable to add a listener: " + this.cause);
    }

    public void executeResponse(@NotNull JsonRpcClientResponse response) {
        LoggerUtil.INSTANCE.logDebug(LOG, "agent service is unavailable and unable to execute a response: " + this.cause);
    }

    @Override
    public CancellablePromise<Object> executeCommonLsp(JsonObject request, String commandName) {
        return Promises.rejectedCancellablePromise("service is unavailable");
    }

    @Override
    public void commonNotification4UI(JsonObject request, String commandName) {
    }

    public void initialize(@NotNull Collection<JsonRpcNotificationListener> listeners) {
    }

    public boolean isShutdown() {
        return false;
    }

    public void shutdown() {
    }

    public void flush() {
    }
}

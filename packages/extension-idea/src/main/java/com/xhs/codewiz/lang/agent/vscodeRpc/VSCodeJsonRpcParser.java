package com.xhs.codewiz.lang.agent.vscodeRpc;

import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcMessageHandler;
import java.nio.charset.StandardCharsets;
import javax.annotation.concurrent.GuardedBy;
import javax.annotation.concurrent.ThreadSafe;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.TestOnly;

@ThreadSafe
class VSCodeJsonRpcParser {
    private static final Logger LOG = Logger.getInstance(VSCodeJsonRpcParser.class);
    private static final byte[] CONTENT_LENGTH;
    private static final byte[] SEPARATOR;
    private final JsonRpcMessageHandler messageHandler;
    private final Object lock = new Object();
    @GuardedBy("lock")
    private int pendingContentLength = 0;
    @GuardedBy("lock")
    private final ByteArray pendingContent = new ByteArray();

    @TestOnly
    public @NotNull String getPendingContent() {
        synchronized(this.lock) {
            return this.pendingContent.toString(StandardCharsets.UTF_8);
        }
    }

    public void append(@NotNull String content) {
        synchronized(this.lock) {
            this.appendLocked(content);
        }
    }

    private void handleContentLengthMessage() {
        assert this.pendingContentLength == 0;

        int lineEnd = this.pendingContent.indexOf(SEPARATOR);
        if (lineEnd > 0) {
            String line = new String(this.pendingContent.getBytes(CONTENT_LENGTH.length, lineEnd), StandardCharsets.UTF_8);
            this.pendingContent.deleteFirst(lineEnd + SEPARATOR.length);
            int length = Integer.parseInt(line, 0, line.length(), 10);
            this.pendingContentLength += length;
        }

    }

    private void appendLocked(@NotNull String content) {
        // if (content.startsWith("Listening for transport dt_socket at address")
        //         || content.contains("INFO CozLogger")) {
        //     // debug jar 启动时会发送listener消息，不符合json-rpc 规范，直接忽略
        //     return;
        // }
        this.pendingContent.add(content.getBytes(StandardCharsets.UTF_8));

        do {
            if (this.pendingContent.indexOf(CONTENT_LENGTH) == 0) {
                this.handleContentLengthMessage();
            }
        } while(this.pendingContentLength > 0 && this.processPendingContent());

    }

    private boolean processPendingContent() {
        if (this.pendingContent.size() < this.pendingContentLength) {
            return false;
        } else {
            String message = this.pendingContent.toString(0, this.pendingContentLength, StandardCharsets.UTF_8);
            this.pendingContent.deleteFirst(this.pendingContentLength);
            this.pendingContentLength = 0;
            this.messageHandler.handleJsonMessage(message);
            return true;
        }
    }

    public VSCodeJsonRpcParser(JsonRpcMessageHandler messageHandler) {
        this.messageHandler = messageHandler;
    }

    static {
        CONTENT_LENGTH = "Content-Length: ".getBytes(StandardCharsets.UTF_8);
        SEPARATOR = "\r\n\r\n".getBytes(StandardCharsets.UTF_8);
    }
}


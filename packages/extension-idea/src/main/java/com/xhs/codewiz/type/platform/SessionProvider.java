package com.xhs.codewiz.type.platform;

import java.util.*;

/**
 * Session 提供者
 */
public class SessionProvider {
    private SessionProviderOptions options;

    /** Session 提供者 ID */
    private String id;

    /** Session 提供者名称 */
    private String label;

    public SessionProviderOptions getOptions() {
        return options;
    }

    public void setOptions(SessionProviderOptions options) {
        this.options = options;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

public static class SessionProviderOptions {
    /** 是否支持多个账户 */
    private Boolean supportsMultipleAccounts;

    public Boolean getSupportsMultipleAccounts() {
        return supportsMultipleAccounts;
    }

    public void setSupportsMultipleAccounts(Boolean supportsMultipleAccounts) {
        this.supportsMultipleAccounts = supportsMultipleAccounts;
    }

}
}

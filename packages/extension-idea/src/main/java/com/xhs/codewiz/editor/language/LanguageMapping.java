package com.xhs.codewiz.editor.language;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.utils.LoggerUtil;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

public class LanguageMapping {
  private static final Logger LOGGER = Logger.getInstance(LanguageMapping.class);
  private static final String OTHER_LANGUAGE = "others";
  public static final String PLAINTEXT = "plaintext";
  private Map<String, String> extToLang = new HashMap<String, String>();
  private Map<String, List<String>> langToExts = new HashMap<String, List<String>>();
  private Map<String, String> prefixToLang = new HashMap<String, String>();

  public void addExtension(String lang, String extName) {
    this.langToExts.computeIfAbsent(lang, k -> new ArrayList<>()).add(extName);
    this.extToLang.put(extName, lang);
  }

  public void addPrefix(String lang, String prefix) {
    this.prefixToLang.put(prefix, lang);
  }

  public List<String> getExtensions(String lang) {
    return this.langToExts.getOrDefault(lang, new ArrayList());
  }

  public String getLanguages(String fileName) {
    String fileExt = LanguageUtil.getExtensionByFileName(fileName = fileName.toLowerCase(Locale.ROOT));
    if (!fileExt.isEmpty()) {
      String langType;
      String tmpFileExt = fileExt;
      if (!(tmpFileExt).startsWith(".")) {
        tmpFileExt = "." + tmpFileExt;
      }
      if ((langType = this.extToLang.get(tmpFileExt)) != null) {
        return langType;
      }
    }
    for (Map.Entry<String, String> entry : this.prefixToLang.entrySet()) {
      if (!fileName.startsWith(entry.getKey())) continue;
      return entry.getValue();
    }
    if (!(fileExt = StringUtils.strip(fileExt, ".")).isEmpty()) {
      return fileExt;
    }
    return PLAINTEXT;
  }

  public boolean isValid() {
    return !this.extToLang.isEmpty() && !this.prefixToLang.isEmpty();
  }

  public void readJson(String jsonStr) throws JsonSyntaxException {
    Gson gson = new Gson();
    LangJsonObject langObj = gson.fromJson(jsonStr, LangJsonObject.class);
    for (Map.Entry<String, String> entry : langObj.suffix.entrySet()) {
      String[] exts = entry.getValue().split(",");
      for (String ext : exts ) {
        this.addExtension(entry.getKey(), ext);
      }
    }
    for (Map.Entry<String, String> entry : langObj.prefix.entrySet()) {
      String[] prefixes  = entry.getValue().split(",");
      for (String prefix : prefixes) {
        this.addPrefix(entry.getKey(), prefix);
      }
    }
  }

  /*
   * Enabled aggressive block sorting
   * Enabled unnecessary exception pruning
   * Enabled aggressive exception aggregation
   */
  public static LanguageMapping buildLanguageMapping() {
    LanguageMapping typeSet = new LanguageMapping();
    try (InputStream is = LanguageMapping.class.getResourceAsStream("/completion/language.json");){
      if (is == null) {
        LanguageMapping languageMapping = typeSet;
        return languageMapping;
      }
      String languageJson = IOUtils.toString(is, StandardCharsets.UTF_8);
      typeSet.readJson(languageJson);
      return typeSet;
    }
    catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(LOGGER,  "Failed to parse language.json: ", e);
    }
    return typeSet;
  }

  public Map<String, String> getExtToLang() {
    return this.extToLang;
  }

  public Map<String, List<String>> getLangToExts() {
    return this.langToExts;
  }

  public Map<String, String> getPrefixToLang() {
    return this.prefixToLang;
  }

  public static class LangJsonObject {
    private Map<String, String> prefix;
    private Map<String, String> suffix;

    public LangJsonObject() {
    }

    public Map<String, String> getPrefix() {
      return this.prefix;
    }

    public Map<String, String> getSuffix() {
      return this.suffix;
    }

    public void setPrefix(Map<String, String> prefix) {
      this.prefix = prefix;
    }

    public void setSuffix(Map<String, String> suffix) {
      this.suffix = suffix;
    }

  }
}
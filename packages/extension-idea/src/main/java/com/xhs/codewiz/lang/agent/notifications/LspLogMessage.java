package com.xhs.codewiz.lang.agent.notifications;

import com.google.gson.annotations.SerializedName;

public abstract class LspLogMessage implements LogMessage {
    @SerializedName("type")
    int messageType;
    @SerializedName("message")
    String message;

    public boolean isDebug() {
        return this.messageType == 4;
    }

    public boolean isInfo() {
        return this.messageType == 3;
    }

    public boolean isWarn() {
        return this.messageType == 2;
    }

    public boolean isError() {
        return this.messageType == 1;
    }

    public LspLogMessage() {
    }

    public int getMessageType() {
        return this.messageType;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessageType(int messageType) {
        this.messageType = messageType;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}

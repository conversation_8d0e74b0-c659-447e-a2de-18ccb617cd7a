package com.xhs.codewiz.enums;

/**
 * Author: liukunpeng Date: 2025-05-23 Description:
 */
public enum CompletionStatusEnum {
  GENERATING(0, "generating"),
  SHOWN(1, "shown"),
  ACCEPTED(2, "accepted"),
  REJECTED(3,   "rejected");

  private final int statusCode;
  private final String description;


  CompletionStatusEnum(int status, String description) {
    this.statusCode = status;
    this.description = description;
  }
}

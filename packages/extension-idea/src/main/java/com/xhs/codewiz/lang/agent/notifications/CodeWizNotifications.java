package com.xhs.codewiz.lang.agent.notifications;


import com.intellij.notification.Notification;
import com.intellij.notification.NotificationListener;
import com.intellij.notification.NotificationType;
import com.intellij.notification.impl.NotificationFullContent;
import com.intellij.openapi.util.NlsContexts.NotificationContent;
import com.intellij.openapi.util.NlsContexts.NotificationTitle;
import org.jetbrains.annotations.NotNull;

public final class CodeWizNotifications {
    private static final String GROUP_ID = "github.copilot.notifications";

    private CodeWizNotifications() {
    }

    public static Notification createFullContentNotification(@NotNull String title, @NotNull String content, @NotNull NotificationType type, boolean expireOnLinkClick) {
        FullContent notification = new FullContent("github.copilot.notifications", title, content, type);
        notification.setListener(new NotificationListener.UrlOpeningListener(expireOnLinkClick));
        return notification;
    }

    private static class FullContent extends Notification implements NotificationFullContent {
        public FullContent(@NotNull String groupId, @NotNull @NotificationTitle String title, @NotNull @NotificationContent String content, @NotNull NotificationType type) {
            super(groupId, title, content, type);
        }
    }
}

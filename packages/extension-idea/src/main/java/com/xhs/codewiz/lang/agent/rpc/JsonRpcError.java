package com.xhs.codewiz.lang.agent.rpc;

import com.google.gson.JsonElement;
import com.google.gson.annotations.SerializedName;
import org.jetbrains.annotations.Nullable;

public class JsonRpcError {
    @SerializedName("code")
    int code;
    @SerializedName("message")
    String error;
    @SerializedName("data")
    @Nullable JsonElement data;

    public JsonRpcError() {
    }

    public int getCode() {
        return this.code;
    }

    public String getError() {
        return this.error;
    }

    public @Nullable JsonElement getData() {
        return this.data;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setError(String error) {
        this.error = error;
    }

    public void setData(@Nullable JsonElement data) {
        this.data = data;
    }
}

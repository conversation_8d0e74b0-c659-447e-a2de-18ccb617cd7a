package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.BuildInPanel;
/**
 * 显示面板
 */
public class ExecuteShowPanel {
    private String schemaProtocol = "platform.execute.showpanel";
    private ExecuteShowPanelParams params;

    public ExecuteShowPanelParams getParams() {
        return params;
    }
    public void setParams(ExecuteShowPanelParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteShowPanelParams {
        private ExecuteShowPanelParamsOptions options; // optional
        /** 面板 ID */
        private String id;
        /** 要显示的面板 */
        private BuildInPanel panel;
    
        public ExecuteShowPanelParamsOptions getOptions() {
            return options;
        }
        public void setOptions(ExecuteShowPanelParamsOptions options) {
            this.options = options;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public BuildInPanel getPanel() {
            return panel;
        }
        public void setPanel(BuildInPanel panel) {
            this.panel = panel;
        }
    }

        public static class ExecuteShowPanelParamsOptions {
        /** 是否需要聚焦面板 */
        private Boolean didFocus; // optional
    
        public Boolean getDidFocus() {
            return didFocus;
        }
        public void setDidFocus(Boolean didFocus) {
            this.didFocus = didFocus;
        }
    }
}

package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
import com.xhs.codewiz.type.file.Content;
/**
 * 创建文件
 */
public class CreateFile {
    private String schemaProtocol = "file.create.file";
    private CreateFileParams params;

    public CreateFileParams getParams() {
        return params;
    }
    public void setParams(CreateFileParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateFileParams {
        private String uri;
        private Content content;
    
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
        public Content getContent() {
            return content;
        }
        public void setContent(Content content) {
            this.content = content;
        }
    }
}

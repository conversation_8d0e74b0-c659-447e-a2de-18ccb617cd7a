package com.xhs.codewiz.lang.featureflags;

public final class CodeWizFeatureFlags {
    private final boolean selfSignedCertificatesEnabled;
    private final boolean restrictedTelemetryEnabled;
    private final boolean chatEnabled;

    public CodeWizFeatureFlags(boolean selfSignedCertificatesEnabled, boolean restrictedTelemetryEnabled, boolean chatEnabled) {
        this.selfSignedCertificatesEnabled = selfSignedCertificatesEnabled;
        this.restrictedTelemetryEnabled = restrictedTelemetryEnabled;
        this.chatEnabled = chatEnabled;
    }

    public boolean isSelfSignedCertificatesEnabled() {
        return this.selfSignedCertificatesEnabled;
    }

    public boolean isRestrictedTelemetryEnabled() {
        return this.restrictedTelemetryEnabled;
    }

    public boolean isChatEnabled() {
        return this.chatEnabled;
    }
}


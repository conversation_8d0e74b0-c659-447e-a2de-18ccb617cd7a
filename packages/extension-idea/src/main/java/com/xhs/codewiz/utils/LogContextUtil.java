package com.xhs.codewiz.utils;

/**
 * Author: liukunpeng Date: 2025-07-02 Description:
 */
public class LogContextUtil {
  private static final ThreadLocal<LogContext> ctx = InheritableThreadLocal.withInitial(
      LogContext::new);

  public static LogContext get() {
    return ctx.get();
  }

  public static class LogContext {
    private String sessionId = "";
    private String requestId = "";
    private String traceId = "";

    public String getSessionId() {
      return sessionId;
    }

    public void setSessionId(String sessionId) {
      this.sessionId = sessionId;
    }

    public String getRequestId() {
      return requestId;
    }

    public void setRequestId(String requestId) {
      this.requestId = requestId;
    }

    public String getTraceId() {
      return traceId;
    }

    public void setTraceId(String traceId) {
      this.traceId = traceId;
    }
  }
}

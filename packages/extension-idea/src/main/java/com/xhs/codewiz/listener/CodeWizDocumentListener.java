package com.xhs.codewiz.listener;

import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.event.DocumentEvent;
import com.intellij.openapi.editor.event.DocumentListener;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.vfs.VirtualFile;
import org.jetbrains.annotations.NotNull;

public class CodeWizDocumentListener implements DocumentListener {
    private static final Key<Integer> KEY_FILE_OPENED = Key.create("codewiz.lspOpen");

    public void documentChanged(@NotNull DocumentEvent event) {
        LSPManager.getInstance().notifyDidChange(event);
    }
}

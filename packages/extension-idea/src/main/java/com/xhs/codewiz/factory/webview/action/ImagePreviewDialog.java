package com.xhs.codewiz.factory.webview.action;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

import javax.imageio.ImageIO;
import javax.swing.Action;
import javax.swing.ImageIcon;
import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.SwingConstants;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.util.ui.JBUI;

/**
 * 图片预览对话框
 * 专门用于显示图片的 Dialog 组件
 */
public class ImagePreviewDialog extends DialogWrapper {
    private final String imagePath;
    private JLabel imageLabel;
    private JScrollPane scrollPane;

    public ImagePreviewDialog(@Nullable Project project, String imagePath) {
        super(project);
        this.imagePath = imagePath;
        setTitle("图片预览");
        setModal(false);
        setResizable(true);
        init();
        loadAndDisplayImage();
    }

    @Override
    protected @Nullable JComponent createCenterPanel() {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setPreferredSize(JBUI.size(800, 600));

        // 创建图片显示区域
        imageLabel = new JLabel();
        imageLabel.setHorizontalAlignment(SwingConstants.CENTER);
        imageLabel.setVerticalAlignment(SwingConstants.CENTER);
        imageLabel.setText("正在加载图片...");
        imageLabel.setOpaque(true);
        imageLabel.setBackground(Color.WHITE);

        scrollPane = new JBScrollPane(imageLabel);
        scrollPane.setPreferredSize(JBUI.size(780, 580));
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        return mainPanel;
    }

    private void loadAndDisplayImage() {
        try {
            File imageFile = new File(imagePath);
            if (!imageFile.exists()) {
                imageLabel.setText("图片文件不存在: " + imagePath);
                return;
            }

            BufferedImage image = ImageIO.read(imageFile);
            if (image != null) {
                displayImage(image, imageFile.getName());
            } else {
                imageLabel.setText("无法读取图片文件 (可能是 SVG 格式不支持): " + imagePath);
            }
        } catch (IOException ex) {
            imageLabel.setText("读取图片文件时发生错误: " + ex.getMessage());
        }
    }

    private void displayImage(BufferedImage image, String imageName) {
        // 获取滚动面板的尺寸
        Dimension scrollSize = scrollPane.getPreferredSize();

        // 计算缩放比例以适应显示区域
        double scaleX = (double) (scrollSize.width - 20) / image.getWidth();
        double scaleY = (double) (scrollSize.height - 20) / image.getHeight();
        double scale = Math.min(scaleX, scaleY);
        scale = Math.min(scale, 1.0); // 不放大图片

        int scaledWidth = (int) (image.getWidth() * scale);
        int scaledHeight = (int) (image.getHeight() * scale);

        // 创建缩放后的图片
        Image scaledImage = image.getScaledInstance(scaledWidth, scaledHeight, Image.SCALE_SMOOTH);
        ImageIcon icon = new ImageIcon(scaledImage);

        imageLabel.setIcon(icon);
        imageLabel.setText(null);
        imageLabel.setToolTipText(String.format("%s (%dx%d)", imageName, image.getWidth(), image.getHeight()));

        // 调整标签大小以适应图片
        imageLabel.setPreferredSize(new Dimension(scaledWidth, scaledHeight));
        scrollPane.revalidate();
        scrollPane.repaint();
    }

    @Override
    protected Action [] createActions() {
        return new Action[] {};
    }
}

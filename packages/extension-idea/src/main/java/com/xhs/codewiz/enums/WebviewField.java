package com.xhs.codewiz.enums;

/**
 * <AUTHOR>
 * @date 2025/7/22 14:33
 */
public enum WebviewField {
    BADGE("badge", "Webview 图标右上角数字提示"),
    OPTIONS("options", "Webview 选项配置"),
    DESCRIPTION("description", "Webview 的描述"),
    HTML("html", "Webview 的 HTML 内容"),
    TITLE("title", "Webview 的标题");

    private final String key;
    private final String desc;

    WebviewField(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据key获取对应的枚举值
     */
    public static WebviewField fromKey(String key) {
        for (WebviewField field : values()) {
            if (field.getKey().equals(key)) {
                return field;
            }
        }
        return null;
    }
}

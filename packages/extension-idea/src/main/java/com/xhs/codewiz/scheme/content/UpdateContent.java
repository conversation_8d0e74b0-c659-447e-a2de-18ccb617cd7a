package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.ChangeReason;
import com.xhs.codewiz.type.content.Changes;
import com.xhs.codewiz.type.file.Uri;
/**
 * 更新文本内容
 */
public class UpdateContent {
    private String schemaProtocol = "content.update.content";
    private UpdateContentParams params;

    public UpdateContentParams getParams() {
        return params;
    }
    public void setParams(UpdateContentParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateContentParams {
        private ChangeReason reason; // optional
        private Changes changes;
        private String uri;
    
        public ChangeReason getReason() {
            return reason;
        }
        public void setReason(ChangeReason reason) {
            this.reason = reason;
        }
        public Changes getChanges() {
            return changes;
        }
        public void setChanges(Changes changes) {
            this.changes = changes;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

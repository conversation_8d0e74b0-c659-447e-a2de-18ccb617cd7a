package com.xhs.codewiz.scheme.workspace;

import java.util.*;
/**
 * Workspace 配置项
 */
public class CreateConfigurations {
    private String schemaProtocol = "workspace.create.configurations";
    /** 配置项的键值对 */
    private Map<String, Object> params;

    public Map<String, Object> getParams() {
        return params;
    }
    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

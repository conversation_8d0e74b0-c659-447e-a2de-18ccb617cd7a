package com.xhs.codewiz.terminal;

import com.google.common.collect.Lists;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.terminal.JBTerminalPanel;
import com.jediterm.terminal.model.TerminalTextBuffer;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.plugins.terminal.ShellTerminalWidget;

/**
 * Author: liukunpeng Date: 2025-07-27 Description:
 */
public class TerminalUtil {
  private static final Logger logger = Logger.getInstance(TerminalUtil.class);
  public static String terminalWindowId = "Terminal";

  /**
   * 这个是用来获取输出的
   * 命令执行后，没有api/监听事件可以获取输出完毕，所以这里需要牺牲时间
   * TODO 后边去看下是不是有隐藏的api可用
   * @param widget
   * @return
   */
  public static List<String> getTerminalText(ShellTerminalWidget widget) {
    int lastLen = 0;
    int equalsCount = 0;
    while (true) {
      ThreadUtil.sleep(500);
      List<String> terminalText = getCurrentTerminalText(widget);
      if (terminalText.size() == lastLen) {
        equalsCount++;
      }
      if (equalsCount > 3) {
        return terminalText;
      }
      lastLen = terminalText.size();
    }
  }

  public static List<String> getCurrentTerminalText(ShellTerminalWidget widget) {
    List<String> res = Lists.newArrayList();
    if (widget == null) {
      return res;
    }
    JBTerminalPanel terminalPanel = widget.getTerminalPanel();

    TerminalTextBuffer buffer = terminalPanel.getTerminalTextBuffer();
    buffer.lock();
    try {
      String history = buffer.getHistoryBuffer().getLines();
      String screen = buffer.getScreenLines();
      String selectionText = StringUtils.isBlank(history) ? screen : (history + screen);
      if (StringUtils.isNotEmpty(selectionText)) {
        res.addAll(selectionText.lines().toList());
        res.removeIf(StringUtils::isBlank);
      }
    } catch (Throwable e) {
      LoggerUtil.INSTANCE.logWarn(logger, "TerminalUtils getText error", e);
    } finally {
      buffer.unlock();
    }
    return res;
  }

  /**
   * 尝试获取命令输入行的前缀+坐标
   * @param terminalText
   * @param index
   * @param projectName
   * @return
   */
  public static Pair<String, Integer> getTerminalAskPrefix(List<String> terminalText,  int index, String prefix, String projectName) {
    if (StringUtils.isEmpty(prefix)) {
      //没有时走兜底逻辑
      return getTerminalAskPrefix(terminalText, index, projectName);
    }
    List<Integer> indexList = Lists.newArrayList(index);
    int prev = index - 1;
    int next = index + 1;
    if (prev >= 0) {
      indexList.add(prev);
    }
    if (next < terminalText.size()) {
      indexList.add(next);
    }
    String input;
    //在预期行附近寻找(createTerminal存在延迟，所以可能会错位一行)
    for (int order : indexList) {
      input = terminalText.get(order).trim();
      if (StringUtils.startsWith(input, prefix)) {
        return Pair.of(prefix, order + 1);
      }
    }
    return Pair.of(prefix, index);
  }

  private static Pair<String, Integer> getTerminalAskPrefix(List<String> terminalText,  int index, String projectName) {
    List<Integer> indexList = Lists.newArrayList(index);
    indexList.add(index);
    int prev = index - 1;
    int next = index + 1;
    if (prev >= 0) {
      indexList.add(prev);
    }
    if (next < terminalText.size()) {
      indexList.add(next);
    }
    String input;
    //在预期行附近寻找(createTerminal存在延迟，所以可能会错位一行)
    for (int order : indexList) {
      input = terminalText.get(order).trim();
      //1.尝试使用project判断
      if (StringUtils.contains(input, projectName + " %")) {
        String prefix = input.substring(0, input.indexOf(projectName + " %") + projectName.length() + 1);
        return Pair.of(prefix, order + 1);
      }
      if (StringUtils.contains(input, projectName + " $")) {
        String prefix = input.substring(0, input.indexOf(projectName + " $") + projectName.length() + 1);
        return Pair.of(prefix, order + 1);
      }
      if (StringUtils.contains(input, projectName + " #")) {
        String prefix = input.substring(0, input.indexOf(projectName + " #") + projectName.length() + 1);
        return Pair.of(prefix, order + 1);
      }
      if (StringUtils.contains(input, projectName) && StringUtils.contains(input, "git:(")) {
        String prefix = input.substring(0, input.indexOf(")") + 1);
        return Pair.of(prefix, order + 1);
      }
    }


    //2.实在找不到，开始从头开始找
    for (int i = 0; i < terminalText.size(); i++) {
      input = terminalText.get(i).trim();
      if (StringUtils.contains(input, projectName + " %")) {
        String prefix = input.substring(0, input.indexOf(projectName + " #") + projectName.length() + 1);
        return Pair.of(prefix, index);
      }
      if (StringUtils.contains(input, projectName + " $")) {
        String prefix = input.substring(0, input.indexOf(projectName + " $") + projectName.length() + 1);
        return Pair.of(prefix, index);
      }
      if (StringUtils.contains(input, projectName + " #")) {
        String prefix = input.substring(0, input.indexOf(projectName + " #") + projectName.length() + 1);
        return Pair.of(prefix, index);
      }
      if (StringUtils.contains(input, projectName) && StringUtils.contains(input, "git:(")) {
        String prefix = input.substring(0, input.indexOf(")") + 1);
        return Pair.of(prefix, index);
      }
    }
    return Pair.of(null, index);
  }
}

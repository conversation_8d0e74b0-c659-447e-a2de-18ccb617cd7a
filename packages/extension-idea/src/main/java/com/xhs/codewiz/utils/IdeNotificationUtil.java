package com.xhs.codewiz.utils;

import com.intellij.notification.Notification;
import com.intellij.notification.NotificationType;
import com.intellij.notification.Notifications;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;

/**
 * Author: liukunpeng Date: 2025-07-24 Description:
 */
public class IdeNotificationUtil {

    private static final Logger log = Logger.getInstance(IdeNotificationUtil.class);

    public static void showMessage(String type, String text, Project project) {
        ApplicationManager.getApplication().invokeLater(() -> {
            NotificationType notificationType = null;
            switch (type) {
                case "info":
                    notificationType = NotificationType.INFORMATION;
                    break;
                case "warn":
                    notificationType = NotificationType.WARNING;
                    break;
                case "error":
                    notificationType = NotificationType.ERROR;
                    break;
                default:
                    return;
            }
            Notification notification = new Notification(
                    BundleUtil.get("codewiz.plugin.id"),
                    BundleUtil.get("notifications.rcs.group.name"),
                    text,
                    notificationType);
            notification.setIcon(IconsUtil.CODEWIZ);
            Notifications.Bus.notify(notification, project);
        });
    }
}

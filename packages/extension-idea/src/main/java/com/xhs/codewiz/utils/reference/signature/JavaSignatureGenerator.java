package com.xhs.codewiz.utils.reference.signature;

import com.intellij.lang.jvm.JvmClassKind;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiAnnotation;
import com.intellij.psi.PsiClass;
import com.intellij.psi.PsiClassType;
import com.intellij.psi.PsiCompiledElement;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiEnumConstant;
import com.intellij.psi.PsiField;
import com.intellij.psi.PsiJavaCodeReferenceElement;
import com.intellij.psi.PsiJavaFile;
import com.intellij.psi.PsiMethod;
import com.intellij.psi.PsiModifier;
import com.intellij.psi.PsiModifierList;
import com.intellij.psi.PsiParameter;
import com.intellij.psi.PsiType;
import com.intellij.psi.PsiTypeParameter;
import com.intellij.psi.PsiTypeParameterList;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class JavaSignatureGenerator
    implements SignatureGenerator {
  private static final Logger logger = Logger.getInstance(JavaSignatureGenerator.class);
  private final Config config;

  public JavaSignatureGenerator(Config cfg) {
    this.config = cfg;
  }

  public JavaSignatureGenerator() {
    this.config = new Config();
  }

  @Override
  @NotNull
  public String generateSignature(@NotNull PsiElement element) {
    if (element == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(0);
    }
    if (this.config.rawTextIfNotCompiled && !(element instanceof PsiCompiledElement)) {
      logger.info("psi element is not compiled, raw text will return");
      String string = element.getText();
      if (string == null) {
        JavaSignatureGenerator.$$$reportNull$$$0(1);
      }
      return string;
    }
    try {
      if (element instanceof PsiJavaFile) {
        return this.generateFileSignature((PsiJavaFile)element);
      }
      if (element instanceof PsiClass) {
        return this.generateClassSignature((PsiClass)element);
      }
      if (element instanceof PsiMethod) {
        return this.generateMethodSignature((PsiMethod)element);
      }
      logger.info(String.format("unsupported psi element[%s]", element.getClass()));
    }
    catch (Exception e) {
      logger.info((Throwable)e);
    }
    logger.info(String.format("fallback to get raw text of psi element[%s], which might be extremely slow", element.getClass()));
    String string = element.getText();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(2);
    }
    return string;
  }

  @NotNull
  private String generateFileSignature(@NotNull PsiJavaFile aFile) throws Exception {
    String packageName;
    if (aFile == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(3);
    }
    Context ctx = new Context();
    StringBuilder sb = new StringBuilder();
    ctx.packageName = packageName = aFile.getPackageName();
    if (!packageName.isBlank()) {
      sb.append("package ").append(packageName).append(";");
      this.addBlankLineIfNeeded(sb);
      this.addBlankLineIfNeeded(sb);
    }
    StringBuilder sbc = new StringBuilder();
    for (PsiClass aClass : aFile.getClasses()) {
      sbc.append(this.generateClassSignature(ctx, aClass));
      this.addBlankLineIfNeeded(sbc);
    }
    Object[] referenceQualifiedNames = ctx.getReferenceQualifiedNames();
    Arrays.sort(referenceQualifiedNames);
    for (Object qualifiedName : referenceQualifiedNames) {
      sb.append("import ").append((String)qualifiedName).append(";");
      this.addBlankLineIfNeeded(sb);
    }
    if (referenceQualifiedNames.length > 0) {
      this.addBlankLineIfNeeded(sb);
    }
    sb.append((CharSequence)sbc);
    String string = sb.toString();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(4);
    }
    return string;
  }

  @NotNull
  private String generateClassSignature(@NotNull PsiClass aClass) throws Exception {
    if (aClass == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(5);
    }
    Context ctx = new Context();
    return this.generateClassSignature(ctx, aClass);
  }

  @NotNull
  private String generateClassSignature(@NotNull Context ctx, @NotNull PsiClass aClass) throws Exception {
    PsiClassType[] implementsListTypes;
    int i;
    PsiClassType[] extendsListTypes;
    if (ctx == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(6);
    }
    if (aClass == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(7);
    }
    StringBuilder sb = new StringBuilder();
    this.appendAnnotations(ctx.indentLevel, this.config.newBlankLine, sb, aClass.getAnnotations());
    for (PsiAnnotation annotation : aClass.getAnnotations()) {
      ctx.addQualifiedNameToSet(annotation.getQualifiedName());
    }
    this.appendModifierList(ctx.indentLevel, sb, aClass.getModifierList());
    sb.append(this.formatClassKind(aClass.getClassKind())).append(' ');
    sb.append(aClass.getName());
    sb.append(' ');
    this.appendTypeParameterList(sb, aClass.getTypeParameterList());
    if (!aClass.isEnum() && !aClass.isAnnotationType() && (extendsListTypes = aClass.getExtendsListTypes()).length > 0) {
      sb.append("extends ");
      for (i = 0; i < extendsListTypes.length; ++i) {
        if (i > 0) {
          sb.append(", ");
        }
        sb.append(extendsListTypes[i].getPresentableText());
        ctx.addTypeToSet((PsiType)extendsListTypes[i]);
      }
      sb.append(' ');
    }
    if (!(aClass.isEnum() || aClass.isInterface() || aClass.isAnnotationType() || (implementsListTypes = aClass.getImplementsListTypes()).length <= 0)) {
      sb.append("implements ");
      for (i = 0; i < implementsListTypes.length; ++i) {
        if (i > 0) {
          sb.append(", ");
        }
        sb.append(implementsListTypes[i].getPresentableText());
        ctx.addTypeToSet((PsiType)implementsListTypes[i]);
      }
      sb.append(' ');
    }
    sb.append("{");
    this.addBlankLineIfNeeded(sb);
    ArrayList<PsiEnumConstant> enumFields = new ArrayList<PsiEnumConstant>();
    ArrayList<PsiField> normalFields = new ArrayList<PsiField>();
    PsiField[] psiFieldArray = aClass.getFields();
    int annotation = psiFieldArray.length;
    for (int j = 0; j < annotation; ++j) {
      PsiField psiField = psiFieldArray[j];
      ctx.addTypeToSet(psiField.getType());
      if (psiField instanceof PsiEnumConstant) {
        enumFields.add((PsiEnumConstant)psiField);
        continue;
      }
      normalFields.add(psiField);
    }
    if (!enumFields.isEmpty()) {
      for (int i2 = 0; i2 < enumFields.size(); ++i2) {
        String enumConstantText = this.formatEnumConstant((PsiEnumConstant)enumFields.get(i2));
        if (enumConstantText.isBlank()) continue;
        if (this.config.newBlankLine) {
          this.appendWithIndent(ctx.indentLevel + 1, sb, enumConstantText);
        } else {
          sb.append(enumConstantText);
        }
        if (i2 < enumFields.size() - 1) {
          sb.append(",");
        } else {
          sb.append(";");
        }
        this.addBlankLineIfNeeded(sb);
      }
      this.addBlankLineIfNeeded(sb);
    }
    if (!normalFields.isEmpty()) {
      ++ctx.indentLevel;
      for (PsiField field : normalFields) {
        sb.append(this.formatField(ctx, field));
        sb.append(";");
        this.addBlankLineIfNeeded(sb);
      }
      --ctx.indentLevel;
    }
    ++ctx.indentLevel;
    for (PsiMethod psiMethod : aClass.getMethods()) {
      sb.append(this.generateMethodSignature(ctx, psiMethod));
      if (psiMethod.isConstructor()) {
        sb.append(" {}");
      } else {
        sb.append(";");
      }
      this.addBlankLineIfNeeded(sb);
    }
    --ctx.indentLevel;
    ++ctx.indentLevel;
    for (PsiClass psiClass : aClass.getInnerClasses()) {
      sb.append(this.generateClassSignature(ctx, psiClass));
    }
    --ctx.indentLevel;
    this.appendWithIndent(ctx.indentLevel, sb, "}");
    this.addBlankLineIfNeeded(sb);
    String string = sb.toString();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(8);
    }
    return string;
  }

  @NotNull
  private String generateMethodSignature(@NotNull PsiMethod aMethod) {
    if (aMethod == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(9);
    }
    Context ctx = new Context();
    return this.generateMethodSignature(ctx, aMethod);
  }

  @NotNull
  private String generateMethodSignature(@NotNull Context ctx, @NotNull PsiMethod aMethod) {
    if (ctx == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(10);
    }
    if (aMethod == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(11);
    }
    StringBuilder sb = new StringBuilder();
    PsiType returnType = aMethod.getReturnType();
    ctx.addTypeToSet(returnType);
    ArrayList<PsiAnnotation> methodAnnotations = new ArrayList<PsiAnnotation>();
    if (returnType != null) {
      HashSet<String> returnTypeAnnotationSet = new HashSet<String>();
      for (PsiAnnotation aAnnotation : returnType.getAnnotations()) {
        returnTypeAnnotationSet.add(aAnnotation.getQualifiedName());
      }
      for (PsiAnnotation aAnnotation : aMethod.getAnnotations()) {
        if (!returnTypeAnnotationSet.contains(aAnnotation.getQualifiedName())) {
          methodAnnotations.add(aAnnotation);
        }
        ctx.addQualifiedNameToSet(aAnnotation.getQualifiedName());
      }
    } else {
      methodAnnotations.addAll(List.of(aMethod.getAnnotations()));
    }
    this.appendAnnotations(ctx.indentLevel, this.config.newBlankLine, sb, methodAnnotations.toArray(PsiAnnotation.EMPTY_ARRAY));
    this.appendModifierList(ctx.indentLevel, sb, aMethod.getModifierList());
    if (aMethod.hasModifierProperty("static")) {
      this.appendTypeParameterList(sb, aMethod.getTypeParameterList());
    }
    if (returnType != null) {
      this.appendAnnotations(0, false, sb, returnType.getAnnotations());
      sb.append(returnType.getPresentableText());
      sb.append(' ');
    }
    sb.append(aMethod.getName());
    sb.append('(');
    PsiParameter[] parameters = aMethod.getParameterList().getParameters();
    if (parameters.length > 0) {
      for (int i = 0; i < parameters.length; ++i) {
        if (i > 0) {
          sb.append(", ");
        }
        PsiParameter parameter = parameters[i];
        ctx.addTypeToSet(parameter.getType());
        this.appendAnnotations(0, false, sb, parameter.getAnnotations());
        sb.append(parameters[i].getType().getPresentableText()).append(' ').append(parameters[i].getName());
      }
    }
    sb.append(')');
    PsiClassType[] thrownTypes = aMethod.getThrowsList().getReferencedTypes();
    if (thrownTypes.length > 0) {
      sb.append(" throws ");
      for (int i = 0; i < thrownTypes.length; ++i) {
        if (i > 0) {
          sb.append(", ");
        }
        ctx.addTypeToSet((PsiType)thrownTypes[i]);
        sb.append(thrownTypes[i].getPresentableText());
      }
    }
    String string = sb.toString();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(12);
    }
    return string;
  }

  @NotNull
  private String formatField(@NotNull Context ctx, @NotNull PsiField aField) {
    if (ctx == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(13);
    }
    if (aField == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(14);
    }
    StringBuilder sb = new StringBuilder();
    this.appendAnnotations(ctx.indentLevel, this.config.newBlankLine, sb, aField.getAnnotations());
    this.appendModifierList(ctx.indentLevel, sb, aField.getModifierList());
    sb.append(aField.getType().getPresentableText()).append(' ');
    sb.append(aField.getName());
    String string = sb.toString();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(15);
    }
    return string;
  }

  @NotNull
  private String formatEnumConstant(@NotNull PsiEnumConstant aEnumConstant) {
    if (aEnumConstant == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(16);
    }
    StringBuilder sb = new StringBuilder();
    sb.append(aEnumConstant.getName());
    String string = sb.toString();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(17);
    }
    return string;
  }

  @NotNull
  private String formatModifierList(@NotNull PsiModifierList aModifierList) {
    if (aModifierList == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(18);
    }
    StringBuilder sb = new StringBuilder();
    ArrayList<String> explicitModifierList = new ArrayList<String>();
    for (String modifier : PsiModifier.MODIFIERS) {
      if (Objects.equals(modifier, "abstract") || !aModifierList.hasModifierProperty(modifier)) continue;
      explicitModifierList.add(modifier);
    }
    if (!explicitModifierList.isEmpty()) {
      sb.append(String.join((CharSequence)" ", explicitModifierList));
    }
    String string = sb.toString();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(19);
    }
    return string;
  }

  @NotNull
  private String formatTypeParameterList(@NotNull PsiTypeParameterList aTypeParameterList) {
    if (aTypeParameterList == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(20);
    }
    StringBuilder sb = new StringBuilder();
    PsiTypeParameter[] typeParameters = aTypeParameterList.getTypeParameters();
    if (typeParameters.length > 0) {
      sb.append("<");
      for (PsiTypeParameter typeParameter : typeParameters) {
        sb.append(typeParameter.getName()).append(", ");
      }
      sb.delete(sb.length() - 2, sb.length());
      sb.append(">");
    }
    String string = sb.toString();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(21);
    }
    return string;
  }

  @NotNull
  private String formatClassKind(JvmClassKind aClassKind) throws Exception {
    if (aClassKind == JvmClassKind.CLASS) {
      return "class";
    }
    if (aClassKind == JvmClassKind.INTERFACE) {
      return "interface";
    }
    if (aClassKind == JvmClassKind.ENUM) {
      return "enum";
    }
    if (aClassKind == JvmClassKind.ANNOTATION) {
      return "@interface";
    }
    throw new Exception("invalid class kind: " + aClassKind);
  }

  @NotNull
  private String formatAnnotation(@NotNull PsiAnnotation aAnnotation) {
    if (aAnnotation == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(22);
    }
    StringBuilder sb = new StringBuilder();
    PsiJavaCodeReferenceElement ref = aAnnotation.getNameReferenceElement();
    if (ref == null) {
      return "";
    }
    sb.append("@");
    sb.append(ref.getReferenceName());
    String string = sb.toString();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(23);
    }
    return string;
  }

  @NotNull
  private String formatAnnotations(int indentLevel, boolean newBlankLine, @NotNull PsiAnnotation[] annotations) {
    if (annotations == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(24);
    }
    StringBuilder sb = new StringBuilder();
    for (PsiAnnotation annotation : annotations) {
      String annotationText = this.formatAnnotation(annotation);
      if (annotationText.isBlank()) continue;
      if (newBlankLine) {
        this.appendWithIndent(indentLevel, sb, annotationText);
        sb.append('\n');
        continue;
      }
      sb.append(annotationText);
    }
    if (annotations.length > 0 && !newBlankLine) {
      sb.append(' ');
    }
    String string = sb.toString();
    if (string == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(25);
    }
    return string;
  }

  private void appendAnnotations(int indentLevel, boolean newBlankLine, @NotNull StringBuilder sb, @Nullable PsiAnnotation[] annotations) {
    if (sb == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(26);
    }
    if (annotations == null) {
      return;
    }
    String annotationsText = this.formatAnnotations(indentLevel, newBlankLine, annotations);
    if (!annotationsText.isBlank()) {
      sb.append(annotationsText);
    }
  }

  private void appendModifierList(int indentLevel, @NotNull StringBuilder sb, @Nullable PsiModifierList aModifierList) {
    if (sb == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(27);
    }
    String modifierListText = "";
    if (aModifierList != null && (modifierListText = this.formatModifierList(aModifierList)).isBlank()) {
      modifierListText = "";
    }
    this.appendWithIndent(indentLevel, sb, modifierListText);
    if (!modifierListText.isEmpty()) {
      sb.append(' ');
    }
  }

  private void appendTypeParameterList(@NotNull StringBuilder sb, @Nullable PsiTypeParameterList aTypeParameterList) {
    if (sb == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(28);
    }
    if (aTypeParameterList == null) {
      return;
    }
    String typeParameterListText = this.formatTypeParameterList(aTypeParameterList);
    sb.append(typeParameterListText);
    if (!typeParameterListText.isBlank()) {
      sb.append(' ');
    }
  }

  private void addBlankLineIfNeeded(@NotNull StringBuilder sb) {
    if (sb == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(29);
    }
    if (this.config.newBlankLine) {
      sb.append('\n');
    }
  }

  private void appendWithIndent(int indentLevel, @NotNull StringBuilder sb, @NotNull String str) {
    if (sb == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(30);
    }
    if (str == null) {
      JavaSignatureGenerator.$$$reportNull$$$0(31);
    }
    int spaceLength = indentLevel * this.config.indentLength;
    sb.append(" ".repeat(Math.max(0, spaceLength)));
    sb.append(str);
  }

  private static /* synthetic */ void $$$reportNull$$$0(int n) {
    RuntimeException runtimeException;
    Object[] objectArray;
    Object[] objectArray2;
    int n2;
    String string;
    switch (n) {
      default: {
        string = "Argument for @NotNull parameter '%s' of %s.%s must not be null";
        break;
      }
      case 1:
      case 2:
      case 4:
      case 8:
      case 12:
      case 15:
      case 17:
      case 19:
      case 21:
      case 23:
      case 25: {
        string = "@NotNull method %s.%s must not return null";
        break;
      }
    }
    switch (n) {
      default: {
        n2 = 3;
        break;
      }
      case 1:
      case 2:
      case 4:
      case 8:
      case 12:
      case 15:
      case 17:
      case 19:
      case 21:
      case 23:
      case 25: {
        n2 = 2;
        break;
      }
    }
    Object[] objectArray3 = new Object[n2];
    switch (n) {
      default: {
        objectArray2 = objectArray3;
        objectArray3[0] = "element";
        break;
      }
      case 1:
      case 2:
      case 4:
      case 8:
      case 12:
      case 15:
      case 17:
      case 19:
      case 21:
      case 23:
      case 25: {
        objectArray2 = objectArray3;
        objectArray3[0] = "com/alibabacloud/intellij/cosy/linguist/reference/signature/JavaSignatureGenerator";
        break;
      }
      case 3: {
        objectArray2 = objectArray3;
        objectArray3[0] = "aFile";
        break;
      }
      case 5:
      case 7: {
        objectArray2 = objectArray3;
        objectArray3[0] = "aClass";
        break;
      }
      case 6:
      case 10:
      case 13: {
        objectArray2 = objectArray3;
        objectArray3[0] = "ctx";
        break;
      }
      case 9:
      case 11: {
        objectArray2 = objectArray3;
        objectArray3[0] = "aMethod";
        break;
      }
      case 14: {
        objectArray2 = objectArray3;
        objectArray3[0] = "aField";
        break;
      }
      case 16: {
        objectArray2 = objectArray3;
        objectArray3[0] = "aEnumConstant";
        break;
      }
      case 18: {
        objectArray2 = objectArray3;
        objectArray3[0] = "aModifierList";
        break;
      }
      case 20: {
        objectArray2 = objectArray3;
        objectArray3[0] = "aTypeParameterList";
        break;
      }
      case 22: {
        objectArray2 = objectArray3;
        objectArray3[0] = "aAnnotation";
        break;
      }
      case 24: {
        objectArray2 = objectArray3;
        objectArray3[0] = "annotations";
        break;
      }
      case 26:
      case 27:
      case 28:
      case 29:
      case 30: {
        objectArray2 = objectArray3;
        objectArray3[0] = "sb";
        break;
      }
      case 31: {
        objectArray2 = objectArray3;
        objectArray3[0] = "str";
        break;
      }
    }
    switch (n) {
      default: {
        objectArray = objectArray2;
        objectArray2[1] = "com/alibabacloud/intellij/cosy/linguist/reference/signature/JavaSignatureGenerator";
        break;
      }
      case 1:
      case 2: {
        objectArray = objectArray2;
        objectArray2[1] = "generateSignature";
        break;
      }
      case 4: {
        objectArray = objectArray2;
        objectArray2[1] = "generateFileSignature";
        break;
      }
      case 8: {
        objectArray = objectArray2;
        objectArray2[1] = "generateClassSignature";
        break;
      }
      case 12: {
        objectArray = objectArray2;
        objectArray2[1] = "generateMethodSignature";
        break;
      }
      case 15: {
        objectArray = objectArray2;
        objectArray2[1] = "formatField";
        break;
      }
      case 17: {
        objectArray = objectArray2;
        objectArray2[1] = "formatEnumConstant";
        break;
      }
      case 19: {
        objectArray = objectArray2;
        objectArray2[1] = "formatModifierList";
        break;
      }
      case 21: {
        objectArray = objectArray2;
        objectArray2[1] = "formatTypeParameterList";
        break;
      }
      case 23: {
        objectArray = objectArray2;
        objectArray2[1] = "formatAnnotation";
        break;
      }
      case 25: {
        objectArray = objectArray2;
        objectArray2[1] = "formatAnnotations";
        break;
      }
    }
    switch (n) {
      default: {
        objectArray = objectArray;
        objectArray[2] = "generateSignature";
        break;
      }
      case 1:
      case 2:
      case 4:
      case 8:
      case 12:
      case 15:
      case 17:
      case 19:
      case 21:
      case 23:
      case 25: {
        break;
      }
      case 3: {
        objectArray = objectArray;
        objectArray[2] = "generateFileSignature";
        break;
      }
      case 5:
      case 6:
      case 7: {
        objectArray = objectArray;
        objectArray[2] = "generateClassSignature";
        break;
      }
      case 9:
      case 10:
      case 11: {
        objectArray = objectArray;
        objectArray[2] = "generateMethodSignature";
        break;
      }
      case 13:
      case 14: {
        objectArray = objectArray;
        objectArray[2] = "formatField";
        break;
      }
      case 16: {
        objectArray = objectArray;
        objectArray[2] = "formatEnumConstant";
        break;
      }
      case 18: {
        objectArray = objectArray;
        objectArray[2] = "formatModifierList";
        break;
      }
      case 20: {
        objectArray = objectArray;
        objectArray[2] = "formatTypeParameterList";
        break;
      }
      case 22: {
        objectArray = objectArray;
        objectArray[2] = "formatAnnotation";
        break;
      }
      case 24: {
        objectArray = objectArray;
        objectArray[2] = "formatAnnotations";
        break;
      }
      case 26: {
        objectArray = objectArray;
        objectArray[2] = "appendAnnotations";
        break;
      }
      case 27: {
        objectArray = objectArray;
        objectArray[2] = "appendModifierList";
        break;
      }
      case 28: {
        objectArray = objectArray;
        objectArray[2] = "appendTypeParameterList";
        break;
      }
      case 29: {
        objectArray = objectArray;
        objectArray[2] = "addBlankLineIfNeeded";
        break;
      }
      case 30:
      case 31: {
        objectArray = objectArray;
        objectArray[2] = "appendWithIndent";
        break;
      }
    }
    String string2 = String.format(string, objectArray);
    switch (n) {
      default: {
        runtimeException = new IllegalArgumentException(string2);
        break;
      }
      case 1:
      case 2:
      case 4:
      case 8:
      case 12:
      case 15:
      case 17:
      case 19:
      case 21:
      case 23:
      case 25: {
        runtimeException = new IllegalStateException(string2);
        break;
      }
    }
    throw runtimeException;
  }

  private static class Context {
    int indentLevel = 0;
    String packageName = "";
    Set<String> referenceQualifiedNameSet = new HashSet<String>();
    private static final List<String> DEFAULT_PACKAGES = List.of("java.lang.");
    private static final String JAVA_IDENTIFIER_REGEX = "^[A-Za-z_$][A-Za-z0-9_$]*$";
    private static final Pattern JAVA_IDENTIFIER_PATTERN = Pattern.compile("^[A-Za-z_$][A-Za-z0-9_$]*$");

    Context() {
    }

    private void addTypeToSet(PsiType type) {
      this.addTypeToSet(type, true, true);
    }

    private void addTypeToSet(PsiType type, boolean skipDefault, boolean skipInPackage) {
      PsiClass aClass;
      if (type instanceof PsiClassType && (aClass = ((PsiClassType)type).resolve()) != null) {
        String qualifiedName = aClass.getQualifiedName();
        this.addQualifiedNameToSet(qualifiedName, skipDefault, skipInPackage);
      }
    }

    private void addQualifiedNameToSet(@Nullable String qualifiedName) {
      this.addQualifiedNameToSet(qualifiedName, true, true);
    }

    private void addQualifiedNameToSet(@Nullable String qualifiedName, boolean skipDefault, boolean skipInPackage) {
      if (qualifiedName == null) {
        return;
      }
      if (skipDefault && Context.isDefaultPackage(qualifiedName)) {
        return;
      }
      if (skipInPackage && Context.isInPackage(this.packageName, qualifiedName)) {
        return;
      }
      this.referenceQualifiedNameSet.add(qualifiedName);
    }

    private String[] getReferenceQualifiedNames() {
      return this.referenceQualifiedNameSet.toArray(new String[0]);
    }

    private static boolean isDefaultPackage(String fullClassName) {
      for (String defaultPackage : DEFAULT_PACKAGES) {
        if (!fullClassName.startsWith(defaultPackage)) continue;
        return true;
      }
      return false;
    }

    public static boolean isValidClassName(String className) {
      if (className == null || className.isEmpty()) {
        return false;
      }
      return JAVA_IDENTIFIER_PATTERN.matcher(className).matches();
    }

    private static boolean isInPackage(String packageName, String fullClassName) {
      if (!fullClassName.startsWith(packageName + ".")) {
        return false;
      }
      String className = fullClassName.substring(packageName.length() + 1);
      return Context.isValidClassName(className);
    }
  }

  public static class Config {
    private final boolean rawTextIfNotCompiled;
    private final boolean newBlankLine;
    private final int indentLength;

    Config() {
      this.rawTextIfNotCompiled = false;
      this.newBlankLine = true;
      this.indentLength = 4;
    }

    Config(boolean rawTextIfNotCompiled, boolean newBlankLine, int indentLength) {
      this.rawTextIfNotCompiled = rawTextIfNotCompiled;
      this.newBlankLine = newBlankLine;
      if (indentLength < 0) {
        indentLength = 0;
      }
      if (indentLength > 8) {
        indentLength = 8;
      }
      this.indentLength = indentLength;
    }
  }
}
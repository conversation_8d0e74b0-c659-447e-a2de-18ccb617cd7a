package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.file.Uri;
/**
 * 更新选区
 */
public class UpdateRanges {
    private String schemaProtocol = "content.update.ranges";
    private UpdateRangesParams params;

    public UpdateRangesParams getParams() {
        return params;
    }
    public void setParams(UpdateRangesParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class UpdateRangesParams {
        private List<Range> ranges;
        private UpdateRangesParamsOptions options; // optional
        private String uri;
    
        public List<Range> getRanges() {
            return ranges;
        }
        public void setRanges(List<Range> ranges) {
            this.ranges = ranges;
        }
        public UpdateRangesParamsOptions getOptions() {
            return options;
        }
        public void setOptions(UpdateRangesParamsOptions options) {
            this.options = options;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }

        public static class UpdateRangesParamsOptions {
        /** 是否在更新后自动滚动到选区位置 */
        private Boolean reveal;
    
        public Boolean getReveal() {
            return reveal;
        }
        public void setReveal(Boolean reveal) {
            this.reveal = reveal;
        }
    }
}

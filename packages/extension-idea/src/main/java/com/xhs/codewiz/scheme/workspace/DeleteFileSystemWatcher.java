package com.xhs.codewiz.scheme.workspace;

import java.util.*;
/**
 * 删除文件系统观察者
 */
public class DeleteFileSystemWatcher {
    private String schemaProtocol = "workspace.delete.filesystemwatcher";
    /** 删除的文件系统观察者 ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

package com.xhs.codewiz.type.platform;

import java.util.*;
import com.xhs.codewiz.type.global.Alignment;
import com.xhs.codewiz.type.global.Command;
import com.xhs.codewiz.type.global.MarkdownOrString;

/**
 * 状态栏项
 */
public class StatusBarItem {
    private String backgroundColor;

    private String color;

    /** 状态栏项的名称 */
    private String name;

    /** 状态栏项的提示信息 */
    private MarkdownOrString tooltip;

    /** 状态栏项 ID */
    private String id;

    /** 状态栏项的文本内容 */
    private String text;

    private Alignment alignment;

    /** 状态栏项的优先级，数值越大越靠中间显示 */
    private Integer priority;

    /** 点击状态栏项时执行的命令 */
    private Command command;

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public MarkdownOrString getTooltip() {
        return tooltip;
    }

    public void setTooltip(MarkdownOrString tooltip) {
        this.tooltip = tooltip;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Alignment getAlignment() {
        return alignment;
    }

    public void setAlignment(Alignment alignment) {
        this.alignment = alignment;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Command getCommand() {
        return command;
    }

    public void setCommand(Command command) {
        this.command = command;
    }

}

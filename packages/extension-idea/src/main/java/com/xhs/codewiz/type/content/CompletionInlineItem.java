package com.xhs.codewiz.type.content;

import java.util.*;
import com.xhs.codewiz.type.global.Command;

/**
 * 补全项
 */
public class CompletionInlineItem {
    /** 插入文本 */
    private String insertText;

    private Range range;

    /** 当输入匹配 filterText 时，补全项就会被显示。 */
    private String filterText;

    private Command command;

    public String getInsertText() {
        return insertText;
    }

    public void setInsertText(String insertText) {
        this.insertText = insertText;
    }

    public Range getRange() {
        return range;
    }

    public void setRange(Range range) {
        this.range = range;
    }

    public String getFilterText() {
        return filterText;
    }

    public void setFilterText(String filterText) {
        this.filterText = filterText;
    }

    public Command getCommand() {
        return command;
    }

    public void setCommand(Command command) {
        this.command = command;
    }

}

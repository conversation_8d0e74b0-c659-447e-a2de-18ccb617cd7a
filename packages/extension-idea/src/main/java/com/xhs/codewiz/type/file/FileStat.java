package com.xhs.codewiz.type.file;

import java.util.*;

/**
 * 文件状态信息
 */
public class FileStat {
    /** 文件大小，以字节为单位 */
    private Integer size;

    private Permissions permissions;

    /** 文件创建时间的时间戳（毫秒） */
    private Integer ctime;

    private FileType type;

    /** 文件最后修改时间的时间戳（毫秒） */
    private Integer mtime;

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Permissions getPermissions() {
        return permissions;
    }

    public void setPermissions(Permissions permissions) {
        this.permissions = permissions;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public FileType getType() {
        return type;
    }

    public void setType(FileType type) {
        this.type = type;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

}

package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.DocumentFilter;
/**
 * 搜索文件
 */
public class ReadSearch {
    private String schemaProtocol = "file.read.search";
    private ReadSearchParams params;

    public ReadSearchParams getParams() {
        return params;
    }
    public void setParams(ReadSearchParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadSearchParams {
        /** 要搜索的文件类型 */
        private List<DocumentFilter> include;
        /** 最大搜索结果数量, 默认 1000 */
        private Integer maxResults; // optional
        /** 要排除的文件类型 */
        private List<DocumentFilter> exclude; // optional
    
        public List<DocumentFilter> getInclude() {
            return include;
        }
        public void setInclude(List<DocumentFilter> include) {
            this.include = include;
        }
        public Integer getMaxResults() {
            return maxResults;
        }
        public void setMaxResults(Integer maxResults) {
            this.maxResults = maxResults;
        }
        public List<DocumentFilter> getExclude() {
            return exclude;
        }
        public void setExclude(List<DocumentFilter> exclude) {
            this.exclude = exclude;
        }
    }
}

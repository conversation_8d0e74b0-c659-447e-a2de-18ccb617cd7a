package com.xhs.codewiz.editor;


public enum InlayDisposeContext {
    UserAction, //这个是copilot用户手动触发的，我们没有这个场景
    IdeCompletion,
    CaretChange, //光标变更，但是没有文本变更，需要触发拒绝判定
    SelectionChange,
    SettingsChange,
    Cycling,
    TypingAsSuggested,
    Typing,
    Applied,
    SKIP; //这个是自定义需要跳过不补全的场景，需要触发拒绝判定

    private InlayDisposeContext() {
    }

    public boolean isResetLastRequest() {
        return this == SettingsChange || this == Applied || isSendRejectedTelemetry();
    }

    public boolean isSendRejectedTelemetry() {
        return this == UserAction || this == CaretChange || this == SKIP;
    }
    public boolean isSendCancelAfterShownTelemetry() {
        return this == Typing || this == TypingAsSuggested;
    }
}


package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.platform.SeriesName;
import com.xhs.codewiz.type.platform.PlatformName;
/**
 * 创建配置
 */
public class CreateConfigurations {
    private String schemaProtocol = "platform.create.configurations";
    private CreateConfigurationsParams params;

    public CreateConfigurationsParams getParams() {
        return params;
    }
    public void setParams(CreateConfigurationsParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateConfigurationsParams {
        /** 适配器名称，默认为idea、vscode插件版本号 */
        private String adapter;
        /** 当前机器的唯一标识 ID，理论上在同一台机器上该 ID 不会变，用于用户未登陆时进行辨识。 */
        private String machineId;
        /** 系列名称，如 vscode 系列，jetbrains系列等 */
        private SeriesName series;
        /** I10N */
        private String language;
        /** platform 版本号 */
        private String version;
        /** 平台名称，如 Android Studio、IntelliJ IDEA、VSCode Remote 等子平台 */
        private PlatformName platform;
    
        public String getAdapter() {
            return adapter;
        }
        public void setAdapter(String adapter) {
            this.adapter = adapter;
        }
        public String getMachineId() {
            return machineId;
        }
        public void setMachineId(String machineId) {
            this.machineId = machineId;
        }
        public SeriesName getSeries() {
            return series;
        }
        public void setSeries(SeriesName series) {
            this.series = series;
        }
        public String getLanguage() {
            return language;
        }
        public void setLanguage(String language) {
            this.language = language;
        }
        public String getVersion() {
            return version;
        }
        public void setVersion(String version) {
            this.version = version;
        }
        public PlatformName getPlatform() {
            return platform;
        }
        public void setPlatform(PlatformName platform) {
            this.platform = platform;
        }
    }
}

package com.xhs.codewiz.lang.agent.commands;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import org.jetbrains.annotations.NotNull;

public final class CancelRequestNotification implements JsonRpcNotification {
    @SerializedName("id")
    private final int requestId;

    public @NotNull String getCommandName() {
        return "$/cancelRequest";
    }

    public CancelRequestNotification(int requestId) {
        this.requestId = requestId;
    }

    public int getRequestId() {
        return this.requestId;
    }

}


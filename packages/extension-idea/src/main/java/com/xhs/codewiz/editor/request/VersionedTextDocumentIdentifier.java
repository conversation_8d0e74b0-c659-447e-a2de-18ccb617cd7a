package com.xhs.codewiz.editor.request;

import com.google.gson.annotations.SerializedName;
import org.jetbrains.annotations.NotNull;

public final class VersionedTextDocumentIdentifier {
    @SerializedName("uri")
    private final @NotNull VirtualFileUri uri;
    @SerializedName("version")
    private final int version;

    public VersionedTextDocumentIdentifier(@NotNull VirtualFileUri uri, int version) {
        this.uri = uri;
        this.version = version;
    }

    public @NotNull VirtualFileUri getUri() {
        return this.uri;
    }

    public int getVersion() {
        return this.version;
    }
}


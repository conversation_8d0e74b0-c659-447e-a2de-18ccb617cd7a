package com.xhs.codewiz.scheme.workspace;

import java.util.*;
/**
 * 删除联想提供者
 */
public class DeleteCompletionProvider {
    private String schemaProtocol = "workspace.delete.completionprovider";
    /** 删除的联想提供者 ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

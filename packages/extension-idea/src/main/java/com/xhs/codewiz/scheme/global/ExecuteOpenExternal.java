package com.xhs.codewiz.scheme.global;

import java.util.*;
/**
 * 打开外部链接
 */
public class ExecuteOpenExternal {
    private String schemaProtocol = "global.execute.openexternal";
    /** 要打开的 URL */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

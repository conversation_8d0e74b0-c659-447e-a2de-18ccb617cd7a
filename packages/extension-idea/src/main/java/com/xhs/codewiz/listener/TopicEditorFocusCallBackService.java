package com.xhs.codewiz.listener;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.fileEditor.FileEditorManagerListener;
import com.intellij.openapi.project.Project;
import com.intellij.util.messages.MessageBusConnection;
import com.xhs.codewiz.factory.editor.TopicEditorVirtualFile;
import com.xhs.codewiz.factory.editor.TopicFileEditor;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * Topic Editor 焦点管理器
 * 负责管理 Topic Editor 焦点监听器的注册和生命周期
 *
 * <AUTHOR>
 * @date 2025/1/27 14:45
 */
@Service(Service.Level.PROJECT)
public final class TopicEditorFocusCallBackService implements TopicEditorFocusCallback {

    private static final Logger LOG = Logger.getInstance(TopicEditorFocusCallBackService.class);

    private final Project project;
    private MessageBusConnection connection;
    private TopicEditorFocusListener focusListener;

    public TopicEditorFocusCallBackService(@NotNull Project project) {
        this.project = project;
        initializeFocusListener();
    }

    /**
     * 获取项目的 TopicEditorFocusManager 实例
     */
    public static TopicEditorFocusCallBackService getInstance(@NotNull Project project) {
        return project.getService(TopicEditorFocusCallBackService.class);
    }

    /**
     * 初始化焦点监听器
     */
    private void initializeFocusListener() {
        try {
            // 创建监听器实例
            focusListener = new TopicEditorFocusListener(project);

            // 设置回调处理器
            TopicEditorFocusListener.setFocusCallback(this);

            // 注册到 MessageBus
            connection = project.getMessageBus().connect();
            connection.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, focusListener);

            LoggerUtil.INSTANCE.logInfo(LOG, "Topic Editor 焦点监听器已启动 [Project: " + project.getName() + "]");

        } catch (Exception e) {
            LoggerUtil.INSTANCE.logError(LOG, "初始化 Topic Editor 焦点监听器失败", e);
        }
    }

    /**
     * 停止焦点监听
     */
    public void stopFocusListener() {
        try {
            if (connection != null) {
                connection.disconnect();
                connection = null;
            }
            focusListener = null;

            LoggerUtil.INSTANCE.logInfo(LOG, "Topic Editor 焦点监听器已停止 [Project: " + project.getName() + "]");

        } catch (Exception e) {
            LoggerUtil.INSTANCE.logError(LOG, "停止 Topic Editor 焦点监听器失败", e);
        }
    }

    /**
     * 检查是否有 Topic Editor 处于焦点状态
     */
    public boolean hasTopicEditorFocused() {
        return focusListener != null && focusListener.hasTopicEditorFocused();
    }

    /**
     * 获取当前焦点的 Topic Editor
     */
    public TopicFileEditor getCurrentFocusedTopicEditor() {
        return focusListener != null ? focusListener.getCurrentFocusedTopicEditor() : null;
    }

    // ========== TopicEditorFocusCallback 实现 ==========

    @Override
    public void onTopicEditorFocused(@NotNull Project project, @NotNull TopicFileEditor topicEditor,
                                     @NotNull TopicEditorVirtualFile virtualFile) {
        // 默认实现：记录日志
        LoggerUtil.INSTANCE.logInfo(LOG,
                "处理 Topic Editor 焦点获得事件: " + virtualFile.getName() +
                        " [Channel: " + virtualFile.getRemoteChannel() +
                        ", Provider: " + virtualFile.getWebviewId() + "]");

        // 这里可以添加具体的焦点获得处理逻辑
        handleTopicEditorFocused(topicEditor, virtualFile);

        // 调用自定义回调（如果有的话）
        if (customCallback != null) {
            try {
                customCallback.onFocused(topicEditor, virtualFile);
            } catch (Exception e) {
                LoggerUtil.INSTANCE.logError(LOG, "自定义焦点回调处理出错", e);
            }
        }
    }

    @Override
    public void onTopicEditorBlurred(@NotNull Project project, @NotNull TopicFileEditor topicEditor,
                                     @NotNull TopicEditorVirtualFile virtualFile) {
        // 默认实现：记录日志
        LoggerUtil.INSTANCE.logInfo(LOG,
                "处理 Topic Editor 焦点失去事件: " + virtualFile.getName() +
                        " [Channel: " + virtualFile.getRemoteChannel() +
                        ", Provider: " + virtualFile.getWebviewId() + "]");

        // 这里可以添加具体的焦点失去处理逻辑
        handleTopicEditorBlurred(topicEditor, virtualFile);

        // 调用自定义回调（如果有的话）
        if (customCallback != null) {
            try {
                customCallback.onBlurred(topicEditor, virtualFile);
            } catch (Exception e) {
                LoggerUtil.INSTANCE.logError(LOG, "自定义失焦回调处理出错", e);
            }
        }
    }

    // ========== 具体的焦点处理逻辑 ==========

    /**
     * 处理 Topic Editor 获得焦点
     */
    private void handleTopicEditorFocused(@NotNull TopicFileEditor topicEditor, @NotNull TopicEditorVirtualFile virtualFile) {
        // 记录日志即可，不做其他操作
        LoggerUtil.INSTANCE.logInfo(LOG,
                "Topic Editor 获得焦点 [Topic: " + virtualFile.getName() + "]");
    }


    /**
     * 处理 Topic Editor 失去焦点
     * 可以在这里添加清理逻辑（如果需要的话）
     */
    private void handleTopicEditorBlurred(@NotNull TopicFileEditor topicEditor, @NotNull TopicEditorVirtualFile virtualFile) {
        LoggerUtil.INSTANCE.logInfo(LOG,
                "Topic Editor 失去焦点 [Topic: " + virtualFile.getName() + "]");

        // 这里可以添加一些清理逻辑，但通常不需要重置window
        // 因为currentWindow的设置应该是持久的，直到下次明确需要改变
    }

    /**
     * 自定义焦点事件回调接口
     * 外部代码可以通过实现这个接口来处理焦点事件
     */
    public interface CustomFocusCallback {
        void onFocused(@NotNull TopicFileEditor editor, @NotNull TopicEditorVirtualFile file);

        void onBlurred(@NotNull TopicFileEditor editor, @NotNull TopicEditorVirtualFile file);
    }

    private CustomFocusCallback customCallback;

    /**
     * 设置自定义焦点回调
     */
    public void setCustomFocusCallback(CustomFocusCallback callback) {
        this.customCallback = callback;
    }

    /**
     * 移除自定义焦点回调
     */
    public void removeCustomFocusCallback() {
        this.customCallback = null;
    }
}

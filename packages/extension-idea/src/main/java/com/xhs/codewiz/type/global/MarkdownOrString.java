package com.xhs.codewiz.type.global;

import java.util.*;

/**
 * Markdown 或纯文本
 */
public class MarkdownOrString {
    /** 是否为 Markdown 格式 */
    private Boolean isMarkdown;

    private String value;

    /** 是否信任内容，信任内容可以包含指令连接，Idea目前支持情况不明。 */
    private Boolean isTrusted;

    public Boolean getIsMarkdown() {
        return isMarkdown;
    }

    public void setIsMarkdown(Boolean isMarkdown) {
        this.isMarkdown = isMarkdown;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Boolean getIsTrusted() {
        return isTrusted;
    }

    public void setIsTrusted(Boolean isTrusted) {
        this.isTrusted = isTrusted;
    }

}

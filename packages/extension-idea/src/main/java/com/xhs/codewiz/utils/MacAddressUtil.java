package com.xhs.codewiz.utils;

import com.google.gson.internal.LinkedHashTreeMap;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.NetworkInterface;
import java.util.Base64;
import java.util.Enumeration;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-07-07 Description:
 */
public class MacAddressUtil {
  public static final String OS_NAME = System.getProperty("os.name").toLowerCase(Locale.ENGLISH);
  public static String getMacAddress() {
    String macAddress = isMac() ? getMacOSAddress() : getDefaultMacAddress();
    return Base64.getEncoder().encodeToString(macAddress.getBytes());
  }

  /**
   * 获取mac os的mac地址
   * 优点：直接执行命令，获取厂商烧录的mac地址
   * @return
   */
  private static String getMacOSAddress() {
    try {
      Process process = Runtime.getRuntime().exec("networksetup -listallhardwareports");
      BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
      String line;
      String hardwarePort = null, device = null, mac = null;
      Map<String, String> portToMac = new LinkedHashTreeMap<>();
      while ((line = reader.readLine()) != null) {
        line = line.trim();
        if (line.startsWith("Hardware Port:")) {
          hardwarePort = line.substring(14).trim();
        } else if (line.startsWith("Device:")) {
          device = line.substring(7).trim();
        } else if (line.startsWith("Ethernet Address:")) {
          if (!isPhysicalInterface(hardwarePort, device)) {
            // 跳过虚拟网卡
            continue;
          }
          mac = line.substring(17).trim();
          if (hardwarePort != null && !mac.equals("N/A")) {
            portToMac.put(device, mac);
          }
        }
      }
      reader.close();
      if (portToMac.isEmpty()) {
        //获取失败时降级获取
        return getDefaultMacAddress();
      } else {
        if (portToMac.containsKey("en0")) {
          return portToMac.get("en0");
        }
        return portToMac.values().stream().toList().get(0);
      }
    } catch (Exception e) {
      //获取失败时降级获取
      return getDefaultMacAddress();
    }
  }


  /**
   * 通用获取mac地址方式
   * 缺点：获取到的mac地址是虚拟/后分配的，不能用于唯一标识
   * 可用于降级场景获取
   * @return
   */
  private static String getDefaultMacAddress() {
    byte[] mac = null;
    try {
      Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
      while (interfaces.hasMoreElements()) {
        NetworkInterface ni = interfaces.nextElement();
        if (ni.isLoopback() || ni.isVirtual() || !ni.isUp()) {
          continue;
        }
        if (!isPhysicalInterface(ni.getName(), ni.getDisplayName())) {
          continue;
        }
        mac = ni.getHardwareAddress();
        if (mac != null && mac.length == 6) {
          StringBuilder sb = new StringBuilder();
          for (byte b : mac) {
            sb.append(String.format("%02X:", b));
          }
          sb.setLength(sb.length() - 1); // 去掉最后的冒号
          return sb.toString(); //返回第一个
        }
      }
    } catch (Exception e) {
      return null;
    }
    return null;
  }

  public static boolean isPhysicalInterface(String hardwarePort, String device) {
    return (
        StringUtils.containsAnyIgnoreCase(device, "en", "eth", "wlan")
            && StringUtils.containsAnyIgnoreCase(hardwarePort, "ethernet", "wireless", "wi-fi")
    );
  }

  public static boolean isMac() {
    return OS_NAME.startsWith("mac");
  }

  public static void main(String[] args) {
    getMacAddress();
  }
}

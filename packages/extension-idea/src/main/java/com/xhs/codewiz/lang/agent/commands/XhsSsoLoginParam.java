package com.xhs.codewiz.lang.agent.commands;

import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes.XhsUserInfo;

/**
 * Author: liukunpeng Date: 2025-07-08 Description:
 */
public class XhsSsoLoginParam implements JsonRpcCommand<XhsUserInfo> {
  private boolean needResponse = true;

  public boolean isNeedResponse() {
    return needResponse;
  }

  public void setNeedResponse(boolean needResponse) {
    this.needResponse = needResponse;
  }

  @Override
  public String getCommandName() {
    return "xhs/login";
  }

  @Override
  public Class<XhsUserInfo> getResponseType() {
    return XhsUserInfo.class;
  }
}

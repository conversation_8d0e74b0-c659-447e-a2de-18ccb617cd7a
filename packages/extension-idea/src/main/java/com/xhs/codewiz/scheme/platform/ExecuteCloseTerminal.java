package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 远程关闭接收方终端。
 */
public class ExecuteCloseTerminal {
    private String schemaProtocol = "platform.execute.closeterminal";
    private ExecuteCloseTerminalParams params;

    public ExecuteCloseTerminalParams getParams() {
        return params;
    }
    public void setParams(ExecuteCloseTerminalParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteCloseTerminalParams {
        /** 是否强制关闭 */
        private Boolean force; // optional
        /** 终端 ID */
        private String terminal;
    
        public Boolean getForce() {
            return force;
        }
        public void setForce(Boolean force) {
            this.force = force;
        }
        public String getTerminal() {
            return terminal;
        }
        public void setTerminal(String terminal) {
            this.terminal = terminal;
        }
    }
}

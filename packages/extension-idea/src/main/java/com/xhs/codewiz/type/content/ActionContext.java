package com.xhs.codewiz.type.content;

import java.util.*;

public class ActionContext {
    private List<Diagnostic> diagnostics;

    /** 仅获取特定类型的操作 */
    private ActionKind only;

    private EditEventTriggerKind triggerKind;

    public List<Diagnostic> getDiagnostics() {
        return diagnostics;
    }

    public void setDiagnostics(List<Diagnostic> diagnostics) {
        this.diagnostics = diagnostics;
    }

    public ActionKind getOnly() {
        return only;
    }

    public void setOnly(ActionKind only) {
        this.only = only;
    }

    public EditEventTriggerKind getTriggerKind() {
        return triggerKind;
    }

    public void setTriggerKind(EditEventTriggerKind triggerKind) {
        this.triggerKind = triggerKind;
    }

}

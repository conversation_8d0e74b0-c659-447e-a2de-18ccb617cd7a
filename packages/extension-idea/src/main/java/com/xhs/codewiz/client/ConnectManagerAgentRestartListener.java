package com.xhs.codewiz.client;

import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.listener.topic.CodewizAgentProcessStatusListener;
import com.xhs.codewiz.utils.LoggerUtil;

public class ConnectManagerAgentRestartListener implements CodewizAgentProcessStatusListener {
    private static final Logger LOG = Logger.getInstance(ConnectManagerAgentRestartListener.class);

    public ConnectManagerAgentRestartListener() {
    }

    public void onAgentProcessRestart() {
        LoggerUtil.INSTANCE.logDebug(LOG, "onAgentProcessRestart");
        //LSPManager.getInstance().afterAgentRestart();
    }
}

package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.FileStat;
import com.xhs.codewiz.type.file.FileSystemError;
public class ReadFileStatResponse {
    private FileStat result; // optional
    private String error; // optional

    public FileStat getResult() {
        return result;
    }
    public void setResult(FileStat result) {
        this.result = result;
    }
    public String getError() {
        return error;
    }
    public void setError(String error) {
        this.error = error;
    }
}

package com.xhs.codewiz.listener;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.StartupActivity;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.listener.FileCloseReasonService.CloseReason;
import com.xhs.codewiz.utils.LoggerUtil;

public class FileCloseReasonStartupActivity implements StartupActivity.DumbAware {

    private static final Logger LOG = Logger.getInstance(FileCloseReasonStartupActivity.class);

    @Override
    public void runActivity(@NotNull Project project) {
        try {
            FileCloseReasonService service = FileCloseReasonService.getInstance(project);
            service.addListener(new FileCloseReasonService.Listener() {
                @Override
                public void onFileClosed(@NotNull Project p, @NotNull VirtualFile file, @NotNull CloseReason reason) {
                    if (reason == CloseReason.CLOSED) {
                        LoggerUtil.INSTANCE.logInfo(LOG, "Notify notifyDid Close: " + file.getPath());
                        LSPManager.getInstance().closeTopicEditorFile(project, file, true);
                    } else {
                        // MOVED：不触发 close，避免移动窗口导致的误判
                        LoggerUtil.INSTANCE.logInfo(LOG, "Notify notifyDid MOVED: " + file.getPath());
                        LSPManager.getInstance().closeTopicEditorFile(project, file, false);
                    }
                }
            });
        } catch (Throwable t) {
            LoggerUtil.INSTANCE.logError(LOG, "FileCloseReasonStartupActivity runActivity error", t);
        }
    }
}



package com.xhs.codewiz.type.content;

import java.util.*;

/**
 * 在范围边缘输入时，对应的装饰器行为
 */
public enum DecorationRangeBehavior {
    /** 前不扩展，后扩展 */
    ExtendEnd(2),
    /** 前后均扩展 */
    ExtendBoth(1),
    /** 前后均不扩展 */
    NoExtension(0),
    /** 前扩展，后不扩展 */
    ExtendStart(3);

    private final int value;

    DecorationRangeBehavior(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}

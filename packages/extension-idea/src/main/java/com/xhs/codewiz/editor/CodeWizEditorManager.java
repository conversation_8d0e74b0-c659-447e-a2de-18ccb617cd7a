package com.xhs.codewiz.editor;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.TextRange;
import com.intellij.util.concurrency.annotations.RequiresEdt;
import com.xhs.codewiz.completion.enums.CodeWizApplyInlayStrategy;
import com.xhs.codewiz.completion.request.CodewizInlayList;
import com.xhs.codewiz.editor.request.EditorRequest;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import org.jetbrains.annotations.NotNull;

public interface CodeWizEditorManager extends Disposable {
    static @NotNull CodeWizEditorManager getInstance() {
        return (CodeWizEditorManager)ApplicationManager.getApplication().getService(
            CodeWizEditorManager.class);
    }

    @RequiresEdt
    boolean isAvailable(@NotNull Editor var1);


    @RequiresEdt
    void disposeInlays(@NotNull Editor var1, @NotNull InlayDisposeContext var2);

    @RequiresEdt
    void disposeInlays(@NotNull Editor var1, @NotNull InlayDisposeContext var2, String eventName);

    default void editorModified(@NotNull Editor editor, @NotNull EditEventTriggerKind triggerKind) {
        this.editorModified(editor, editor.getCaretModel().getOffset(), triggerKind);
    }

    @RequiresEdt
    void editorModified(@NotNull Editor var1, int var2, @NotNull EditEventTriggerKind var3);

    @RequiresEdt
    void cancelCompletionRequests(@NotNull Editor var1);

    @RequiresEdt
    int countCompletionInlays(@NotNull Editor var1, @NotNull TextRange var2, boolean inlineInlays, boolean afterLineEndInlays, boolean blockInlays, boolean matchInLeadingWhitespace);

    @RequiresEdt
    boolean applyCompletion(@NotNull Editor var1, CodeWizApplyInlayStrategy var2);

    @RequiresEdt
    void applyCompletion(@NotNull Project project, @NotNull Editor editor, @NotNull EditorRequest request, @NotNull CodewizInlayList completion, @NotNull CodeWizApplyInlayStrategy applyStrategy);

    @RequiresEdt
    default boolean hasCompletionInlays(@NotNull Editor editor) {
        if (!this.isAvailable(editor)) {
            return false;
        } else {
            return this.countCompletionInlays(editor, TextRange.from(0, editor.getDocument().getTextLength()), true, true, true, true) > 0;
        }
    }
}


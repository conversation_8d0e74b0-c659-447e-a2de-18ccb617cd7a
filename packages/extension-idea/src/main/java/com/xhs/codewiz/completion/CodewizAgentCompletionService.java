package com.xhs.codewiz.completion;


import static com.xhs.codewiz.editor.CodeWizEditorManagerImpl.KEY_LAST_REQUEST;

import com.google.common.collect.Lists;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.SelectionModel;
import com.intellij.openapi.editor.VisualPosition;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.util.TextRange;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.psi.PsiBinaryFile;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiFile;
import com.intellij.util.messages.MessageBus;
import com.xhs.codewiz.actions.status.CodeWizStatus;
import com.xhs.codewiz.actions.status.CodeWizStatusService;
import com.xhs.codewiz.client.ProviderTypeEnum;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.completion.request.AgentCompletion;
import com.xhs.codewiz.completion.request.CodewizInlayList;
import com.xhs.codewiz.completion.request.CompletionCache;
import com.xhs.codewiz.completion.enums.CompletionType;
import com.xhs.codewiz.completion.request.CompletionExtraContext;
import com.xhs.codewiz.completion.request.CompletionItem;
import com.xhs.codewiz.completion.request.SimpleCompletionCache;
import com.xhs.codewiz.completion.util.CompletionUtil;
import com.xhs.codewiz.editor.CodeWizEditorUtil;
import com.xhs.codewiz.editor.request.AgentEditorRequest;
import com.xhs.codewiz.editor.request.CodewizEditorInlay;
import com.xhs.codewiz.editor.request.Document;
import com.xhs.codewiz.editor.request.EditorRequest;
import com.xhs.codewiz.editor.request.EditorRequestResultList;
import com.xhs.codewiz.lang.agent.CodeWizAgentProcessService;
import com.xhs.codewiz.lang.agent.commands.CompletionsCommand;
import com.xhs.codewiz.lang.agent.commands.GetCompletionsCommand;
import com.xhs.codewiz.lang.agent.commands.GetCompletionsResult;
import com.xhs.codewiz.lang.agent.commands.NotifyAcceptedCommand;
import com.xhs.codewiz.lang.agent.commands.NotifyRejectedCommand;
import com.xhs.codewiz.lang.agent.commands.NotifyShownCommand;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;
import com.xhs.codewiz.listener.topic.CodewizAgentProcessStatusListener;
import com.xhs.codewiz.scheme.workspace.CreateInlineCompletionProvider;
import com.xhs.codewiz.scheme.workspace.DeleteInlineCompletionProvider;
import com.xhs.codewiz.scheme.workspace.ReadInlineCompletion;
import com.xhs.codewiz.scheme.workspace.ReadInlineCompletion.ReadInlineCompletionParams;
import com.xhs.codewiz.scheme.workspace.ReadInlineCompletion.ReadInlineCompletionParamsContext;
import com.xhs.codewiz.scheme.workspace.ReadInlineCompletion.ReadInlineCompletionParamsContextSelectedCompletionInfo;
import com.xhs.codewiz.setting.CodeWizApplicationState;
import com.xhs.codewiz.type.content.CompletionInlineItem;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.type.content.Position;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.workspace.InlineCompletionItem;
import com.xhs.codewiz.utils.FileSizeUtil;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Flow;
import java.util.concurrent.SubmissionPublisher;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.CancellablePromise;
import org.jetbrains.concurrency.Promise.State;

public class CodewizAgentCompletionService implements CodewizCompletionService, Disposable {
    private static final Logger LOG = Logger.getInstance(CodewizAgentCompletionService.class);
    protected final CompletionCache cache = new SimpleCompletionCache(32);

    public CodewizAgentCompletionService() {
        MessageBus bus = ApplicationManager.getApplication().getMessageBus();
        //这里需要在启动socket时重置
        bus.connect(this).subscribe(CodewizAgentProcessStatusListener.TOPIC, (CodewizAgentProcessStatusListener) () -> CodewizAgentCompletionService.this.reset());
    }

    public void dispose() {
        this.reset();
    }

    public boolean isAvailable(@NotNull Editor editor) {
        Project project = editor.getProject();
        if (project == null) {
            return false;
        } else {
            PsiFile file = PsiDocumentManager.getInstance(project).getPsiFile(editor.getDocument());
            return file != null && !(file instanceof PsiBinaryFile) && !file.getFileType().isBinary() && FileSizeUtil.isSupported(file.getVirtualFile());
        }
    }
    public @Nullable EditorRequest createRequest(@NotNull Editor editor, int offset, @NotNull CompletionType completionType, EditEventTriggerKind triggerKind) {
        return AgentEditorRequest.create(editor, offset, completionType, triggerKind);
    }

    public @Nullable List<CodewizInlayList> fetchCachedCompletions(@NotNull EditorRequest request) {
        return TypingAsSuggestedCompletionUtil.handleTypeAheadCaching(request, this.cache);
    }

    public boolean fetchCompletions(Editor editor, @NotNull EditorRequest request, @Nullable Integer maxCompletions, boolean cycling, @NotNull Flow.Subscriber<List<CodewizInlayList>> subscriber) {
        assert request.getCompletionType() == CompletionType.GhostText;

        Document doc = this.createDocument(request);
        CodeWizStatusService.notifyApplication(CodeWizStatus.CompletionInProgress);
        SubmissionPublisher<List<CodewizInlayList>> publisher = new SubmissionPublisher();
        try {
            publisher.subscribe(subscriber);
            ArrayList<CodewizInlayList> inlayLists = new ArrayList();

            ReadInlineCompletion completionReq = new ReadInlineCompletion();
            ReadInlineCompletionParams params = new ReadInlineCompletionParams();
            params.setUri(doc.getUri().getUri());
            params.setPosition(doc.getPosition());
            ReadInlineCompletionParamsContext context = new ReadInlineCompletionParamsContext();
            context.setTriggerKind(request.getEditEventTriggerKind());
            setSelectedForCompletion(editor, context);
            params.setContext(context);
            completionReq.setParams(params);
            List<CompletableFuture<List<InlineCompletionItem>>> futureList= RcsWebSocketManager.INSTANCE.doCompletion(completionReq, editor.getProject());
            if (CollectionUtils.isEmpty(futureList)) {
                CodeWizStatusService.notifyApplication(CodeWizStatus.Ready);
                return true;
            }
            // 等待全部Future完成（allOf），再收集结果
            CompletableFuture<Void> allDone = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
            request.setCompletionFeature(allDone);
            int tryTimes = 50;
            //最多等待50次，共5s
            boolean isDown = false;
            while (tryTimes-- > 0) {
                //判断是否已经取消
                if (allDone.isCancelled()) {
                    //取消如果未触发新补全，那么恢复状态
                    EditorRequestResultList lastRequest = KEY_LAST_REQUEST.get(editor);
                    if (null == lastRequest || lastRequest.getRequest().equalsRequest(request)) {
                        CodeWizStatusService.notifyApplication(CodeWizStatus.Ready);
                    }
                    return true;
                }
                if (allDone.isDone()) {
                    isDown = true;
                    break;
                }
                ThreadUtil.sleep(100);
            }
            if (!isDown) {
                CodeWizStatusService.notifyApplication(CodeWizStatus.Ready);
                return true;
            }

            // 收集非空返回
            // 合并所有List
            List<InlineCompletionItem> itemList = new ArrayList<>();
            for (CompletableFuture<List<InlineCompletionItem>> f : futureList) {
                try {
                    List<InlineCompletionItem> items = f.getNow(null);
                    if (CollectionUtils.isNotEmpty(items)) {
                        itemList.addAll(items);
                    }
                } catch (Exception ex) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "future get err", ex);
                }
            }
            if (CollectionUtils.isEmpty(itemList)) {
                CodeWizStatusService.notifyApplication(CodeWizStatus.Ready);
                return true;
            }
            List<CompletionItem> completions = itemList.stream().map(item -> {
                CompletionItem completionItem = new CompletionItem();
                completionItem.setRange(item.getRange());
                completionItem.setInsertText(item.getInsertText().getText());
                completionItem.setFilterText(completionItem.getInsertText());
                return completionItem;
            }).toList();
            CodeWizStatusService.notifyApplication(CodeWizStatus.Ready);
            for (CompletionItem completion : completions) {
                AgentCompletion agentCompletion = new AgentCompletion(completion);
                this.cache.add(request.getCurrentDocumentPrefix(), request.getCurrentDocumentPrefix(), true, agentCompletion);
                CodewizInlayList inlays = CompletionUtil.createEditorCompletion(request, agentCompletion, true);
                inlayLists.add(new AgentCompletionList(inlays, agentCompletion, request));
            }
            if (!inlayLists.isEmpty()) {
                publisher.submit(inlayLists);
            }
            CodeWizStatusService.notifyApplication(CodeWizStatus.Ready);
        } catch (Throwable throwable) {
            try {
                publisher.close();
            } catch (Throwable var11) {
                throwable.addSuppressed(var11);
            }
            CodeWizStatusService.notifyApplication(CodeWizStatus.UnknownError);
            LoggerUtil.INSTANCE.logWarn(LOG, "Error while fetching completions", throwable);
        }
        publisher.close();
        return true;
    }

    public boolean fetchCompletionsFromLs(CompletionExtraContext extraContext, @NotNull EditorRequest request, @Nullable Integer maxCompletions, boolean cycling, @NotNull Flow.Subscriber<List<CodewizInlayList>> subscriber) {
        assert request.getCompletionType() == CompletionType.GhostText;

        Document doc = this.createDocument(request);
        CompletionsCommand<GetCompletionsResult> command = new GetCompletionsCommand(doc, (Map)null, extraContext);
        CancellablePromise<GetCompletionsResult> promise = CodeWizAgentProcessService.getInstance().executeCommand((JsonRpcCommand)command);
        if (promise.getState() == State.REJECTED) {
            LoggerUtil.INSTANCE.logWarn(LOG, "promise was rejected: " + promise);
            SubmissionPublisher<List<CodewizInlayList>> publisher = new SubmissionPublisher();

            try {
                publisher.subscribe(subscriber);
                publisher.closeExceptionally(new IllegalStateException("promise was rejected"));
            } catch (Throwable var12) {
                try {
                    publisher.close();
                } catch (Throwable var11) {
                    var12.addSuppressed(var11);
                }

                throw var12;
            }

            publisher.close();
            return false;
        } else {
            Disposable var10000 = request.getDisposable();
            Objects.requireNonNull(promise);
            Disposer.tryRegister(var10000, promise::cancel);
            promise.onError((throwable) -> {
                SubmissionPublisher<List<CodewizInlayList>> publisher = new SubmissionPublisher();

                try {
                    publisher.subscribe(subscriber);
                    publisher.closeExceptionally(throwable);
                } catch (Throwable var6) {
                    try {
                        publisher.close();
                    } catch (Throwable var5) {
                        var6.addSuppressed(var5);
                    }

                    throw var6;
                }

                publisher.close();
            });
            promise.onSuccess((result) -> {
                SubmissionPublisher<List<CodewizInlayList>> publisher = new SubmissionPublisher();

                try {
                    publisher.subscribe(subscriber);
                    ArrayList<CodewizInlayList> inlayLists = new ArrayList();
                    List<GetCompletionsResult.Completion> completionRes = result.getCompletions();

                    List<CompletionItem> completions = completionRes.stream().map(item -> {
                        CompletionItem completionItem = new CompletionItem();
                        completionItem.setRange(item.getRange());
                        completionItem.setInsertText(item.getText());
                        completionItem.setFilterText(item.getDisplayText());
                        completionItem.setUuid(item.getUuid());
                        return completionItem;
                    }).toList();

                    for (CompletionItem completion : completions) {
                        AgentCompletion agentCompletion = new AgentCompletion(completion);
                        this.cache.add(request.getCurrentDocumentPrefix(), request.getCurrentDocumentPrefix(), true, agentCompletion);
                        CodewizInlayList inlays = CompletionUtil.createEditorCompletion(request, agentCompletion, true);
                        inlayLists.add(new AgentCompletionList(inlays, agentCompletion, request));
                    }
                    if (!inlayLists.isEmpty()) {
                        publisher.submit(inlayLists);
                    }
                } catch (Throwable var12) {
                    try {
                        publisher.close();
                    } catch (Throwable var11) {
                        var12.addSuppressed(var11);
                    }

                    throw var12;
                }

                publisher.close();
            });
            return true;
        }
    }

    private void setSelectedForCompletion(Editor editor, ReadInlineCompletionParamsContext context) {
        if (null == editor) {
            return;
        }
        CodeWizEditorUtil.SelectionModelInfo selectionModelInfo = CodeWizEditorUtil.getSelectionModelInfo(editor.getSelectionModel());
        if (StringUtils.isEmpty(selectionModelInfo.getSelectedText())) {
            return;
        }
        ReadInlineCompletionParamsContextSelectedCompletionInfo selected = new ReadInlineCompletionParamsContextSelectedCompletionInfo();
        VisualPosition startPosition = selectionModelInfo.getSelectionStartPosition();
        VisualPosition endPosition = selectionModelInfo.getSelectionEndPosition();

        Position start = new Position();
        start.setLine(startPosition.getLine());
        start.setCharacter(startPosition.getColumn());

        Position end = new Position();
        end.setLine(endPosition.getLine());
        end.setCharacter(endPosition.getColumn());

        Range range = new Range();
        range.setStart(start);
        range.setEnd(end);
        selected.setRange(range);
        selected.setText(selectionModelInfo.getSelectedText());
        context.setSelectedCompletionInfo(selected);
    }
    public void reset() {
        this.cache.clear();
    }

    public boolean isSupportingOnDemandCycling(@NotNull Editor editor) {
        return true;
    }

    @Override
    public void sendShownTelemetry(@NotNull CodewizCompletion var1, String sessionId, int start,
        int end, int shownLineCount, String shownContent, String filePath) {
        long timestamp = System.currentTimeMillis();
        String uuid = ((AgentCompletion)var1).getAgentData().getUuid();
        CodeWizAgentProcessService.getInstance().executeCommand(new NotifyShownCommand(sessionId, uuid, start, end, shownLineCount, timestamp, shownContent, filePath));
    }

    @Override
    public void sendAcceptedTelemetry(NotifyAcceptedCommand command) {
        CodeWizAgentProcessService.getInstance().executeCommand(command);
    }

    @Override
    public void sendRejectedTelemetry(@NotNull List<CodewizCompletion> var1, String sessionId,
        String eventName) {
        if (!var1.isEmpty()) {
            List<String> uuids = var1.stream().map((i) -> {
                return ((AgentCompletion)i).getAgentData().getUuid();
            }).collect(Collectors.toList());
            CodeWizAgentProcessService.getInstance().executeCommand(new NotifyRejectedCommand(sessionId, uuids, eventName, System.currentTimeMillis()));
        }
    }

    private @NotNull Document createDocument(@NotNull EditorRequest request) {
        Position position = new Position();
        position.setLine(request.getLineInfo().getLineNumber());
        position.setCharacter(request.getLineInfo().getColumnOffset());
        return new Document(position, !request.isUseTabIndents(), request.getTabWidth(),
            ((AgentEditorRequest)request).getUri(), request.getDocumentVersion(), request.getDocumentContent());
    }

    public static void initInlineCompletionProvider(String params, String channel) {
        CreateInlineCompletionProvider inlinePro = GsonUtil.fromJson(params, CreateInlineCompletionProvider.class);
        if (null == inlinePro
            || null == inlinePro.getParams()
            || null == inlinePro.getParams().getProvider()
            || StringUtil.isEmpty(inlinePro.getParams().getProvider().getId())) {
            return;
        }
        List<Object> nowProviders = RcsWebSocketManager.INSTANCE.getProvider(channel, ProviderTypeEnum.INLINE_COMPLETION);
        //如果有重复的，先删除
        if (CollectionUtils.isNotEmpty(nowProviders)) {
            for (Object obj : nowProviders) {
                CreateInlineCompletionProvider provider = (CreateInlineCompletionProvider) obj;
                if (provider.getParams().getProvider().getId().equals(inlinePro.getParams().getProvider().getId())) {
                    RcsWebSocketManager.INSTANCE.deleteProvider(channel, obj);
                    break;
                }
            }
        }
        //初始化
        RcsWebSocketManager.INSTANCE.addProvider(channel, inlinePro);
        //如果有其他用途，可以拿来写
    }

    public static void delInlineCompletionProvider(String params, String channel) {
        DeleteInlineCompletionProvider inlinePro = GsonUtil.fromJson(params, DeleteInlineCompletionProvider.class);
        if (null == inlinePro
            || StringUtil.isEmpty(inlinePro.getParams())) {
            return;
        }
        List<Object> nowProviders = RcsWebSocketManager.INSTANCE.getProvider(channel, ProviderTypeEnum.INLINE_COMPLETION);
        if (CollectionUtils.isEmpty(nowProviders)) {
            return;
        }
        for (Object obj : nowProviders) {
            CreateInlineCompletionProvider provider = (CreateInlineCompletionProvider) obj;
            if (provider.getParams().getProvider().getId().equals(inlinePro.getParams())) {
                RcsWebSocketManager.INSTANCE.deleteProvider(channel, obj);
                break;
            }
        }
    }

    private static class AgentCompletionList implements CodewizInlayList {
        private final CodewizInlayList inlays;
        private final AgentCompletion completion;
        private final @NotNull EditorRequest request;
        private final String replacementText;

        public AgentCompletionList(@Nullable CodewizInlayList inlays, @NotNull AgentCompletion completion, @NotNull EditorRequest request) {
            this.inlays = inlays;
            this.completion = completion;
            this.request = request;
            this.replacementText = CompletionUtil.dropOverlappingTrailingLines(completion.getAgentData().getInsertText(), request.getDocumentContent(), request.getOffset());
        }

        public boolean isEmpty() {
            return this.inlays == null || this.inlays.isEmpty();
        }

        public @NotNull CodewizCompletion getCodewizCompletion() {
            return this.completion;
        }

        public @NotNull TextRange getReplacementRange() {
            String text = this.request.getDocumentContent();
            Range range = this.completion.getAgentData().getRange();
            int startOffset = StringUtil.lineColToOffset(text, range.getStart().getLine(), range.getStart().getCharacter());
            int endOffset = StringUtil.lineColToOffset(text, range.getEnd().getLine(), range.getEnd().getCharacter());

            assert startOffset >= 0;

            assert endOffset >= startOffset;

            return TextRange.create(startOffset, endOffset);
        }

        public @NotNull String getReplacementText() {
            return this.replacementText;
        }

        public @NotNull List<CodewizEditorInlay> getInlays() {
            return this.inlays == null ? Collections.emptyList() : this.inlays.getInlays();
        }

        public @NotNull Iterator<CodewizEditorInlay> iterator() {
            return this.inlays != null ? this.inlays.iterator() : Collections.emptyIterator();
        }
    }
}


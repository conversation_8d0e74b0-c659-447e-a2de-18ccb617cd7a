package com.xhs.codewiz.platform;

import com.intellij.diff.DiffContentFactory;
import com.intellij.diff.actions.impl.MutableDiffRequestChain;
import com.intellij.diff.contents.DiffContent;
import com.intellij.diff.editor.ChainDiffVirtualFile;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.event.DocumentEvent;
import com.intellij.openapi.editor.event.DocumentListener;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.util.Alarm;
import com.intellij.util.Alarm.ThreadToUse;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.client.service.RcsWorkspaceContentProviderService;
import com.xhs.codewiz.constant.PluginCommonConstant;
import com.xhs.codewiz.editor.util.EditorTabGroupUtil;
import com.xhs.codewiz.scheme.platform.ExecuteShowPanel.ExecuteShowPanelParamsOptions;
import com.xhs.codewiz.type.platform.BuildInPanel.BuildInPanelDiff;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-07-28 Description:
 */
public class DiffPanelUtil {
  private static final Logger LOG = Logger.getInstance(LocalCrDiffUtil.class);
  private static final Alarm myAlarm = new Alarm(ThreadToUse.SWING_THREAD);
  private static final int DELAY = 500;
  //常规diff展示
  private static Map<String, Pair<ChainDiffVirtualFile, Long>> editorDiffChainFileMap = new HashMap<>();
  //评论用展示
  private static Map<String, ChainDiffVirtualFile> commentChainFileMap = new HashMap<>();
  private static Key<String> PANEL_FILE_KEY = Key.create("PANEL_FILE_KEY");


  public static void showDiffPanel(String panelId, BuildInPanelDiff diff, ExecuteShowPanelParamsOptions options, String channel, VirtualFile left, String leftContent, Integer retryTime) {
    VirtualFile right;
    String rightContent = null;
    FileType fileType = null;
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);

    //本地文件内容
    Document document;
    if (StringUtils.isNotEmpty(diff.getRight())) {
      right = RcsWorkspaceContentProviderService.getContentProviderFileByUri(diff.getRight(), channel);
      if (null != right) {
        fileType = right.getFileType();
        document = ReadAction.compute(() -> {
          return FileDocumentManager.getInstance().getDocument(right);
        });
        if (document != null) {
          rightContent = document.getText();
        }
      } else {
        document = null;
      }
    } else {
      document = null;
    }

    //远端缓存内容，用于撤销/拒绝采纳
    if (StringUtils.isEmpty(leftContent) && StringUtils.isNotEmpty(diff.getLeft())) {
      left = RcsWorkspaceContentProviderService.getContentProviderFileByUri(diff.getLeft(), channel);
      if (null != left) {
        if (null == fileType) {
          fileType = left.getFileType();
        }
        VirtualFile finalLeft1 = left;
        PsiFile leftPsi = ReadAction.compute(() -> {
          return PsiManager.getInstance(project).findFile(finalLeft1);
        });
        if (leftPsi != null) {
          leftContent = leftPsi.getText();
        }
      }
    }
    if (StringUtils.equals(leftContent, rightContent) && retryTime-- > 0) {
      LoggerUtil.INSTANCE.logWarn(LOG, "leftContent equals rightContent, retry");
      ThreadUtil.sleep(500);
      showDiffPanel(panelId, diff, options, channel, left, leftContent, retryTime);
      return;
    }
    DiffContentFactory contentFactory = DiffContentFactory.getInstance();
    DiffContent content1 = contentFactory.create(project, leftContent, fileType);
    DiffContent content2 = contentFactory.create(project, rightContent, fileType);


    String finalLeftContent = leftContent;
    VirtualFile finalLeft = left;
    Integer finalRetryTime = retryTime;
    ApplicationManager.getApplication().invokeLater(() -> {
      FileEditorManager editorManager = FileEditorManager.getInstance(project);
      boolean isComment;
      if (StringUtils.startsWith(diff.getLeft(), PluginCommonConstant.DIFF_PANEL_COMPARE_SCHEMA)) {
        isComment = false;
      } else if (StringUtils.startsWith(diff.getLeft(), PluginCommonConstant.DIFF_PANEL_COMMENT_SCHEMA)) {
        isComment = true;
      } else {
        isComment = LocalCrDiffUtil.isDiffComment(project, channel, diff.getRight());
      }
      if (!isComment) {
        //常规diff展示面板
        ChainDiffVirtualFile diffFile;
        //使用右来确认唯一的diff窗口
        if (editorDiffChainFileMap.containsKey(diff.getRight())) {
          //本地的一个文件，最多仅允许打开一个diffPanel
          Pair<ChainDiffVirtualFile, Long> pair = editorDiffChainFileMap.get(diff.getRight());
          diffFile = pair.getLeft();
          MutableDiffRequestChain requestChain = (MutableDiffRequestChain) diffFile.getChain();
          requestChain.setContent1(content1);
          requestChain.setContent2(content2);
          editorDiffChainFileMap.put(diff.getRight(), Pair.of(diffFile, System.currentTimeMillis()));
        } else {
          MutableDiffRequestChain requestChain = new MutableDiffRequestChain(content1, content2);
          String title = diff.getTitle().replace("Editable", "read-only");
          diffFile = new ChainDiffVirtualFile(requestChain, title);
          diffFile.putUserData(PANEL_FILE_KEY, panelId);
          //建立右链接与文件的映射关系
          editorDiffChainFileMap.put(diff.getRight(), Pair.of(diffFile, System.currentTimeMillis()));
        }
        diffFile.putUserData(EditorTabGroupUtil.CHAIN_DIFF_FILE_PATH, Pair.of(diff.getLeft(), diff.getRight()));
        //刷新panel,移除agent编辑标记
        RcsWorkspaceContentProviderService.contentEditFlagMap.remove(diff.getRight());
        if (editorManager.isFileOpen(diffFile)) {
          editorManager.closeFile(diffFile);
        }
        //非评论展示，直接show
        boolean isFocus = null != options && null != options.getDidFocus() && options.getDidFocus();
        //打开并关联新的
        editorManager.openFile(diffFile, isFocus);

        //监听本地文件的变更
        if (null != document) {
          DocumentListener documentListener = new DocumentListener() {
            private AtomicBoolean isRemove = new AtomicBoolean(false);
            @Override
            public void documentChanged(@NotNull DocumentEvent event) {
              if (StringUtils.isNotEmpty(event.getOldFragment())
                  || StringUtils.isNotEmpty(event.getNewFragment())) {
                LoggerUtil.INSTANCE.logWarn(LOG, "documentChanged success");
                reloadDiffPanel();
              }
            }

            @Override
            public void bulkUpdateFinished(@NotNull Document document) {
              LoggerUtil.INSTANCE.logWarn(LOG, "bulkUpdateFinished success");
              reloadDiffPanel();
            }

            private void reloadDiffPanel() {
              //取消抖动时间内的文件变更
              myAlarm.cancelAllRequests();
              myAlarm.addRequest(() -> ApplicationManager.getApplication().invokeLater(() -> {
                //监听触发的，仅允许对比文件还存在的时候进行
                if (editorManager.isFileOpen(diffFile)) {
                  AtomicBoolean agentEditFlag = RcsWorkspaceContentProviderService
                      .contentEditFlagMap.getOrDefault(diff.getRight(), null);
                  if (null == agentEditFlag || !agentEditFlag.get()) {
                    //没有agent编辑标记存在，说明是本地人工编辑，不应去更新panel
                    return;
                  }
                  //移除监听器
                  try {
                    if (!isRemove.getAndSet(true)) {
                      document.removeDocumentListener(this);
                    }
                  } catch (Throwable e) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "LOCAL Failed to remove document listener: ", e);
                  }
                  showDiffPanel(panelId, diff, options, channel, finalLeft, finalLeftContent, finalRetryTime);
                } else {
                  //关闭了的直接移除监听器
                  try {
                    if (!isRemove.getAndSet(true)) {
                      document.removeDocumentListener(this);
                    }
                  } catch (Throwable e) {
                    LoggerUtil.INSTANCE.logWarn(LOG, "LOCAL Failed to remove document listener: ", e);
                  }
                  Pair<ChainDiffVirtualFile, Long> pair = editorDiffChainFileMap.get(diff.getRight());
                  if (null != pair && null != pair.getLeft() && pair.getLeft().equals(diffFile)) {
                    editorDiffChainFileMap.remove(diff.getRight());
                  }
                }
              }), DELAY);
            }
          };
          document.addDocumentListener(documentListener);
        }
      } else {
        //评论用diff展示面板
        ChainDiffVirtualFile diffFile;
        //使用右来确认唯一的diff窗口
        if (commentChainFileMap.containsKey(diff.getRight())) {
          //本地的一个文件，最多仅允许打开一个diffPanel
          diffFile = commentChainFileMap.get(diff.getRight());
          MutableDiffRequestChain requestChain = (MutableDiffRequestChain) diffFile.getChain();
          requestChain.setContent1(content1);
          requestChain.setContent2(content2);
        } else {
          MutableDiffRequestChain requestChain = new MutableDiffRequestChain(content1, content2);
          diffFile = new ChainDiffVirtualFile(requestChain, diff.getTitle());
          //建立右链接与文件的映射关系
          commentChainFileMap.put(diff.getRight(), diffFile);
        }
        diffFile.putUserData(EditorTabGroupUtil.CHAIN_DIFF_FILE_PATH, Pair.of(diff.getLeft(), diff.getRight()));
        //评论的，需要走评论面板
        LocalCrDiffUtil.showCommentDiffPanel(project, diff.getRight(), diff);
      }
    });
  }

  public static void closeDiffPanel(String panelId, String channel) {
    String removeKey = null;
    for (Map.Entry<String, Pair<ChainDiffVirtualFile, Long>> entry : editorDiffChainFileMap.entrySet()) {
      Pair<ChainDiffVirtualFile, Long> pair = entry.getValue();
      if (null == pair || null == pair.getLeft()) {
        continue;
      }
      String filePanelId = pair.getLeft().getUserData(PANEL_FILE_KEY);
      if (StringUtils.isEmpty(filePanelId)) {
        continue;
      }
      if (StringUtils.equals(filePanelId, panelId)) {
        removeKey = entry.getKey();
      }
    }
    if (StringUtils.isEmpty(removeKey)) {
      return;
    }
    Pair<ChainDiffVirtualFile, Long> pair = editorDiffChainFileMap.remove(removeKey);
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    ApplicationManager.getApplication().invokeLater(() -> {
      FileEditorManager editorManager = FileEditorManager.getInstance(project);
      editorManager.closeFile(pair.getLeft());
    });
  }


  public static ChainDiffVirtualFile getCommentChainDiffFileByRightUri(String uri) {
    return commentChainFileMap.getOrDefault(uri, null);
  }
  public static Pair<ChainDiffVirtualFile, Long> getEditorChainDiffFileByRightUri(String uri) {
    return editorDiffChainFileMap.getOrDefault(uri, null);
  }
}

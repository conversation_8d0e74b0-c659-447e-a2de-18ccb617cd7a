package com.xhs.codewiz.platform.enums;

/**
 * Comment状态枚举
 * 用于表示用户对评论的操作状态
 *
 * <AUTHOR>
 */
public enum CommentStatus {
    /**
     * 点赞
     */
    LIKE("UP", "点赞"),

    /**
     * 点踩
     */
    UNLIKE("DOWN", "点踩"),

    /**
     * 延期修复
     */
    DELAY("DELAY", "延期修复"),

    /**
     * 取消操作
     */
    CANCEL_UP("CANCEL_UP", "取消点赞"),

    /**
     * 取消操作
     */
    CANCEL_DOWN("CANCEL_DOWN", "取消点踩"),

    /**
     * 取消操作--用来识别when条件
     */
    CANCEL("CANCEL", "取消"),

    /**
     * 未操作(默认状态)
     */
    UNKNOWN("UNKNOWN", "未操作");

    private final String value;
    private final String description;

    CommentStatus(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据value获取枚举
     */
    public static CommentStatus fromValue(String value) {
        if (value == null) {
            return UNKNOWN;
        }
        for (CommentStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return UNKNOWN;
    }
} 
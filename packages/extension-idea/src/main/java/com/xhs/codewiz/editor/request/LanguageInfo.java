package com.xhs.codewiz.editor.request;

import com.intellij.lang.Language;
import java.util.Locale;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class LanguageInfo {
    private final @NotNull Language language;
    private final @Nullable String vscodeId;

    public LanguageInfo(@NotNull Language language, @Nullable String vscodeId) {
        this.language = language;
        this.vscodeId = vscodeId == null ? null : vscodeId.toLowerCase(Locale.ENGLISH);
    }

    public @NotNull String getVSCodeIdWithFallback() {
        return this.vscodeId != null ? this.vscodeId : this.language.getID().toLowerCase(Locale.ENGLISH);
    }

    public @NotNull Language getLanguage() {
        return this.language;
    }

    public @Nullable String getVscodeId() {
        return this.vscodeId;
    }
}


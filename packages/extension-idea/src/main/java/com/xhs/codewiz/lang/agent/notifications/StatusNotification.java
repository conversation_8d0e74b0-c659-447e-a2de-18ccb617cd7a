package com.xhs.codewiz.lang.agent.notifications;

import com.google.gson.annotations.SerializedName;
import org.jetbrains.annotations.NotNull;

public class StatusNotification {
    public static final @NotNull String NAME = "statusNotification";
    @SerializedName("status")
    @NotNull Status status;
    @SerializedName("message")
    @NotNull String message;

    public StatusNotification(@NotNull  Status status, @NotNull String message) {
        this.status = status;
        this.message = message;
    }

    public @NotNull Status getStatus() {
        return this.status;
    }

    public @NotNull String getMessage() {
        return this.message;
    }

    public void setStatus( @NotNull Status status) {
        this.status = status;
    }

    public void setMessage(@NotNull String message) {
        this.message = message;
    }

    public static enum Status {
        @SerializedName("InProgress")
        InProgress,
        @SerializedName("Normal")
        Normal,
        @SerializedName("Error")
        Error,
        @SerializedName("Warning")
        Warning;

        private Status() {
        }
    }
}


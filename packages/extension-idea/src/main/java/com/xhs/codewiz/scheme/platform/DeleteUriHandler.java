package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 删除 Uri Handler
 */
public class DeleteUriHandler {
    private String schemaProtocol = "platform.delete.urihandler";
    /** 要删除的 Uri Handler ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

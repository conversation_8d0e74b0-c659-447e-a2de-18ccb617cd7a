package com.xhs.codewiz.actions;

import com.intellij.codeInsight.lookup.LookupManager;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.editor.Caret;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.actionSystem.EditorActionHandler;
import com.xhs.codewiz.editor.CodeWizEditorManager;
import com.xhs.codewiz.editor.InlayDisposeContext;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CodeWizDisposeInlaysEditorHandler extends EditorActionHandler {
    private final @Nullable EditorActionHandler baseHandler;

    public CodeWizDisposeInlaysEditorHandler(@Nullable EditorActionHandler baseHandler) {
        this.baseHandler = baseHandler;
    }

    protected boolean isEnabledForCaret(@NotNull Editor editor, @NotNull Caret caret, DataContext dataContext) {
        return isEditorSupported(editor) || this.baseHandler != null && this.baseHandler.isEnabled(editor, caret, dataContext);
    }

    public boolean executeInCommand(@NotNull Editor editor, DataContext dataContext) {
        return this.baseHandler != null && this.baseHandler.executeInCommand(editor, dataContext);
    }

    protected void doExecute(@NotNull Editor editor, @Nullable Caret caret, DataContext dataContext) {
        if (isEditorSupported(editor)) {
            CodeWizEditorManager.getInstance().disposeInlays(editor, InlayDisposeContext.CaretChange);
        }

        if (this.baseHandler != null && this.baseHandler.isEnabled(editor, caret, dataContext)) {
            this.baseHandler.execute(editor, caret, dataContext);
        }

    }

    static boolean isEditorSupported(@NotNull Editor editor) {
        CodeWizEditorManager manager = CodeWizEditorManager.getInstance();
        return manager.isAvailable(editor) && manager.hasCompletionInlays(editor) && LookupManager.getActiveLookup(editor) == null;
    }
}


package com.xhs.codewiz.type.content;

import java.util.*;

/**
 * 装饰器渲染设置
 */
public class DecorationRenderOptions {
    /** 浅色主题下的样式覆盖 */
    private DecorationRenderOptionsBase light;

    /** 是否整行渲染 */
    private Boolean isWholeLine;

    /** 深色主题下的样式覆盖 */
    private DecorationRenderOptionsBase dark;

    private OverviewRulerLane overviewRulerLane;

    private DecorationRangeBehavior rangeBehavior;

    public DecorationRenderOptionsBase getLight() {
        return light;
    }

    public void setLight(DecorationRenderOptionsBase light) {
        this.light = light;
    }

    public Boolean getIsWholeLine() {
        return isWholeLine;
    }

    public void setIsWholeLine(Boolean isWholeLine) {
        this.isWholeLine = isWholeLine;
    }

    public DecorationRenderOptionsBase getDark() {
        return dark;
    }

    public void setDark(DecorationRenderOptionsBase dark) {
        this.dark = dark;
    }

    public OverviewRulerLane getOverviewRulerLane() {
        return overviewRulerLane;
    }

    public void setOverviewRulerLane(OverviewRulerLane overviewRulerLane) {
        this.overviewRulerLane = overviewRulerLane;
    }

    public DecorationRangeBehavior getRangeBehavior() {
        return rangeBehavior;
    }

    public void setRangeBehavior(DecorationRangeBehavior rangeBehavior) {
        this.rangeBehavior = rangeBehavior;
    }

}

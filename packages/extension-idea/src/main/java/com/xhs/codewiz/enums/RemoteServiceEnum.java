package com.xhs.codewiz.enums;

import java.util.List;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

/**
 * Author: liukunpeng Date: 2025-07-15 Description:
 */
public enum RemoteServiceEnum {
//    CODE_WIZ(Lists.newArrayList(0, 9, 4), "rednote-codewiz-old", true),
    REDE_NOTE(Lists.newArrayList(0, 10, 2), "rednote-codewiz", false),
    XHS_VERIFICATION(Lists.newArrayList(0, 0, 1), "xhs-verification", false),
    // UPDATE(Lists.newArrayList(0, 0, 1), "rcs-update", false),
    CODEWIZ_WORKSPACE(Lists.newArrayList(0, 0, 1, 19), "codewiz-workspace", false),
    ;

    private final List<Integer> version;
    private final String name;
    private final boolean disableWebview;

    RemoteServiceEnum(List<Integer> version, String name, boolean disableWebview) {
        this.version = version;
        this.name = name;
        this.disableWebview = disableWebview;
    }

    public List<Integer> version() {
        return version;
    }
    public String getName() {
        return name;
    }

    public boolean isDisableWebview() {
        return disableWebview;
    }

    public static boolean isLocalUpdate() {
        for (RemoteServiceEnum value : values()) {
            //如果引入了 rcs-update，则由rcs执行更新
            if (StringUtils.equals(value.getName(), "rcs-update")) {
                return false;
            }
        }
        return true;
    }
}

package com.xhs.codewiz.scheme.content;

import java.util.*;
import com.xhs.codewiz.type.content.ActionItem;
/**
 * 获取 Action 完整内容
 */
public class ReadActionDetail {
    private String schemaProtocol = "content.read.actiondetail";
    private ReadActionDetailParams params;

    public ReadActionDetailParams getParams() {
        return params;
    }
    public void setParams(ReadActionDetailParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ReadActionDetailParams {
        /** Action Provider ID */
        private String provider;
        private ActionItem action;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public ActionItem getAction() {
            return action;
        }
        public void setAction(ActionItem action) {
            this.action = action;
        }
    }
}

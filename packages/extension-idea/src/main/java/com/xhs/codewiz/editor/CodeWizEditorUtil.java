package com.xhs.codewiz.editor;


import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.editor.CaretModel;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.LogicalPosition;
import com.intellij.openapi.editor.SelectionModel;
import com.intellij.openapi.editor.VisualPosition;
import com.intellij.openapi.editor.ex.DocumentEx;
import com.intellij.openapi.editor.ex.util.EditorUtil;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.TextEditor;
import com.intellij.openapi.fileEditor.impl.FileEditorManagerImpl;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Computable;
import com.intellij.openapi.util.Key;
import com.intellij.util.containers.ContainerUtil;
import com.xhs.codewiz.editor.request.EditorRequest;
import com.xhs.codewiz.utils.PluginUtil;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.annotations.TestOnly;

public final class CodeWizEditorUtil {
    static final Key<List<EditorRequest>> KEY_REQUESTS = Key.create("copilot.editorRequests");

    private CodeWizEditorUtil() {
    }

    public static boolean isFocusedEditor(@NotNull Editor editor) {
        if (ApplicationManager.getApplication().isUnitTestMode()) {
            return true;
        } else {
            return PluginUtil.isRemoteIDE() ? true : editor.getContentComponent().isFocusOwner();
        }
    }

    public static boolean isSelectedEditor(@NotNull Editor editor) {
        Project project = editor.getProject();
        if (project != null && !project.isDisposed()) {
            FileEditorManager editorManager = FileEditorManager.getInstance(project);
            if (editorManager == null) {
                return false;
            } else if (editorManager instanceof FileEditorManagerImpl) {
                Editor current = ((FileEditorManagerImpl)editorManager).getSelectedTextEditor(true);
                return current != null && current.equals(editor);
            } else {
                FileEditor current = editorManager.getSelectedEditor();
                return current instanceof TextEditor && editor.equals(((TextEditor)current).getEditor());
            }
        } else {
            return false;
        }
    }

    public static long getDocumentModificationStamp(@NotNull Document document) {
        return document instanceof DocumentEx ? (long)((DocumentEx)document).getModificationSequence() : document.getModificationStamp();
    }

    public static int whitespacePrefixLength(@NotNull String lineContent) {
        int maxLength = lineContent.length();

        int i;
        for(i = 0; i < maxLength; ++i) {
            char c = lineContent.charAt(i);
            if (c != ' ' && c != '\t') {
                return i;
            }
        }

        return i;
    }
    public static int getCaretOffset(@NotNull Editor editor) {
        return ApplicationManager.getApplication().runReadAction((Computable<Integer>)() -> {
            CaretModel caretModel = editor.getCaretModel();
            return caretModel.getOffset();
        });
    }

    public static void addEditorRequest(@NotNull Editor editor, @NotNull EditorRequest request) {
        EditorUtil.disposeWithEditor(editor, request.getDisposable());
        if (!KEY_REQUESTS.isIn(editor)) {
            KEY_REQUESTS.set(editor, ContainerUtil.createLockFreeCopyOnWriteList());
        }

        ((List)KEY_REQUESTS.getRequired(editor)).add(request);
    }

    @TestOnly
    public static List<EditorRequest> getEditorRequests(@NotNull Editor editor) {
        return (List)KEY_REQUESTS.get(editor, List.of());
    }

    public static @Nullable Editor getSelectedEditorSafely(@NotNull Project project) {
        try {
            FileEditorManager editorManager = FileEditorManager.getInstance(project);
            return editorManager != null ? editorManager.getSelectedTextEditor() : null;
        } catch (Exception var2) {
            return null;
        }
    }

    public static SelectionModelInfo getSelectionModelInfo(@NotNull SelectionModel selectionModel) {
        return ReadAction.compute(() -> new SelectionModelInfo(selectionModel));
    }

    public static class SelectionModelInfo {
        String selectedText;
        VisualPosition selectionStartPosition;
        VisualPosition selectionEndPosition;
        int selectionStart;
        int selectionEnd;

        public SelectionModelInfo(SelectionModel selectionModel) {
            this.selectedText = selectionModel.getSelectedText();
            this.selectionStart = selectionModel.getSelectionStart();
            this.selectionEnd = selectionModel.getSelectionEnd();
            LogicalPosition logicalStart = selectionModel.getEditor().offsetToLogicalPosition(selectionStart);
            selectionStartPosition = new VisualPosition(logicalStart.line, logicalStart.column);
            LogicalPosition logicalEnd = selectionModel.getEditor().offsetToLogicalPosition(selectionEnd);
            selectionEndPosition = new VisualPosition(logicalEnd.line, logicalEnd.column);
        }

        public SelectionModelInfo(String selectedText, VisualPosition selectionStartPosition, VisualPosition selectionEndPosition, int selectionStart, int selectionEnd) {
            this.selectedText = selectedText;
            this.selectionStartPosition = selectionStartPosition;
            this.selectionEndPosition = selectionEndPosition;
            this.selectionStart = selectionStart;
            this.selectionEnd = selectionEnd;
        }

        public String getSelectedText() {
            return this.selectedText;
        }

        public VisualPosition getSelectionStartPosition() {
            return this.selectionStartPosition;
        }

        public VisualPosition getSelectionEndPosition() {
            return this.selectionEndPosition;
        }

        public int getSelectionStart() {
            return this.selectionStart;
        }

        public int getSelectionEnd() {
            return this.selectionEnd;
        }

        public void setSelectedText(String selectedText) {
            this.selectedText = selectedText;
        }

        public void setSelectionStartPosition(VisualPosition selectionStartPosition) {
            this.selectionStartPosition = selectionStartPosition;
        }

        public void setSelectionEndPosition(VisualPosition selectionEndPosition) {
            this.selectionEndPosition = selectionEndPosition;
        }

        public void setSelectionStart(int selectionStart) {
            this.selectionStart = selectionStart;
        }

        public void setSelectionEnd(int selectionEnd) {
            this.selectionEnd = selectionEnd;
        }
    }
}


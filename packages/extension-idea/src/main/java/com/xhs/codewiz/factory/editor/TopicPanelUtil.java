package com.xhs.codewiz.factory.editor;

import java.util.HashMap;
import java.util.Map;

import javax.swing.SwingConstants;

import org.apache.commons.lang3.StringUtils;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.ex.FileEditorManagerEx;
import com.intellij.openapi.fileEditor.impl.EditorWindow;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.xhs.codewiz.factory.webview.WebviewBuilder;
import com.xhs.codewiz.listener.TopicEditorFocusCallBackService;
import com.xhs.codewiz.service.WebviewService;
import com.xhs.codewiz.type.platform.Webview;
import com.xhs.codewiz.type.platform.WebviewProvider;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.JBCefBrowserUtil;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * panel放入editor
 *
 * <AUTHOR>
 * @date 2025/8/6 14:33
 */
public class TopicPanelUtil {

    private static final Logger LOG = Logger.getInstance(TopicPanelUtil.class);

    private static final Map<String, TopicEditorVirtualFile> TOPIC_EDITOR_FILE = new HashMap<>();

    // public static void showTopicPanel(BuildInPannel.BuildInPannelViews webview,
    //                                   ExecuteShowPannel.ExecuteShowPannelParamsOptions options,
    //                                   String channel) {
    //
    // }

    public static void openTopicEditor(Project project, String remoteChannel, WebviewProvider webviewProvider) {
        LoggerUtil.INSTANCE.logInfo(LOG,
                "[Stage2-TopicPanel] openTopicEditor started - project: " + project.getName() + ", remoteChannel: " + remoteChannel +
                        ", providerId: " + webviewProvider.getId());
        WebviewBuilder webviewBuilder = new WebviewBuilder(
                null,
                webviewProvider,
                remoteChannel,
                null,
                project
        );
        Webview webview = WebviewService.readWebview(project, webviewBuilder);
        webviewBuilder.setWebview(webview);

        // 创建cef实例，用于加载webview
        JBCefBrowserUtil.buildBrowserPanel(project, webviewBuilder);

        // 确保焦点监听器已初始化
        // ensureTopicFocusManagerInitialized(project);

        // topic webview 创建逻辑
        buildAndShowTopicFile(project, remoteChannel, webviewProvider.getId(),
                StringUtils.isEmpty(webview.getTitle()) ? "codewiz topic" : webview.getTitle());
    }

    public static void buildAndShowTopicFile(Project project,
                                             String remoteChannel,
                                             String providerId,
                                             String fileName) {

        TopicEditorVirtualFile virtualFile = buildChatTopicFile(project, remoteChannel, providerId, fileName);
        LoggerUtil.INSTANCE.logInfo(LOG,
                "[Stage6-TopicPanel] buildChatTopicFile completed - fileName: " + fileName + ", virtualFileCreated: " +
                        (virtualFile != null));
        showChatTopic(project, virtualFile);
    }

    public static TopicEditorVirtualFile buildChatTopicFile(Project project,
                                                            String remoteChannel,
                                                            String providerId,
                                                            String fileName) {
        try {
            // 创建虚拟文件
            // TopicEditorVirtualFile virtualFile;
            // String topicEditorKey = buildTopicEditorKey(project, webviewBuilder);
            //
            // if (TOPIC_EDITOR_FILE.containsKey(topicEditorKey)) {
            //     LoggerUtil.INSTANCE.logInfo(LOG, "topicEditorKey: " + topicEditorKey + " already exists, file name: " + fileName);
            //     virtualFile = TOPIC_EDITOR_FILE.get(topicEditorKey);
            //     virtualFile.setWebviewBuilder(webviewBuilder);
            // } else {
            //     LoggerUtil.INSTANCE.logInfo(LOG, "topicEditorKey: " + topicEditorKey + " not exists, file name: " + fileName);
            //     virtualFile = new TopicEditorVirtualFile(fileName, webviewBuilder);
            //     TOPIC_EDITOR_FILE.put(topicEditorKey, virtualFile);
            // }
            //
            // return virtualFile;

            return new TopicEditorVirtualFile(fileName, remoteChannel, providerId);
        } catch (Exception ex) {
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed to show chat editor", ex);
            Messages.showErrorDialog(project, "无法显示聊天编辑器: " + ex.getMessage(), "错误");
            return null;
        }
    }

    private static void showChatTopic(Project project, TopicEditorVirtualFile virtualFile) {
        FileEditorManagerEx fileEditorManager = FileEditorManagerEx.getInstanceEx(project);

        TopicFileEditor existingEditor = getExistingTopicEditor(fileEditorManager);
        if (existingEditor != null) {
            // 如果已经存在TopicEditor，更新内容而不是打开新的
            LoggerUtil.INSTANCE.logInfo(LOG, "hasTopicEditor, updating content instead of opening new");
            existingEditor.updateName(virtualFile.getName());

            // 激活现有的编辑器标签页
            fileEditorManager.openFile(existingEditor.getVirtualFile(), false);
            return;
        }

        // 检查是否存在其他打开的编辑器
        if (hasOpenEditors(fileEditorManager)) {
            // 如果存在其他编辑器，执行分屏操作
            LoggerUtil.INSTANCE.logInfo(LOG, "hasOpenEditors, splitAndOpenEditor");
            splitAndOpenEditor(fileEditorManager, virtualFile);
        } else {
            // 如果没有其他编辑器，直接打开
            LoggerUtil.INSTANCE.logInfo(LOG, "noOpenEditors, openFile");
            fileEditorManager.openFile(virtualFile, false);
        }
    }

    public static void updateEditorTitle(String title) {
        Project currentProject = ApplicationUtil.findCurrentProject();
        if (currentProject == null) {
            LoggerUtil.INSTANCE.logWarn(LOG, "updateEditorTitle: currentProject is null");
            return;
        }
        FileEditorManagerEx fileEditorManager = FileEditorManagerEx.getInstanceEx(currentProject);
        TopicFileEditor existingEditor = getExistingTopicEditor(fileEditorManager);
        if (existingEditor != null) {
            // 如果已经存在TopicEditor，更新内容而不是打开新的
            LoggerUtil.INSTANCE.logInfo(LOG, "hasTopicEditor, updating panel title");
            existingEditor.updateName(title);

            // 激活现有的编辑器标签页
            fileEditorManager.openFile(existingEditor.getVirtualFile(), false);
        }
    }

    public static void openExistEditor(Project currentProject) {
        if (currentProject == null) {
            LoggerUtil.INSTANCE.logWarn(LOG, "updateEditorTitle: currentProject is null");
            return;
        }
        FileEditorManagerEx fileEditorManager = FileEditorManagerEx.getInstanceEx(currentProject);
        TopicFileEditor existingEditor = getExistingTopicEditor(fileEditorManager);
        if (existingEditor != null) {
            // 激活现有的编辑器标签页
            fileEditorManager.openFile(existingEditor.getVirtualFile(), true);
        }
    }

    private static String buildTopicEditorKey(Project project, WebviewBuilder webviewBuilder) {
        String providerId = webviewBuilder.getWebviewProvider().getId();
        String remoteChannel = webviewBuilder.getRemoteChannel();
        String projectName = project.getName();
        return projectName + "#" + providerId + "#" + remoteChannel;
    }

    /**
     * 检查是否有打开的编辑器
     */
    private static boolean hasOpenEditors(FileEditorManagerEx fileEditorManager) {
        return fileEditorManager.getAllEditors().length > 0;
    }

    private static boolean hasTopicEditor(FileEditorManagerEx fileEditorManager) {
        FileEditor[] editors = fileEditorManager.getAllEditors();
        for (FileEditor editor : editors) {
            if (editor instanceof TopicFileEditor) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取现有的TopicFileEditor实例
     */
    public static TopicFileEditor getExistingTopicEditor(FileEditorManagerEx fileEditorManager) {
        FileEditor[] editors = fileEditorManager.getAllEditors();
        for (FileEditor editor : editors) {
            if (editor instanceof TopicFileEditor) {
                return (TopicFileEditor) editor;
            }
        }
        return null;
    }

    /**
     * 分屏并打开编辑器，实现split and move right效果
     */
    private static void splitAndOpenEditor(FileEditorManagerEx fileEditorManager, TopicEditorVirtualFile virtualFile) {
        // 获取当前活动的编辑器窗口
        EditorWindow currentWindow = fileEditorManager.getCurrentWindow();
        if (currentWindow != null) {
            // 创建右侧分屏并直接在新窗口中打开文件
            currentWindow.split(SwingConstants.VERTICAL, false, virtualFile, true);
        } else {
            // 如果没有当前窗口，直接打开
            fileEditorManager.openFile(virtualFile, false);
        }
    }

    // /**
    //  * 清理指定项目的所有TopicEditorVirtualFile
    //  * 在项目关闭时调用
    //  */
    // public static void clearTopicEditorsForProject(Project project) {
    //     if (project == null) {
    //         return;
    //     }
    //
    //     String projectName = project.getName();
    //     // 移除与该项目相关的所有缓存文件
    //     TOPIC_EDITOR_FILE.entrySet().removeIf(entry ->
    //             entry.getKey().startsWith(projectName + "#"));
    // }
    //
    // /**
    //  * 获取指定项目的所有TopicEditorVirtualFile
    //  */
    // public static List<TopicEditorVirtualFile> getTopicEditorsForProject(Project project) {
    //     if (project == null) {
    //         return new java.util.ArrayList<>();
    //     }
    //
    //     String projectName = project.getName();
    //     return TOPIC_EDITOR_FILE.entrySet().stream()
    //             .filter(entry -> entry.getKey().startsWith(projectName + "#"))
    //             .map(Map.Entry::getValue)
    //             .collect(java.util.stream.Collectors.toList());
    // }

    /**
     * 确保 Topic Editor 焦点管理器已初始化
     */
    private static void ensureTopicFocusManagerInitialized(Project project) {
        if (project == null || project.isDisposed()) {
            return;
        }

        try {
            // 获取焦点管理器实例（如果不存在会自动创建）
            TopicEditorFocusCallBackService focusManager = TopicEditorFocusCallBackService.getInstance(project);
            LoggerUtil.INSTANCE.logInfo(LOG, "Topic Editor 焦点管理器已准备就绪 [Project: " + project.getName() + "]");
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(LOG, "初始化 Topic Editor 焦点管理器失败", e);
        }
    }

    /**
     * 获取 Topic Editor 焦点管理器
     *
     * @param project 项目实例
     * @return TopicEditorFocusManager 实例，如果项目无效则返回 null
     */
    public static TopicEditorFocusCallBackService getTopicFocusManager(Project project) {
        if (project == null || project.isDisposed()) {
            return null;
        }
        return TopicEditorFocusCallBackService.getInstance(project);
    }

    /**
     * 检查当前是否有 Topic Editor 处于焦点状态
     *
     * @param project 项目实例
     * @return 如果有 Topic Editor 获得焦点则返回 true
     */
    public static boolean hasTopicEditorFocused(Project project) {
        TopicEditorFocusCallBackService manager = getTopicFocusManager(project);
        return manager != null && manager.hasTopicEditorFocused();
    }

    /**
     * 获取当前焦点的 Topic Editor
     *
     * @param project 项目实例
     * @return 当前焦点的 TopicFileEditor 实例，如果没有则返回 null
     */
    public static TopicFileEditor getCurrentFocusedTopicEditor(Project project) {
        TopicEditorFocusCallBackService manager = getTopicFocusManager(project);
        return manager != null ? manager.getCurrentFocusedTopicEditor() : null;
    }

}

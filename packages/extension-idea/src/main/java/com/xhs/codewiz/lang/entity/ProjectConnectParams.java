package com.xhs.codewiz.lang.entity;

import com.google.gson.annotations.SerializedName;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcCommand;

/**
 * Author: liukunpeng Date: 2025-03-18 Description:
 */
public class ProjectConnectParams implements JsonRpcCommand<Boolean> {
  @SerializedName("projectUri")
  private String projectUri;

  @SerializedName("autoStart")
  private boolean autoStart;

  public String getProjectUri() {
    return projectUri;
  }

  public void setProjectUri(String projectUri) {
    this.projectUri = projectUri;
  }

  public boolean isAutoStart() {
    return autoStart;
  }
  public void setAutoStart(boolean autoStart) {
    this.autoStart = autoStart;
  }
  @Override
  public String getCommandName() {
    return "tylm/checkProjectConnectStatus";
  }

  @Override
  public Class<Boolean> getResponseType() {
    return Boolean.class;
  }
}

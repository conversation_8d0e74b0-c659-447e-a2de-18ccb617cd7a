package com.xhs.codewiz.scheme.global;

import java.util.*;
import com.xhs.codewiz.type.global.CommandType;
/**
 * 执行全局命令
 */
public class ExecuteCommand {
    private String schemaProtocol = "global.execute.command";
    private ExecuteCommandParams params;

    public ExecuteCommandParams getParams() {
        return params;
    }
    public void setParams(ExecuteCommandParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteCommandParams {
        /** 命令参数 */
        private List<String> args; // optional
        private CommandType type;
        /** 要执行的命令 */
        private String command;
    
        public List<String> getArgs() {
            return args;
        }
        public void setArgs(List<String> args) {
            this.args = args;
        }
        public CommandType getType() {
            return type;
        }
        public void setType(CommandType type) {
            this.type = type;
        }
        public String getCommand() {
            return command;
        }
        public void setCommand(String command) {
            this.command = command;
        }
    }
}

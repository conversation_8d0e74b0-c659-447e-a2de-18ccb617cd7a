package com.xhs.codewiz.actions.problem;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.psi.PsiFile;
import com.intellij.util.IncorrectOperationException;
import com.xhs.codewiz.client.service.RcsContentService;
import com.xhs.codewiz.scheme.content.CreateActionProvider;
import com.xhs.codewiz.scheme.content.UpdateContent;
import com.xhs.codewiz.scheme.content.UpdateContent.UpdateContentParams;
import com.xhs.codewiz.scheme.content.UpdateRanges;
import com.xhs.codewiz.scheme.content.UpdateRanges.UpdateRangesParams;
import com.xhs.codewiz.type.content.ActionItem;
import com.xhs.codewiz.type.content.Changes;
import com.xhs.codewiz.type.workspace.FileChanges;
import com.xhs.codewiz.type.workspace.FileChanges.FileChangesContent;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

/**
 * 问题修复操作子类，动态加载/卸载用
 */
public class DynamicCodeProblemsIntentionAction extends CodeProblemsIntentionAction {
    private static Logger log = Logger.getInstance(DynamicCodeProblemsIntentionAction.class);
    private final ActionItem actionItem;
    private final CreateActionProvider provider;
    
    public DynamicCodeProblemsIntentionAction(ActionItem actionItem, CreateActionProvider provider) {
        this.actionItem = actionItem;
        this.provider = provider;
    }

    public boolean isAvailable(@NotNull Project project, Editor editor, PsiFile psiFile) {
        return true;
    }
    public void invoke(@NotNull Project project, Editor editor, PsiFile psiFile) throws IncorrectOperationException {
        try {
            //执行动作
            if (null != actionItem.getCommand()) {
                //需要执行命令
                ThreadUtil.execute(() -> {
                    //TODO 需要接入executeCommand
                });
            }
            //开始执行本地文本覆盖
            String uri = null;
            if (CollectionUtils.isNotEmpty(actionItem.getEdit())) {
                List<FileChanges> changesList = actionItem.getEdit();
                for (FileChanges fileChanges : changesList) {
                    if (fileChanges instanceof FileChangesContent contentChanges) {
                        UpdateContent updateContent = new UpdateContent();
                        UpdateContentParams params = new UpdateContentParams();
                        Changes changes = new Changes();
                        changes.setChanges(contentChanges.getChanges());
                        params.setChanges(changes);
                        uri = contentChanges.getUri();
                        params.setUri(uri);
                        updateContent.setParams(params);
                        RcsContentService.contentUpdate(GsonUtil.toJson(updateContent), "");
                    }
                }

            }
            //范围选中
            if (null != uri && CollectionUtils.isNotEmpty(actionItem.getRanges())) {
                UpdateRanges updateRanges = new UpdateRanges();
                UpdateRangesParams params = new UpdateRangesParams();
                params.setRanges(actionItem.getRanges());
                params.setUri(uri);
                updateRanges.setParams(params);
                RcsContentService.rangesUpdate(GsonUtil.toJson(updateRanges), "");
            }
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "IntentionAction 执行失败", e);
        } finally {
            //执行操作后，销毁本次拉取的所有操作及相关对象
            super.destroy();
        }
    }
    @NotNull
    @Override
    public String getText() {
        return actionItem.getTitle();
    }
    
    @NotNull
    @Override
    public String getFamilyName() {
        return actionItem.getTitle();
    }
} 
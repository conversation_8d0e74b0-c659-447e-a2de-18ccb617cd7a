package com.xhs.codewiz.utils;

import java.util.Objects;

import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.fileEditor.ex.FileEditorManagerEx;
import com.intellij.openapi.fileEditor.impl.EditorWindow;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.xhs.codewiz.factory.editor.TopicEditorVirtualFile;

/**
 * 智能文件打开拦截器
 * 监听文件打开事件，在文件实际打开后检查是否需要移动到合适的窗口
 *
 * <AUTHOR>
 * @date 2025/1/27 19:20
 */
public final class SmartFileOpenUtil {

    public static void syncFileOpen(@NotNull VirtualFile file, @NotNull Project project) {
        // invoke later
        ApplicationManager.getApplication().invokeLater(() -> {
            // 获取当前项目
            handleFileOpenedPost(file, project);
        });
    }

    /**
     * 处理文件打开后的智能重定位
     */
    public static void handleFileOpenedPost(@NotNull VirtualFile file, @NotNull Project project) {
        if (file instanceof TopicEditorVirtualFile) {
            // Topic文件不需要重定位
            return;
        }

        // 检查当前是否有topic editor处于活跃状态
        // FileEditor selectedEditor = fileEditorManager.getSelectedEditor();
        // if (!(selectedEditor instanceof TopicFileEditor)) {
        //     // 当前不在topic editor中，不需要重定位
        //     return;
        // }

        // 检查文件是否在包含topic editor的窗口中打开
        if (isFileInTopicWindow(file, project)) {
            // 文件在topic窗口中，需要移动到其他窗口
            moveFileToAlternativeWindow(file, project);
        }
    }

    /**
     * 检查文件是否在包含topic editor的窗口中
     */
    private static boolean isFileInTopicWindow(@NotNull VirtualFile file, @NotNull Project project) {
        FileEditorManagerEx fileEditorManager = FileEditorManagerEx.getInstanceEx(project);

        // 找到包含该文件的窗口
        for (EditorWindow window : fileEditorManager.getWindows()) {
            if (window.isFileOpen(file)) {
                // 检查这个窗口是否包含topic editor
                VirtualFile[] filesInWindow = window.getFileList().toArray(new VirtualFile[0]);
                for (VirtualFile vf : filesInWindow) {
                    if (vf instanceof TopicEditorVirtualFile) {
                        return true; // 找到了topic文件，说明在topic窗口中
                    }
                }
            }
        }
        return false;
    }

    /**
     * 将文件移动到其他窗口
     */
    private static void moveFileToAlternativeWindow(@NotNull VirtualFile file, @NotNull Project project) {
        FileEditorManagerEx fileEditorManager = FileEditorManagerEx.getInstanceEx(project);

        // 找到不包含topic的窗口
        com.intellij.openapi.fileEditor.impl.EditorWindow targetWindow = null;
        for (com.intellij.openapi.fileEditor.impl.EditorWindow window : fileEditorManager.getWindows()) {
            if (!containsTopicFile(window)) {
                targetWindow = window;
                break;
            }
        }

        if (targetWindow != null) {
            // 关闭当前窗口中的文件
            fileEditorManager.closeFile(file);

            // 在目标窗口中重新打开
            fileEditorManager.setCurrentWindow(targetWindow);
            fileEditorManager.openFile(file, true);
        } else {
            // 如果没有合适的窗口，在当前窗口右侧分屏
            com.intellij.openapi.fileEditor.impl.EditorWindow currentWindow = fileEditorManager.getCurrentWindow();
            if (currentWindow != null) {
                fileEditorManager.closeFile(file);
                currentWindow.split(javax.swing.SwingConstants.RIGHT, false, file, true);
            }
        }
    }

    /**
     * 检查窗口是否包含topic文件
     */
    private static boolean containsTopicFile(com.intellij.openapi.fileEditor.impl.EditorWindow window) {
        if (window == null) {
            return false;
        }
        VirtualFile[] filesInWindow = window.getFileList().toArray(new VirtualFile[0]);
        for (VirtualFile vf : filesInWindow) {
            if (vf instanceof TopicEditorVirtualFile) {
                return true;
            }
        }
        return false;
    }

    public static boolean containsTopicEditorVirtualFile(EditorWindow window) {
        if (window == null) {
            return false;
        }
        VirtualFile[] filesInWindow = window.getFiles();
        if (filesInWindow == null || filesInWindow.length == 0) {
            return false;
        }
        for (VirtualFile vf : filesInWindow) {
            if (vf instanceof TopicEditorVirtualFile) {
                return true;
            }
        }
        return false;
    }

    public static EditorWindow findAlternativeWindowWithoutTopic(EditorWindow[] all, EditorWindow exclude) {
        if (all == null || all.length == 0) {
            return null;
        }
        for (EditorWindow w : all) {
            if (w == null || w.isDisposed()) {
                continue;
            }
            if (Objects.equals(w, exclude)) {
                continue;
            }
            if (!containsTopicEditorVirtualFile(w)) {
                return w; // 选择第一个不包含 Topic 的窗口
            }
        }
        return null;
    }
}

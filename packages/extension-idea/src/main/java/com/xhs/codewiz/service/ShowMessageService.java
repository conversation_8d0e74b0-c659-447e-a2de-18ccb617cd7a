package com.xhs.codewiz.service;

import com.xhs.codewiz.client.RcsWebSocketManager;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.google.common.collect.Lists;
import com.intellij.notification.Notification;
import com.intellij.notification.NotificationType;
import com.intellij.notification.Notifications;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.xhs.codewiz.scheme.platform.ExecuteShowMessage;
import com.xhs.codewiz.type.global.Severity;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.BundleUtil;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * <AUTHOR>
 * @date 2025/7/25 15:43
 */
public class ShowMessageService {

    private static final Logger log = Logger.getInstance(ShowMessageService.class);

    private static final Map<String, String> messageValue = new HashMap<>();

    private static final Long sleepTime = 1000L; // 1s

    private static final int maxSleepNum = 20;

    public static String showMessage(String param, String channel) {
        ExecuteShowMessage showMessage = GsonUtil.fromJson(param, ExecuteShowMessage.class);
        if (showMessage == null || showMessage.getParams() == null) {
            LoggerUtil.INSTANCE.logInfo(log, "showMessage showMessage is null");
            return StringUtils.EMPTY;
        }
        if (showMessage.getParams().getOptions() != null) {
            LoggerUtil.INSTANCE.logInfo(log, "showMessage options: " + showMessage.getParams().getOptions());
        }
        if (showMessage.getParams().getMessage() != null) {
            LoggerUtil.INSTANCE.logInfo(log, "showMessage message: " + showMessage.getParams().getMessage());
        }

        String uuid = UUID.randomUUID().toString();
        Project currentProject = RcsWebSocketManager.INSTANCE.getProjectByLocalChannel(channel);
        if (currentProject == null) {
            LoggerUtil.INSTANCE.logInfo(log, "showMessage no project found");
            return "no project found";
        }
        String key = currentProject.getName() + uuid;
        rcsShowMessage(
            showMessage,
            currentProject,
            key);
        int sleepNum = 0;
        while (true) {
            sleepNum++;
            // 等待sleepTime次数返回消息值
            try {
                if (messageValue.containsKey(key)) {
                    break;
                }
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                LoggerUtil.INSTANCE.logWarn(log, "showMessage InterruptedException: " + e.getMessage());
            }
            if (sleepNum > maxSleepNum) {
                break;
            }
        }
        return messageValue.getOrDefault(key, getDefaultText(showMessage));
    }

    public static void rcsShowMessage(ExecuteShowMessage executeShowMessage,
                                      Project project,
                                      String key) {
        // severity
        Severity severity = executeShowMessage.getParams().getType();
        // message
        ExecuteShowMessage.ExecuteShowMessageParamsMessage message = executeShowMessage.getParams().getMessage();
        // options
        ExecuteShowMessage.ExecuteShowMessageParamsOptions options = executeShowMessage.getParams().getOptions();

        if (!Objects.isNull(options) && !Objects.isNull(options.getModal()) && options.getModal()) {
            // 增加对话框
            ApplicationManager.getApplication().invokeLater(() -> {
                showModalDialog(severity, message, project, key);
            });
        } else {
            // 增加通知框
            ApplicationManager.getApplication().invokeLater(() -> {
                showNotify(project, message, severity, key);
            });
        }
    }

    private static void showNotify(Project project, ExecuteShowMessage.ExecuteShowMessageParamsMessage message, Severity severity, String key) {
        Notification notification = new Notification(
                BundleUtil.get("codewiz.plugin.id"),
                BundleUtil.get("notifications.rcs.group.name"),
                message.getTitle(),
                getNotificationType(severity));

        // 增加actions
        notification.addActions(ShowMessageService.buildActionsByOptions(message.getOptions(), key, notification));

        // 设置通知框
        Notifications.Bus.notify(notification, project);
        // 自动关闭
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                notification.expire();
                timer.cancel();
            }
        }, sleepTime * maxSleepNum);
    }

    private static NotificationType getNotificationType(Severity type) {
        return switch (type) {
            case Warning -> NotificationType.WARNING;
            case Error -> NotificationType.ERROR;
            default -> NotificationType.INFORMATION;
        };
    }

    public static void setMessageValue(String key, String value) {
        messageValue.put(key, value);
    }

    public static String getMessageValue(String key) {
        return messageValue.getOrDefault(key, "yes");
    }

    public static List<AnAction> buildActionsByOptions(List<ExecuteShowMessage.ExecuteShowMessageParamsMessageOptions> options,
                                                       String key,
                                                       Notification notification) {
        List<AnAction> anActions = Lists.newArrayList();
        if (CollectionUtils.isEmpty(options)) {
            setMessageValue(key, "no action options");
            return anActions;
        }

        for (ExecuteShowMessage.ExecuteShowMessageParamsMessageOptions option : options) {
            anActions.add(new AnAction(option.getTitle()) {
                @Override
                public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
                    LoggerUtil.INSTANCE.logInfo(log, "showMessage options: " + option.getTitle());
                    String value = this.getTemplatePresentation().getText();
                    setMessageValue(key, value);
                    // 关闭弹窗
                    notification.expire();
                }
            });
        }

        return anActions;
    }


    public static void showModalDialog(Severity severity,
                                       ExecuteShowMessage.ExecuteShowMessageParamsMessage message,
                                       @Nullable Project project,
                                       String key) {
        String title = BundleUtil.get("notifications.rcs.group.name");
        String messageText = message.getTitle();
        List<ExecuteShowMessage.ExecuteShowMessageParamsMessageOptions> messageOptions = message.getOptions();

        if (CollectionUtils.isEmpty(messageOptions)) {
            // 显示简单的确认对话框
            switch (severity) {
                case Error -> Messages.showErrorDialog(project, messageText, title);
                case Warning -> Messages.showWarningDialog(project, messageText, title);
                default -> Messages.showInfoMessage(project, messageText, title);
            }
        } else {
            // 显示带选项的对话框
            String[] options = messageOptions.stream()
                    .map(ExecuteShowMessage.ExecuteShowMessageParamsMessageOptions::getTitle)
                    .toArray(String[]::new);

            int selectedOption = switch (severity) {
                case Error -> Messages.showDialog(project, messageText, title, options, 0, Messages.getErrorIcon());
                case Warning -> Messages.showDialog(project, messageText, title, options, 0, Messages.getWarningIcon());
                default -> Messages.showDialog(project, messageText, title, options, 0, Messages.getInformationIcon());
            };

            // 处理用户选择
            if (selectedOption >= 0 && selectedOption < messageOptions.size()) {
                ExecuteShowMessage.ExecuteShowMessageParamsMessageOptions selectedChoice = messageOptions.get(selectedOption);
                LoggerUtil.INSTANCE.logInfo(log, "Dialog option selected: " + selectedChoice.getTitle());

                setMessageValue(key, selectedChoice.getTitle());
            }
        }
    }

    private static String getDefaultText(ExecuteShowMessage showMessage) {
        //先都返回null，不然有些默认行为与预期不符
        return null;
        /*if (showMessage == null
                || showMessage.getParams() == null
                || showMessage.getParams().getMessage() == null
                || CollectionUtils.isEmpty(showMessage.getParams().getMessage().getOptions())) {
            LoggerUtil.INSTANCE.logInfo(log, "showMessage no options title");
            return "no options title";
        }
        // 如果params.message.options数组某个isCloseAffordance is true, 返回该数组元素的title
        for (ExecuteShowMessage.ExecuteShowMessageParamsMessageOptions option : showMessage.getParams().getMessage().getOptions()) {
            if (BooleanUtils.isTrue(option.getIsCloseAffordance())) {
                return option.getTitle();
            }
        }
        // 否则return params.message.options[1].title
        return showMessage.getParams().getMessage().getOptions().get(0).getTitle();*/
    }
}

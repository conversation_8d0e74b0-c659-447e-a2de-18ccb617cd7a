package com.xhs.codewiz.client.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Computable;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.testFramework.LightVirtualFile;
import com.xhs.codewiz.client.ProviderTypeEnum;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.scheme.content.UpdateContent;
import com.xhs.codewiz.scheme.content.UpdateContent.UpdateContentParams;
import com.xhs.codewiz.scheme.file.CreateFile;
import com.xhs.codewiz.scheme.file.CreateFile.CreateFileParams;
import com.xhs.codewiz.scheme.file.DeleteFile;
import com.xhs.codewiz.scheme.workspace.CreateContentProvider;
import com.xhs.codewiz.scheme.workspace.DeleteContentProvider;
import com.xhs.codewiz.scheme.workspace.ExecuteChanges;
import com.xhs.codewiz.scheme.workspace.ExecuteChanges.ExecuteChangesParams;
import com.xhs.codewiz.scheme.workspace.ReadContent;
import com.xhs.codewiz.scheme.workspace.UpdateContentProvider;
import com.xhs.codewiz.type.content.Changes;
import com.xhs.codewiz.type.file.Content;
import com.xhs.codewiz.type.workspace.FileChanges;
import com.xhs.codewiz.type.workspace.FileChanges.FileChangesContent;
import com.xhs.codewiz.type.workspace.FileChanges.FileChangesFile;
import com.xhs.codewiz.utils.ApplicationUtil;
import com.xhs.codewiz.utils.GitHandlerUtil;
import com.xhs.codewiz.utils.GsonUtil;

import com.xhs.codewiz.utils.LoggerUtil;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.file.Path;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * create on 2025/7/17 19:27
 */
public class RcsWorkspaceContentProviderService {

    private static final Logger logger = Logger.getInstance(RcsWorkspaceContentProviderService.class);

    // 存储ContentProvider的映射，key为providerId，value为虚拟文档映射
    private static final Map<String, Map<String, LightVirtualFile>> contentProviders = new ConcurrentHashMap<>();
    // 存储ContentProvider的映射，key为schema，value为providerId
    private static final Map<String, String> schemaWithProvider = new ConcurrentHashMap<>();
    public static final Map<String, AtomicBoolean> contentEditFlagMap = new ConcurrentHashMap<>();

    // Git内容缓存
    private static final Cache<String, String> GIT_CONTENT_CACHE = Caffeine.newBuilder()
        .expireAfterWrite(10, TimeUnit.MINUTES)
        .maximumSize(100)
        .build();

    /**
     * 调用IntelliJ IDEA的sdk，实现 创建一个新的 ContentProvider。ContentProvider 为虚拟文档 URI 提供内容，即你可以生成一个只读文档，显示在编辑器中，而不依赖本地文件。
     */
    public static void contentProviderCreate(String str, String channel) {
        CreateContentProvider createContentProvider = GsonUtil.fromJson(str, CreateContentProvider.class);

        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                if (createContentProvider == null || createContentProvider.getParams() == null) {
                    return;
                }
                String providerId = createContentProvider.getParams().getId();
                String schema = createContentProvider.getParams().getSchema();

                if (providerId == null || schema == null) {
                    logger.warn("Invalid content provider data");
                    return;
                }

                // 在IntelliJ IDEA中，ContentProvider主要通过LightVirtualFile实现
                // 注册Content Provider
                contentProviders.put(providerId, new ConcurrentHashMap<>());
                RcsWebSocketManager.INSTANCE.addProvider(channel, createContentProvider);
                if (!schemaWithProvider.containsKey(createContentProvider.getParams().getSchema())) {
                    schemaWithProvider.put(createContentProvider.getParams().getSchema(), createContentProvider.getParams().getId());
                }
                logger.info("Content provider created: " + providerId + " for schema: " + schema);
            } catch (Exception e) {
              LoggerUtil.INSTANCE.logWarn(logger, "Failed to create content provider", e);
            }
        });
    }

    public static VirtualFile getContentProviderFileByUri(String uri, String channel) {
        if (StringUtils.isEmpty(uri)) {
            return null;
        }
        String[] schemaArray = uri.split(":");
        String schema = schemaArray[0];
        if (schemaWithProvider.containsKey(schema)) {
            String providerId = schemaWithProvider.get(schema);
            Map<String, LightVirtualFile> providerContentMap = contentProviders.get(providerId);
            if (providerContentMap == null) {
              LoggerUtil.INSTANCE.logWarn(logger, "getContentProviderFileByUri error, providerContentMap is null, providerId:" + providerId + ", uri:" + uri);
                return null;
            }
            Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
            return readContentByContentUpdate(channel, providerId, uri, project);
        }
        //非远端自定义scheme,需要自己处理
        if (StringUtils.startsWith(uri, "git:")) {
            Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
            //需要解析diff协议
            String[] split = uri.split("\\?");
            String filePath = null, commitId = null;
            if (split.length > 1) {
                JsonObject json = GsonUtil.fromJson(URLDecoder.decode(split[1]));
                filePath = json.get("path").getAsString();
                commitId = json.get("ref").getAsString();
            }
            String finalFilePath = StringUtils.isEmpty(filePath) ? split[0].replace("git:", "") : filePath;
            String text = getFileContentByGitHead(project, finalFilePath, commitId);
            FileType fileType = getFileTypeFromPath(finalFilePath);
            LightVirtualFile virtualFile = new LightVirtualFile(uri, fileType, text);
            try {
                virtualFile.setWritable(false); // 设置为只读
            } catch (Exception e) {
                LoggerUtil.INSTANCE.logWarn(logger, "Failed to set read-only for virtual document");
            }
            return virtualFile;
        } else if (StringUtils.startsWith(uri, "untitled:")) {
            //代表空
            LightVirtualFile virtualFile = new LightVirtualFile(uri, "");
            return virtualFile;
        }
        //如果不在自定义范围内，尝试返回本地文件
        Path path = RcsFileService.getPathWithUri(uri);
        return VirtualFileManager.getInstance().refreshAndFindFileByNioPath(path);
    }

    /**
     * 调用IntelliJ IDEA的sdk，实现 获取该 provider 提供的虚拟内容。
     * @param newFile
     * @param manager
     */
    public static void contentProviderReadContent(VirtualFile newFile, FileEditorManager manager) {
        // 这里的str，应该是文件的 uri
        String[] schemaArray = newFile.getUrl().split("://");
        String schema = schemaArray[0];
        if (schemaWithProvider.containsKey(schema)) {
            Project project = ApplicationUtil.findCurrentProject();
            for (Map.Entry<String, Map<String, LightVirtualFile>> entry : contentProviders.entrySet()) {
                Map<String, LightVirtualFile> urlWithFileMap = entry.getValue();
                if (urlWithFileMap.containsKey(newFile.getUrl())) {
                    ReadContent readContent = new ReadContent();
                    ReadContent.ReadContentParams readContentParams = new ReadContent.ReadContentParams();
                    readContentParams.setId(entry.getKey());
                    readContentParams.setUri(newFile.getUrl());
                    readContent.setParams(readContentParams);
                    List<Content> contents =
                        RcsWebSocketManager.INSTANCE.sendRequestWithProvider(ProviderTypeEnum.CONTENT, readContent, Content.class,
                            project);
                    if (CollectionUtils.isEmpty(contents)) {
                      LoggerUtil.INSTANCE.logWarn(logger, "contentProviderReadContent error, contents is null, providerId:" + entry.getKey() + ",uri:" +
                            newFile.getUrl());
                        return;
                    }

                    LightVirtualFile virtualFile = urlWithFileMap.get(newFile.getUrl());
                    Content content = contents.get(0);

                    try {
                        // 刷新IDEA，让IDEA实时刷新文件状态
                        LightVirtualFile finalVirtualFile = virtualFile;
                        ApplicationManager.getApplication().invokeLater(() -> {
                            manager.openFile(finalVirtualFile, true);
                            updateFileContent(project, finalVirtualFile, content.getText());
                        });
                        logger.info("Updated existing virtual file: " + newFile.getUrl());
                    } catch (Exception e) {
                      LoggerUtil.INSTANCE.logWarn(logger, "Failed to update virtual file content", e);
                    }
                    return;
                }
            }
        }
    }

    /**
     * 调用IntelliJ IDEA的sdk，实现 通知本地，该 provider 提供的内容发生变化
     */
    public static void contentProviderUpdate(String str, String channel) {
        UpdateContentProvider updateContentProvider = GsonUtil.fromJson(str, UpdateContentProvider.class);

        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                if (updateContentProvider == null || updateContentProvider.getParams() == null) {
                    return;
                }
                String providerId = updateContentProvider.getParams().getId();
                String uri = updateContentProvider.getParams().getUri();

                if (providerId == null || uri == null) {
                    logger.warn("Invalid update content provider data");
                    return;
                }
                Project projectByChannel = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);

                LightVirtualFile lightVirtualFile = readContentByContentUpdate(channel, providerId, uri, projectByChannel);
                if (lightVirtualFile != null) {
                    Map<String, LightVirtualFile> lightVirtualFileMap = contentProviders.get(providerId);
                    lightVirtualFileMap.put(lightVirtualFile.getUrl(), lightVirtualFile);
                }
            } catch (Exception e) {
              LoggerUtil.INSTANCE.logWarn(logger, "Failed to update content provider", e);
            }
        });
    }

    public static LightVirtualFile readContentByContentUpdate(String channel, String providerId, String uri, Project project) {
        // 拉取文件，更新内容
        ReadContent readContent = new ReadContent();
        ReadContent.ReadContentParams readContentParams = new ReadContent.ReadContentParams();
        readContentParams.setId(providerId);
        readContentParams.setUri(uri);
        readContent.setParams(readContentParams);
        Content content = RcsWebSocketManager.INSTANCE.sendRequestWithChannelProvider(channel, providerId, readContent, new TypeToken<Content>(){},  project, 10000);
        if (null == content) {
            LoggerUtil.INSTANCE.logWarn(logger, "readContentByContentUpdate error, contents is null, providerId:" + providerId + ",uri:" + uri);
            content = new Content();
            content.setText("");
            content.setLanguageId("");
        }

        // 拉取到更新内容后
        // 1. 查看IDEA的文件系统里是否有该临时虚拟文件
        // 2. 有的情况下，直接更新文件内容，并刷新IDEA。让IDEA实时刷新文件状态
        // 3. 没有的情况下，新建一个虚拟文件，并将内容写入新的虚拟文件内，同时让IDEA当前项目打开该文件

        // 确保provider存在
        Map<String, LightVirtualFile> providerFiles = contentProviders.get(providerId);
        if (providerFiles == null) {
            providerFiles = new ConcurrentHashMap<>();
            contentProviders.put(providerId, providerFiles);
        }

        LightVirtualFile virtualFile = providerFiles.get(uri);

        if (virtualFile != null) {
            // 情况2：虚拟文件已存在，更新其内容
            try {

                // 刷新IDEA，让IDEA实时刷新文件状态
                if (project != null) {
                    LightVirtualFile finalVirtualFile = virtualFile;
                    Content finalContent = content;
                    ApplicationManager.getApplication().invokeLater(() -> {
                        // 确保文件在编辑器中保持打开状态
                        //FileEditorManager.getInstance(project).openFile(finalVirtualFile, true);
                        updateFileContent(project, finalVirtualFile, finalContent.getText());
                    });
                }
                logger.info("Updated existing virtual file: " + uri);
            } catch (Exception e) {
              LoggerUtil.INSTANCE.logWarn(logger, "Failed to update virtual file content", e);
            }
        } else {
            // 情况3：虚拟文件不存在，创建新的虚拟文件
            virtualFile = createVirtualDocument(uri, content);
            if (virtualFile != null) {
                providerFiles.put(uri, virtualFile);

                // 让IDEA当前项目打开该文件
                if (project != null) {
                    LightVirtualFile finalVirtualFile1 = virtualFile;
                    /*ApplicationManager.getApplication().invokeLater(() -> {
                        FileEditorManager.getInstance(project).openFile(finalVirtualFile1, true);
                    });*/
                }
                logger.info("Created new virtual file: " + uri);
            }
        }

        return virtualFile;
    }

    /**
     * 调用IntelliJ IDEA的sdk，实现 删除指定的 ContentProvider。
     */
    public static void contentProviderDelete(String str, String channel) {
        DeleteContentProvider deleteContentProvider = GsonUtil.fromJson(str, DeleteContentProvider.class);

        ApplicationManager.getApplication().invokeLater(() -> {
            try {
                Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
                String providerId = deleteContentProvider.getParams();

                if (providerId == null) {
                    logger.warn("Invalid delete content provider data");
                    return;
                }

                List<Object> provider = RcsWebSocketManager.INSTANCE.getProvider(channel, ProviderTypeEnum.CONTENT);
                if (CollectionUtils.isEmpty(provider)) {
                  LoggerUtil.INSTANCE.logWarn(logger, "contentProviderUpdate error, channel:" + channel + ", provider is null");
                    return;
                }

                CreateContentProvider needRemove = null;
                int needRemoveSchemaCount = 0;
                for (Object o : provider) {
                    if (o instanceof CreateContentProvider createContentProvider) {
                        if (providerId.equals(createContentProvider.getParams().getId())) {
                            needRemove = createContentProvider;
                            needRemoveSchemaCount++;
                        }
                    }
                }

                if (needRemove == null) {
                    logger.warn("contentProviderDelete error, needRemove is null, providerId:" + providerId + ",channel:" + channel);
                    return;
                }
                Map<String, LightVirtualFile> providerFiles = contentProviders.remove(providerId);
                if (providerFiles != null) {
                    if (project != null) {
                        FileEditorManager editorManager = FileEditorManager.getInstance(project);

                        // 关闭所有由该Provider创建的虚拟文档
                        for (LightVirtualFile virtualFile : providerFiles.values()) {
                            if (editorManager.isFileOpen(virtualFile)) {
                                editorManager.closeFile(virtualFile);
                            }
                        }
                    }
                }

                // 只有当一个schema的时候，才需要移除。
                if (needRemoveSchemaCount == 1) {
                    schemaWithProvider.remove(needRemove.getParams().getSchema());
                }
                RcsWebSocketManager.INSTANCE.deleteProvider(channel, needRemove);

                logger.info("Content provider deleted: " + providerId);

            } catch (Exception e) {
              LoggerUtil.INSTANCE.logWarn(logger, "Failed to delete content provider", e);
            }
        });
    }

    public static Boolean updateWorkspaceContent(String str, String channel) {
        ExecuteChanges executeChanges = GsonUtil.fromJson(str, ExecuteChanges.class);
        if (null ==  executeChanges || executeChanges.getParams() == null) {
            logger.warn("Invalid update workspace content data");
            return false;
        }
        ExecuteChangesParams params = executeChanges.getParams();
        List<FileChanges> changes = params.getChanges();
        if (CollectionUtils.isEmpty(changes)) {
            return true;
        }
        boolean res = true;
        for (FileChanges change : changes) {
            if (change instanceof FileChangesFile file) {
                res = res && updateFileChangeFile(file,  channel);
            } else if (change instanceof FileChangesContent content) {
                res = res && updateFileChangeContent(content, channel);
            }
        }
        return res;
    }
    private static boolean  updateFileChangeFile(FileChangesFile file, String channel) {
        if (StringUtils.isAllEmpty(file.getFrom(), file.getTo())) {
            return false;
        }
        if (StringUtils.isEmpty(file.getFrom())) {
            //新增文件
            CreateFile createFile = new CreateFile();
            CreateFileParams params = new CreateFileParams();
            params.setUri(file.getTo());
            Content content = new Content();
            content.setText("");
            params.setContent(content);
            createFile.setParams(params);
            contentEditFlagMap.computeIfAbsent(params.getUri(), k -> new AtomicBoolean(false)).set(true);
            return RcsFileService.fileCreateWithRes(GsonUtil.toJson(createFile), channel);
        } else if (StringUtils.isEmpty(file.getTo())) {
            DeleteFile delete = new DeleteFile();
            delete.setParams(file.getFrom());
            contentEditFlagMap.computeIfAbsent(file.getFrom(), k -> new AtomicBoolean(false)).set(true);
            return RcsFileService.fileDeleteWithRes(GsonUtil.toJson(delete), channel);
        } else {
            Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
            Path fromPath = RcsFileService.getPathWithUri(file.getFrom());
            VirtualFile virtualFile = VirtualFileManager.getInstance().refreshAndFindFileByNioPath(fromPath);
            return WriteCommandAction.runWriteCommandAction(project, (Computable<Boolean>)() -> {
                try {
                    contentEditFlagMap.computeIfAbsent(file.getFrom(), k -> new AtomicBoolean(false)).set(true);
                    virtualFile.rename(null, file.getTo());
                    return true;
                } catch (IOException e) {
                    LoggerUtil.INSTANCE.logWarn(logger, "Failed to rename file", e);
                    return false;
                }
            });
        }
    }
    private static Boolean updateFileChangeContent(FileChangesContent content, String channel) {
        UpdateContent updateContent = new UpdateContent();
        UpdateContentParams params = new UpdateContentParams();
        Changes changes = new Changes();
        changes.setChanges(content.getChanges());
        params.setChanges(changes);
        String uri = content.getUri();
        params.setUri(uri);
        updateContent.setParams(params);
        contentEditFlagMap.computeIfAbsent(uri, k -> new AtomicBoolean(false)).set(true);
        return RcsContentService.contentUpdateWithRes(GsonUtil.toJson(updateContent), channel);
    }
    /**
     * 创建虚拟文档的辅助方法
     */
    public static LightVirtualFile createVirtualDocument(String uri, Content content) {
        try {
            // 从URI中提取文件名
            String fileName;
            if (StringUtils.contains(uri, "?")) {
                fileName = uri.substring(0, uri.indexOf("?"));
            } else {
                fileName = uri;
            }

            if (fileName.isEmpty()) {
                fileName = "virtual_document";
            }

            // 如果文件名没有扩展名，根据languageId添加适当的扩展名
            if (!fileName.contains(".")) {
                // 根据languageId确定文件类型
                String languageId = content.getLanguageId();
                if (languageId != null && !languageId.isEmpty()) {
                    String extension = getExtensionByLanguageId(languageId);
                    if (extension != null) {
                        fileName += extension;
                    }
                }
            }
            FileType fileType = FileTypeManager.getInstance().getFileTypeByFileName(fileName);
            LightVirtualFile virtualFile = new LightVirtualFile(fileName, fileType, content.getText());
            virtualFile.setWritable(false); // 设置为只读

            return virtualFile;
        } catch (Exception e) {
          LoggerUtil.INSTANCE.logWarn(logger, "Failed to create virtual document for URI: " + uri, e);
            return null;
        }
    }
    private static String getFileContentByGitHead(Project project, String filePath, String commitId) {
        if (project == null || StringUtils.isEmpty(filePath)) {
            return null;
        }

        final String commitIdReal = StringUtils.isBlank(commitId) ? "HEAD" : commitId;
        String cacheKey = commitIdReal + ":" + filePath;

        return GIT_CONTENT_CACHE.get(cacheKey, key -> {
            try {
                return GitHandlerUtil.getFileContentByHead(project, filePath, commitIdReal);
            } catch (Exception e) {
                // 如果获取失败，返回错误信息
                return "// 无法获取 Git 文件内容\n" +
                    "// Commit: HEAD" + "\n" +
                    "// File: " + filePath + "\n" +
                    "// Error: " + e.getMessage();
            }
        });
    }
    /**
     * 根据语言ID获取对应的文件扩展名
     */
    private static String getExtensionByLanguageId(String languageId) {
        switch (languageId.toLowerCase()) {
            case "java": return ".java";
            case "python": return ".py";
            case "javascript": return ".js";
            case "typescript": return ".ts";
            case "json": return ".json";
            case "xml": return ".xml";
            case "html": return ".html";
            case "css": return ".css";
            case "markdown": return ".md";
            case "yaml": return ".yml";
            case "sql": return ".sql";
            case "go": return ".go";
            case "rust": return ".rs";
            case "cpp": case "c++": return ".cpp";
            case "c": return ".c";
            case "kotlin": return ".kt";
            case "scala": return ".scala";
            case "php": return ".php";
            case "ruby": return ".rb";
            case "swift": return ".swift";
            case "dart": return ".dart";
            default: return null;
        }
    }

    private static void updateFileContent(Project project, VirtualFile virtualFile, String newContent) {
        WriteCommandAction.runWriteCommandAction(project, "Update File", null, () -> {
            Document document = FileDocumentManager.getInstance().getDocument(virtualFile);
            if (document == null) {
                LoggerUtil.INSTANCE.logWarn(logger, "updateFileContent error, document is null, fileUrl:" + virtualFile.getUrl() + ", project:" + project.getBasePath());
                return;
            }
            document.setReadOnly(false);
            document.setText(newContent);
            // 保存文件
            FileDocumentManager.getInstance().saveDocument(document);
            document.setReadOnly(!virtualFile.isWritable());
        });
    }

    /**
     * 根据文件路径获取文件类型，用于语法高亮
     */
    private static FileType getFileTypeFromPath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return FileTypeManager.getInstance().getStdFileType("txt");
        }

        // 获取文件名
        String fileName = filePath;
        int lastSlash = filePath.lastIndexOf('/');
        if (lastSlash >= 0 && lastSlash < filePath.length() - 1) {
            fileName = filePath.substring(lastSlash + 1);
        }

        // 根据文件名获取文件类型
        FileType fileType = FileTypeManager.getInstance().getFileTypeByFileName(fileName);
        return fileType;
    }

}

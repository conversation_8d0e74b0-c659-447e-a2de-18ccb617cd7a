package com.xhs.codewiz.service;

import com.intellij.openapi.project.Project;
import com.xhs.codewiz.client.model.RemoteServiceMeta;
import com.xhs.codewiz.lang.LspServiceForTylm;
import com.xhs.codewiz.lang.agent.CodeWizAgentProcessService;
import com.xhs.codewiz.lang.agent.commands.XhsSsoLoginParam;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes;
import com.xhs.codewiz.lang.entity.XhsUserInfoRes.XhsUserInfo;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.apache.commons.collections4.CollectionUtils;

import com.google.gson.reflect.TypeToken;
import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.client.ProviderTypeEnum;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.scheme.platform.CreateSessionProvider;
import com.xhs.codewiz.scheme.platform.DeleteSessionProvider;
import com.xhs.codewiz.scheme.platform.ReadSession;
import com.xhs.codewiz.type.platform.Session;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/7/23 14:51
 */
public class SessionService {
    private static Map<Project, Map<String, CreateSessionProvider>> sessionProviderMap = new ConcurrentHashMap<>();

    static Logger log = Logger.getInstance(SessionService.class);

    public static void createSessionProvider(String param, String channel) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "createSessionProvider param: " + param + " channel: " + channel);
            CreateSessionProvider request = GsonUtil.fromJson(param, CreateSessionProvider.class);
            Project project = RcsWebSocketManager.INSTANCE.getProjectByLocalChannel(channel);
            Map<String, CreateSessionProvider> projectMap = sessionProviderMap.computeIfAbsent(project, k -> new HashMap<>());
            projectMap.put(channel,  request);
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process createSessionProvider", e);
        }
    }

    public static void deleteSessionProvider(String param, String channel) {
        try {
            LoggerUtil.INSTANCE.logInfo(log, "deleteSessionProvider param: " + param + " channel: " + channel);
            DeleteSessionProvider request = GsonUtil.fromJson(param, DeleteSessionProvider.class);
            if (null == request || StringUtils.isEmpty(request.getParams())) {
                return;
            }
            Project project = RcsWebSocketManager.INSTANCE.getProjectByLocalChannel(channel);
            Map<String, CreateSessionProvider> projectMap = sessionProviderMap.computeIfAbsent(project, k -> new HashMap<>());
            if ((projectMap.isEmpty() || !projectMap.containsKey(request.getParams()))) {
                return;
            }
            RcsWebSocketManager.INSTANCE.deleteProvider(channel, request);
            projectMap.remove(request.getParams());
        } catch (Exception e) {
            LoggerUtil.INSTANCE.logWarn(log, "Failed to process deleteSessionProvider", e);
        }
    }

    public static void sendSeadSession() {

    }

    public static List<Session> notifySeadSession(String param, String channel) {
        LoggerUtil.INSTANCE.logInfo(log, "notifySeadSession param: " + param + " channel: " + channel);
        if (sessionProviderMap.isEmpty()) {
            return Lists.newArrayList();
        }
        ReadSession readSession = GsonUtil.fromJson(param, ReadSession.class);
        if (null == readSession || null == readSession.getParams()) {
            return Lists.newArrayList();
        }
        String providerId = readSession.getParams().getProvider();
        CreateSessionProvider provider = null;
        String orderChannel = null;
        Project project = RcsWebSocketManager.INSTANCE.getProjectByLocalChannel(channel);
        Map<String, CreateSessionProvider> projectMap = sessionProviderMap.computeIfAbsent(project, k -> new HashMap<>());
        for (Entry<String, CreateSessionProvider> entry : projectMap.entrySet()) {
            if (StringUtils.equals(entry.getValue().getParams().getProvider().getId(), providerId)) {
                provider = entry.getValue();
                orderChannel = entry.getKey();
                break;
            }
        }
        if (null == provider) {
            return Lists.newArrayList();
        }
        List<Session> sessions = RcsWebSocketManager.INSTANCE.sendRequestWithChannelProvider(
            orderChannel,
            providerId,
            readSession,
            new TypeToken<List<Session>>() {
            },
            RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel),
            1000);
        LoggerUtil.INSTANCE.logInfo(log, "notifySeadSession sessions success");

        //由于补全走本地ls，所以必须确保补全登陆
        if (CollectionUtils.isNotEmpty(sessions)) {
            //存在登陆信息，如果本地没有，尝试从ls拉取
            XhsUserInfo res = LspServiceForTylm.getUserLoginInfo();
            if (null == res) {
                XhsSsoLoginParam login = new XhsSsoLoginParam();
                login.setNeedResponse(false); //异步获取出结果
                try {
                    CodeWizAgentProcessService.getInstance().executeCommand(login).get(10, TimeUnit.SECONDS);
                } catch (Exception e) {
                    LoggerUtil.INSTANCE.logWarn(log, "获取授权token失败", e);
                }
            }
        }
        return sessions;
    }

    public static void clearProjectSessionProvider(Project project) {
        if (sessionProviderMap.isEmpty()) {
            return;
        }
        List<RemoteServiceMeta> serviceMetas = RcsWebSocketManager.INSTANCE.getRemoteServiceList(project);
        if (CollectionUtils.isEmpty(serviceMetas)) {
            return;
        }
        sessionProviderMap.remove(project);
    }
}

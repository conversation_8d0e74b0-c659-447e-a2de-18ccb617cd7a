package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 删除 Webview View
 */
public class DeleteWebview {
    private String schemaProtocol = "platform.delete.webview";
    /** Webview View Provider ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

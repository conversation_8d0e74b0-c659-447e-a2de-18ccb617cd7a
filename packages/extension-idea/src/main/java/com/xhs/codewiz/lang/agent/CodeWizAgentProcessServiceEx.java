package com.xhs.codewiz.lang.agent;

import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotificationListener;
import java.util.Collection;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.TestOnly;

interface CodeWizAgentProcessServiceEx extends CodeWizAgentProcessService {
    void initialize(@NotNull Collection<JsonRpcNotificationListener> var1);

    void startNotify();

    void shutdown();

    boolean isShutdown();

    @TestOnly
    void flush();
}


package com.xhs.codewiz.listener;


import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.project.Project;
import com.xhs.codewiz.factory.editor.TopicEditorVirtualFile;
import com.xhs.codewiz.factory.editor.TopicFileEditor;

/**
 * 焦点变化回调接口
 *
 * <AUTHOR>
 * @date 2025/8/11 20:46
 */
public interface TopicEditorFocusCallback {
    /**
     * Topic Editor 获得焦点时调用
     *
     * @param project     项目实例
     * @param topicEditor Topic编辑器实例
     * @param virtualFile 虚拟文件
     */
    void onTopicEditorFocused(@NotNull Project project, @NotNull TopicFileEditor topicEditor,
                              @NotNull TopicEditorVirtualFile virtualFile);

    /**
     * Topic Editor 失去焦点时调用
     *
     * @param project     项目实例
     * @param topicEditor Topic编辑器实例
     * @param virtualFile 虚拟文件
     */
    void onTopicEditorBlurred(@NotNull Project project, @NotNull TopicFileEditor topicEditor,
                              @NotNull TopicEditorVirtualFile virtualFile);
}

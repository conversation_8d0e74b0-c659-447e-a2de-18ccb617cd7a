package com.xhs.codewiz.editor.language;

import com.intellij.lang.Language;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.fileTypes.LanguageFileType;
import com.intellij.openapi.fileTypes.PlainTextLanguage;
import com.intellij.openapi.fileTypes.PlainTextLikeFileType;
import com.intellij.openapi.fileTypes.impl.AbstractFileType;
import com.intellij.openapi.util.io.FileUtilRt;
import com.intellij.psi.PsiFile;
import com.xhs.codewiz.editor.request.LanguageInfo;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class DefaultLanguageInfoSupport implements LanguageInfoSupport {
    public DefaultLanguageInfoSupport() {
    }

    public @Nullable LanguageInfo findVSCodeLanguageMapping(@NotNull PsiFile file) {
        Language language = this.findBestLanguage(file);
        if (file.getFileType() instanceof AbstractFileType) {
            return new LanguageInfo(language, file.getFileType().getName());
        } else if (language != Language.ANY && !isAutoDetectedPlainText(language, file.getName())) {
            return new LanguageInfo(language, VSCodeLanguageMap.getId(language));
        } else {
            CharSequence extension = FileUtilRt.getExtension(file.getName(), (String)null);
            return new LanguageInfo(language, extension != null ? extension.toString() : "plaintext");
        }
    }

    private @NotNull Language findBestLanguage(@NotNull PsiFile file) {
        Language language = file.getLanguage();
        if (language == Language.ANY && file.getFileType() instanceof LanguageFileType) {
            language = ((LanguageFileType)file.getFileType()).getLanguage();
        }

        return language;
    }

    private static boolean isAutoDetectedPlainText(@NotNull Language language, @NotNull String fileName) {
        if (language != PlainTextLanguage.INSTANCE) {
            return false;
        } else {
            return !(FileTypeManager.getInstance().getFileTypeByFileName(fileName) instanceof PlainTextLikeFileType);
        }
    }
}


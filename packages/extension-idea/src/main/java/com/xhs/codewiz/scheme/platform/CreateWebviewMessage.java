package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 创建 Webview 消息
 */
public class CreateWebviewMessage {
    private String schemaProtocol = "platform.create.webviewmessage";
    private CreateWebviewMessageParams params;

    public CreateWebviewMessageParams getParams() {
        return params;
    }
    public void setParams(CreateWebviewMessageParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateWebviewMessageParams {
        /** Webview ID */
        private String webview;
        /** Webview 消息内容 */
        private String message;
    
        public String getWebview() {
            return webview;
        }
        public void setWebview(String webview) {
            this.webview = webview;
        }
        public String getMessage() {
            return message;
        }
        public void setMessage(String message) {
            this.message = message;
        }
    }
}

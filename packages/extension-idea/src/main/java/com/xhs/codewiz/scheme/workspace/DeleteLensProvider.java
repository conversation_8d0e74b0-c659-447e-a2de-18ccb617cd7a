package com.xhs.codewiz.scheme.workspace;

import java.util.*;
/**
 * 删除 Lens 提供者
 */
public class DeleteLensProvider {
    private String schemaProtocol = "workspace.delete.lensprovider";
    /** 删除的 Lens 提供者 ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

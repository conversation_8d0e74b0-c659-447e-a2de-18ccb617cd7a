package com.xhs.codewiz.setting;

import com.google.gson.JsonObject;
import com.intellij.openapi.options.Configurable;
import com.xhs.codewiz.setting.ui.AppSettingsComponent;
import java.util.List;
import javax.swing.JComponent;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.Nullable;

/**
 * Provides controller functionality for application settings.
 */
public class AppSettingsConfigurable implements Configurable {

  private AppSettingsComponent mySettingsComponent;

  @Nls(capitalization = Nls.Capitalization.Title)
  @Override
  public String getDisplayName() {
    return "Rednote CodeWiz";
  }

  @Override
  public JComponent getPreferredFocusedComponent() {
    return mySettingsComponent.getPreferredFocusedComponent();
  }

  @Nullable
  @Override
  public JComponent createComponent() {
    mySettingsComponent = new AppSettingsComponent();
    return mySettingsComponent.getPanel();
  }

  @Override
  public boolean isModified() {
    CodeWizApplicationState settings = CodeWizApplicationSettings.settings();
    return mySettingsComponent.getUpdateCheck() != settings.autoUpdate.checkForUpdate
        || mySettingsComponent.getInlineCompletionColor() != settings.inlayTextColor;
  }

  @Override
  public void apply() {
    CodeWizApplicationState settings = CodeWizApplicationSettings.settings();
    if (settings.autoUpdate.checkForUpdate != mySettingsComponent.getUpdateCheck()) {
      settings.autoUpdate.checkForUpdate = mySettingsComponent.getUpdateCheck();
      settings.autoUpdate.userApply = true; //用户修改过，后续仅允许用户变更此配置
    }
    settings.inlayTextColor = mySettingsComponent.getInlineCompletionColor();
  }

  @Override
  public void reset() {
    CodeWizApplicationState settings = CodeWizApplicationSettings.settings();
    mySettingsComponent.setUpdateCheck(settings.autoUpdate.checkForUpdate);
    mySettingsComponent.setInlineCompletionColor(settings.inlayTextColor);
  }

  @Override
  public void disposeUIResources() {
    mySettingsComponent = null;
  }
}
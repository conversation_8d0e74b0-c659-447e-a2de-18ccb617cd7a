package com.xhs.codewiz.lang.featureflags;


import com.intellij.openapi.application.ApplicationManager;
import org.jetbrains.annotations.NotNull;

public class CodeWizFeatureFlagsService {
    public CodeWizFeatureFlagsService() {
    }

    public static void notifyApplication(@NotNull CodeWizFeatureFlags featureFlags) {
        (ApplicationManager.getApplication().getMessageBus().syncPublisher(
            CodeWizFeatureFlagsListener.TOPIC)).onFeatureFlags(featureFlags);
    }
}

package com.xhs.codewiz.scheme.workspace;

import java.util.*;
import com.xhs.codewiz.type.file.GlobPattern;
/**
 * 创建文件系统观察者
 */
public class CreateFileSystemWatcher {
    private String schemaProtocol = "workspace.create.filesystemwatcher";
    private CreateFileSystemWatcherParams params;

    public CreateFileSystemWatcherParams getParams() {
        return params;
    }
    public void setParams(CreateFileSystemWatcherParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class CreateFileSystemWatcherParams {
        /** 是否忽略删除事件 */
        private Boolean ignoreDeleteEvents; // optional
        /** Glob 模式，用于匹配文件路径 */
        private GlobPattern globPattern;
        /** 是否忽略修改事件 */
        private Boolean ignoreChangeEvents; // optional
        /** 文件系统观察者的 ID */
        private String id;
        /** 是否忽略创建事件 */
        private Boolean ignoreCreateEvents; // optional
    
        public Boolean getIgnoreDeleteEvents() {
            return ignoreDeleteEvents;
        }
        public void setIgnoreDeleteEvents(Boolean ignoreDeleteEvents) {
            this.ignoreDeleteEvents = ignoreDeleteEvents;
        }
        public GlobPattern getGlobPattern() {
            return globPattern;
        }
        public void setGlobPattern(GlobPattern globPattern) {
            this.globPattern = globPattern;
        }
        public Boolean getIgnoreChangeEvents() {
            return ignoreChangeEvents;
        }
        public void setIgnoreChangeEvents(Boolean ignoreChangeEvents) {
            this.ignoreChangeEvents = ignoreChangeEvents;
        }
        public String getId() {
            return id;
        }
        public void setId(String id) {
            this.id = id;
        }
        public Boolean getIgnoreCreateEvents() {
            return ignoreCreateEvents;
        }
        public void setIgnoreCreateEvents(Boolean ignoreCreateEvents) {
            this.ignoreCreateEvents = ignoreCreateEvents;
        }
    }
}

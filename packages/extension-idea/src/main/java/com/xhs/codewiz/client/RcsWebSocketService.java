package com.xhs.codewiz.client;

import com.intellij.openapi.project.Project;
import com.xhs.codewiz.client.websocket.RcsWebSocketClient;
import java.io.IOException;
import java.net.URI;
import javax.websocket.DeploymentException;

/**
 * Author: liukunpeng Date: 2025-03-17 Description:
 */
public class RcsWebSocketService {
  RcsLanguageClient client;
  RcsLanguageServer server;
  RcsWebSocketClient rcsWebSocketClient;
  public RcsLanguageServer getServer() {
    return this.server;
  }

  private RcsWebSocketService(RcsWebSocketClient rcsWebSocketClient, RcsLanguageClient client, RcsLanguageServer server) {
    this.rcsWebSocketClient = rcsWebSocketClient;
    this.client = client;
    this.server = server;
  }

  public static RcsWebSocketService createService() {
    RcsWebSocketClient rcsWebSocketClient = new RcsWebSocketClient();
    return new RcsWebSocketService(rcsWebSocketClient, rcsWebSocketClient.getClient(), rcsWebSocketClient.getServer());
  }

  public void connect(URI uri, Project project) throws DeploymentException, IOException {
    this.rcsWebSocketClient.connect(uri, project);
    this.server = this.rcsWebSocketClient.getServer();
  }

  public boolean isSessionOpen() {
    return this.rcsWebSocketClient.isSessionOpen();
  }
  public RcsWebSocketClient getWebSocketClient() {
    return this.rcsWebSocketClient;
  }
  public void closeSession() {
    this.rcsWebSocketClient.closeSession();
  }
}

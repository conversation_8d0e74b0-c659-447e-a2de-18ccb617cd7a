package com.xhs.codewiz.client.model;

/**
 * Author: liukunpeng Date: 2025-07-15 Description:
 */
public class ChannelCommonResponse {
  private final String protocol = "rcs/jrpc/request";
  //通信渠道
  private String channel;
  //实际对应的请求参数
  private Object data;

  private Integer success;

  private String error;

  // 构造函数
  public ChannelCommonResponse() {
  }

  public String getProtocol() {
    return protocol;
  }

  public String getChannel() {
    return channel;
  }

  public void setChannel(String channel) {
    this.channel = channel;
  }

  public Object getData() {
    return data;
  }

  public void setData(Object data) {
    this.data = data;
  }

  public Integer getSuccess() {
    return success;
  }

  public void setSuccess(Integer success) {
    this.success = success;
  }

  public String getError() {
    return error;
  }

  public void setError(String error) {
    this.error = error;
  }
}

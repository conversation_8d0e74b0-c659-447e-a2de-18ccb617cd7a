package com.xhs.codewiz.actions;

import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.editor.Caret;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.actionSystem.EditorAction;
import com.intellij.openapi.editor.actionSystem.EditorActionHandler;
import com.intellij.openapi.keymap.KeymapUtil;
import com.intellij.openapi.project.DumbAware;
import com.xhs.codewiz.completion.enums.CodeWizApplyInlayStrategy;
import com.xhs.codewiz.editor.CodeWizEditorManager;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class AbstractCodeWizApplyInlayAction extends EditorAction implements DumbAware {
    protected AbstractCodeWizApplyInlayAction(CodeWizApplyInlayStrategy strategy) {
        super(new ApplyInlayHandler(strategy));
        this.setInjectedContext(true);
    }

    private static class ApplyInlayHandler extends EditorActionHandler {
        private final CodeWizApplyInlayStrategy strategy;

        public ApplyInlayHandler(CodeWizApplyInlayStrategy strategy) {
            this.strategy = strategy;
        }

        protected boolean isEnabledForCaret(@NotNull Editor editor, @NotNull Caret caret, DataContext dataContext) {
            return CodeWizApplyInlaysAction.isSupported(editor);
        }

        public boolean executeInCommand(@NotNull Editor editor, DataContext dataContext) {
            return false;
        }

        protected void doExecute(@NotNull Editor editor, @Nullable Caret caret, DataContext dataContext) {
            CodeWizEditorManager.getInstance().applyCompletion(editor, this.strategy);
        }
    }
}

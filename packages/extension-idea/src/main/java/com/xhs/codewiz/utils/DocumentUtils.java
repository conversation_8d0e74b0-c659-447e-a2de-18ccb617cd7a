package com.xhs.codewiz.utils;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ModalityState;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.impl.DocumentImpl;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.util.TextRange;
import java.lang.reflect.InvocationTargetException;
import javax.swing.SwingUtilities;
import org.jetbrains.annotations.NotNull;

public class DocumentUtils {
  private static final Logger log = Logger.getInstance(DocumentUtils.class);

  public static String getCompleteLine(@NotNull Document document, int line) {
    if (document.getLineCount() < line || line < 0) {
      return "";
    }
    StringBuilder sb = new StringBuilder();
    int startLineHead = document.getLineStartOffset(line);
    int startLineEnd = document.getLineEndOffset(line);
    String currentLine = document.getText(new TextRange(startLineHead, startLineEnd));
    sb.append(currentLine);
    while (line > 0 && ! CodewizStringUtil.isJavaLineEnding(currentLine = document.getText(new TextRange(startLineHead = document.getLineStartOffset(--line), startLineEnd = document.getLineEndOffset(line))))) {
      sb.insert(0, currentLine);
    }
    return sb.toString();
  }

  public static String getCurrentLine(@NotNull Editor editor) {
    int caretOffset = editor.getCaretModel().getOffset();
    int line = editor.getDocument().getLineNumber(caretOffset);
    int startLineHead = editor.getDocument().getLineStartOffset(line);
    int startLineEnd = editor.getDocument().getLineEndOffset(line);
    return editor.getDocument().getText(new TextRange(startLineHead, startLineEnd));
  }

  public static boolean isValidEditorDocument(Document document) {
    return document != null && !document.getClass().getName().contains("com.intellij.openapi.editor.textarea.TextAreaDocument") && !document.getClass().getName().contains("com.intellij.openapi.editor.textarea.TextComponentDocument");
  }

  public static String filterDocumentSlashR(Document document, String content) {
    if (document instanceof DocumentImpl && !((DocumentImpl)document).acceptsSlashR()) {
      return org.apache.commons.lang3.StringUtils.remove((String)content, (char)'\r');
    }
    return content;
  }

  public static String convertSeparatorToSlashN(String content) {
    String separator = FileUtil.getLineSeparator(content);
    return org.apache.commons.lang3.StringUtils.isEmpty((CharSequence)separator) ? content : org.apache.commons.lang3.StringUtils.replace((String)content, (String)separator, (String)"\n");
  }

  public static String convertSeparator(String content, String beforeSeparator, String afterSeparator) {
    return org.apache.commons.lang3.StringUtils.isEmpty((CharSequence)afterSeparator) || org.apache.commons.lang3.StringUtils.equals((CharSequence)beforeSeparator, (CharSequence)afterSeparator) ? content : org.apache.commons.lang3.StringUtils.replace((String)content, (String)beforeSeparator, (String)afterSeparator);
  }

  public static void saveAllUnsavedFiles() {

      ApplicationManager.getApplication().invokeAndWait(() -> FileDocumentManager.getInstance().saveAllDocuments(), ModalityState.current());
    try {
      SwingUtilities.invokeAndWait(() -> FileDocumentManager.getInstance().saveAllDocuments());
    } catch (Exception e) {
      log.warn("saveAllUnsavedFiles error", e);
      return;
    }
    FileDocumentManager.getInstance().saveAllDocuments();
  }

  public static int getOffset(Document document, int line, int column) {
    if (line < 0 || line >= document.getLineCount()) {
      line = document.getLineCount() - 1;
    }
    if (line < 0) {
      return 0;
    }
    int lineStartOffset = document.getLineStartOffset(line);
    int lineEndOffset = document.getLineEndOffset(line);
    int lineLength = lineEndOffset - lineStartOffset;
    if (column < 0 || column > lineLength) {
      column = lineLength;
    }
    return lineStartOffset + column;
  }
}
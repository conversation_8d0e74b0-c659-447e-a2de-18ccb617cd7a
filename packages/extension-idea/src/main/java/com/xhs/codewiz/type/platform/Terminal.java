package com.xhs.codewiz.type.platform;

import java.util.*;

/**
 * 终端
 */
public class Terminal {
    /** 终端工作目录 */
    private String cwd;

    /** 是否不显示终端 */
    private Boolean hideFromUser;

    /** 终端颜色 */
    private String color;

    /** 自定义终端 Shell 路径 */
    private String shellPath;

    /** 是否严格使用环境变量, 为 True 时，终端将只使用提供的环境变量，不会继承系统环境变量 */
    private Boolean strictEnv;

    /** 自定义终端 Shell 参数 */
    private List<String> shellArgs;

    /** 终端名称 */
    private String name;

    /** 终端环境变量 */
    private Map<String, String> env;

    /** 每次启动时终端的显示消息。\n这条信息不会发送给终端进程，而是直接写到终端界面上。\n支持转义序列（escape sequences），比如设置文本样式（如颜色、粗体等） */
    private String message;

    /** 终端图标路径 */
    private String iconPath;

    /** 是否为临时终端\n临时终端在重启或重新加载时不会恢复 */
    private Boolean isTransient;

    public String getCwd() {
        return cwd;
    }

    public void setCwd(String cwd) {
        this.cwd = cwd;
    }

    public Boolean getHideFromUser() {
        return hideFromUser;
    }

    public void setHideFromUser(Boolean hideFromUser) {
        this.hideFromUser = hideFromUser;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getShellPath() {
        return shellPath;
    }

    public void setShellPath(String shellPath) {
        this.shellPath = shellPath;
    }

    public Boolean getStrictEnv() {
        return strictEnv;
    }

    public void setStrictEnv(Boolean strictEnv) {
        this.strictEnv = strictEnv;
    }

    public List<String> getShellArgs() {
        return shellArgs;
    }

    public void setShellArgs(List<String> shellArgs) {
        this.shellArgs = shellArgs;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, String> getEnv() {
        return env;
    }

    public void setEnv(Map<String, String> env) {
        this.env = env;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getIconPath() {
        return iconPath;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

    public Boolean getIsTransient() {
        return isTransient;
    }

    public void setIsTransient(Boolean isTransient) {
        this.isTransient = isTransient;
    }

}

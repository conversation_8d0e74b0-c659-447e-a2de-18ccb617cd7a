package com.xhs.codewiz.factory.webview;

import static com.intellij.openapi.wm.ToolWindowAnchor.BOTTOM;
import static com.intellij.openapi.wm.ToolWindowAnchor.LEFT;
import static com.intellij.openapi.wm.ToolWindowAnchor.RIGHT;
import static com.intellij.openapi.wm.ToolWindowAnchor.TOP;

import java.awt.Color;
import java.awt.Window;
import java.util.List;

import javax.swing.JPanel;
import javax.swing.SwingUtilities;
import javax.swing.border.Border;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.cef.browser.CefBrowser;
import org.cef.browser.CefFrame;
import org.cef.handler.CefLoadHandlerAdapter;
import org.cef.network.CefRequest;
import org.jetbrains.annotations.NotNull;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.actionSystem.DefaultActionGroup;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.colors.EditorColorsManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowAnchor;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.openapi.wm.WindowManager;
import com.intellij.openapi.wm.ex.ToolWindowManagerListener;
import com.intellij.ui.OnePixelSplitter;
import com.intellij.ui.content.Content;
import com.intellij.ui.jcef.JBCefBrowser;
import com.intellij.ui.jcef.JBCefBrowserBase;
import com.intellij.ui.jcef.JBCefJSQuery;
import com.intellij.util.ui.JBUI;
import com.xhs.codewiz.actions.command.BaseCommandAction;
import com.xhs.codewiz.actions.command.ChatWindowCommandUtil;
import com.xhs.codewiz.factory.webview.action.DevToolAction;
import com.xhs.codewiz.factory.webview.util.FocusTransferUtil;
import com.xhs.codewiz.factory.webview.util.HighBuildVersionReflectUtil;
import com.xhs.codewiz.service.WebviewService;
import com.xhs.codewiz.type.platform.Webview;
import com.xhs.codewiz.type.platform.WebviewProvider;
import com.xhs.codewiz.utils.JBCefBrowserUtil;
import com.xhs.codewiz.utils.LoggerUtil;

/**
 * codewiz 工具窗口类
 */
public class ToolBrowserWindow implements Disposable {
    private static final Logger logger = Logger.getInstance(ToolBrowserWindow.class);
    private ToolWindow activeWindow;
    private JBCefBrowser codeWizBrowser;
    private ToolWindowAnchor lastWindowAnchor;
    // private Webview webview = new Webview();
    // private WebviewProvider webviewProvider;
    // private String remoteChannel;
    private WebviewBuilder webviewBuilder = new WebviewBuilder();
    private final OnePixelSplitter rootPanel = new OnePixelSplitter(false, 0.0f);

    private volatile boolean initialized = false;


    public void toolWindowStateChange(ToolWindowManager mgr,
                                      ToolWindow tw,
                                      @NotNull ToolWindowManagerListener.ToolWindowManagerEventType changeType,
                                      Project project) {
        if (tw != null && tw.isAvailable()) {
            ToolWindowAnchor anchor = tw.getAnchor();
            if (!anchor.equals(lastWindowAnchor)) {
                lastWindowAnchor = anchor;
                updateBorder(true);
            }

            if (changeType == ToolWindowManagerListener.ToolWindowManagerEventType.HideToolWindow) {
                logger.info("ToolWindow 隐藏 - 项目: " + project.getName());
                WebviewService.executeHideWebview(project, this);
                WebviewService.deleteWebview(project);

                FocusTransferUtil.transferFocusToEditorDelay(project);
            }
        }
    }

    public void toolWindowShown(@NotNull ToolWindow toolWindow, Project project) {
        if (toolWindow.getId().equals(this.getActiveWindow().getId())) {
            if (null != getWebviewProvider()
                    && null != getWebviewProvider().getOptions()
                    && (null == getWebviewProvider().getOptions().getRetainContextWhenHidden()
                    || !getWebviewProvider().getOptions().getRetainContextWhenHidden())) {
                this.setWebview(WebviewService.readWebview(project, this.getWebviewBuilder()));
            }
            LoggerUtil.INSTANCE.logDebug(logger, "ToolWindow 打开 - 项目: " + project.getName());
            WebviewService.executeShowWebview(project, this);

            FocusTransferUtil.transferFocusToEditorDelay(project);
        }
    }

    public ToolWindow getActiveWindow() {
        return activeWindow;
    }

    public void setActiveWindow(ToolWindow activeWindow) {
        this.activeWindow = activeWindow;
    }

    public JBCefBrowser getCodeWizBrowser() {
        return codeWizBrowser;
    }

    public void setCodeWizBrowser(JBCefBrowser codeWizBrowser) {
        this.codeWizBrowser = codeWizBrowser;
        // 把browser绑定到toolwindow上去
        // JPanel contentPanel = new JPanel(new BorderLayout());
        rootPanel.setFirstComponent(codeWizBrowser.getComponent());
        JPanel leftPanel = new JPanel();
        leftPanel.setBackground(EditorColorsManager.getInstance().getGlobalScheme().getDefaultBackground());
        rootPanel.setSecondComponent(leftPanel);
        Content uiContent = this.activeWindow.getContentManager().getFactory().createContent(rootPanel, "", false);

        // 初始化与ide主题一致的加载颜色等设置
        changeBrowserLoadingColor();

        // ui绑定到对应的window上去
        this.activeWindow.getContentManager().addContent(uiContent);

        // 初始化边框分割线
        updateBorder(false);
        lastWindowAnchor = this.activeWindow.getAnchor();
    }

    public void setWebview(Webview webview) {
        LoggerUtil.INSTANCE.logInfo(logger, "[Stage6-UI] setWebview started - providerId: " + this.getWebviewProvider().getId() + ", webviewNull: " + (webview == null));
        if (webview == null) {
            LoggerUtil.INSTANCE.logWarn(logger, "setWebview: webview is null, providerId: " + this.getWebviewProvider().getId());
            return;
        }
        this.getWebviewBuilder().setWebview(webview);
        // url 格式判断
        if (getWebview().getHtml().startsWith("http")) {
            LoggerUtil.INSTANCE.logInfo(logger, "[Stage6-UI] Loading external URL - providerId: " + this.getWebviewProvider().getId() + ", url: " + getWebview().getHtml());
            this.codeWizBrowser.loadURL(getWebview().getHtml());
        } else {
            LoggerUtil.INSTANCE.logInfo(logger, "[Stage6-UI] Loading HTML content - providerId: " + this.getWebviewProvider().getId() + ", contentLength: " + getWebview().getHtml().length());
            this.codeWizBrowser.loadHTML(getWebview().getHtml());
        }
    }

    public Webview getWebview() {
        return this.getWebviewBuilder().getWebview();
    }

    public WebviewProvider getWebviewProvider() {
        return this.getWebviewBuilder().getWebviewProvider();
    }

    public void setWebviewProvider(WebviewProvider webviewProvider) {
        this.getWebviewBuilder().setWebviewProvider(webviewProvider);
    }

    public String getRemoteChannel() {
        return this.getWebviewBuilder().getRemoteChannel();
    }

    public void setRemoteChannel(String remoteChannel) {
        this.getWebviewBuilder().setRemoteChannel(remoteChannel);
    }


    public WebviewBuilder getWebviewBuilder() {
        return webviewBuilder;
    }

    public void setWebviewBuilder(WebviewBuilder webviewBuilder) {
        this.webviewBuilder = webviewBuilder;
    }

    public void updateBorder(boolean isChangeWindow) {
        SwingUtilities.invokeLater(() -> {
            ToolWindowAnchor anchor = this.activeWindow.getAnchor();
            Border splitBorder = null;
            Color color = getBorderColor();
            if (anchor == LEFT) {
                splitBorder = JBUI.Borders.customLine(color, 0, 0, 0, 3);
            } else if (anchor == RIGHT) {
                splitBorder = JBUI.Borders.customLine(color, 0, 3, 0, 0);
            } else if (anchor == TOP) {
                splitBorder = JBUI.Borders.customLine(color, 0, 0, 3, 0);
            } else if (anchor == BOTTOM) {
                splitBorder = JBUI.Borders.customLine(color, 3, 0, 0, 0);
            }
            if (null != splitBorder) {
                JPanel panel = (JPanel) this.activeWindow.getContentManager().getContent(0)
                        .getComponent();
                panel.setBorder(splitBorder);
                // jetbrains本身框架问题，手动拖动窗口变更anchor会只渲染一半(快捷操作无影响)，需要手动resize
                if (isChangeWindow) {
                    Window mainWindow = WindowManager.getInstance().getFrame(activeWindow.getProject());
                    if (mainWindow != null) {
                        int width = mainWindow.getSize().width;
                        int height = mainWindow.getSize().height;
                        // 模拟resize，只变更1px，该操作属于极低频操作，基本无影响
                        mainWindow.setSize(width - 1, height - 1);
                    }
                }
            }
        });
    }

    private Color getBorderColor() {
        Color borderColor;
        if (HighBuildVersionReflectUtil.isUnderDarcula()) {
            borderColor = new Color(0x252526);
        } else {
            borderColor = new Color(0xf3f3f3);
        }
        return borderColor;
    }

    @Override
    public void dispose() {
    }

    /**
     * 根据主题切换浏览器加载背景颜色
     */
    private void changeBrowserLoadingColor() {
        if (null != this.codeWizBrowser) {
            Color bgColor = EditorColorsManager.getInstance().getGlobalScheme().getDefaultBackground();
            String hex = String.format("#%02x%02x%02x", bgColor.getRed(), bgColor.getGreen(), bgColor.getBlue());
            /*String loadingColor = HighBuildVersionReflectUtil.isUnderDarcula() ? "#252526" : "#f3f3f3";*/
            this.codeWizBrowser.setPageBackgroundColor(hex);
        }
    }

    /**
     * 根据register的title声明an action
     */
    public void changeWindowAction(Project project) {
        if (null != this.getWebviewProvider()
                && StringUtils.isNotEmpty(this.getWebviewProvider().getId())) {
            Pair<List<BaseCommandAction>, List<BaseCommandAction>> pair =
                    ChatWindowCommandUtil.getTitleActions(project, this.getWebviewProvider().getId());
            List<BaseCommandAction> navigationActions = pair.getLeft();
            List<BaseCommandAction> overflowActions = pair.getRight();

            DefaultActionGroup actionGroup = new DefaultActionGroup();
            actionGroup.add(new DevToolAction(this));
            ApplicationManager.getApplication().invokeLater(() -> {
                if (CollectionUtils.isNotEmpty(navigationActions)) {
                    this.getActiveWindow().setTitleActions(navigationActions);
                }
                if (CollectionUtils.isNotEmpty(overflowActions)) {
                    actionGroup.addAll(overflowActions);
                }
                this.getActiveWindow().setAdditionalGearActions(actionGroup);
            });

        }
    }


    // public void initBrowser(Project project) {
    //     JBCefBrowser htmlPanel;
    //     try {
    //         // 移除离屏渲染,这个功能会有兼容性问题,比如输入汉字会一直输入中效果,部分机器无法输入中文问号
    //         htmlPanel = JBCefBrowser.createBuilder().setOffScreenRendering(false).build();
    //     } catch (Exception e) {
    //         htmlPanel = new JBCefBrowser();
    //         logger.error("JBCefBrowser not support builder model.");
    //     }
    //     // 设置指针样式映射，可以正常由swing显示UI光标样式
    //     JComponent uiComp = htmlPanel.getComponent();
    //     htmlPanel.getJBCefClient().addDisplayHandler(new CefDisplayHandlerAdapter() {
    //         @Override
    //         public boolean onCursorChange(CefBrowser browser, int cursorType) {
    //             if (cursorType < Cursor.DEFAULT_CURSOR || cursorType > Cursor.MOVE_CURSOR) {
    //                 SwingUtilities.invokeLater(() -> uiComp.setCursor(Cursor.getPredefinedCursor(Cursor.DEFAULT_CURSOR)));
    //             } else {
    //                 SwingUtilities.invokeLater(() -> uiComp.setCursor(Cursor.getPredefinedCursor(cursorType)));
    //             }
    //             return true;
    //         }
    //     }, htmlPanel.getCefBrowser());
    //     htmlPanel.getJBCefClient().addLifeSpanHandler(new CefLifeSpanHandlerAdapter() {
    //         public boolean onBeforePopup(CefBrowser browser, CefFrame frame, String target_url, String target_frame_name) {
    //             // 这里 target_url就是要打开的新链接，在这里创建新的JBCefBrowser窗口
    //             // JDialog popupFrame = new JDialog((Frame) null, "新窗口", false);
    //             // popupFrame.setType(Window.Type.POPUP); // 设置为弹出类型
    //             // popupFrame.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
    //             // JBCefBrowser popupBrowser = new JBCefBrowser(target_url);
    //             // popupFrame.add(popupBrowser.getComponent());
    //             // popupFrame.setSize(800, 600);
    //             // popupFrame.setVisible(true);
    //             // return true阻止Cef自动弹窗；false允许。返回true，表示你自己处理了弹窗
    //             RcsGlobalService.openExtLink(target_url);
    //             return true;
    //         }
    //     }, htmlPanel.getCefBrowser());
    //     htmlPanel.getJBCefClient().addRequestHandler(new CefRequestHandlerAdapter() {
    //         @Override
    //         public boolean onBeforeBrowse(CefBrowser cefBrowser,
    //                                       CefFrame cefFrame,
    //                                       CefRequest cefRequest,
    //                                       boolean userGesture,
    //                                       boolean isRedirect) {
    //             String url = cefRequest.getURL();
    //             if (StringUtils.containsAny(url, "localhost", "127.0.0.1")) {
    //                 // 返回false则放行
    //                 return false;
    //             }
    //             RcsGlobalService.openExtLink(url);
    //             return true;
    //         }
    //     }, htmlPanel.getCefBrowser());
    //
    //     // 绑定 browser ui 到 toolwindow 的 component
    //     this.setCodeWizBrowser(htmlPanel, project);
    //
    //     // cef 设置
    //     CefMessageRouter.CefMessageRouterConfig config = new CefMessageRouter
    //             .CefMessageRouterConfig("codeWizQuery", "codeWizCancel");
    //     CefMessageRouter cefMessageRouter = CefMessageRouter.create(config);
    //     cefMessageRouter.addHandler(new ListenerJsRouteHandle(project, this), true);
    //
    //     // 设置消息路由
    //     this.codeWizBrowser.getJBCefClient().getCefClient().addMessageRouter(cefMessageRouter);
    // }

    public void initBrowser(Project project) {
        JBCefBrowser jbCefBrowser = JBCefBrowserUtil.buildBrowserPanel(project, this.getWebviewBuilder());
        if (jbCefBrowser == null) {
            return;
        }
        this.setCodeWizBrowser(jbCefBrowser);
        this.setupJSQuery(jbCefBrowser);
    }

    private void setupJSQuery(JBCefBrowser htmlPanel) {
        JBCefJSQuery jsQuery = JBCefJSQuery.create((JBCefBrowserBase) htmlPanel);
        jsQuery.addHandler((query) -> {
            // 处理点击事件
            handleClickEvent(query, htmlPanel);
            return new JBCefJSQuery.Response("OK");
        });
        htmlPanel.getJBCefClient().addLoadHandler(new CefLoadHandlerAdapter() {
            @Override
            public void onLoadStart(CefBrowser browser, CefFrame frame, CefRequest.TransitionType transitionType) {
                if (frame.isMain()) {
                    initialized = false;
                    rootPanel.setProportion(0.0f);
                    JPanel leftPanel = new JPanel();
                    leftPanel.setBackground(EditorColorsManager.getInstance().getGlobalScheme().getDefaultBackground());
                    rootPanel.setSecondComponent(leftPanel);
                    rootPanel.repaint();
                }
            }

            @Override
            public void onLoadEnd(CefBrowser browser, CefFrame frame, int httpStatusCode) {
                if (frame.isMain() && !initialized) {
                    initialized = true;
                    String js = String.format(
                            "document.addEventListener('click', function(e) {" +
                                    "    %s;" +
                                    "});",
                            jsQuery.inject("JSON.stringify({" +
                                    "id: e.target.id, " +
                                    "tag: e.target.tagName, " +
                                    "x: e.clientX, " +
                                    "y: e.clientY" +
                                    "})")
                    );
                    browser.executeJavaScript(js, null, 0);
                    rootPanel.setSecondComponent(null);
                    rootPanel.setProportion(1.0f);
                    rootPanel.repaint();
                }
            }
        }, htmlPanel.getCefBrowser());
    }

    private void handleClickEvent(String clickData, JBCefBrowser htmlPanel) {
        htmlPanel.getCefBrowser().setFocus(true);
    }
}

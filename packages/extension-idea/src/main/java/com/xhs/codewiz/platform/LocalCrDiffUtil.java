package com.xhs.codewiz.platform;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.intellij.diff.editor.ChainDiffVirtualFile;
import com.intellij.diff.impl.CacheDiffRequestChainProcessor;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Key;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.util.Alarm;
import com.xhs.codewiz.client.RcsWebSocketManager;
import com.xhs.codewiz.platform.comment.CommentInlayService;
import com.xhs.codewiz.platform.enums.CommentStatus;
import com.xhs.codewiz.platform.model.ShowDiffRequest;
import com.xhs.codewiz.platform.model.ShowDiffRequest.DiffComment;
import com.xhs.codewiz.platform.model.ShowDiffRequest.DiffItem;
import com.xhs.codewiz.scheme.content.CreateCommentCollection;
import com.xhs.codewiz.scheme.content.CreateCommentCollection.CreateCommentCollectionParams;
import com.xhs.codewiz.scheme.content.CreateCommentProvider;
import com.xhs.codewiz.scheme.content.CreateComments;
import com.xhs.codewiz.scheme.content.CreateComments.CreateCommentsParams;
import com.xhs.codewiz.scheme.content.DeleteCommentCollection;
import com.xhs.codewiz.scheme.content.DeleteCommentCollection.DeleteCommentCollectionParams;
import com.xhs.codewiz.scheme.content.DeleteCommentProvider;
import com.xhs.codewiz.scheme.content.DeleteComments;
import com.xhs.codewiz.scheme.content.DeleteComments.DeleteCommentsParams;
import com.xhs.codewiz.scheme.content.UpdateComment;
import com.xhs.codewiz.scheme.content.UpdateComment.UpdateCommentParams;
import com.xhs.codewiz.scheme.content.UpdateCommentCollection;
import com.xhs.codewiz.scheme.content.UpdateCommentCollection.UpdateCommentCollectionParams;
import com.xhs.codewiz.type.content.Comment;
import com.xhs.codewiz.type.content.CommentCollection;
import com.xhs.codewiz.type.content.Range;
import com.xhs.codewiz.type.platform.BuildInPanel;
import com.xhs.codewiz.utils.DiffEditorVersionUtil;
import com.xhs.codewiz.utils.GitHandlerUtil;
import com.xhs.codewiz.utils.GsonUtil;
import com.xhs.codewiz.utils.LoggerUtil;
import com.xhs.codewiz.utils.ThreadUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import javax.swing.Timer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * Author: liukunpeng Date: 2025-08-01 Description:
 */
public class LocalCrDiffUtil {

  private static final Logger LOG = Logger.getInstance(LocalCrDiffUtil.class);

  private static final Alarm myAlarm = new Alarm(Alarm.ThreadToUse.SWING_THREAD);

  private static final int DELAY = 500;

  //同project+channel下，仅允许同时存在一个provider:key->project, value->channel,providerList
  private static Map<Project, Map<String, List<CreateCommentProvider>> > commentProviderMap = new ConcurrentHashMap<>();
  //key->providerChannel, value->collectionList
  private static Map<String, List<CommentCollection>> commentCollectionMap = new ConcurrentHashMap<>();
  //key->collectionChannel, value->commentList
  private static Map<String, List<Comment>> commentMap = new ConcurrentHashMap<>();

  private final static Key<String> KEY_DIFF_ITEMS = Key.create("SHOW_DIFF_ACTION_ITEMS");
  private final static Key<Integer> KEY_CURRENT_INDEX = Key.create("SHOW_DIFF_ACTION_CURRENT_INDEX");
  private final static Key<String> KEY_DIFF_GROUP_ID = Key.create("SHOW_DIFF_ACTION_GROUP_ID");


  // 保存当前的diff状态
  private static List<ShowDiffRequest.DiffItem> currentDiffItems;
  private static int currentIndex = -1;
  private static Project currentProject;
  private static ChainDiffVirtualFile currentDiffFile;
  private static ShowDiffRequest currentRequest;

  public static void createCommentProvider(String str, String channel) {
    CreateCommentProvider provider = GsonUtil.fromJson(str, CreateCommentProvider.class);
    if (null == provider || null == provider.getParams()) {
      return;
    }
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    Map<String, List<CreateCommentProvider>> projectMap = commentProviderMap.computeIfAbsent(project, k -> new ConcurrentHashMap<>());
    List<CreateCommentProvider> channelProviders = projectMap.computeIfAbsent(channel, k -> new CopyOnWriteArrayList<>());
    if (CollectionUtils.isNotEmpty(channelProviders)) {
      if (projectMap.get(channel).stream().anyMatch(p -> p.getParams().getId().equals(provider.getParams().getId()))) {
        //已经有了，返回
        return;
      }
    }
    channelProviders.add(provider);
  }

  public static void deleteCommentProvider(String str, String channel) {
    DeleteCommentProvider deleteCommentProvider = GsonUtil.fromJson(str, DeleteCommentProvider.class);
    if (null == deleteCommentProvider || StringUtils.isEmpty(deleteCommentProvider.getParams())) {
      return;
    }
    Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
    Map<String, List<CreateCommentProvider>> projectMap = commentProviderMap.computeIfAbsent(project, k -> new ConcurrentHashMap<>());
    List<CreateCommentProvider> channelProviders = projectMap.computeIfAbsent(channel, k -> new ArrayList<>());
    if (CollectionUtils.isNotEmpty(channelProviders)) {
      //只删除id可以对应的
      channelProviders.removeIf(provider -> provider.getParams().getId().equals(deleteCommentProvider.getParams()));
      //尝试删除collection
      List<CommentCollection> needDel = commentCollectionMap.computeIfAbsent(deleteCommentProvider.getParams(), k -> new ArrayList<>());
      if (CollectionUtils.isEmpty(needDel)) {
        //删除commetn
        for (CommentCollection collection : needDel) {
          commentMap.remove(collection.getId());
        }
        commentCollectionMap.remove(deleteCommentProvider.getParams());
      }
    }
  }

  public static void createCommentCollection(String str, String channel) {
    CreateCommentCollection createCommentCollection = GsonUtil.fromJson(str, CreateCommentCollection.class);
    if (null == createCommentCollection || null == createCommentCollection.getParams()) {
      return;
    }
    //channel: local/remote/provider
    CreateCommentCollectionParams params = createCommentCollection.getParams();
    List<CommentCollection> collections = commentCollectionMap.computeIfAbsent(channel, k -> new ArrayList<>());
    if (collections.stream().anyMatch(collection
        -> collection.getId().equals(params.getCollection().getId()))) {
      return;
    }
    collections.add(params.getCollection());
  }

  public static void updateCommentCollection(String str, String channel) {
    UpdateCommentCollection updateCommentCollection = GsonUtil.fromJson(str, UpdateCommentCollection.class);
    if (null == updateCommentCollection || null == updateCommentCollection.getParams()) {
      return;
    }
    //channel: local/remote/provider/collection
    UpdateCommentCollectionParams params = updateCommentCollection.getParams();
    String providerMapKey = channel.substring(0,  channel.lastIndexOf("/"));
    List<CommentCollection> collections = commentCollectionMap.computeIfAbsent(providerMapKey, k -> new ArrayList<>());
    collections.stream()
        .filter(collection -> collection.getId()
            .equals(params.getCollectionId()))
        .findFirst().ifPresent(order -> {
          order.setLabel(params.getCollection().getLabel());
          order.setRange(params.getCollection().getRange());
          order.setUri(params.getCollection().getUri());
          order.setCollapsible(params.getCollection().getCollapsible());
          //Project project = RcsWebSocketManager.INSTANCE.getProjectByRemoteChannel(channel);
          //LocalCrDiffUtil.showCommentDiffPanel(project, order.getUri());
        });
  }

  public static void deleteCommentCollection(String str, String channel) {
    DeleteCommentCollection deleteCommentCollection = GsonUtil.fromJson(str, DeleteCommentCollection.class);
    if (null == deleteCommentCollection || null == deleteCommentCollection.getParams()) {
      return;
    }
    //channel: local/remote/provider/collection
    DeleteCommentCollectionParams params = deleteCommentCollection.getParams();
    String providerMapKey = channel.substring(0,  channel.lastIndexOf("/"));
    List<CommentCollection> collections = commentCollectionMap.computeIfAbsent(providerMapKey, k -> new ArrayList<>());
    if (CollectionUtils.isNotEmpty(collections)) {
      collections.removeIf(collection -> collection.getId().equals(params.getCollectionId()));
    }
    commentMap.remove(params.getCollectionId());
  }

  public static void createComments(String str, String channel) {
    CreateComments comments = GsonUtil.fromJson(str, CreateComments.class);
    if (null == comments || null == comments.getParams()) {
      return;
    }
    CreateCommentsParams params = comments.getParams();
    if (CollectionUtils.isEmpty(params.getComments())) {
      return;
    }
    List<Comment> nowComments = commentMap.computeIfAbsent(channel, k -> Collections.synchronizedList(Lists.newArrayList()));
    if (CollectionUtils.isNotEmpty(nowComments)) {
      //不为空，说明可能是del + create场景，create先进来了
      ThreadUtil.sleep(200);
    }
    nowComments.addAll(params.getComments());
    //这里的实际channel是local/remote/provider/collection，所以休要处理一下

    String providerMapKey = channel.substring(0,  channel.lastIndexOf("/"));
    List<CommentCollection> collections = commentCollectionMap.getOrDefault(providerMapKey, null);
    if (CollectionUtils.isEmpty(collections)) {
      return;
    }
    CommentCollection order = collections.stream()
        .filter(collection -> collection.getId().equals(params.getCollectionId()))
        .findFirst()
        .orElse(null);
    if (null == order) {
      return;
    }
    LocalCrDiffUtil.showCommentDiffPanel(RcsWebSocketManager.INSTANCE.getProjectByLocalChannel(channel), order.getUri());
  }

  public static void updateComment(String str, String channel) {
    UpdateComment updateComment = GsonUtil.fromJson(str, UpdateComment.class);
    if (null == updateComment || null == updateComment.getParams()) {
      return;
    }
    UpdateCommentParams params = updateComment.getParams();
    Comment newComment = params.getComment();
    List<Comment> nowComments = commentMap.computeIfAbsent(channel, k -> Collections.synchronizedList(Lists.newArrayList()));
    for (Comment comment : nowComments) {
      if (comment.getId().equals(params.getComment().getId())) {
        comment.setAuthor(newComment.getAuthor());
        comment.setLabel(newComment.getLabel());
        comment.setBody(newComment.getBody());
        comment.setTimestamp(newComment.getTimestamp());
        break;
      }
    }
    String providerMapKey = channel.substring(0,  channel.lastIndexOf("/"));
    List<CommentCollection> collections = commentCollectionMap.getOrDefault(providerMapKey, null);
    if (CollectionUtils.isEmpty(collections)) {
      return;
    }
    //更新后，需要刷新
    CommentCollection order = collections.stream()
        .filter(collection -> collection.getId().equals(params.getCollectionId()))
        .findFirst()
        .orElse(null);
    if (null == order) {
      return;
    }
    LocalCrDiffUtil.showCommentDiffPanel(RcsWebSocketManager.INSTANCE.getProjectByLocalChannel(channel), order.getUri());
  }
  public static void delComment(String str, String channel) {
    DeleteComments del = GsonUtil.fromJson(str, DeleteComments.class);
    if (null == del || null == del.getParams() || CollectionUtils.isEmpty(del.getParams().getCommentIds())) {
      return;
    }
    DeleteCommentsParams params = del.getParams();
    List<Comment> nowComments = commentMap.computeIfAbsent(channel, k -> Collections.synchronizedList(Lists.newArrayList()));
    if (CollectionUtils.isNotEmpty(nowComments)) {
      nowComments.removeIf(comment -> params.getCommentIds().contains(comment.getId()));
    }
  }

  public static void clearProjectComment() {
    //TODO
  }

  public static boolean isDiffComment(Project project, String channel, String rightUri) {
    Map<String, List<CreateCommentProvider>> projectMap = commentProviderMap.getOrDefault(project, new HashMap<>());
    if (projectMap.isEmpty() || !projectMap.containsKey(channel)) {
      return false;
    }
    List<CreateCommentProvider> createCommentProviders = projectMap.get(channel);
    for (CreateCommentProvider createCommentProvider : createCommentProviders) {
      String provideId = createCommentProvider.getParams().getId();
      String collectionMapKey = channel.replace("default", provideId);
      List<CommentCollection> collections = commentCollectionMap.getOrDefault(collectionMapKey, null);
      if (CollectionUtils.isEmpty(collections)) {
        continue;
      }
      if (collections.stream().anyMatch(
          collection -> StringUtils.equals(rightUri, collection.getUri()))) {
        return true;
      }
    }
    return false;
  }

  public static void showCommentDiffPanel(Project project, String uri, BuildInPanel.BuildInPanelDiff diff) {
    myAlarm.cancelAllRequests();
    myAlarm.addRequest(() -> ApplicationManager.getApplication().invokeLater(() -> {
      doShowCommentDiffPanel(project, uri, diff);
    }), DELAY);
  }

  private static void doShowCommentDiffPanel(Project project, String uri, BuildInPanel.BuildInPanelDiff diff) {
    List<Pair<String, CommentCollection>> orderCollectionList = buildOrderCollectionList(uri);
    if (CollectionUtils.isEmpty(orderCollectionList)) {
      return;
    }
    ShowDiffRequest request = new ShowDiffRequest();
    List<DiffItem> diffItems = buildDiffItems(orderCollectionList);
    request.setDiffItems(diffItems);
    request.setIndex(0);
    request.setNoteId("0");
    showDiffFromRequest(project, request, uri, diff);
  }

  /**
   * 评论都是异步发送过来的，所以需要抖动处理
   * @param project
   * @param uri
   */
  public static void showCommentDiffPanel(Project project, String uri) {
    myAlarm.cancelAllRequests();
    myAlarm.addRequest(() -> ApplicationManager.getApplication().invokeLater(() -> {
      doShowCommentDiffPanel(project, uri);
    }), DELAY);
  }

  private static void doShowCommentDiffPanel(Project project, String uri) {
    List<Pair<String, CommentCollection>> orderCollectionList = buildOrderCollectionList(uri);
    if (CollectionUtils.isEmpty(orderCollectionList)) {
      return;
    }
    ShowDiffRequest request = new ShowDiffRequest();
    List<DiffItem> diffItems = buildDiffItems(orderCollectionList);
    request.setDiffItems(diffItems);
    request.setIndex(0);
    request.setNoteId("0");
    showDiffFromRequest(project, request, uri, null);
  }

  private static List<Pair<String, CommentCollection>> buildOrderCollectionList(String uri) {
    List<Pair<String, CommentCollection>> orderCollectionList = Lists.newArrayList();
    for (Map.Entry<String, List<CommentCollection>> entry : commentCollectionMap.entrySet()) {
      for (CommentCollection collection : entry.getValue()) {
        if (StringUtils.equals(collection.getUri(), uri)) {
          orderCollectionList.add(Pair.of(entry.getKey(), collection));
        }
      }
    }
    return orderCollectionList;
  }

  private static List<DiffItem> buildDiffItems(List<Pair<String, CommentCollection>> orderCollectionList) {
    return orderCollectionList.stream().map(pair -> {
      CommentCollection collection = pair.getRight();
      String commentMapKey = pair.getLeft() + "/" + collection.getId();
      DiffItem item = new DiffItem();
      item.setStartLine(collection.getRange().getStart().getLine());
      item.setEndLine(collection.getRange().getEnd().getLine());
      List<Comment> comments = commentMap.getOrDefault(commentMapKey, Collections.synchronizedList(Lists.newArrayList()));
      if (CollectionUtils.isNotEmpty(comments)) {
        List<DiffComment> diffComments = comments.stream().map(comment -> {
          DiffComment diffComment = new DiffComment();
          diffComment.setStartLine(item.getStartLine());
          diffComment.setEndLine(item.getEndLine());
          diffComment.setNoteId(comment.getId());
          diffComment.setStatus(CommentStatus.UNKNOWN);
          diffComment.setNoteContent(comment.getBody().getValue());
          diffComment.setChannel(pair.getLeft());
          diffComment.setCollectId(collection.getId());
          diffComment.setStatus(CommentStatus.fromValue(comment.getContextValue()));
          return diffComment;
        }).toList();
        item.setComments(diffComments);
      }
      return item;
    }).toList();
  }
  /**
   * 基于ShowDiffRequest创建diff展示页面
   *
   * @param project 项目
   * @param request diff请求
   */
  private static void showDiffFromRequest(Project project, ShowDiffRequest request, String uri, BuildInPanel.BuildInPanelDiff selectDiff) {
    if (project == null || project.isDisposed() ||
        request == null || request.getDiffItems() == null || request.getDiffItems().isEmpty()) {
      return;
    }

    // 在后台线程执行Git操作
    ApplicationManager.getApplication().executeOnPooledThread(() -> {
      try {
        List<ShowDiffRequest.DiffItem> diffItems = prepareDiffItems(request, selectDiff);
        request.setDiffItems(diffItems);
        int targetIndex = Math.max(0, Math.min(request.getIndex(), diffItems.size() - 1));

        // 在EDT线程上更新UI
        ApplicationManager.getApplication().invokeLater(() -> {
          if (!project.isDisposed()) {
            showMultipleDiff(project, diffItems, targetIndex, request, uri);
          }
        });
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(LOG, "Failed to show diff from request", e);
      }
    });
  }

  /**
   * 准备diff项目，补充缺失的内容
   */
  private static List<ShowDiffRequest.DiffItem> prepareDiffItems(ShowDiffRequest request, BuildInPanel.BuildInPanelDiff selectDiff) {
    List<DiffItem> requestItems = request.getDiffItems();
    if (CollectionUtils.isEmpty(requestItems)) {
      return requestItems;
    }

    List<ShowDiffRequest.DiffItem> sortedItems = new LinkedList<>(requestItems);
    sortedItems.sort(Comparator.comparingInt(ShowDiffRequest.DiffItem::getStartLine));
    int size = sortedItems.size();

    for (int i = 0; i < sortedItems.size(); i++) {
      if (CollectionUtils.isEmpty(sortedItems.get(i).getComments())) {
        continue;
      }
      // 设置评论的索引和总数
      sortedItems.get(i).getComments().get(0).setIndex(i + 1);
      sortedItems.get(i).getComments().get(0).setSize(size);
      if (selectDiff != null && selectDiff.getSelection() != null
              && selectDiff.getSelection().getStart().getLine() == sortedItems.get(i).getStartLine()
              && selectDiff.getSelection().getEnd().getLine() == sortedItems.get(i).getEndLine()) {
        request.setIndex(i);
        request.setNoteId(sortedItems.get(i).getComments().get(0).getNoteId());
      }
    }
    return sortedItems;
  }

  /**
   * 展示多个文件的diff，支持在同一个编辑器组中切换
   *
   * @param project     项目
   * @param diffItems   diff项目列表
   * @param targetIndex 目标显示的diff索引
   * @param request     原始请求对象（用于获取外层noteId）
   */
  private static void showMultipleDiff(Project project, List<ShowDiffRequest.DiffItem> diffItems, int targetIndex, ShowDiffRequest request, String uri) {
    if (project == null || project.isDisposed()) {
      return;
    }
    if (diffItems == null || diffItems.isEmpty() || targetIndex < 0 || targetIndex >= diffItems.size()) {
      return;
    }
    try {
      // 保存当前请求
      currentRequest = request;
      // 创建新的diff页面
      createDiffTab(project, diffItems, targetIndex, uri);
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(LOG, "Failed to show multiple diff", e);
    }
  }

  /**
   * 关闭当前的diff编辑器（优化版）
   */
  private static void closeCurrentDiffEditor() {
    if (currentProject == null || currentProject.isDisposed()) {
      return;
    }

    try {
      // 直接关闭当前的diff文件，而不是遍历查找
      if (currentDiffFile != null) {
        FileEditorManager editorManager = FileEditorManager.getInstance(currentProject);
        editorManager.closeFile(currentDiffFile);
        currentDiffFile = null;
      }
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(LOG, "Failed to close current diff editor", e);
    }
  }

  /**
   * 创建diff tab
   */
  private static void createDiffTab(Project project,
      List<ShowDiffRequest.DiffItem> diffItems,
      int targetIndex, String uri) {
    if (project.isDisposed()) {
      return;
    }
    ShowDiffRequest.DiffItem targetItem = diffItems.get(targetIndex);
    // 在后台线程获取文件内容
    ApplicationManager.getApplication().executeOnPooledThread(() -> {
      try {
        ApplicationManager.getApplication().invokeLater(() -> {
          if (project.isDisposed()) {
            return;
          }
          try {
            // 保存状态
            currentDiffItems = new ArrayList<>(diffItems);
            currentIndex = targetIndex;
            currentProject = project;
            ChainDiffVirtualFile newFile = DiffPanelUtil.getCommentChainDiffFileByRightUri(uri);
            if (null == newFile) {
              return;
            }
            if (!newFile.equals(currentDiffFile)) {
              // 关闭之前的diff编辑器
              closeCurrentDiffEditor();
            }
            currentDiffFile = newFile;
            // 打开编辑器
            FileEditorManager editorManager = FileEditorManager.getInstance(project);
            //尝试关闭重新打开（当前模式下，打开panel与show不是一体的，是异步发送的，所以此时可能已经展示了一个空评论页面了）
            //editorManager.closeFile(currentDiffFile);

            // 在VirtualFile中保存diff信息
            currentDiffFile.putUserData(KEY_DIFF_ITEMS, serializeDiffItems(diffItems));
            currentDiffFile.putUserData(KEY_CURRENT_INDEX, targetIndex);

            FileEditor[] editors = editorManager.openFile(currentDiffFile, true);
            if (editors.length > 0) {
              // addBottomNavigationPanel(project, editors[0], diffItems, targetIndex);

              // 添加评论显示
              List<DiffComment> list = diffItems.stream().flatMap(item -> item.getComments().stream()).toList();
              targetItem.setComments(list);
              addDiffComments(project, editors[0], targetItem);
            }
          } catch (Exception e) {
            // 创建diff内容失败时的日志
            LoggerUtil.INSTANCE.logWarn(LOG, "Failed to create diff content", e);
          }
        });
      } catch (Exception e) {
        LoggerUtil.INSTANCE.logWarn(LOG, "Failed to get file content from git", e);
      }
    });
  }

  /**
   * 序列化diff项目列表（简化实现）
   */
  private static String serializeDiffItems(List<ShowDiffRequest.DiffItem> diffItems) {
    // 这里可以用JSON或其他方式序列化，简化起见直接用toString
    return diffItems.toString();
  }

  /**
   * 为diff页面添加评论显示
   */
  private static void addDiffComments(Project project, FileEditor fileEditor, ShowDiffRequest.DiffItem diffItem) {
    if (project == null || project.isDisposed() || fileEditor == null || diffItem == null) {
      return;
    }

    // 等待diff编辑器完全初始化
    waitForDiffEditorInitialization(fileEditor, () -> {
      Editor rightEditor = getRightEditorFromDiff(fileEditor);
      if (rightEditor == null) {
        LoggerUtil.INSTANCE.logWarn(LOG, "Failed to get right editor from diff", null);
        return;
      }

      // 显示评论
      if (diffItem.getComments() != null && !diffItem.getComments().isEmpty()) {
        // 根据request的noteId找到要定位的comment
        ShowDiffRequest.DiffComment targetComment = findCommentByNoteId(diffItem.getComments());
        if (targetComment == null) {
          // 如果找不到匹配的comment，默认定位到第一个comment
          targetComment = diffItem.getComments().get(0);
        }

        try {
          // 先添加评论，不立即滚动
          CommentInlayService.getInstance().addDiffComments(rightEditor, diffItem.getComments(), null);
          // 延迟滚动，确保在diff编辑器的滚动之后执行
          scheduleCommentScrolling(rightEditor, targetComment.getNoteId(), 3); // 重试3次
        } catch (Exception e) {
          LoggerUtil.INSTANCE.logWarn(LOG, "Failed to add diff comments", e);
        }
      }
    });
  }

  /**
   * 等待diff编辑器完全初始化
   */
  private static void waitForDiffEditorInitialization(FileEditor fileEditor, Runnable callback) {
    if (!DiffEditorVersionUtil.isDiffEditor(fileEditor)) {
      return;
    }

    // 检查diff编辑器是否已经初始化
    // 50ms检查一次，等待完成召回
    Timer checkTimer = new Timer(50, null);
    checkTimer.addActionListener(e -> {
      try {
        CacheDiffRequestChainProcessor processor = DiffEditorVersionUtil.getDiffProcessor(fileEditor);
        if (processor != null && processor.getActiveViewer() != null) {
          checkTimer.stop();
          // 再延迟一点，确保滚动完成
          Timer delayTimer = new Timer(100, evt -> {
            ((Timer) evt.getSource()).stop();
            // 在read action中执行回调
            ApplicationManager.getApplication().runReadAction(callback);
          });
          delayTimer.setRepeats(false);
          delayTimer.start();
        }
      } catch (Exception ex) {
        // 继续等待
        LoggerUtil.INSTANCE.logWarn(LOG, "Failed to get right editor from diff", ex);
      }
    });
    checkTimer.start();
  }

  /**
   * 调度评论滚动，带重试机制
   */
  private static void scheduleCommentScrolling(Editor editor, String targetNoteId, int maxRetries) {
    Timer scrollTimer = new Timer(300, null); // 300ms后执行滚动
    scrollTimer.addActionListener(e -> {
      scrollTimer.stop();

      boolean scrolled = performCommentScrolling(editor, targetNoteId);

      if (!scrolled && maxRetries > 0) {
        // 如果滚动失败且还有重试次数，继续重试
        scheduleCommentScrolling(editor, targetNoteId, maxRetries - 1);
      }
    });
    scrollTimer.setRepeats(false);
    scrollTimer.start();
  }

  /**
   * 执行评论滚动
   */
  private static boolean performCommentScrolling(Editor editor, String targetNoteId) {
    try {
      // 检查编辑器是否还在滚动中
      if (isEditorScrolling(editor)) {
        return false; // 稍后重试
      }

      // 执行滚动
      CommentInlayService service = CommentInlayService.getInstance();
      return service.scrollToDiffComment(targetNoteId);
    } catch (Exception e) {
      LoggerUtil.INSTANCE.logWarn(LOG, "Failed to scroll to comment", e);
      return false;
    }
  }

  /**
   * 检查编辑器是否正在滚动
   */
  private static boolean isEditorScrolling(Editor editor) {
    // 在read action中记录当前滚动位置
    int currentScrollY = ApplicationManager.getApplication().runReadAction((com.intellij.openapi.util.Computable<Integer>) () -> {
      return editor.getScrollingModel().getVerticalScrollOffset();
    });

    // 等待一小段时间后检查位置是否变化
    final int[] newScrollY = {currentScrollY};
    Timer checkTimer = new Timer(50, e -> {
      ApplicationManager.getApplication().runReadAction(() -> {
        newScrollY[0] = editor.getScrollingModel().getVerticalScrollOffset();
      });
    });
    checkTimer.setRepeats(false);
    checkTimer.start();

    try {
      Thread.sleep(60);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }

    return Math.abs(newScrollY[0] - currentScrollY) > 5; // 如果滚动距离超过5像素，认为正在滚动
  }

  /**
   * 根据request的noteId在comment列表中找到对应的comment
   */
  private static ShowDiffRequest.DiffComment findCommentByNoteId(List<ShowDiffRequest.DiffComment> comments) {
    if (currentRequest == null || currentRequest.getNoteId() == null || comments == null) {
      return null;
    }

    String requestNoteId = currentRequest.getNoteId();
    for (ShowDiffRequest.DiffComment comment : comments) {
      if (Objects.equals(comment.getNoteId(), requestNoteId)) {
        return comment;
      }
    }
    return null;
  }

  /**
   * 找到第一个有评论的diffItem
   */
  private static ShowDiffRequest.DiffItem findFirstDiffItemWithComments() {
    if (currentDiffItems == null) {
      return null;
    }

    for (ShowDiffRequest.DiffItem item : currentDiffItems) {
      if (item.getComments() != null && !item.getComments().isEmpty()) {
        return item;
      }
    }
    return null;
  }

  /**
   * 从diff文件编辑器获取右侧编辑器
   * 使用DiffEditorVersionUtil工具类自动处理版本兼容性
   */
  private static Editor getRightEditorFromDiff(FileEditor fileEditor) {
    return DiffEditorVersionUtil.getRightEditor(fileEditor);
  }
}

package com.xhs.codewiz.editor.request;



import com.intellij.openapi.Disposable;
import com.intellij.openapi.project.Project;
import com.xhs.codewiz.completion.enums.CompletionType;
import com.xhs.codewiz.type.content.EditEventTriggerKind;
import com.xhs.codewiz.utils.Cancellable;
import java.util.concurrent.CompletableFuture;
import org.jetbrains.annotations.NotNull;

public interface EditorRequest extends Cancellable {
    @NotNull LineInfo getLineInfo();

    boolean equalsRequest(@NotNull EditorRequest var1);

    default @NotNull String getCurrentDocumentPrefix() {
        return this.getDocumentContent().substring(0, this.getOffset());
    }

    @NotNull String getDocumentContent();

    @NotNull LanguageInfo getFileLanguage();

    @NotNull Project getProject();

    @NotNull CompletionType getCompletionType();

    EditEventTriggerKind getEditEventTriggerKind();

    int getOffset();

    boolean isUseTabIndents();

    int getTabWidth();

    int getRequestId();

    int getDocumentVersion();

    Disposable getDisposable();

    long getRequestTimestamp();

    long getDocumentModificationSequence();

    void setCompletionFeature(CompletableFuture<Void> allDone);
}

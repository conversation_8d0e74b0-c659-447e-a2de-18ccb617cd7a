package com.xhs.codewiz.scheme.file;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 还原文件
 */
public class ExecuteRevert {
    private String schemaProtocol = "file.execute.revert";
    private ExecuteRevertParams params;

    public ExecuteRevertParams getParams() {
        return params;
    }
    public void setParams(ExecuteRevertParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        public static class ExecuteRevertParams {
        /** 要还原的文件 */
        private String uri;
    
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

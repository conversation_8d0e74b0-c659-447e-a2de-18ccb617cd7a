package com.xhs.codewiz.client.websocket;

import com.intellij.openapi.diagnostic.Logger;
import com.xhs.codewiz.client.RcsLanguageServer;
import java.nio.ByteBuffer;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import javax.websocket.Session;

public class RcsHeartbeatRunner implements Runnable {
  private static final Logger LOG = Logger.getInstance(RcsHeartbeatRunner.class);

  private boolean isRunning = true;
  private long lastHeartbeat;
  private long lastGetState = System.currentTimeMillis();
  private static final long THREAD_LOOP_INTERVAL = TimeUnit.SECONDS.toMillis(5L);
  private static final long HEARTBEAT_INTERVAL = TimeUnit.SECONDS.toMillis(5L);
  private static final long GETSTATE_INTERVAL = TimeUnit.HOURS.toMillis(6L);
  private Session session;
  private static final ByteBuffer pingBuffer = ByteBuffer.wrap("Ping".getBytes());
  private RcsLanguageServer server;

  public RcsHeartbeatRunner(Session session, RcsLanguageServer server) {
    this.session = session;
    this.server = server;
  }

  @Override
  public void run() {
    try {
      while (this.isRunning) {
        long startTime = System.currentTimeMillis();
        if (startTime - this.lastGetState >= GETSTATE_INTERVAL) {
          this.lastGetState = startTime;
          if (!this.checkStatus()) break;
        }
        if (startTime - this.lastHeartbeat >= HEARTBEAT_INTERVAL) {
          if (!this.checkPing()) break;
          this.session.getAsyncRemote().sendPing(pingBuffer);
          this.lastHeartbeat = startTime;
        }
        Thread.sleep(THREAD_LOOP_INTERVAL);
      }
    }
    catch (Throwable e) {
      LOG.warn(this.getLogHeader() + "Heart beat exception:" + e.getMessage());
    }
  }

  private boolean checkStatus() {
    /*try {
      if (CopilotWebSocketManager.INSTANCE.checkCopilotAgent()) {
        logger.logMessage(this.getLogHeader() + "copilot heart beat check status success");
        return true;
      } else {
        logger.logMessage(this.getLogHeader() + "copilot heart beat check status fail");
        return false;
      }
    } catch (Throwable e) {
      logger.logWarn(this.getLogHeader() + "copilot get auth status in heartbeat error " + e.getMessage());
    }*/
    return true;
  }

  private boolean checkPing() {
    /*try {
      if (CopilotWebSocketManager.INSTANCE.checkCopilotAgent()) {
        logger.logMessage(this.getLogHeader() + "copilot start ping heart beat");
        this.server.ping();
      }
    }
    catch (Throwable e) {
      logger.logWarn(this.getLogHeader() + "copilot ping in heartbeat error " + e.getMessage());
    }
    if (!this.session.isOpen()) {
      logger.logWarn(this.getLogHeader() + "copilot session is closed. exit current thread");
      reConnect();
      return false;
    }*/
    return true;
  }

  private void reConnect() {
    CompletableFuture.runAsync(() -> {
      /*try {
        List<String> projectUris = CopilotWebSocketManager.INSTANCE.getProjectUris();
        if (CollectionUtils.isNotEmpty(projectUris)) {
          for (String projectUri : projectUris) {
            ArrayList<WorkspaceFolder> workspaceFolders = new ArrayList<WorkspaceFolder>();
            WorkspaceFolder folder = new WorkspaceFolder();
            folder.setName(projectUri.substring(projectUri.lastIndexOf("/") + 1));
            folder.setUri(projectUri);
            workspaceFolders.add(folder);
            InitializeParamsWithConfig params = new InitializeParamsWithConfig();
            params.setWorkspaceFolders(workspaceFolders);
            CopilotWebSocketManager.INSTANCE.connectCosyServer(params);
          }
        }

      } catch (Throwable e) {
        logger.logWarn(this.getLogHeader() + "copilot reconnect error " + e.getMessage());
      }*/
    });
  }
  private String getLogHeader() {
    return String.format("[%s] ", Thread.currentThread().getName());
  }
}
package com.xhs.codewiz.type.content;

import java.util.*;
import com.xhs.codewiz.type.global.Command;
import com.xhs.codewiz.type.workspace.FileChanges;

/**
 * Action 项
 */
public class ActionItem {
    /** 相关诊断信息 */
    private List<Diagnostic> diagnostics;

    /** 操作影响的文本范围 */
    private List<Range> ranges;

    /** 编辑内容变更 */
    private List<FileChanges> edit;

    private ActionKind kind;

    /** 是否作为默认操作，用于快捷键等 */
    private Boolean isPreferred;

    /** 操作标题 */
    private String title;

    private Command command;

    public List<Diagnostic> getDiagnostics() {
        return diagnostics;
    }

    public void setDiagnostics(List<Diagnostic> diagnostics) {
        this.diagnostics = diagnostics;
    }

    public List<Range> getRanges() {
        return ranges;
    }

    public void setRanges(List<Range> ranges) {
        this.ranges = ranges;
    }

    public List<FileChanges> getEdit() {
        return edit;
    }

    public void setEdit(List<FileChanges> edit) {
        this.edit = edit;
    }

    public ActionKind getKind() {
        return kind;
    }

    public void setKind(ActionKind kind) {
        this.kind = kind;
    }

    public Boolean getIsPreferred() {
        return isPreferred;
    }

    public void setIsPreferred(Boolean isPreferred) {
        this.isPreferred = isPreferred;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Command getCommand() {
        return command;
    }

    public void setCommand(Command command) {
        this.command = command;
    }

}

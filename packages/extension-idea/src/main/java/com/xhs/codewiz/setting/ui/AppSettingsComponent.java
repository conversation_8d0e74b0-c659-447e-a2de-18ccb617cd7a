package com.xhs.codewiz.setting.ui;

import com.intellij.openapi.ui.ComboBox;
import com.intellij.ui.ColorPanel;
import com.intellij.ui.TitledSeparator;
import com.intellij.ui.components.JBCheckBox;
import com.intellij.util.ui.FormBuilder;
import com.xhs.codewiz.setting.CodeWizApplicationSettings;
import java.awt.Color;
import java.awt.Cursor;
import java.awt.FlowLayout;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import javax.swing.Box;
import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JPanel;
import org.apache.commons.collections4.CollectionUtils;

/**
 * Supports creating and managing a {@link JPanel} for the Settings Dialog.
 */
public class AppSettingsComponent {

  private final JPanel myMainPanel;
  private J<PERSON>heckBox autoUpdateCheckBox;
  private final ColorPanel inlineCompletionColorPanel;

  public AppSettingsComponent() {
    // 区域标题（分隔线）
    TitledSeparator sectionTitle = new TitledSeparator("更新设置");
    // 设置自动更新复选框
    autoUpdateCheckBox = new JBCheckBox("自动检测新版本");
    autoUpdateCheckBox.setSelected(CodeWizApplicationSettings.settings().autoUpdate.checkForUpdate);

    // 设置补全颜色
    ColorPanel colorPanel = new ColorPanel();
    this.inlineCompletionColorPanel = colorPanel;

    FormBuilder formBuilder = FormBuilder.createFormBuilder()
        .addComponent(sectionTitle)
        .addComponent(autoUpdateCheckBox)
        .addLabeledComponent("补全颜色", colorPanel);

    formBuilder.addComponentFillVertically(new JPanel(), 0);
    //设计设置布局
    myMainPanel = formBuilder.getPanel();

  }

  public JPanel getPanel() {
    return myMainPanel;
  }

  public JComponent getPreferredFocusedComponent() {
    return autoUpdateCheckBox;
  }

  public Color getInlineCompletionColor() {
    return inlineCompletionColorPanel.getSelectedColor();
  }

  public void setInlineCompletionColor(Color color) {
    inlineCompletionColorPanel.setSelectedColor(color);
  }

  public boolean getUpdateCheck() {
    return autoUpdateCheckBox.isSelected();
  }

  public void setUpdateCheck(boolean newStatus) {
    autoUpdateCheckBox.setSelected(newStatus);
  }
}
package com.xhs.codewiz.scheme.platform;

import java.util.*;
/**
 * 聚焦面板
 */
public class ExecuteFocusPanel {
    private String schemaProtocol = "platform.execute.focuspanel";
    /** 面板 ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

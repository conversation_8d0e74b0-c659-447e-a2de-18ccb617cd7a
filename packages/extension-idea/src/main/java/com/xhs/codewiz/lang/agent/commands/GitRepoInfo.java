package com.xhs.codewiz.lang.agent.commands;

import com.intellij.openapi.project.Project;
import com.xhs.codewiz.lang.agent.rpc.JsonRpcNotification;
import com.xhs.codewiz.utils.GitHandlerUtil;
import org.jetbrains.annotations.NotNull;

/**
 * Author: liukunpeng Date: 2025-07-22 Description:
 */
public class GitRepoInfo implements JsonRpcNotification {
  private String gitRepo;
  private String gitBranch;
  private String gitCommit;

  private String projectUri;

  public GitRepoInfo(Project project) {
    gitRepo = GitHandlerUtil.getGitUrl(project);
    gitBranch = GitHandlerUtil.getGitBranch(project);
    gitCommit = GitHandlerUtil.getLatestCommitId(project);
    projectUri = project.getBasePath();
  }
  public String getGitRepo() {
    return gitRepo;
  }

  public void setGitRepo(String gitRepo) {
    this.gitRepo = gitRepo;
  }

  public String getGitBranch() {
    return gitBranch;
  }

  public void setGitBranch(String gitBranch) {
    this.gitBranch = gitBranch;
  }

  public String getGitCommit() {
    return gitCommit;
  }

  public void setGitCommit(String gitCommit) {
    this.gitCommit = gitCommit;
  }

  public String getProjectUri() {
    return projectUri;
  }

  public void setProjectUri(String projectUri) {
    this.projectUri = projectUri;
  }
  @Override
  public @NotNull String getCommandName() {
    return "updateGitInfo";
  }
}

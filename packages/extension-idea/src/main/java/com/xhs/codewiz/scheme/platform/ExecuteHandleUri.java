package com.xhs.codewiz.scheme.platform;

import java.util.*;
import com.xhs.codewiz.type.file.Uri;
/**
 * 执行 Uri Handler
 */
public class ExecuteHandleUri {
    private String schemaProtocol = "platform.execute.handleuri";
    /** 要处理的 Uri */
    private ExecuteHandleUriParams params;

    public ExecuteHandleUriParams getParams() {
        return params;
    }
    public void setParams(ExecuteHandleUriParams params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }

        /**
     * 要处理的 Uri
     */
    public static class ExecuteHandleUriParams {
        /** Uri Handler Provider ID */
        private String provider;
        /** 要处理的 Uri */
        private String uri;
    
        public String getProvider() {
            return provider;
        }
        public void setProvider(String provider) {
            this.provider = provider;
        }
        public String getUri() {
            return uri;
        }
        public void setUri(String uri) {
            this.uri = uri;
        }
    }
}

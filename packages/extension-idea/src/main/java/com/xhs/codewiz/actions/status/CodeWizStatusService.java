package com.xhs.codewiz.actions.status;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.Application;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.openapi.util.Pair;
import com.intellij.util.concurrency.annotations.RequiresEdt;
import com.xhs.codewiz.actions.statusBar.CodeWizStatusBarWidget;
import com.xhs.codewiz.utils.ApplicationUtil;
import java.util.concurrent.atomic.AtomicBoolean;
import javax.annotation.concurrent.GuardedBy;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CodeWizStatusService implements CodeWizStatusListener, Disposable {
    private final Object lock = new Object();
    @GuardedBy("lock")
    private @NotNull CodeWizStatus status;
    @GuardedBy("lock")
    private @Nullable String message;
    private static final AtomicBoolean clientRequestsDisabled = new AtomicBoolean();

    public static boolean isClientRequestsDisabled() {
        return clientRequestsDisabled.get();
    }

    public static @NotNull Pair<CodeWizStatus, String> getCurrentStatus() {
        return (ApplicationManager.getApplication().getService(CodeWizStatusService.class)).getStatus();
    }

    public CodeWizStatusService() {
        /*if (PluginToUIMsgService.isLogin()) {
            this.status = CopilotStatus.Ready;
        } else {
            this.status = CopilotStatus.NotSignedIn;
        }*/
        this.status = CodeWizStatus.Ready;
        ApplicationManager.getApplication().getMessageBus().connect(this).subscribe(CodeWizStatusListener.TOPIC, this);
    }

    public static void notifyApplication(@NotNull CodeWizStatus status) {
        notifyApplication(status, null);
    }

    public static void notifyApplication(@NotNull CodeWizStatus status, @Nullable String customMessage) {
        ApplicationManager.getApplication().getMessageBus().syncPublisher(CodeWizStatusListener.TOPIC).onCodeWizStatus(status, customMessage);
    }

    public void onCodeWizStatus(@NotNull CodeWizStatus status, @Nullable String customMessage) {
        boolean notify = false;
        synchronized(this.lock) {
            CodeWizStatus oldStatus = this.status;
            if (!oldStatus.isDisablingClientRequests()) {
                notify = this.status != status;
                this.status = status;
                this.message = customMessage;
            }
        }

        if (status.isDisablingClientRequests()) {
            boolean changed = clientRequestsDisabled.compareAndSet(false, true);
            if (changed && status == CodeWizStatus.IncompatibleClient) {
                Project project = ApplicationUtil.findCurrentProject();
                if (project != null) {
                    ApplicationManager.getApplication().invokeLater(() -> {
                        showRequestsDisabledNotification(project);
                    });
                }
            }
        }

        if (notify) {
            this.updateAllStatusBarIcons();
        }

    }

    public void dispose() {
    }

    private @NotNull Pair<CodeWizStatus, String> getStatus() {
        synchronized(this.lock) {
            return Pair.create(this.status, this.message);
        }
    }

    private void updateAllStatusBarIcons() {
        Runnable action = () -> {
            Project[] var0 = ProjectManager.getInstance().getOpenProjects();
            int var1 = var0.length;

            for(int var2 = 0; var2 < var1; ++var2) {
                Project project = var0[var2];
                if (!project.isDisposed()) {
                    CodeWizStatusBarWidget.update(project);
                }
            }

        };
        Application application = ApplicationManager.getApplication();
        if (application.isDispatchThread()) {
            action.run();
        } else {
            application.invokeLater(action);
        }

    }

    @RequiresEdt
    private static void showRequestsDisabledNotification(@NotNull Project project) {
//        Notification notification = CopilotNotifications.createFullContentNotification(CopilotBundle.get("requestsDisabledNotification.title"), CopilotBundle.get("requestsDisabledNotification.text"), NotificationType.ERROR, true);
//        notification.addAction(NotificationAction.createSimpleExpiring(CopilotBundle.get("requestsDisabledNotification.checkUpdates"), () -> {
//            (new CopilotPluginUpdater.CheckUpdatesTask(project, true)).queue();
//        }));
//        notification.addAction(NotificationAction.createSimpleExpiring(CopilotBundle.get("requestsDisabledNotification.hide"), EmptyRunnable.getInstance()));
//        notification.notify(project);
    }
}


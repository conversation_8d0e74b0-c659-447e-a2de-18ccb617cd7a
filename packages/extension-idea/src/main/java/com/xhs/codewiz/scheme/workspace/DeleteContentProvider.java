package com.xhs.codewiz.scheme.workspace;

import java.util.*;
/**
 * 删除内容提供者
 */
public class DeleteContentProvider {
    private String schemaProtocol = "workspace.delete.contentprovider";
    /** 删除的内容提供者 ID */
    private String params;

    public String getParams() {
        return params;
    }
    public void setParams(String params) {
        this.params = params;
    }
    public String getSchemaProtocol() {
        return schemaProtocol;
    }
}

package com.xhs.codewiz.utils.reference;

import com.intellij.openapi.application.ApplicationManager;
import java.util.Map;
import org.jetbrains.annotations.NotNull;

public interface FeatureService {
  @NotNull
  public static FeatureService getInstance() {
    FeatureService service = (FeatureService)ApplicationManager.getApplication().getService(FeatureService.class);
    FeatureService featureService = service != null ? service : FeatureServiceImpl.getImplInstance();
    if (featureService == null) {
      FeatureService.$$$reportNull$$$0(0);
    }
    return featureService;
  }

  public void updateFeatures(Map<String, String> var1);

  public void updateFeatures(Object var1);

  public Integer getIntegerFeature(String var1);

  public Integer getIntegerFeature(String var1, Integer var2);

  public String getStringFeature(String var1);

  public String getStringFeature(String var1, String var2);

  public Boolean getBooleanFeature(String var1);

  public Boolean getBooleanFeature(String var1, Boolean var2);

  public Long getLongFeature(String var1);

  public Long getLongFeature(String var1, Long var2);

  public Double getDoubleFeature(String var1);

  public Double getDoubleFeature(String var1, Double var2);

  private static /* synthetic */ void $$$reportNull$$$0(int n) {
    throw new IllegalStateException(String.format("@NotNull method %s.%s must not return null", "com/alibabacloud/intellij/cosy/service/FeatureService", "getInstance"));
  }
}
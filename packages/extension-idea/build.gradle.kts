plugins {
    id("java")
    id("org.jetbrains.kotlin.jvm") version "2.0.0"
    id("org.jetbrains.intellij.platform") version "2.5.0"

}

group = "com.xhs.codewiz"
version = "0.4.2"

repositories {
    maven { url = uri("https://artifactory.devops.xiaohongshu.com/artifactory/maven-releases") }
    maven { url = uri("https://maven.aliyun.com/repository/public") }
    maven { url = uri("https://maven.aliyun.com/repository/central") }
    intellijPlatform {
        defaultRepositories()
    }
    /*mavenCentral()*/
}
var type = "IC"
var buildVersion = "2025.1"
dependencies {
    intellijPlatform {
        //intellijIdeaCommunity("2025.1")
        create(type, buildVersion)
        bundledPlugin("com.intellij.java")
        bundledPlugin("Git4Idea")
        bundledPlugin("org.jetbrains.completion.full.line")
        bundledPlugin("org.jetbrains.plugins.terminal")
    }
    implementation(fileTree(mapOf("dir" to "lib", "include" to listOf("*.jar"))))
    implementation ("org.apache.commons:commons-collections4:4.4")
    implementation ("org.reflections:reflections:0.9.10")
    implementation ("com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.13.0")
    implementation ("org.eclipse.lsp4j:org.eclipse.lsp4j:0.12.0")
    implementation ("org.eclipse.lsp4j:org.eclipse.lsp4j.websocket:0.12.0")
    implementation ("org.apache.tomcat.embed:tomcat-embed-core:9.0.52")
    implementation ("org.apache.tomcat.embed:tomcat-embed-websocket:9.0.52")
    implementation ("javax.websocket:javax.websocket-api:1.1")
    implementation ("org.apache.commons:commons-text:1.13.0")
    implementation("com.google.code.gson:gson:2.8.9")
    implementation ("com.alibaba.fastjson2:fastjson2:2.0.53")
    /*implementation("org.projectlombok:lombok:1.16.10")
    implementation ("org.eclipse.jetty:jetty-server:11.0.7")
    implementation ("org.eclipse.jetty:jetty-servlet:11.0.7")
    implementation ("org.slf4j:slf4j-api:2.0.4")


    implementation ("org.eclipse.lsp4j:org.eclipse.lsp4j:0.12.0")
    implementation ("org.eclipse.xtext:org.eclipse.xtext.xbase.lib:2.37.0")

    implementation ("net.sourceforge.plantuml:plantuml:1.2023.11")


    testImplementation("junit:junit:4.13.2")*/
}

intellijPlatform {
    pluginConfiguration {
        ideaVersion {
            sinceBuild = "231"
            untilBuild = "252.*"
        }
        vendor {
            email = "<EMAIL>"
            name = "Rednote"
        }
        name = "Rednote CodeWiz"
        description = """
<h2>什么是 Rednote CodeWiz？</h2>
<p><strong>Rednote CodeWiz</strong> 是深度集成于您IDE的智能编程助手，旨在通过无缝融合的AI能力，助您高效编码、调试与协作。</p>
 
<h3>核心功能一览</h3>
<ol>
  <li><strong>编码加速</strong>
    <ul>
      <li><strong>智能代码补全：</strong>编写代码时，实时预测并生成代码片段。告别重复劳动，专注于业务逻辑。</li>
      <li><strong>Agent：</strong>在IDE里直接与AI对话。无论是debug、重构代码，还是学习新技术，随时向它提问，即刻获得答案。</li>
    </ul>
  </li>
  <li><strong>代码解释增强</strong>
    <ul>
      <li><strong>不止于代码：</strong>选中任意一段代码，它不仅能告诉你"这段代码是做什么的"，更能告诉你它的"前世今生"！</li>
      <li><strong>内部上下文自动关联：</strong>我们独家整合了公司内部的知识库。解释代码时，它会自动附上相关的<strong>MR记录、REDoc、PingCode</strong>，让你瞬间洞悉代码背后的完整故事。</li>
    </ul>
  </li>
  <li><strong>智能助手</strong>
    <ul>
      <li><strong>LocalCR：</strong>提交代码前，让AI帮你做一次快速Review，提前发现潜在问题和优化点，并支持一键Fix。</li>
      <li><strong>CreateMR：</strong>在窗口输入命令，即可在IDE内完成云效MR的创建，自动为你生成精准、完善的MR标题与描述等信息。</li>
      <li><strong>PipelineDeploy：</strong>在IDE内直接部署分支到指定环境/泳道，并无缝连接远程环境进行Debug</li>
    </ul>
  </li>
</ol>
        """.trimIndent()
        changeNotes = """
<h3>0.4.2</h3>
<ul>
  <li><strong>FEAT:编译部署场景，识别本地未提交 commit，提交后触发 build/deploy</strong></li>
  <li><strong>FEAT:支持热更新，通过发布平台实现release/pre-release/beta的交替升级</strong></li>
  <li><strong>FEAT:点赞点踩 - Chat 列表支持 LocalCR Review Comments thumpsup/thumpsdown 功能</strong></li>
  <li><strong>FEAT:Terminal运行指令支持oh-my-zsh</strong></li>
  <li>FIX:一直loading问题修复</li>
  <li>FIX:对已打开文件编辑时有概率无法编辑</li>
  <li>FIX:模型返回为空问题修复</li>
  <li>FIX:修复2023.1部分场景下list的api兼容问题</li>
  <li>FIX:错误样式修复</li>
</ul>
<h3>0.3.2</h3>
<ul>
  <li><strong>FEAT：- 支持2025.2版本</strong></li>
  <li><strong>FEAT：- 编译部署-支持获取当前项目分支的历史泳道、编译错误时，对错误进行Fix、支持beta环境</strong></li>
  <li><strong>FEAT：- 插件包体积治理</li>
  <li><strong> FIX：- Terminal运行指令期间，提前返回结果问题修复</li>
  <li><strong> FIX：- 自动更新(未重启)导致新打开项目无法使用插件问题修复</li>
  <li><strong> FIX：- 中文路径导致rcs打开文件失败修复</li>
  <li><strong> FIX：- 修复EDT线程执行问题和DiffPanel跳转问题</li>
</ul>
<h3>0.2.6</h3>
<ul>
  <li><strong>代码补全</strong> - 智能代码补全功能，实时预测并生成代码片段</li>
  <li><strong>代码解释增强</strong> - 深度代码解释，自动关联内部知识库，提供代码的"前世今生"</li>
  <li><strong>支持LocalCR、CreateMR以及PipelineDeploy</strong> - 完整的开发流程支持，从代码审查到合并请求创建，再到管道部署</li>
</ul>
    """.trimIndent()
    }
}

tasks {
    // Set the JVM compatibility versions
    withType<JavaCompile> {
        options.encoding = "UTF-8"
        sourceCompatibility = "17"
        targetCompatibility = "17"
    }
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        kotlinOptions.jvmTarget = "17"
    }

    signPlugin {
        certificateChain.set(System.getenv("CERTIFICATE_CHAIN"))
        privateKey.set(System.getenv("PRIVATE_KEY"))
        password.set(System.getenv("PRIVATE_KEY_PASSWORD"))
    }

    publishPlugin {
        token.set(System.getenv("PUBLISH_TOKEN"))
    }

    buildPlugin {
        archiveBaseName.set("codewiz-idea-plugin")
    }
}

//跳过检测
tasks.buildSearchableOptions {
    enabled = false
}

val projectName = rootProject.name

/**
 * 这个是编译打包用：将额外内容(ls)提前复制出来
 **/
tasks.named<ProcessResources>("processResources") {
    dependsOn("copyFilesFromServerForPackage")
    exclude("**/server/**") //这些文件不需要再打到jar包里
}
tasks.register<Copy>("copyFilesFromServerForPackage") {
    from("src/main/resources/server") {
        into("server_lib")
    }
    into("$buildDir/intellijPlugin/updated/$projectName") // 指定目标文件夹
}


/**
 * 这个是本地运行用：在本地执行前将额外内容(ls)提前复制到沙箱环境下
 *
 **/
tasks.named("runIde") {
    val cefCacheDir = file("$buildDir/idea-sandbox/$type-$buildVersion/plugins/$projectName/cefcache")
    logger.lifecycle("dir == ${cefCacheDir.absolutePath}")
    if (cefCacheDir.exists()) {
        logger.lifecycle("删除 CEF 缓存目录: ${cefCacheDir.absolutePath}")
        val result = cefCacheDir.deleteRecursively()
        logger.lifecycle("删除结果: $result")
        if (result) {
            logger.lifecycle("CEF 缓存目录删除成功")
        } else {
            logger.lifecycle("CEF 缓存目录删除失败")
        }
    } else {
        logger.lifecycle("CEF 缓存目录不存在: ${cefCacheDir.absolutePath}")
    }
    doFirst {
        copy {
            from("src/main/resources/server") {
                into("server_lib")
            }
            into("$buildDir/idea-sandbox/$type-$buildVersion/plugins/$projectName")
        }
    }
}


/**
 * 下边是为了给打出的zip包加额外内容
 */
//默认打出的zip包的名称
val zipName = projectName + "-" + version + ".zip"
var newZipName = "rednote-codewiz-" + version + ".zip"

//该方法用于合并原zip内容&&html内容
var zipPluginWithExtraFiles = tasks.register<Zip>("zipPluginWithExtraFiles") {
    //需要去处理加入html内容
    dependsOn(addZipFiles)
    from("$buildDir/intellijPlugin/updated")
    //新打包
    archiveFileName.set(newZipName)
    destinationDirectory.set(file("$buildDir/distributions/"))
}
//获取buildPlugin任务实例
val buildPluginTask = tasks.named("buildPlugin")
//为该任务设置后置操作，解压并添加额外的html内容
buildPluginTask.configure {
    finalizedBy(zipPluginWithExtraFiles)
}
var addZipFiles = tasks.register<Copy>("addZipFiles") {
    //先解压已经打好的zip文件，提供给后续添加html内容
    dependsOn(unzipPlugin)
    from("$buildDir/intellijPlugin/unzipped/")
    //from(extraFilesDir)
    into("$buildDir/intellijPlugin/updated")
}
var unzipPlugin = tasks.register<Copy>("unzipPlugin") {
    dependsOn(buildPluginTask)
    from(zipTree("$buildDir/distributions/$zipName"))
    into("$buildDir/intellijPlugin/unzipped")
    includeEmptyDirs = false
}

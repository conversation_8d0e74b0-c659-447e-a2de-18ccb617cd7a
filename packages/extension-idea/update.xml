<plugins>
  <plugin id="Code Wiz" url="https://artifactory.devops.xiaohongshu.com/artifactory/generic-codewiz-plugin/prod/jetbrains/rednote-codewiz-0.3.3.zip" version="0.3.3">
    <name>Rednote CodeWiz</name>
    <description><![CDATA[
<h2>什么是 Rednote CodeWiz？</h2>
<p><strong>Rednote CodeWiz</strong> 是深度集成于您IDE的智能编程助手，旨在通过无缝融合的AI能力，助您高效编码、调试与协作。</p>

<h3>核心功能一览</h3>
<ol>
  <li><strong>编码加速</strong>
    <ul>
      <li><strong>智能代码补全：</strong>编写代码时，实时预测并生成代码片段。告别重复劳动，专注于业务逻辑。</li>
      <li><strong>Agent：</strong>在IDE里直接与AI对话。无论是debug、重构代码，还是学习新技术，随时向它提问，即刻获得答案。</li>
    </ul>
  </li>
  <li><strong>代码解释增强</strong>
    <ul>
      <li><strong>不止于代码：</strong>选中任意一段代码，它不仅能告诉你"这段代码是做什么的"，更能告诉你它的"前世今生"！</li>
      <li><strong>内部上下文自动关联：</strong>我们独家整合了公司内部的知识库。解释代码时，它会自动附上相关的<strong>MR记录、REDoc、PingCode</strong>，让你瞬间洞悉代码背后的完整故事。</li>
    </ul>
  </li>
  <li><strong>智能助手</strong>
    <ul>
      <li><strong>LocalCR：</strong>提交代码前，让AI帮你做一次快速Review，提前发现潜在问题和优化点，并支持一键Fix。</li>
      <li><strong>CreateMR：</strong>在窗口输入命令，即可在IDE内完成云效MR的创建，自动为你生成精准、完善的MR标题与描述等信息。</li>
      <li><strong>PipelineDeploy：</strong>在IDE内直接部署分支到指定环境/泳道，并无缝连接远程环境进行Debug</li>
    </ul>
  </li>
</ol>
        ]]></description>
    <vendor email="<EMAIL>" url="https://yunxiao.devops.xiaohongshu.com/">Rednote</vendor>
    <change-notes>
      <![CDATA[
<h3>0.3.3</h3>
<ul>
  <li><strong>FEAT：- 支持2025.2版本</strong></li>
  <li><strong>FEAT：- 编译部署-支持获取当前项目分支的历史泳道、编译错误时，对错误进行Fix、支持beta环境</strong></li>
  <li><strong>FEAT：- 插件包体积治理</li>
  <li><strong> FIX：- Terminal运行指令期间，提前返回结果问题修复</li>
  <li><strong> FIX：- 自动更新(未重启)导致新打开项目无法使用插件问题修复</li>
  <li><strong> FIX：- 中文路径导致rcs打开文件失败修复</li>
  <li><strong> FIX：- 修复EDT线程执行问题和DiffPanel跳转问题</li>
</ul>
<h3>0.2.6</h3>
<ul>
  <li><strong>代码补全</strong> - 智能代码补全功能，实时预测并生成代码片段</li>
  <li><strong>代码解释增强</strong> - 深度代码解释，自动关联内部知识库，提供代码的"前世今生"</li>
  <li><strong>支持LocalCR、CreateMR以及PipelineDeploy</strong> - 完整的开发流程支持，从代码审查到合并请求创建，再到管道部署</li>
</ul>

]]>
    </change-notes>
  </plugin>
  <!-- 如果有其他插件，可以继续添加 -->
</plugins>